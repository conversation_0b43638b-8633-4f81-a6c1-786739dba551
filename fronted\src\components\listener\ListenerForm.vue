<template>
  <a-modal
    :title="isEdit ? '编辑监听器' : '创建监听器'"
    :open="props.open"
    :confirm-loading="loading"
    @cancel="handleCancel"
    @ok="handleSubmit"
    width="700px"
  >
    <a-form
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      ref="formRef"
    >
      <a-form-item label="监听类型" name="type">
        <a-select v-model:value="form.type" placeholder="请选择监听类型">
          <a-select-option value="tcp">TCP</a-select-option>
          <a-select-option value="pipe">PIPE</a-select-option>
          <a-select-option value="websocket">WebSocket</a-select-option>
          <a-select-option value="kcp">KCP</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="本地监听地址" name="localListenAddr">
        <a-input 
          v-model:value="form.localListenAddr" 
          placeholder="格式: IP:端口，例如 0.0.0.0:8080" 
        />
      </a-form-item>

      <a-form-item 
        :label="form.type === 'websocket' ? '远程WS服务地址' : '远程连接地址'" 
        name="remoteConnectAddr"
      >
        <a-input 
          v-model:value="form.remoteConnectAddr" 
          :placeholder="form.type === 'websocket' ? '格式: ws://example.com:8080/path' : '格式: IP:端口，例如 example.com:8080'" 
        />
      </a-form-item>

      <a-form-item v-if="form.type !== 'pipe'" label="加密密钥" name="key">
        <a-input 
          v-model:value="form.key" 
          placeholder="用于流量加密的密钥" 
        />
      </a-form-item>

      <a-form-item v-if="form.type !== 'pipe'" label="加密盐值" name="salt">
        <a-input 
          v-model:value="form.salt" 
          placeholder="用于流量加密的盐值" 
        />
      </a-form-item>

      <a-form-item v-if="form.type !== 'pipe'" label="心跳包间隔(秒)" name="pingDuration">
        <a-input-number 
          v-model:value="form.pingDuration" 
          :min="1" 
          :max="3600" 
          style="width: 100%" 
        />
      </a-form-item>

      <a-form-item v-if="form.type !== 'pipe'" label="最大超时次数" name="maxTimeoutCount">
        <a-input-number 
          v-model:value="form.maxTimeoutCount" 
          :min="1" 
          :max="100" 
          style="width: 100%" 
        />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status">
          <a-radio :value="1">启用</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea 
          v-model:value="form.remark" 
          placeholder="请输入备注信息" 
          :rows="2" 
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { listenerApi } from '@/api';

// 接收父组件传递的属性
const props = defineProps({
  open: {
    type: Boolean,
    required: true
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:open', 'success']);

// 表单引用
const formRef = ref(null);

// 状态变量
const loading = ref(false);

// 表单数据
const form = reactive({
  id: undefined,
  type: 'tcp',
  localListenAddr: '',
  remoteConnectAddr: '',
  key: '',
  salt: '',
  pingDuration: 30,
  maxTimeoutCount: 3,
  status: 1,
  remark: ''
});

// 表单验证规则
const rules = {
  type: [
    { required: true, message: '请选择监听类型', trigger: 'change' }
  ],
  localListenAddr: [
    { required: true, message: '请输入本地监听地址', trigger: 'blur' },
    { pattern: /^.+:\d+$/, message: '格式应为 IP:端口', trigger: 'blur' }
  ],
  remoteConnectAddr: [
    { required: true, message: '请输入远程连接地址', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (form.type === 'websocket') {
          // WebSocket URL格式验证
          if (!/^(ws|wss):\/\/.+/.test(value)) {
            callback(new Error('WebSocket地址应以ws://或wss://开头'));
          } else {
            callback();
          }
        } else {
          // 普通IP:端口格式验证
          if (!/^.+:\d+$/.test(value)) {
            callback(new Error('格式应为 IP:端口'));
          } else {
            callback();
          }
        }
      },
      trigger: 'blur'
    }
  ],
  key: [
    { 
      validator: (rule, value, callback) => {
        if (form.type !== 'pipe' && !value) {
          callback(new Error('请输入加密密钥'));
        } else {
          callback();
        }
      },
      trigger: 'blur' 
    }
  ],
  salt: [
    { 
      validator: (rule, value, callback) => {
        if (form.type !== 'pipe' && !value) {
          callback(new Error('请输入加密盐值'));
        } else {
          callback();
        }
      },
      trigger: 'blur' 
    }
  ],
  pingDuration: [
    { 
      validator: (rule, value, callback) => {
        if (form.type !== 'pipe') {
          if (!value) {
            callback(new Error('请输入心跳包间隔'));
          } else if (value < 1 || value > 3600) {
            callback(new Error('心跳包间隔应在1-3600秒之间'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: 'blur' 
    }
  ],
  maxTimeoutCount: [
    { 
      validator: (rule, value, callback) => {
        if (form.type !== 'pipe') {
          if (!value) {
            callback(new Error('请输入最大超时次数'));
          } else if (value < 1 || value > 100) {
            callback(new Error('最大超时次数应在1-100之间'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: 'blur' 
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 监听formData变化，更新表单数据
watch(() => props.formData, (newVal) => {
  if (newVal) {
    Object.keys(form).forEach(key => {
      if (newVal[key] !== undefined) {
        form[key] = newVal[key];
      }
    });
  }
}, { deep: true, immediate: true });

// 监听监听器类型变化，更新表单字段
watch(() => form.type, (newType) => {
  // 如果切换到pipe类型，清空不需要的字段
  if (newType === 'pipe') {
    form.key = '';
    form.salt = '';
    form.pingDuration = 0;
    form.maxTimeoutCount = 0;
  } else if (newType === 'websocket') {
    // 如果切换到WebSocket类型，确保远程地址格式正确
    if (form.remoteConnectAddr && !form.remoteConnectAddr.startsWith('ws://') && !form.remoteConnectAddr.startsWith('wss://')) {
      form.remoteConnectAddr = 'ws://' + form.remoteConnectAddr;
    }
    // 设置默认值
    if (!form.pingDuration) form.pingDuration = 30;
    if (!form.maxTimeoutCount) form.maxTimeoutCount = 3;
  } else {
    // 其他类型设置默认值
    if (!form.pingDuration) form.pingDuration = 30;
    if (!form.maxTimeoutCount) form.maxTimeoutCount = 3;
  }
  
  // 如果表单已经初始化，重新验证
  if (formRef.value) {
    formRef.value.validate().catch(() => {});
  }
});

// 处理取消
const handleCancel = () => {
  emit('update:open', false);
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    if (!formRef.value) return;
    
    await formRef.value.validate();
    loading.value = true;
    
    if (props.isEdit) {
      await listenerApi.updateListener(form);
      message.success('更新成功');
    } else {
      await listenerApi.createListener(form);
      message.success('创建成功');
    }
    
    emit('success');
    emit('update:open', false);
    resetForm();
  } catch (error) {
    console.error('表单提交失败:', error);
    message.error('表单提交失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};
</script>