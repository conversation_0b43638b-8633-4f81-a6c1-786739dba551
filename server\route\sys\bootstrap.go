package sys

import (
	"server/api"
)

type RouteGroup struct {
	AuthRoute
	DashboardRoute
	ConfigRoute
	DownloadRouter
	UploadRouter
	UserManageRoute
	LogRoute
	FileTransferStatsRouter
	PerformanceRouter
	NotificationRoute
}

var (
	authApi              = api.ApiGroupManagerAPP.SysApiGroup.AuthApi
	dashboardApi         = api.ApiGroupManagerAPP.SysApiGroup.DashboardApi
	configApi            = api.ApiGroupManagerAPP.SysApiGroup.ConfigApi
	uploadApi            = api.ApiGroupManagerAPP.SysApiGroup.UploadApi
	userManageApi        = api.ApiGroupManagerAPP.SysApiGroup.UserManageApi
	logApi               = api.ApiGroupManagerAPP.SysApiGroup.LogApi
	fileTransferStatsApi = api.ApiGroupManagerAPP.SysApiGroup.FileTransferStatsApi
	performanceApi       = api.ApiGroupManagerAPP.SysApiGroup.PerformanceApi
	notificationApi      = api.ApiGroupManagerAPP.SysApiGroup.NotificationApi
)
