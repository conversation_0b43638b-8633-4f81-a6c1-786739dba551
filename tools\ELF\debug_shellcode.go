package main

import (
	"encoding/binary"
	"fmt"
	"os"
)

func main() {
	if len(os.Args) != 2 {
		fmt.Fprintf(os.<PERSON>derr, "Usage: %s <shellcode_file>\n", os.Args[0])
		os.Exit(1)
	}

	data, err := os.ReadFile(os.Args[1])
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to read file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("File size: %d bytes\n", len(data))
	fmt.Printf("First 120 bytes (hex):\n")
	
	for i := 0; i < 120 && i < len(data); i += 16 {
		fmt.Printf("%04x: ", i)
		for j := 0; j < 16 && i+j < len(data) && i+j < 120; j++ {
			fmt.Printf("%02x ", data[i+j])
		}
		fmt.Printf("\n")
	}

	// Try to find ELF size at different offsets
	fmt.Printf("\nTrying to parse ELF size at different offsets:\n")
	for offset := 100; offset < 120 && offset+8 <= len(data); offset++ {
		elfSize := binary.LittleEndian.Uint64(data[offset:offset+8])
		fmt.Printf("Offset %d: %d (0x%x)\n", offset, elfSize, elfSize)
	}

	// Look for ELF magic
	fmt.Printf("\nLooking for ELF magic (7f 45 4c 46):\n")
	for i := 0; i < len(data)-4; i++ {
		if data[i] == 0x7f && data[i+1] == 0x45 && data[i+2] == 0x4c && data[i+3] == 0x46 {
			fmt.Printf("Found ELF magic at offset %d\n", i)
		}
	}
}
