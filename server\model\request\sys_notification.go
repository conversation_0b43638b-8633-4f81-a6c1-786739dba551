package request

import (
	"server/model/sys"
)

// NotificationListRequest 通知列表请求
type NotificationListRequest struct {
	PageInfo
	Type     string `json:"type" form:"type"`         // 通知类型过滤
	Level    string `json:"level" form:"level"`       // 通知级别过滤
	ReadOnly *bool  `json:"readOnly" form:"readOnly"` // 只显示已读/未读
}

// NotificationMarkReadRequest 标记已读请求
type NotificationMarkReadRequest struct {
	IDs []uint `json:"ids" binding:"required,min=1"` // 通知ID列表
}

// NotificationMarkAllReadRequest 标记全部已读请求
type NotificationMarkAllReadRequest struct {
	Type  string `json:"type"`  // 可选：只标记特定类型的通知
	Level string `json:"level"` // 可选：只标记特定级别的通知
}

// NotificationDeleteRequest 删除通知请求
type NotificationDeleteRequest struct {
	IDs []uint `json:"ids" binding:"required,min=1"` // 通知ID列表
}

// NotificationCreateRequest 创建通知请求（内部使用）
type NotificationCreateRequest struct {
	UserID  uint                     `json:"userId" binding:"required"`
	Type    sys.NotificationType     `json:"type" binding:"required"`
	Level   sys.NotificationLevel    `json:"level"`
	Title   string                   `json:"title" binding:"required,max=255"`
	Content string                   `json:"content" binding:"required,max=1000"`
	Data    sys.NotificationData     `json:"data"`
}

// NotificationBatchCreateRequest 批量创建通知请求（内部使用）
type NotificationBatchCreateRequest struct {
	UserIDs []uint                   `json:"userIds" binding:"required,min=1"`
	Type    sys.NotificationType     `json:"type" binding:"required"`
	Level   sys.NotificationLevel    `json:"level"`
	Title   string                   `json:"title" binding:"required,max=255"`
	Content string                   `json:"content" binding:"required,max=1000"`
	Data    sys.NotificationData     `json:"data"`
}

// NotificationSettingsRequest 通知设置请求
type NotificationSettingsRequest struct {
	ClientOnline     bool `json:"clientOnline"`     // 客户端上线通知
	ClientOffline    bool `json:"clientOffline"`    // 客户端离线通知
	ClientDeleted    bool `json:"clientDeleted"`    // 客户端删除通知
	ListenerCreated  bool `json:"listenerCreated"`  // 监听器创建通知
	ListenerClosed   bool `json:"listenerClosed"`   // 监听器关闭通知
	ListenerDeleted  bool `json:"listenerDeleted"`  // 监听器删除通知
	ServerAlert      bool `json:"serverAlert"`      // 服务器告警通知
	SystemWarning    bool `json:"systemWarning"`    // 系统警告通知
	SoundEnabled     bool `json:"soundEnabled"`     // 声音提醒
	DesktopEnabled   bool `json:"desktopEnabled"`   // 桌面通知
	EmailEnabled     bool `json:"emailEnabled"`     // 邮件通知
	DisplayDuration  int  `json:"displayDuration"`  // 显示时长（毫秒）
	MaxNotifications int  `json:"maxNotifications"` // 最大通知数量
}
