//go:build linux
// +build linux

package common

import (
	"fmt"
	"log"
	"runtime"
	"sync"
	"sync/atomic"
)

type FragmentBuffer struct {
	fragments [][]byte     // 使用slice替代map，提升访问速度
	totalSize int          // 预计算总大小，避免重复计算
	received  uint32       // 原子计数器，无锁操作
	expected  uint32       // 期望分片数
	mu        sync.RWMutex // 读写锁，提升并发性能
}

// 🚀 内存池优化 - 复用缓冲区
var (
	fragmentSlicePool = sync.Pool{
		New: func() interface{} {
			return make([][]byte, 0, 64) // 预分配64个分片容量
		},
	}

	dataBufferPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 1024*1024) // 预分配1MB缓冲区
		},
	}

	fragmentBufferPool = sync.Pool{
		New: func() interface{} {
			return &FragmentBuffer{}
		},
	}
)

// NewFragmentBuffer 创建新的分片缓冲区
func NewFragmentBuffer() *FragmentBuffer {
	fb := fragmentBufferPool.Get().(*FragmentBuffer)

	// 重置状态
	fb.fragments = fragmentSlicePool.Get().([][]byte)[:0]
	fb.totalSize = 0
	atomic.StoreUint32(&fb.received, 0)
	atomic.StoreUint32(&fb.expected, 0)

	return fb
}

// FragmentData 将大数据分片
func FragmentData(data []byte, maxFragmentSize int) [][]byte {
	if len(data) <= maxFragmentSize {
		return [][]byte{data}
	}

	//var fragments [][]byte
	//for i := 0; i < len(data); i += maxFragmentSize {
	//	end := i + maxFragmentSize
	//	if end > len(data) {
	//		end = len(data)
	//	}
	//	fragments = append(fragments, data[i:end])
	//}
	//return fragments
	// 🚀 预计算分片数量，避免slice扩容
	fragmentCount := (len(data) + maxFragmentSize - 1) / maxFragmentSize
	fragments := make([][]byte, fragmentCount)

	// 🚀 批量处理，减少循环开销
	for i := 0; i < fragmentCount; i++ {
		start := i * maxFragmentSize
		end := start + maxFragmentSize
		if end > len(data) {
			end = len(data)
		}
		// 🚀 零拷贝：直接引用原始数据的slice，不进行内存拷贝
		fragments[i] = data[start:end]
	}

	return fragments
}

// GenerateLabel 生成唯一标签
func (cm *ConnectionManager) GenerateLabel() uint32 {
	return uint32(atomic.AddUint64(&cm.labelCounter, 1))
}

func (cm *ConnectionManager) CreatePacket(output []byte, Type, Code uint8) *Packet {
	label := cm.GenerateLabel()
	data := output
	if Type == RunCommand && Code == ExecOutput && runtime.GOOS == "darwin" {
		data = RemoveBetween(data, []byte("\u001b[1m\u001b[7m"), []byte("\u001b[27m\u001b[1m\u001b[0m"))
	}

	// 使用内存池获取Header和Packet
	header := cm.memoryPool.GetHeader()
	header.Type = Type
	header.Code = Code
	header.Label = label
	header.FragIndex = 0
	header.Flags = Nop

	packetData := &PacketData{
		Data: data,
	}

	packet := cm.memoryPool.GetPacket()
	packet.Header = header
	packet.PacketData = packetData

	return packet
}

// CreatePackets 创建可分片输出包
func (cm *ConnectionManager) CreatePackets(output []byte, Type, Code uint8) ([]*Packet, error) {
	// 如果数据较小，创建单个包
	if len(output) <= 40000 {
		packet := cm.CreatePacket(output, Type, Code)
		if err := packet.EncryptPacket(cm.metadata); err != nil {
			return nil, err
		}
		return []*Packet{packet}, nil
	}
	// 创建分片数据包
	packets := cm.CreateFragmentedPackets(Type, Code, output, 40000, cm.GenerateLabel())

	const maxConcurrency = 4
	if len(packets) > maxConcurrency {
		return cm.parallelEncryptPackets(packets)
	}

	// 对每个分片数据包进行加密
	for i, packet := range packets {
		if err := packet.EncryptPacket(cm.metadata); err != nil {
			log.Printf("加密分片数据包 %d 失败: %v", i, err)
			return nil, fmt.Errorf("加密分片数据包失败: %v", err)
		}
	}
	return packets, nil
}

func (cm *ConnectionManager) parallelEncryptPackets(packets []*Packet) ([]*Packet, error) {
	const maxWorkers = 4
	workers := len(packets)
	if workers > maxWorkers {
		workers = maxWorkers
	}

	errChan := make(chan error, len(packets))
	semaphore := make(chan struct{}, workers)

	var wg sync.WaitGroup

	for i, packet := range packets {
		wg.Add(1)
		go func(idx int, pkt *Packet) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			if err := pkt.EncryptPacket(cm.metadata); err != nil {
				errChan <- fmt.Errorf("加密分片数据包 %d 失败: %v", idx, err)
			}
		}(i, packet)
	}

	wg.Wait()
	close(errChan)

	// 检查错误
	if err := <-errChan; err != nil {
		return nil, err
	}

	return packets, nil
}

// CreateFragmentedPackets 创建分片数据包（使用内存池优化）
func (cm *ConnectionManager) CreateFragmentedPackets(taskType, taskCode uint8, data []byte, maxFragmentSize int, uniqueLabel uint32) []*Packet {

	fragments := FragmentData(data, maxFragmentSize)
	packets := make([]*Packet, len(fragments))

	for i, fragment := range fragments {
		// 使用内存池获取Header和Packet
		header := cm.memoryPool.GetHeader()
		header.Type = taskType
		header.Code = taskCode
		header.FragIndex = uint32(i)
		header.Label = uniqueLabel

		// 设置分片标志
		if len(fragments) == 1 {
			header.Flags = Nop // 无分片
		} else if i == len(fragments)-1 {
			header.Flags = NoMoreFrag // 最后一个分片
		} else {
			header.Flags = MoreFrag // 还有更多分片
		}

		packetData := &PacketData{
			Data: fragment,
		}

		packet := cm.memoryPool.GetPacket()
		packet.Header = header
		packet.PacketData = packetData
		packets[i] = packet
	}

	return packets
}

// AddFragment 添加分片
func (fb *FragmentBuffer) AddFragment(fragIndex uint32, data []byte, isLast bool) ([]byte, bool) {
	fb.mu.Lock()
	defer fb.mu.Unlock()

	// 🚀 动态扩展fragments slice
	if int(fragIndex) >= len(fb.fragments) {
		newSize := int(fragIndex) + 1
		if newSize > cap(fb.fragments) {
			// 扩容策略：2倍增长
			newCap := cap(fb.fragments) * 2
			if newCap < newSize {
				newCap = newSize
			}
			newFragments := make([][]byte, newSize, newCap)
			copy(newFragments, fb.fragments)
			fb.fragments = newFragments
		} else {
			fb.fragments = fb.fragments[:newSize]
		}
	}

	fb.fragments[fragIndex] = data
	fb.totalSize += len(data)
	atomic.AddUint32(&fb.received, 1)

	if isLast {
		atomic.StoreUint32(&fb.expected, fragIndex+1)
	}

	expected := atomic.LoadUint32(&fb.expected)
	received := atomic.LoadUint32(&fb.received)

	if expected > 0 && received == expected {
		// 🚀 预分配结果缓冲区，避免多次扩容
		result := dataBufferPool.Get().([]byte)
		if cap(result) < fb.totalSize {
			result = make([]byte, 0, fb.totalSize)
		} else {
			result = result[:0]
		}

		// 🚀 高效数据拷贝
		for i := uint32(0); i < expected; i++ {
			if fb.fragments[i] != nil {
				result = append(result, fb.fragments[i]...)
			} else {
				dataBufferPool.Put(result)
				return nil, false // 缺少分片
			}
		}

		// 创建最终结果的副本
		finalResult := make([]byte, len(result))
		copy(finalResult, result)
		dataBufferPool.Put(result)

		return finalResult, true
	}

	return nil, false
}
