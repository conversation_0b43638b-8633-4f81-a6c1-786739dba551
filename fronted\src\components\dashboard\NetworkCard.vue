<template>
  <a-card class="dashboard-card network-card" :loading="loading">
    <template #title>
      <div class="card-title">
        <GlobalOutlined />
        <span>网络</span>
      </div>
    </template>
    <div class="card-content">
      <div class="network-stats">
        <div class="network-item">
          <div class="network-icon-container upload">
            <ArrowUpOutlined class="network-icon" />
          </div>
          <div class="network-value">
            {{ systemInfo?.net?.uploadMbps > 0 ? 
               `${systemInfo.net.uploadMbps} Mbps` : 
               (systemInfo?.net?.uploadKbps > 1 ? 
                `${systemInfo.net.uploadKbps} Kbps` : 
                '0 Kbps') 
            }}
          </div>
          <div class="network-label">上传</div>
        </div>
        <div class="network-item">
          <div class="network-icon-container download">
            <ArrowDownOutlined class="network-icon" />
          </div>
          <div class="network-value">
            {{ systemInfo?.net?.downloadMbps > 0 ? 
               `${systemInfo.net.downloadMbps} Mbps` : 
               (systemInfo?.net?.downloadKbps > 1 ? 
                `${systemInfo.net.downloadKbps} Kbps` : 
                '0 Kbps') 
            }}
          </div>
          <div class="network-label">下载</div>
        </div>
      </div>
      
      <!-- 网络流量图表 -->
      <div class="network-chart">
        <div class="chart-title">实时网络流量</div>
        <div class="chart-container">
          <div class="chart-bar-container">
            <div class="chart-label">上传</div>
            <div class="chart-bar-wrapper">
              <div 
                class="chart-bar upload" 
                :style="{
                  width: `${Math.min((systemInfo?.net?.uploadKbps || 0) / 10, 100)}%`,
                  opacity: systemInfo?.net?.uploadKbps > 0 ? 1 : 0.3
                }"
              ></div>
            </div>
          </div>
          <div class="chart-bar-container">
            <div class="chart-label">下载</div>
            <div class="chart-bar-wrapper">
              <div 
                class="chart-bar download" 
                :style="{
                  width: `${Math.min((systemInfo?.net?.downloadKbps || 0) / 10, 100)}%`,
                  opacity: systemInfo?.net?.downloadKbps > 0 ? 1 : 0.3
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="network-status">
        <div class="status-indicator" :class="{ 'active': systemInfo?.net?.uploadKbps > 0 || systemInfo?.net?.downloadKbps > 0, 'inactive': !systemInfo?.net?.uploadKbps && !systemInfo?.net?.downloadKbps }"></div>
        <div class="status-text">{{ systemInfo?.net?.uploadKbps > 0 || systemInfo?.net?.downloadKbps > 0 ? '活动中' : '空闲' }}</div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { GlobalOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons-vue';

// 接收父组件传递的属性
const props = defineProps({
  systemInfo: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped lang="scss">
.dashboard-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .card-title {
    display: flex;
    align-items: center;
    
    .anticon {
      font-size: 18px;
      transition: transform 0.3s ease;
    }
    
    span {
      margin-left: 8px;
      font-weight: 500;
    }
  }
  
  &:hover .card-title .anticon {
    transform: scale(1.2);
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0;
    position: relative;
    overflow: hidden;
  }
}

// 网络卡片特殊样式
.network-card {
  .network-stats {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-bottom: 16px;
    
    .network-item {
      text-align: center;
      
      .network-icon-container {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        transition: all 0.3s ease;
        
        &.upload {
          background-color: rgba(245, 34, 45, 0.1);
          
          .network-icon {
            color: #f5222d;
          }
        }
        
        &.download {
          background-color: rgba(82, 196, 26, 0.1);
          
          .network-icon {
            color: #52c41a;
          }
        }
      }
      
      &:hover .network-icon-container {
        transform: scale(1.1);
        
        &.upload {
          background-color: rgba(245, 34, 45, 0.2);
        }
        
        &.download {
          background-color: rgba(82, 196, 26, 0.2);
        }
      }
      
      .network-icon {
        font-size: 20px;
        transition: all 0.3s ease;
      }
      
      .network-value {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .network-label {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  
  .network-chart {
    width: 100%;
    margin-bottom: 16px;
    
    .chart-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      text-align: center;
      color: rgba(0, 0, 0, 0.65);
    }
    
    .chart-container {
      width: 100%;
    }
    
    .chart-bar-container {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .chart-label {
      width: 40px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      text-align: right;
      margin-right: 8px;
    }
    
    .chart-bar-wrapper {
      flex: 1;
      height: 8px;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      overflow: hidden;
    }
    
    .chart-bar {
      height: 100%;
      border-radius: 4px;
      transition: width 0.3s ease, opacity 0.3s ease;
      
      &.upload {
        background-color: #f5222d;
      }
      
      &.download {
        background-color: #52c41a;
      }
    }
  }
  
  .network-status {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    
    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
      transition: background-color 0.3s ease;
      
      &.active {
        background-color: #52c41a;
        box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);
      }
      
      &.inactive {
        background-color: #d9d9d9;
      }
    }
    
    .status-text {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
</style>