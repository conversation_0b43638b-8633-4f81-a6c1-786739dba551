package sys

import (
	"server/global"
	"server/model/response"
	"server/service/sys"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UploadApi struct{}

// UploadFileToServerUploadDir 上传文件到上传目录
func (u *UploadApi) UploadFileToServerUploadDir(c *gin.Context) {
	u.uploadFile(c, sys.UploadTypeUpload)
}

// UploadFileToServerDownloadDir 上传文件到下载目录
func (u *UploadApi) UploadFileToServerDownloadDir(c *gin.Context) {
	u.uploadFile(c, sys.UploadTypeDownload)
}

// uploadFile 通用上传文件处理函数
func (u *UploadApi) uploadFile(c *gin.Context, uploadType sys.UploadType) {
	// 接收文件
	file, err := c.FormFile("file")
	if err != nil {
		global.LOG.Error("接收文件失败", zap.Error(err))
		response.ErrorWithMessage("接收文件失败", c)
		return
	}

	// 获取可选的子目录参数
	subDir := c.PostForm("sub_dir")

	// 构建上传请求
	req := &sys.UploadRequest{
		File:       file,
		UploadType: uploadType,
		SubDir:     subDir,
	}

	// 调用service层处理上传
	resp, err := uploadService.UploadFile(req)
	if err != nil {
		global.LOG.Error("文件上传失败",
			zap.Error(err),
			zap.String("fileName", file.Filename),
			zap.String("uploadType", string(uploadType)),
		)
		response.ErrorWithMessage("文件上传失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(resp, "文件上传成功", c)
}

// GetUploadConfig 获取上传配置信息
func (u *UploadApi) GetUploadConfig(c *gin.Context) {
	config := map[string]interface{}{
		"allowed_extensions": uploadService.GetAllowedExtensions(),
		"max_file_size":      uploadService.GetMaxFileSize(),
		"max_file_size_mb":   uploadService.GetMaxFileSize() / (1024 * 1024),
		"upload_dir":         global.CONFIG.Server.UploadDir,
		"download_dir":       global.CONFIG.Server.DownloadDir,
	}
	response.OkWithDetailed(config, "获取上传配置成功", c)
}
