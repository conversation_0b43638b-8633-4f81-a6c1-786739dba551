package c2

import (
	"fmt"

	"server/global"
	"server/model/request/fs"
	"server/model/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FileApi struct{}

// GetFileInfo 获取文件信息
func (f *FileApi) GetFileInfo(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileInfoRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.GetFileInfo(uint(clientID), req)
	if err != nil {
		global.LOG.Error("获取文件信息失败", zap.Error(err))
		response.ErrorWithMessage("获取文件信息失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "获取文件信息", clientIDStr)
}

// CopyFile 复制文件
func (f *FileApi) CopyFile(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileCopyRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.CopyFile(uint(clientID), req)
	if err != nil {
		global.LOG.Error("复制文件失败", zap.Error(err))
		response.ErrorWithMessage("复制文件失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsyncWithClientId(ctx, taskID, "复制文件", clientIDStr)
}

// DeleteFile 删除文件
func (f *FileApi) DeleteFile(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileDeleteRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.DeleteFile(uint(clientID), req)
	if err != nil {
		global.LOG.Error("删除文件失败", zap.Error(err))
		response.ErrorWithMessage("删除文件失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsyncWithClientId(ctx, taskID, "删除文件", clientIDStr)
}

// MoveFile 移动文件
func (f *FileApi) MoveFile(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileMoveRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.MoveFile(uint(clientID), req)
	if err != nil {
		global.LOG.Error("移动文件失败", zap.Error(err))
		response.ErrorWithMessage("移动文件失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsyncWithClientId(ctx, taskID, "移动文件", clientIDStr)
}

// CreateFile 创建文件
func (f *FileApi) CreateFile(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileCreateRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.CreateFile(uint(clientID), req)
	if err != nil {
		global.LOG.Error("创建文件失败", zap.Error(err))
		response.ErrorWithMessage("创建文件失败: "+err.Error(), ctx)
		return
	}

	waitForResponseAsyncWithClientId(ctx, taskID, "创建文件", clientIDStr)
}

// DownloadFileToServer 从客户端下载文件
func (f *FileApi) DownloadFileToServer(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileDownloadRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.DownloadFileFromClient(uint(clientID), req)
	if err != nil {
		global.LOG.Error("下载文件失败", zap.Error(err))
		response.ErrorWithMessage("下载文件失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(gin.H{"taskID": taskID, "msg": "开始下载文件: " + req.Path}, ctx)
}

// UploadFileToServerAndTransferToClient 上传文件到服务器
func (f *FileApi) UploadFileToServerAndTransferToClient(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		response.ErrorWithMessage("获取上传文件失败: "+err.Error(), ctx)
		return
	}
	defer file.Close()

	// 获取目标路径参数
	destinationPath := ctx.PostForm("destination_path")
	if destinationPath == "" {
		response.ErrorWithMessage("目标路径参数不能为空", ctx)
		return
	}

	filePath, err := fileService.SaveUploadedFile(file, header.Filename)
	if err != nil {
		global.LOG.Error("保存上传文件失败", zap.Error(err))
		response.ErrorWithMessage("保存上传文件失败: "+err.Error(), ctx)
		return
	}

	// 自动创建传输任务到客户端
	transferReq := fs.FileUploadToClientRequest{
		SourcePath:      filePath,
		DestinationPath: destinationPath,
		Force:           false,
		ChunkSize:       65536, // 64KB
	}

	taskID, err := fileService.UploadFileToClient(uint(clientID), transferReq)
	if err != nil {
		global.LOG.Error("创建传输任务失败", zap.Error(err))
		// 即使传输任务创建失败，文件已经上传成功，返回成功但带警告
		response.OkWithData(gin.H{
			"file_path": filePath,
			"task_id":   nil,
			"warning":   "文件上传成功，但创建传输任务失败: " + err.Error(),
		}, ctx)
		return
	}

	response.OkWithData(gin.H{
		"file_path": filePath,
		"task_id":   taskID,
		"message":   "文件上传成功，传输任务已创建",
	}, ctx)
}

// GetUploadedFiles 获取已上传的文件列表
func (f *FileApi) GetUploadedFiles(ctx *gin.Context) {
	// 获取路径参数，默认为根目录
	subPath := ctx.Query("path")
	if subPath == "" {
		subPath = ""
	}

	var files []response.ServerFile
	var err error

	if subPath == "" {
		files, err = fileService.GetUploadedFiles()
	} else {
		files, err = fileService.GetUploadedFilesWithPath(subPath)
	}

	if err != nil {
		global.LOG.Error("获取上传文件列表失败", zap.Error(err))
		response.ErrorWithMessage("获取上传文件列表失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(files, ctx)
}

// GetDownloadFiles 获取下载目录文件列表
func (f *FileApi) GetDownloadFiles(ctx *gin.Context) {
	// 获取路径参数，默认为根目录
	subPath := ctx.Query("path")
	if subPath == "" {
		subPath = ""
	}

	var files []response.ServerFile
	var err error

	if subPath == "" {
		files, err = fileService.GetDownloadFiles()
	} else {
		files, err = fileService.GetDownloadFilesWithPath(subPath)
	}

	if err != nil {
		global.LOG.Error("获取下载文件列表失败", zap.Error(err))
		response.ErrorWithMessage("获取下载文件列表失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(files, ctx)
}

// TransferServerFileToClient 传输服务器文件到客户端
func (f *FileApi) TransferServerFileToClient(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.TransferServerFileToClientRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// 验证必要参数
	if req.ServerFilePath == "" {
		response.ErrorWithMessage("服务器文件路径不能为空", ctx)
		return
	}
	if req.DestinationPath == "" {
		response.ErrorWithMessage("目标路径不能为空", ctx)
		return
	}

	taskID, err := fileService.TransferServerFileToClient(uint(clientID), req)
	if err != nil {
		global.LOG.Error("传输服务器文件失败", zap.Error(err))
		response.ErrorWithMessage("传输服务器文件失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(gin.H{
		"task_id": taskID,
		"message": fmt.Sprintf("开始传输文件: %s -> %s", req.ServerFilePath, req.DestinationPath),
	}, ctx)
}

// GetFileTransferTasks 获取文件传输任务列表
func (f *FileApi) GetFileTransferTasks(ctx *gin.Context) {
	tasks, err := fileService.GetFileTransferTasks()
	if err != nil {
		global.LOG.Error("获取文件传输任务失败", zap.Error(err))
		response.ErrorWithMessage("获取文件传输任务失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(tasks, ctx)
}

// GetFileTransferTask 获取单个文件传输任务状态
func (f *FileApi) GetFileTransferTask(ctx *gin.Context) {
	taskIDStr := ctx.Param("taskId")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", ctx)
		return
	}

	task, err := fileService.GetFileTransferTask(taskID)
	if err != nil {
		global.LOG.Error("获取文件传输任务失败", zap.Error(err))
		response.ErrorWithMessage("获取文件传输任务失败: "+err.Error(), ctx)
		return
	}

	response.OkWithData(task, ctx)
}

// CancelFileTransferTask 取消文件传输任务
func (f *FileApi) CancelFileTransferTask(ctx *gin.Context) {
	taskIDStr := ctx.Param("taskId")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", ctx)
		return
	}

	err = fileService.CancelFileTransferTask(taskID)
	if err != nil {
		global.LOG.Error("取消文件传输任务失败", zap.Error(err))
		response.ErrorWithMessage("取消文件传输任务失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("取消任务成功", ctx)
}

// ReadFileContent 读取文件内容
func (f *FileApi) ReadFileContent(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileReadRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// 设置默认最大文件大小为32MB
	if req.MaxSize <= 0 {
		req.MaxSize = 32 * 1024 * 1024
	}

	taskID, err := fileService.ReadFileContent(uint(clientID), req)
	if err != nil {
		global.LOG.Error("读取文件内容失败", zap.Error(err))
		response.ErrorWithMessage("读取文件内容失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "读取文件内容", clientIDStr)
}

// WriteFileContent 写入文件内容
func (f *FileApi) WriteFileContent(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req fs.FileWriteRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	taskID, err := fileService.WriteFileContent(uint(clientID), req)
	if err != nil {
		global.LOG.Error("写入文件内容失败", zap.Error(err))
		response.ErrorWithMessage("写入文件内容失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "写入文件内容", clientIDStr)
}

// DeleteServerFile 删除服务器本地文件
func (f *FileApi) DeleteServerFile(ctx *gin.Context) {
	type DeleteRequest struct {
		FilePath string `json:"file_path" binding:"required"`
	}

	var req DeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if err := fileService.DeleteServerFile(req.FilePath); err != nil {
		global.LOG.Error("删除服务器文件失败", zap.Error(err))
		response.ErrorWithMessage("删除文件失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage("文件删除成功", ctx)
}

// DeleteServerFiles 批量删除服务器本地文件
func (f *FileApi) DeleteServerFiles(ctx *gin.Context) {
	type BatchDeleteRequest struct {
		FilePaths []string `json:"file_paths" binding:"required"`
	}

	var req BatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	if len(req.FilePaths) == 0 {
		response.ErrorWithMessage("文件路径列表不能为空", ctx)
		return
	}

	if err := fileService.DeleteServerFiles(req.FilePaths); err != nil {
		global.LOG.Error("批量删除服务器文件失败", zap.Error(err))
		response.ErrorWithMessage("批量删除文件失败: "+err.Error(), ctx)
		return
	}

	response.OkWithMessage(fmt.Sprintf("成功删除 %d 个文件", len(req.FilePaths)), ctx)
}
