<template>
  <div class="download-manager">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>下载/上传管理</h2>
      <p>浏览和下载服务器文件</p>
    </div>



    <!-- 文件浏览区域 -->
    <a-card class="file-browser-card" :bordered="false">
      <template #title>
        <div class="section-header">
          <span>服务器文件</span>
          <a-radio-group v-model:value="currentDirectory" @change="loadFileList">
            <a-radio-button value="download">下载目录</a-radio-button>
            <a-radio-button value="upload">上传目录</a-radio-button>
          </a-radio-group>
        </div>
      </template>
      <template #extra>
        <a-space>
          <a-button 
            type="primary" 
            size="small" 
            @click="showUploadModal"
            :loading="uploadLoading"
          >
            <template #icon><UploadOutlined /></template>
            {{ currentDirectory === 'download' ? '上传到下载目录' : '上传到上传目录' }}
          </a-button>
          <a-button size="small" @click="refreshFileList" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>
      
      <!-- 面包屑导航 -->
      <div class="breadcrumb-navigation">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <a @click="navigateToPath('')">
              <HomeOutlined />
              {{ currentDirectory === 'download' ? '下载目录' : '上传目录' }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="(segment, index) in pathSegments" :key="index">
            <a @click="navigateToSegment(index)">{{ segment }}</a>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <!-- 文件列表 -->
      <div class="file-list">
        <a-spin :spinning="loading">
          <a-table
          :data-source="fileList"
          :columns="fileTableColumns"
          :row-selection="{ selectedRowKeys: selectedFileKeys, onChange: handleFileSelectionChange, getCheckboxProps: (record) => ({ disabled: record.is_dir }) }"
          row-key="path"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div 
                class="file-item-display" 
                @dblclick="handleFileNameDoubleClick(record, $event)"
                style="cursor: pointer;"
              >
                <div class="file-icon-wrapper">
                  <FolderOutlined v-if="record.is_dir" class="folder-icon" />
                  <FileOutlined v-else class="file-icon" />
                </div>
                <div class="file-info">
                  <div class="file-name" :class="{ 'is-directory': record.is_dir }">
                    {{ record.name }}
                  </div>
                  <div v-if="record.is_dir" class="file-type-hint">目录</div>
                  <div v-else-if="getFileExtension(record.name)" class="file-type-hint">
                    {{ getFileExtension(record.name).toUpperCase() }} 文件
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.key === 'size'">
              <span v-if="!record.is_dir">{{ formatFileSize(record.size) }}</span>
              <span v-else>-</span>
            </template>
            <template v-if="column.key === 'mod_time'">
              {{ formatTime(record.mod_time) }}
            </template>
            <template v-if="column.key === 'action'">
              <div class="file-actions" v-if="!record.is_dir">
                <a-button
                  type="primary"
                  size="small"
                  @click="downloadSingleFile(record)"
                  :loading="downloadingFiles.has(record.path)"
                  class="action-btn"
                >
                  <template #icon><DownloadOutlined /></template>
                  下载
                </a-button>
                <a-button
                  danger
                  size="small"
                  @click="deleteSingleFile(record)"
                  class="action-btn"
                >
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </div>
              <div class="file-actions" v-else>
                <a-button
                  type="default"
                  size="small"
                  @click="enterDirectory(record)"
                  class="action-btn"
                >
                  <template #icon><FolderOutlined /></template>
                  进入
                </a-button>
                <a-button
                  danger
                  size="small"
                  @click="deleteSingleFile(record)"
                  class="action-btn"
                >
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </div>
            </template>
          </template>
          </a-table>
        </a-spin>

        <div v-if="selectedFiles.length > 0" class="batch-actions">
          <a-space>
            <a-button
              type="primary"
              @click="downloadSelectedFiles"
              :loading="batchDownloading"
            >
              <template #icon><DownloadOutlined /></template>
              批量下载 ({{ selectedFiles.length }})
            </a-button>
            <a-button
              danger
              @click="deleteSelectedFiles"
              :loading="batchDeleting"
            >
              <template #icon><DeleteOutlined /></template>
              批量删除 ({{ selectedFiles.length }})
            </a-button>
          </a-space>
        </div>
      </div>
    </a-card>

    <!-- 下载历史 -->
    <a-card class="download-history-card" :bordered="false">
      <template #title>
        <span>下载历史</span>
      </template>
      <template #extra>
        <a-space>
          <a-button size="small" @click="refreshHistory">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button 
            size="small" 
            danger
            @click="clearHistory"
          >
            <template #icon><DeleteOutlined /></template>
            清空历史
          </a-button>
        </a-space>
      </template>
      
      <div class="download-history">
        <div v-if="downloadHistory.length > 0">
          <div 
            v-for="item in downloadHistory" 
            :key="item.id" 
            class="history-item"
            :class="`status-${item.status}`"
          >
            <div class="history-info">
              <div class="file-name">{{ item.fileName }}</div>
              <div class="file-path">{{ item.filePath }}</div>
              <div class="file-size" v-if="item.totalBytes">
                {{ formatFileSize(item.downloadedBytes) }} / {{ formatFileSize(item.totalBytes) }}
                <span v-if="item.progress !== undefined" class="progress-percent">
                  ({{ Math.round(item.progress) }}%)
                </span>
              </div>
              <div class="download-time">{{ formatTime(item.startTime) }}</div>
            </div>
            <div class="history-status">
              <a-tag :color="getStatusTagType(item.status)" size="small">
                {{ getStatusText(item.status) }}
              </a-tag>
            </div>
            <div class="history-actions">
              <a-button
                v-if="item.status === 'error'"
                size="small"
                type="primary"
                @click="retryDownload(item)"
              >
                <template #icon><ReloadOutlined /></template>
                重试
              </a-button>
            </div>
          </div>
        </div>
        <div v-else class="empty-history">
          <ClockCircleOutlined style="font-size: 48px; margin-bottom: 12px; display: block;" />
          <p>暂无下载历史</p>
        </div>
      </div>
    </a-card>



    <!-- 下载进度组件 -->
    <download-progress
      @retry-download="handleRetryDownload"
    />

    <!-- 文件上传弹窗 -->
    <a-modal
      v-model:open="uploadModalVisible"
      :title="`文件上传 - ${currentDirectory === 'download' ? '下载目录' : '上传目录'}`"
      width="800px"
      :footer="null"
      :destroyOnClose="true"
      class="upload-modal"
      @cancel="handleUploadModalClose"
    >
      <file-upload 
        :target-dir="currentDirectory"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @upload-complete="handleUploadComplete"
      />
    </a-modal>
  </div>
</template>

<script>
import DownloadProgress from '@/components/download/DownloadProgress.vue'
import FileUpload from '@/components/upload/FileUpload.vue'
import { downloadServerFile, getUploadedFiles, getDownloadFiles, deleteServerFile, deleteServerFiles } from '@/api/download'
import { message, Modal } from 'ant-design-vue'
import downloadManager from '@/utils/downloadManager'
import { 
  DeleteOutlined, 
  DownloadOutlined, 
  ReloadOutlined,
  ClockCircleOutlined,
  FolderOutlined,
  FileOutlined,
  UploadOutlined,
  HomeOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'DownloadManager',
  components: {
    DownloadProgress,
    FileUpload,
    DeleteOutlined,
    DownloadOutlined,
    ReloadOutlined,
    ClockCircleOutlined,
    FolderOutlined,
    FileOutlined,
    UploadOutlined,
    HomeOutlined
  },
  data() {
    return {
      // 文件上传
      uploadTargetDir: 'upload',
      
      // 文件浏览
      currentDirectory: 'download',
      currentPath: '',
      fileList: [],
      selectedFiles: [],
      selectedFileKeys: [],
      loading: false,
      downloadingFiles: new Set(),
      batchDownloading: false,
      batchDeleting: false,
      
      fileTableColumns: [
        {
          title: '文件名',
          dataIndex: 'name',
          key: 'name',
          ellipsis: true,
          width: '40%'
        },
        {
          title: '文件大小',
          dataIndex: 'size',
          key: 'size',
          width: '15%',
          align: 'right'
        },
        {
          title: '修改时间',
          dataIndex: 'mod_time',
          key: 'mod_time',
          width: '25%'
        },
        {
          title: '操作',
          key: 'action',
          width: '20%',
          align: 'center'
        }
      ],
      
      // 下载历史
      downloadHistory: [],
      
      // 同步定时器
      syncTimer: null,
      
      // 文件上传
      uploadModalVisible: false,
      uploadLoading: false
    }
  },
  computed: {
    pathSegments() {
      if (!this.currentPath) return []
      return this.currentPath.split('/').filter(segment => segment.length > 0)
    }
  },
  mounted() {
    this.loadDownloadHistory()
    this.loadFileList()
    this.startSyncTimer()
  },
  
  beforeUnmount() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }
  },
  methods: {
    /**
      * 加载文件列表
      */
     async loadFileList() {
       this.loading = true
       
       try {
         let files = []
         if (this.currentDirectory === 'download') {
           files = await getDownloadFiles(this.currentPath)
         } else if (this.currentDirectory === 'upload') {
           files = await getUploadedFiles(this.currentPath)
         }
         
         this.fileList = files || []
         // 清空选择
         this.selectedFiles = []
         this.selectedFileKeys = []
       } catch (error) {
         message.error(`加载文件列表失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
         this.fileList = []
       } finally {
         this.loading = false
       }
     },
    
    /**
     * 刷新文件列表
     */
    refreshFileList() {
      this.loadFileList()
    },
    
    /**
     * 处理文件选择变化
     */
    handleFileSelectionChange(selectedRowKeys, selectedRows) {
      this.selectedFileKeys = selectedRowKeys
      this.selectedFiles = selectedRows.filter(file => !file.is_dir)
    },
    
    /**
     * 下载单个文件
     */
    async downloadSingleFile(file) {
      this.downloadingFiles.add(file.path)
      
      try {
        const downloadId = downloadServerFile(
          file.path,
          file.name,
          {
            onProgress: (progressData) => {
              this.handleDownloadProgress(progressData)
              this.updateHistoryProgress(file.path, progressData)
            },
            onComplete: (data) => {
              this.handleDownloadComplete({ filePath: file.path, ...data })
              this.downloadingFiles.delete(file.path)
            },
            onError: (error) => {
              this.handleDownloadError({ filePath: file.path, ...error })
              this.downloadingFiles.delete(file.path)
            }
          }
        )
        
        this.addToHistory({
          id: downloadId,
          filePath: file.path,
          fileName: file.name,
          status: 'downloading',
          startTime: Date.now(),
          progress: 0,
          downloadedBytes: 0,
          totalBytes: 0
        })
        
        message.success('开始下载文件')
        
      } catch (error) {
        message.error(`下载失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
        this.downloadingFiles.delete(file.path)
      }
    },
    
    /**
     * 下载选中文件
     */
    async downloadSelectedFiles() {
      if (this.selectedFiles.length === 0) {
        message.warning('请先选择要下载的文件')
        return
      }
      
      this.batchDownloading = true
      
      try {
        for (const file of this.selectedFiles) {
          await this.downloadSingleFile(file)
        }
        
        message.success(`开始批量下载 ${this.selectedFiles.length} 个文件`)
        
      } catch (error) {
        message.error(`批量下载失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
      } finally {
        this.batchDownloading = false
      }
    },
    

    
    /**
     * 处理下载进度
     */
    handleDownloadProgress(progress) {
      // 进度更新由DownloadProgress组件处理
    },
    
    /**
     * 处理下载完成
     */
    handleDownloadComplete(data) {
      message.success('文件下载完成')
      this.updateHistoryStatus(data?.filePath, 'completed')
    },
    
    /**
     * 处理下载错误
     */
    handleDownloadError(error) {
      message.error(`下载失败: ${error.response?.data?.error || error.response?.data?.message || error.error || error}`)
      this.updateHistoryStatus(error?.filePath, 'error')
    },
    

    
    /**
     * 重试下载
     */
    handleRetryDownload(data) {
      this.handleSingleDownload({
        filePath: data.filePath,
        fileName: data.fileName
      })
    },
    
    /**
     * 重试下载（从历史记录）
     */
    retryDownload(historyItem) {
      this.handleSingleDownload({
        filePath: historyItem.filePath,
        fileName: historyItem.fileName
      })
    },
    
    /**
     * 删除单个文件
     */
    deleteSingleFile(file) {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件 "${file.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await deleteServerFile(file.path)
            message.success('文件删除成功')
            this.refreshFileList()
          } catch (error) {
            console.error('删除文件失败:', error)
            message.error('删除文件失败: ' + (error.response?.msg||error.response?.data?.message || error.message))
          }
        }
      })
    },

    /**
     * 批量删除文件
     */
    deleteSelectedFiles() {
      const fileCount = this.selectedFiles.length
      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${fileCount} 个文件吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          this.batchDeleting = true
          try {
            const filePaths = this.selectedFiles.map(file => file.path)
            await deleteServerFiles(filePaths)
            message.success(`成功删除 ${fileCount} 个文件`)
            this.selectedFiles = []
            this.refreshFileList()
          } catch (error) {
            console.error('批量删除文件失败:', error)
            message.error('批量删除文件失败: ' + (error.response?.data?.message || error.message))
          } finally {
            this.batchDeleting = false
          }
        }
      })
    },
    
    /**
     * 加载下载历史
     */
    loadDownloadHistory() {
      const history = localStorage.getItem('downloadHistory')
      if (history) {
        this.downloadHistory = JSON.parse(history)
      }
    },
    
    /**
     * 保存下载历史
     */
    saveDownloadHistory() {
      localStorage.setItem('downloadHistory', JSON.stringify(this.downloadHistory))
    },
    
    /**
     * 添加到历史记录
     */
    addToHistory(item) {
      this.downloadHistory.unshift(item)
      // 限制历史记录数量
      if (this.downloadHistory.length > 100) {
        this.downloadHistory = this.downloadHistory.slice(0, 100)
      }
      this.saveDownloadHistory()
    },
    
    /**
     * 更新历史状态
     */
    updateHistoryStatus(filePath, status) {
      if (!filePath) return
      
      const item = this.downloadHistory.find(h => h.filePath === filePath)
      if (item) {
        item.status = status
        if (status === 'completed') {
          item.progress = 100
        }
        this.saveDownloadHistory()
      }
    },
    
    /**
     * 更新历史进度
     */
    updateHistoryProgress(filePath, progressData) {
      if (!filePath) return
      
      const item = this.downloadHistory.find(h => h.filePath === filePath)
      if (item && progressData) {
        if (progressData.progress !== undefined) {
          item.progress = progressData.progress
        }
        if (progressData.downloadedBytes !== undefined) {
          item.downloadedBytes = progressData.downloadedBytes
        }
        if (progressData.totalBytes !== undefined) {
          item.totalBytes = progressData.totalBytes
        }
        this.saveDownloadHistory()
      }
    },
    
    /**
     * 刷新历史
     */
    refreshHistory() {
      this.loadDownloadHistory()
      message.success('历史记录已刷新')
    },
    
    /**
     * 清空历史
     */
    clearHistory() {
      Modal.confirm({
        title: '确认清空',
        content: '确定要清空下载历史吗？',
        onOk: () => {
          this.downloadHistory = []
          this.saveDownloadHistory()
          message.success('下载历史已清空')
        }
      })
    },
    
    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      const typeMap = {
        pending: 'default',
        downloading: 'processing',
        completed: 'success',
        error: 'error',
        paused: 'warning',
        ready: 'success'
      }
      return typeMap[status] || 'default'
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const textMap = {
        pending: '等待中',
        downloading: '下载中',
        completed: '已完成',
        error: '失败',
        paused: '已暂停',
        ready: '就绪'
      }
      return textMap[status] || '未知'
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
      if (!bytes) return '-'
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      const date = new Date(timestamp)
      if (isNaN(date.getTime())) {
        return 'Invalid Date'
      }
      
      return date.toLocaleString()
    },
    
    /**
     * 启动同步定时器
     */
    startSyncTimer() {
      // 每秒同步一次downloadManager的状态到历史记录
      this.syncTimer = setInterval(() => {
        this.syncDownloadStatus()
      }, 1000)
    },
    
    /**
     * 同步下载状态
     */
    syncDownloadStatus() {
      try {
        const activeDownloads = downloadManager.getAllDownloads()
        
        // 更新历史记录中对应的下载状态
        let hasUpdates = false
        this.downloadHistory.forEach(historyItem => {
          const activeDownload = activeDownloads.find(d => d.id === historyItem.id)
          if (activeDownload) {
            // 同步状态
            if (historyItem.status !== activeDownload.status) {
              historyItem.status = activeDownload.status
              hasUpdates = true
            }
            
            // 同步进度信息
            if (historyItem.progress !== activeDownload.progress) {
              historyItem.progress = activeDownload.progress || 0
              hasUpdates = true
            }
            if (historyItem.downloadedBytes !== activeDownload.downloadedBytes) {
              historyItem.downloadedBytes = activeDownload.downloadedBytes || 0
              hasUpdates = true
            }
            if (historyItem.totalBytes !== activeDownload.totalBytes) {
              historyItem.totalBytes = activeDownload.totalBytes || 0
              hasUpdates = true
            }
          }
        })
        
        // 只有在有更新时才保存
        if (hasUpdates) {
          this.saveDownloadHistory()
        }
      } catch (error) {
        console.warn('同步下载状态失败:', error)
      }
    },
    
    /**
     * 处理上传目标目录变化
     */
    handleUploadTargetChange() {
      // 上传目标目录变化时的处理逻辑
    },
    
    /**
     * 处理上传成功
     */
    handleUploadSuccess(data) {
      message.success('文件上传成功')
      // 如果当前浏览的是上传目录，刷新文件列表
      if (this.currentDirectory === 'upload') {
        this.refreshFileList()
      }
    },
    
    /**
     * 处理上传错误
     */
    handleUploadError(error) {
      message.error(`文件上传失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
    },
    
    /**
     * 处理上传完成
     */
    handleUploadComplete() {
      // 上传完成后的处理逻辑
    },
    
    /**
     * 显示上传弹窗
     */
    showUploadModal() {
      this.uploadModalVisible = true
    },

    /**
     * 处理上传弹窗关闭
     */
    handleUploadModalClose() {
      // 关闭弹窗时自动刷新当前目录
      this.refreshFileList()
    },

    /**
     * 获取文件扩展名
     */
    getFileExtension(filename) {
      if (!filename || typeof filename !== 'string') return ''
      const lastDotIndex = filename.lastIndexOf('.')
      if (lastDotIndex === -1 || lastDotIndex === 0) return ''
      return filename.substring(lastDotIndex + 1)
    },

    /**
     * 进入目录
     */
    enterDirectory(record) {
      if (!record.is_dir) return
      
      // 构建新路径
      const newPath = this.currentPath ? `${this.currentPath}/${record.name}` : record.name
      this.currentPath = newPath
      this.loadFileList()
    },

    /**
     * 导航到指定路径
     */
    navigateToPath(path) {
      this.currentPath = path
      this.loadFileList()
    },

    /**
     * 导航到路径片段
     */
    navigateToSegment(index) {
      const segments = this.pathSegments.slice(0, index + 1)
      this.currentPath = segments.join('/')
      this.loadFileList()
    },

    /**
     * 处理文件名双击事件
     */
    handleFileNameDoubleClick(record, event) {
      // 阻止默认的文本选择行为
      event.preventDefault()
      event.stopPropagation()
      
      if (record.is_dir) {
        this.enterDirectory(record)
      }
    }

  }
}
</script>

<style scoped>
.download-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #f2f2f7;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 48px 32px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
  color: #1d1d1f;
}

.page-header h2 {
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 600;
  letter-spacing: -0.5px;
  color: #1d1d1f;
}

.page-header p {
  margin: 0;
  font-size: 17px;
  color: #86868b;
  font-weight: 400;
  letter-spacing: -0.2px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin: -24px -24px 24px -24px;
}

.header-actions {
  display: flex;
  gap: 16px;
}

/* 上传弹窗样式 */
.upload-modal {
  max-height: 80vh;
  overflow-y: auto;
}

.upload-modal .ant-modal-content {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.upload-modal .ant-modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 24px 32px 20px;
  background: transparent;
  border-radius: 20px 20px 0 0;
}

.upload-modal .ant-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  letter-spacing: -0.3px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.upload-modal .ant-modal-body {
  padding: 24px 32px 32px;
  background: transparent;
}

.file-browser-card,
.download-history-card {
  margin-bottom: 24px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.file-browser-card:hover,
.download-history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1d1d1f;
  font-size: 18px;
  gap: 16px;
  letter-spacing: -0.3px;
}

.file-browser-card .ant-card-head {
  padding: 20px 28px;
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.file-browser-card .ant-card-head-title {
  padding: 0;
  margin-right: 32px;
}

.file-browser-card .ant-card-extra {
  margin-left: auto;
}

.file-list {
  margin-bottom: 20px;
}

.file-item-display {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 4px 0;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  transition: background-color 0.2s ease;
}

.file-item-display:hover {
  background-color: rgba(0, 122, 255, 0.05);
  border-radius: 8px;
}

.file-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(0, 122, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 600;
  color: #1d1d1f;
  font-size: 15px;
  line-height: 1.4;
  margin-bottom: 2px;
  word-break: break-all;
}

.file-name.is-directory {
  color: #007aff;
}

.file-type-hint {
  font-size: 12px;
  color: #86868b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.folder-icon {
  color: #007aff;
  font-size: 20px;
}

.file-icon {
  color: #34c759;
  font-size: 20px;
}

.file-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  min-width: 140px;
}

.action-btn {
  min-width: 60px;
  height: 32px;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.directory-indicator {
  color: #86868b;
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  background: rgba(0, 122, 255, 0.06);
  border-radius: 8px;
}

.batch-actions {
  padding: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  text-align: center;
  background: rgba(0, 122, 255, 0.04);
  backdrop-filter: blur(20px);
  margin: 20px -28px -28px -28px;
  border-radius: 0 0 20px 20px;
}

.breadcrumb-navigation {
  padding: 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.breadcrumb-navigation :deep(.ant-breadcrumb) {
  font-size: 14px;
}

.breadcrumb-navigation :deep(.ant-breadcrumb a) {
  color: #007aff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-navigation :deep(.ant-breadcrumb a:hover) {
  color: #0056cc;
}

.breadcrumb-navigation :deep(.anticon) {
  margin-right: 4px;
}

.empty-batch,
.empty-history {
  text-align: center;
  padding: 48px 32px;
  color: #86868b;
  background: rgba(0, 122, 255, 0.04);
  border-radius: 16px;
  margin: 24px 0;
  backdrop-filter: blur(20px);
}

.empty-batch i,
.empty-history i {
  font-size: 56px;
  margin-bottom: 16px;
  display: block;
  color: #c7c7cc;
}

.download-history {
  max-height: 400px;
  overflow-y: auto;
  padding: 0 4px;
}

.download-history::-webkit-scrollbar {
  width: 8px;
}

.download-history::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
}

.download-history::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.download-history::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 12px;
  margin: 4px 0;
}

.history-item:hover {
  background: rgba(0, 122, 255, 0.08);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  flex: 1;
}

.history-info .file-name {
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 6px;
  font-size: 15px;
  letter-spacing: -0.2px;
}

.history-info .file-path {
  font-size: 13px;
  color: #86868b;
  margin-bottom: 6px;
  font-family: 'SF Mono', 'Monaco', 'Menlo', monospace;
  font-weight: 500;
}

.history-info .download-time {
  font-size: 13px;
  color: #c7c7cc;
  font-weight: 500;
}

.history-info .file-size {
  font-size: 13px;
  color: #86868b;
  margin-bottom: 6px;
  font-family: 'SF Mono', 'Monaco', 'Menlo', monospace;
  font-weight: 500;
}

.history-info .progress-percent {
  color: #007aff;
  font-weight: 600;
}

.history-status {
  margin-right: 20px;
}

.history-actions {
  display: flex;
  gap: 12px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.path-hint {
  margin-top: 8px;
  color: #86868b;
  font-size: 13px;
  font-weight: 500;
}

/* 状态样式 */
.status-downloading {
  border-left: 4px solid #007aff;
  background: rgba(0, 122, 255, 0.08);
  backdrop-filter: blur(20px);
}

.status-completed {
  border-left: 4px solid #34c759;
  background: rgba(52, 199, 89, 0.08);
  backdrop-filter: blur(20px);
}

.status-error {
  border-left: 4px solid #ff3b30;
  background: rgba(255, 59, 48, 0.08);
  backdrop-filter: blur(20px);
}

.status-paused {
  border-left: 4px solid #ff9500;
  background: rgba(255, 149, 0, 0.08);
  backdrop-filter: blur(20px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .download-manager {
    max-width: 100%;
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .download-manager {
    padding: 16px;
  }
  
  .page-header {
    padding: 32px 24px;
  }
  
  .page-header h2 {
    font-size: 28px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .file-browser-card .ant-card-head,
  .download-history-card .ant-card-head {
    padding: 16px 20px;
  }
  
  .batch-actions {
    margin: 20px -20px -20px -20px;
  }
}

/* Ant Design 组件苹果风格优化 */
:deep(.ant-btn) {
  border-radius: 12px;
  font-weight: 500;
  padding: 8px 20px;
  height: auto;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

:deep(.ant-btn-primary) {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

:deep(.ant-btn-primary:hover) {
  background: #0056cc;
  border-color: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
}

:deep(.ant-btn-danger) {
  background: #ff3b30;
  border-color: #ff3b30;
  color: white;
}

:deep(.ant-btn-danger:hover) {
  background: #d70015;
  border-color: #d70015;
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(255, 59, 48, 0.3);
}

:deep(.ant-btn:not(.ant-btn-primary):not(.ant-btn-danger)) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  color: #1d1d1f;
}

:deep(.ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):hover) {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

:deep(.ant-radio-group) {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(20px);
}

:deep(.ant-radio-button-wrapper) {
  border-radius: 8px;
  border: none;
  background: transparent;
  color: #86868b;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

:deep(.ant-radio-button-wrapper-checked) {
  background: #007aff;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

:deep(.ant-radio-button-wrapper:hover) {
  color: #007aff;
}

:deep(.ant-table) {
  background: transparent;
  border-radius: 16px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: rgba(0, 122, 255, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  color: #1d1d1f;
  font-weight: 600;
  font-size: 15px;
  padding: 16px 20px;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  padding: 16px 20px;
  background: transparent;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(0, 122, 255, 0.04);
}

:deep(.ant-table-row-selected > td) {
  background: rgba(0, 122, 255, 0.08) !important;
}

:deep(.ant-tag) {
  border-radius: 8px;
  font-weight: 500;
  padding: 4px 12px;
  border: none;
  font-size: 13px;
}

:deep(.ant-space-item) {
  display: flex;
  align-items: center;
}
</style>