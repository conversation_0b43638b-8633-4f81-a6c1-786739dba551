package events

import (
	"server/global"
	"server/model/sys"
	"sync"

	"go.uber.org/zap"
)

// ClientEventType 客户端事件类型
type ClientEventType string

const (
	ClientOnlineEvent  ClientEventType = "client_online"
	ClientOfflineEvent ClientEventType = "client_offline"
	ClientDeletedEvent ClientEventType = "client_deleted"
)

// ClientEvent 客户端事件
type ClientEvent struct {
	Type   ClientEventType
	Client *sys.Client
}

// ClientEventHandler 客户端事件处理器
type ClientEventHandler func(event ClientEvent)

// ClientEventManager 客户端事件管理器
type ClientEventManager struct {
	handlers map[ClientEventType][]ClientEventHandler
	mutex    sync.RWMutex
}

// 全局客户端事件管理器
var GlobalClientEventManager *ClientEventManager

// InitClientEventManager 初始化客户端事件管理器
func InitClientEventManager() {
	GlobalClientEventManager = &ClientEventManager{
		handlers: make(map[ClientEventType][]ClientEventHandler),
	}
	global.LOG.Info("客户端事件管理器初始化完成")
}

// Subscribe 订阅客户端事件
func (m *ClientEventManager) Subscribe(eventType ClientEventType, handler ClientEventHandler) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.handlers[eventType] = append(m.handlers[eventType], handler)
	global.LOG.Debug("订阅客户端事件", 
		zap.String("event_type", string(eventType)))
}

// Publish 发布客户端事件
func (m *ClientEventManager) Publish(event ClientEvent) {
	m.mutex.RLock()
	handlers := m.handlers[event.Type]
	m.mutex.RUnlock()
	
	if len(handlers) == 0 {
		global.LOG.Debug("没有找到事件处理器", 
			zap.String("event_type", string(event.Type)))
		return
	}
	
	global.LOG.Debug("发布客户端事件", 
		zap.String("event_type", string(event.Type)),
		zap.Uint("client_id", event.Client.ID),
		zap.String("hostname", event.Client.Hostname))
	
	// 异步处理事件，避免阻塞
	go func() {
		for _, handler := range handlers {
			func() {
				defer func() {
					if r := recover(); r != nil {
						global.LOG.Error("客户端事件处理器异常", 
							zap.String("event_type", string(event.Type)),
							zap.Any("panic", r))
					}
				}()
				handler(event)
			}()
		}
	}()
}

// PublishClientOnline 发布客户端上线事件
func PublishClientOnline(client *sys.Client) {
	global.LOG.Info("📢 准备发布客户端上线事件",
		zap.Uint("client_id", client.ID),
		zap.String("hostname", client.Hostname),
		zap.String("remote_addr", client.RemoteAddr))

	if GlobalClientEventManager != nil {
		global.LOG.Info("✅ 事件管理器存在，发布事件")
		GlobalClientEventManager.Publish(ClientEvent{
			Type:   ClientOnlineEvent,
			Client: client,
		})
	} else {
		global.LOG.Error("❌ 全局客户端事件管理器为空！")
	}
}

// PublishClientOffline 发布客户端离线事件
func PublishClientOffline(client *sys.Client) {
	if GlobalClientEventManager != nil {
		GlobalClientEventManager.Publish(ClientEvent{
			Type:   ClientOfflineEvent,
			Client: client,
		})
	}
}

// PublishClientDeleted 发布客户端删除事件
func PublishClientDeleted(client *sys.Client) {
	if GlobalClientEventManager != nil {
		GlobalClientEventManager.Publish(ClientEvent{
			Type:   ClientDeletedEvent,
			Client: client,
		})
	}
}
