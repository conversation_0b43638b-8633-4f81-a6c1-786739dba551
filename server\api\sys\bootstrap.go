package sys

import (
	"server/service"
)

type ApiGroup struct {
	AuthApi              AuthApi
	DashboardApi         DashboardApi

	ConfigApi            ConfigApi

	DownloadApi          DownloadApi
	UploadApi            UploadApi
	UserManageApi        UserManageApi
	LogApi               LogApi
	FileTransferStatsApi FileTransferStatsApi
	PerformanceApi       PerformanceApi
	NotificationApi      NotificationApi
}

var (
	dashboardService = service.ServiceGroupManagerAPP.SysServiceGroup.DashboardService

	userService = service.ServiceGroupManagerAPP.SysServiceGroup.UserService

	uploadService = service.ServiceGroupManagerAPP.SysServiceGroup.UploadService

	userManageService = service.ServiceGroupManagerAPP.SysServiceGroup.UserService

	configService = service.ServiceGroupManagerAPP.SysServiceGroup.ConfigService
)
