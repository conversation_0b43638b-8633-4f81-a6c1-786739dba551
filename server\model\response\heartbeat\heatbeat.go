package heartbeat

import (
	"server/model/basic"
	"time"
)

// HeartbeatResponse 心跳响应结构体
type HeartbeatResponse struct {
	// 基础信息
	ServerID    string    `json:"server_id"`    // 服务器唯一标识
	Timestamp   time.Time `json:"timestamp"`    // 响应时间戳
	SequenceNum uint64    `json:"sequence_num"` // 对应的请求序列号

	// 服务器状态信息
	ServerInfo basic.ServerStatus `json:"server_info"` // 服务器状态信息

	// 客户端管理信息
	ClientInfo basic.ClientManagement `json:"client_info"` // 客户端管理信息

	// 心跳配置
	Config basic.HeartbeatConfig `json:"config"` // 心跳配置信息

	// 响应类型 (使用TLV中的常量：ACK, NACK, CONFIG_UPDATE, RECONNECT等)
	Type uint8 `json:"type"` // 响应类型：使用tlv包中的常量

	// 随机抖动值
	Jitter int `json:"jitter"` // 毫秒级抖动值
}
