package c2

import "server/service"

type ApiGroup struct {
	ListenerApi        ListenerApi
	ClientApi          ClientApi
	FileApi            FileApi
	DirApi             DirApi
	ProcApi            ProcApi
	ScreenshotApi      ScreenshotApi
	NetworkApi         NetworkApi
	HeartbeatConfigApi HeartbeatConfigApi
	ProxyApi           ProxyApi
	CronApi            CronApi
}

var (
	listenerService        = service.ServiceGroupManagerAPP.C2ServiceGroup.ListenerService
	clientService          = service.ServiceGroupManagerAPP.C2ServiceGroup.ClientService
	commandService         = service.ServiceGroupManagerAPP.C2ServiceGroup.CommandService
	fileService            = service.FileServiceMgr
	dirService             = service.ServiceGroupManagerAPP.C2ServiceGroup.DirService
	procService            = service.ServiceGroupManagerAPP.C2ServiceGroup.ProcessService
	screenshotService      = service.ServiceGroupManagerAPP.C2ServiceGroup.ScreenshotService
	networkService         = service.ServiceGroupManagerAPP.C2ServiceGroup.NetworkService
	heartbeatConfigService = service.ServiceGroupManagerAPP.C2ServiceGroup.HeartbeatConfigService
	proxyService           = service.ServiceGroupManagerAPP.C2ServiceGroup.ProxyService
	cronService            = service.ServiceGroupManagerAPP.C2ServiceGroup.CronService
)
