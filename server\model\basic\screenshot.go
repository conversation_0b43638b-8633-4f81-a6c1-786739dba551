package basic

// ScreenshotInfo 截图信息
type ScreenshotInfo struct {
	ID         uint   `json:"id"`          // 截图ID
	ClientID   uint   `json:"client_id"`   // 客户端ID
	ClientName string `json:"client_name"` // 客户端名称
	ClientIP   string `json:"client_ip"`   // 客户端IP
	Filename   string `json:"filename"`    // 文件名
	FilePath   string `json:"file_path"`   // 文件路径
	Width      int    `json:"width"`       // 图片宽度
	Height     int    `json:"height"`      // 图片高度
	Format     string `json:"format"`      // 图片格式
	Size       int64  `json:"size"`        // 文件大小
	Timestamp  int64  `json:"timestamp"`   // 截图时间戳
	CreatedAt  string `json:"created_at"`  // 创建时间
}

// MonitorInfo 显示器信息
type MonitorInfo struct {
	Index   int  `json:"index"`   // 显示器索引
	X       int  `json:"x"`       // X坐标
	Y       int  `json:"y"`       // Y坐标
	Width   int  `json:"width"`   // 宽度
	Height  int  `json:"height"`  // 高度
	Primary bool `json:"primary"` // 是否主显示器
}
