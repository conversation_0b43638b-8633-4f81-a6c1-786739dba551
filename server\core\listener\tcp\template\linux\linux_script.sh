#!/bin/bash

# 检测架构
ARCH=$(uname -m)
ARCH_FLAG=""

case "$ARCH" in
    x86_64)
        ARCH_FLAG="l64"
        ;;
    i386|i686)
        ARCH_FLAG="l32"
        ;;
    aarch64|arm64)
        ARCH_FLAG="a64"
        ;;
    armv7*|armv6*|arm)
        ARCH_FLAG="a32"
        ;;
    *)
        echo "不支持的架构: $ARCH"
        exit 1
        ;;
esac

# 检测X11环境
HEADLESS_FLAG=""
if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
    # 没有显示环境，检查是否有X11相关进程
    if ! pgrep -x "Xorg\|X\|Xwayland" > /dev/null 2>&1; then
        # 检查是否安装了X11库
        if ! ldconfig -p | grep -q "libX11\|libxcb" 2>/dev/null; then
            echo "检测到headless环境（无X11支持）"
            HEADLESS_FLAG="h"
        fi
    fi
fi

# 构建下载URL
DOWNLOAD_PATH="/download"
if [ -n "$HEADLESS_FLAG" ]; then
    DOWNLOAD_PATH="/download-headless"
fi

# 下载客户端
echo "正在下载客户端..."
if command -v wget > /dev/null; then
    wget -q "http://%s${DOWNLOAD_PATH}?a=${ARCH_FLAG}" -O /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    if [ $? -ne 0 ]; then
        echo "下载失败，尝试使用IP地址..."
        wget -q "http://%s${DOWNLOAD_PATH}?a=${ARCH_FLAG}" -O /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    fi
elif command -v curl > /dev/null; then
    curl -s "http://%s${DOWNLOAD_PATH}?a=${ARCH_FLAG}" -o /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    if [ $? -ne 0 ]; then
        echo "下载失败，尝试使用IP地址..."
        curl -s "http://%s${DOWNLOAD_PATH}?a=${ARCH_FLAG}" -o /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
    fi
else
    echo "未找到wget或curl，无法下载客户端"
    exit 1
fi

# 设置执行权限并运行
chmod +x /tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG}
/tmp/{{.ClientPrefix}}tcp_${ARCH_FLAG} &

echo "客户端已启动"