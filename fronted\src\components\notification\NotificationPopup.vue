<template>
  <div class="notification-popup-container">
    <!-- 通知弹窗 -->
    <transition-group name="notification" tag="div" class="notification-list">
      <div
        v-for="notification in visibleNotifications"
        :key="notification.id"
        :class="[
          'notification-popup',
          `notification-${notification.level}`,
          { 'notification-clickable': notification.clickable }
        ]"
        @click="handleNotificationClick(notification)"
      >
        <!-- 通知图标 -->
        <div class="notification-icon">
          <component :is="getNotificationIcon(notification)" />
        </div>

        <!-- 通知内容 -->
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.content }}</div>
          <div class="notification-time">{{ formatTime(notification.created_at) }}</div>
        </div>

        <!-- 关闭按钮 -->
        <div class="notification-close" @click.stop="closeNotification(notification.id)">
          <CloseOutlined />
        </div>

        <!-- 进度条 -->
        <div
          v-if="notification.autoClose"
          class="notification-progress"
          :style="{ animationDuration: `${notification.duration}ms` }"
        ></div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  CloseOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  ApiOutlined,
  DisconnectOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export default {
  name: 'NotificationPopup',
  components: {
    CloseOutlined,
    InfoCircleOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    CloseCircleOutlined,
    UserAddOutlined,
    UserDeleteOutlined,
    ApiOutlined,
    DisconnectOutlined
  },
  emits: ['notification-click', 'notification-close'],
  setup(props, { emit }) {
    const notifications = ref([])
    const maxVisible = ref(5) // 最多同时显示5个通知

    // 可见的通知列表
    const visibleNotifications = computed(() => {
      return notifications.value.slice(0, maxVisible.value)
    })

    // 添加通知
    const addNotification = (notification) => {
      const id = Date.now() + Math.random()
      const newNotification = {
        id,
        ...notification,
        autoClose: notification.autoClose !== false,
        duration: notification.duration || 5000,
        clickable: notification.clickable !== false,
        created_at: new Date().toISOString()
      }

      notifications.value.unshift(newNotification)

      // 自动关闭
      if (newNotification.autoClose) {
        setTimeout(() => {
          closeNotification(id)
        }, newNotification.duration)
      }

      // 限制通知数量
      if (notifications.value.length > 20) {
        notifications.value = notifications.value.slice(0, 20)
      }
    }

    // 关闭通知
    const closeNotification = (id) => {
      const index = notifications.value.findIndex(n => n.id === id)
      if (index > -1) {
        const notification = notifications.value[index]
        notifications.value.splice(index, 1)
        emit('notification-close', notification)
      }
    }

    // 处理通知点击
    const handleNotificationClick = (notification) => {
      if (notification.clickable) {
        emit('notification-click', notification)
        closeNotification(notification.id)
      }
    }

    // 获取通知图标
    const getNotificationIcon = (notification) => {
      const iconMap = {
        // 通知级别图标
        info: InfoCircleOutlined,
        success: CheckCircleOutlined,
        warning: ExclamationCircleOutlined,
        error: CloseCircleOutlined,
        
        // 通知类型图标
        client_online: UserAddOutlined,
        client_offline: UserDeleteOutlined,
        client_deleted: UserDeleteOutlined,
        listener_created: ApiOutlined,
        listener_deleted: ApiOutlined,
        listener_closed: DisconnectOutlined
      }

      return iconMap[notification.type] || iconMap[notification.level] || InfoCircleOutlined
    }

    // 格式化时间
    const formatTime = (timeString) => {
      try {
        return dayjs(timeString).fromNow()
      } catch (error) {
        return '刚刚'
      }
    }

    // 清空所有通知
    const clearAll = () => {
      notifications.value = []
    }

    // 暴露方法给父组件
    const expose = {
      addNotification,
      closeNotification,
      clearAll
    }

    return {
      notifications,
      visibleNotifications,
      addNotification,
      closeNotification,
      handleNotificationClick,
      getNotificationIcon,
      formatTime,
      clearAll,
      ...expose
    }
  }
}
</script>

<style scoped>
.notification-popup-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-popup {
  position: relative;
  min-width: 320px;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid #1890ff;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  pointer-events: auto;
  overflow: hidden;
}

.notification-popup.notification-clickable {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.notification-popup.notification-clickable:hover {
  transform: translateX(-4px);
}

.notification-popup.notification-success {
  border-left-color: #52c41a;
}

.notification-popup.notification-warning {
  border-left-color: #faad14;
}

.notification-popup.notification-error {
  border-left-color: #ff4d4f;
}

.notification-icon {
  flex-shrink: 0;
  font-size: 20px;
  margin-top: 2px;
}

.notification-popup.notification-info .notification-icon {
  color: #1890ff;
}

.notification-popup.notification-success .notification-icon {
  color: #52c41a;
}

.notification-popup.notification-warning .notification-icon {
  color: #faad14;
}

.notification-popup.notification-error .notification-icon {
  color: #ff4d4f;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #595959;
  line-height: 1.4;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.notification-close {
  flex-shrink: 0;
  cursor: pointer;
  color: #8c8c8c;
  font-size: 14px;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.notification-close:hover {
  color: #595959;
  background: #f5f5f5;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  animation: progress-shrink linear forwards;
}

@keyframes progress-shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* 过渡动画 */
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
