// forward/forward_manager.go
package forward

import (
	"context"
	"errors"
	"fmt"
	"net"
	"server/core/manager/dbpool"
	"server/global"
	"server/model/basic"
	"server/model/sys"
	"sync"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

/*
正向代理的实现

User (外网)
 |
 v
(userPort)
Server (作为socks5客户端) <===> Client (内网的socks5服务)

userPort面向用户: 可以由用户自定义，也可以自动分配
Client运行socks5服务: 由Client启动并告知Server端口

支持多层内网穿透：
第一层：User -> Server:1080 -> Client1:10000
第二层：User -> Server:1080 -> Client1:20000 -> Client2:10000
第三层：User -> Server:1080 -> Client1:20000 -> Client2:30000 -> Client3:10000

端口复用：用户可以一直使用同一个Server端口（如1080）穿透多层内网
*/

// ===== 模块化组件 =====

// ForwardConnectionManager 正向代理连接管理器
type ForwardConnectionManager struct {
	connections map[string]net.Conn
	mutex       sync.RWMutex
}

// NewForwardConnectionManager 创建连接管理器
func NewForwardConnectionManager() *ForwardConnectionManager {
	return &ForwardConnectionManager{
		connections: make(map[string]net.Conn),
	}
}

// AddConnection 添加连接
func (cm *ForwardConnectionManager) AddConnection(id string, conn net.Conn) {
	cm.mutex.Lock()
	cm.connections[id] = conn
	cm.mutex.Unlock()
}

// RemoveConnection 移除连接
func (cm *ForwardConnectionManager) RemoveConnection(id string) {
	cm.mutex.Lock()
	if conn, exists := cm.connections[id]; exists {
		conn.Close()
		delete(cm.connections, id)
	}
	cm.mutex.Unlock()
}

// CloseAll 关闭所有连接
func (cm *ForwardConnectionManager) CloseAll() {
	cm.mutex.Lock()
	for id, conn := range cm.connections {
		conn.Close()
		delete(cm.connections, id)
	}
	cm.mutex.Unlock()
}

// ===== 主要结构体 =====

// ForwardManager 正向代理管理器
type ForwardManager struct {
	servers map[*basic.Proxy]*ForwardProxyServer
	lock    sync.Mutex

	// 端口复用管理
	portReuse map[uint16][]*ForwardProxyServer // 端口 -> 服务器列表
	portLock  sync.RWMutex
}

var GlobalForwardManager = &ForwardManager{
	servers:   make(map[*basic.Proxy]*ForwardProxyServer),
	portReuse: make(map[uint16][]*ForwardProxyServer),
}

// StartServer 启动正向代理服务
func (fm *ForwardManager) StartServer(proxy *basic.Proxy) error {
	if global.LOG != nil {
		global.LOG.Info("🚀 [FORWARD] 开始启动正向代理服务",
			zap.String("proxyID", proxy.ProxyID),
			zap.String("name", proxy.Name),
			zap.Uint("clientID", proxy.ClientID),
			zap.Uint16("userPort", proxy.UserPort),
			zap.Uint16("clientPort", proxy.ClientPort))
	}

	fm.lock.Lock()
	defer fm.lock.Unlock()

	if _, exists := fm.servers[proxy]; exists {
		if global.LOG != nil {
			global.LOG.Warn("⚠️ [FORWARD] 正向代理服务器已存在", zap.String("proxyID", proxy.ProxyID))
		}
		return errors.New("正向代理服务器已存在")
	}

	// 🚀 获取客户端信息
	if global.LOG != nil {
		global.LOG.Info("📋 [FORWARD] 获取客户端信息", zap.Uint("clientID", proxy.ClientID))
	}

	var client sys.Client
	if err := dbpool.ExecuteDBOperationAsyncAndWait("forward_client_info", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).Where("id = ?", proxy.ClientID).First(&client).Error
	}); err != nil {
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD] 获取客户端信息失败",
				zap.Uint("clientID", proxy.ClientID),
				zap.Error(err))
		}
		return fmt.Errorf("客户端不存在: %v", err)
	}

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD] 客户端信息获取成功",
			zap.Uint("clientID", proxy.ClientID),
			zap.String("hostname", client.Hostname),
			zap.String("remoteAddr", client.RemoteAddr),
			zap.String("os", client.OS),
			zap.Int("status", client.Status))
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 创建模块化组件
	if global.LOG != nil {
		global.LOG.Info("🔧 [FORWARD] 创建正向代理组件")
	}

	config := NewForwardProxyConfig(proxy)
	accessController := NewForwardAccessController(config)
	stats := NewForwardProxyStats(proxy)
	connectionManager := NewForwardConnectionManager()

	// 从客户端地址中提取IP地址
	clientIP, _, err := net.SplitHostPort(client.RemoteAddr)
	if err != nil {
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD] 解析客户端地址失败",
				zap.String("remoteAddr", client.RemoteAddr),
				zap.Error(err))
		}
		cancel()
		return fmt.Errorf("解析客户端地址失败: %v", err)
	}

	server := &ForwardProxyServer{
		userPort:          proxy.UserPort,
		clientPort:        proxy.ClientPort,
		clientAddr:        clientIP, // 只保存IP地址，不包含端口
		config:            config,
		accessController:  accessController,
		stats:             stats,
		connectionManager: connectionManager,
		ctx:               ctx,
		cancel:            cancel,
		running:           false,
	}

	if global.LOG != nil {
		global.LOG.Info("🚀 [FORWARD] 启动正向代理服务器",
			zap.Uint16("userPort", proxy.UserPort),
			zap.Uint16("clientPort", proxy.ClientPort),
			zap.String("clientAddr", client.RemoteAddr))
	}

	if err := server.Start(proxy); err != nil {
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD] 正向代理服务器启动失败",
				zap.String("proxyID", proxy.ProxyID),
				zap.Error(err))
		}
		cancel()
		return err
	}

	// 启动成功后添加到map中
	fm.servers[proxy] = server

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD] 正向代理服务器启动成功",
			zap.String("proxyID", proxy.ProxyID),
			zap.String("name", proxy.Name))
	}

	return nil
}

// StopServer 停止正向代理服务
func (fm *ForwardManager) StopServer(proxy *basic.Proxy) error {
	fm.lock.Lock()
	defer fm.lock.Unlock()

	server, exists := fm.servers[proxy]
	if !exists {
		return nil // 服务不存在，认为已停止
	}

	// 调用 Stop() 方法
	if err := server.Stop(); err != nil {
		return err
	}

	// 从 map 中删除
	delete(fm.servers, proxy)
	return nil
}
