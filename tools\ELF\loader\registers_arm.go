//go:build linux && arm

package main

import (
"syscall"
"unsafe"
)

// ARM register structure for Linux
type PtraceRegsArm struct {
Uregs [18]uint32  // ARM registers r0-r17
}

// Register access functions for ARM architecture on Linux

func getRIP(regs *syscall.PtraceRegs) uint64 {
// Convert to ARM specific structure
armRegs := (*PtraceRegsArm)(unsafe.Pointer(regs))
return uint64(armRegs.Uregs[15])  // PC is at index 15
}

func setRIP(regs *syscall.PtraceRegs, addr uint64) {
// Convert to ARM specific structure
armRegs := (*PtraceRegsArm)(unsafe.Pointer(regs))
armRegs.Uregs[15] = uint32(addr)
}

func getRSP(regs *syscall.PtraceRegs) uint64 {
// Convert to ARM specific structure
armRegs := (*PtraceRegsArm)(unsafe.Pointer(regs))
return uint64(armRegs.Uregs[13])  // SP is at index 13
}

func setRSP(regs *syscall.PtraceRegs, addr uint64) {
// Convert to ARM specific structure
armRegs := (*PtraceRegsArm)(unsafe.Pointer(regs))
armRegs.Uregs[13] = uint32(addr)
}
