//go:build linux
// +build linux

package common

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"time"
)

// FileDownloadRequest 文件下载请求
type FileDownloadRequest struct {
	TaskID     uint64 `json:"task_id"`
	Path       string `json:"path"`        // 文件路径
	StartChunk int64  `json:"start_chunk"` // 起始分块编号
	ChunkSize  int64  `json:"chunk_size"`  // 分块大小（字节）
	RangeStart int64  `json:"range_start"` // 范围下载起始位置
	RangeEnd   int64  `json:"range_end"`   // 范围下载结束位置
}

// FileDownloadResponse 文件下载响应
type FileDownloadResponse struct {
	TaskID              uint64 `json:"task_id"`
	NotExist            bool   `json:"not_exist"`             // 文件是否不存在
	Success             bool   `json:"success"`               // 操作是否成功
	CurrentChunk        int64  `json:"current_chunk"`         // 当前分块编号
	TotalChunk          int64  `json:"total_chunk"`           // 总分块数
	CurrentChunkContent []byte `json:"current_chunk_content"` // 当前分块内容
	FileSize            int64  `json:"file_size"`             // 文件总大小
	TransferredBytes    int64  `json:"transferred_bytes"`     // 已传输字节数
	Error               string `json:"error"`                 // 错误信息
	Completed           bool   `json:"completed"`             // 是否传输完成
	FileHash            string `json:"file_hash"`             // 文件SHA256哈希值（仅在完成时提供）
}

// handleFileDownload 处理文件下载请求
func (cm *ConnectionManager) handleFileDownload(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileDownloadResponse{
		TaskID:   0,
		NotExist: false,
		Success:  false,
		Error:    "请求格式错误",
	}

	var req FileDownloadRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileDownloadRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendResp(File, FileDownload, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID
	// --- 1. 处理显式取消信令，需要加锁 ---
	if req.StartChunk == -1 {
		downloadTaskTrackers.Lock()
		tracker, exists := downloadTaskTrackers.tasks[req.TaskID]
		log.Printf("收到文件下载中止/取消信令: 任务ID=%d", req.TaskID)
		if exists && tracker.cancelFunc != nil {
			tracker.cancelFunc()
		}
		delete(downloadTaskTrackers.tasks, req.TaskID)
		downloadTaskTrackers.Unlock()
		return
	}

	// --- 2. 读取文件块 (耗时IO，不加锁) ---
	fileMutex.RLock()
	resp := downloadFile(&req) // downloadFile是纯函数，不访问全局状态
	fileMutex.RUnlock()

	if !resp.Success {
		cm.sendResp(File, FileDownload, resp)
		// 任务失败，清理tracker
		downloadTaskTrackers.Lock()
		if tracker, exists := downloadTaskTrackers.tasks[req.TaskID]; exists {
			if tracker.cancelFunc != nil {
				tracker.cancelFunc()
			}
			delete(downloadTaskTrackers.tasks, req.TaskID)
		}
		downloadTaskTrackers.Unlock()
		return
	}

	// --- 3. 发送响应 (耗时IO，不加锁) ---
	cm.sendResp(File, FileDownload, resp)

	// --- 4. 更新tracker和管理goroutine (访问共享资源，加锁) ---
	downloadTaskTrackers.Lock()
	defer downloadTaskTrackers.Unlock()

	tracker, exists := downloadTaskTrackers.tasks[req.TaskID]

	if exists {
		// 取消旧的重试
		if req.StartChunk > tracker.LastChunk && tracker.cancelFunc != nil {
			// log.Printf(...) // 注释掉日志
			tracker.cancelFunc()
			tracker.cancelFunc = nil
		}
	} else {
		// 如果tracker不存在，说明是第一个块，创建它
		tracker = &DownloadTaskTracker{}
		downloadTaskTrackers.tasks[req.TaskID] = tracker
	}

	// 更新tracker状态
	tracker.LastChunk = resp.CurrentChunk
	tracker.LastUpdated = time.Now()

	// 启动新的重试
	if !resp.Completed {
		ctx, cancel := context.WithCancel(context.Background())
		tracker.cancelFunc = cancel
		go cm.startDownloadRetryMechanism(ctx, req.TaskID, req.Path, resp.CurrentChunk, resp.TotalChunk)
	} else {
		// 任务完成，清理
		if tracker.cancelFunc != nil {
			tracker.cancelFunc()
		}
		delete(downloadTaskTrackers.tasks, req.TaskID)
		log.Printf("文件下载完成: 任务ID=%d, 路径=%s, 清理任务跟踪器", req.TaskID, req.Path)
	}
}

// startDownloadRetryMechanism 实现文件下载的重试机制
// 如果某个分片的下载发送后10秒内没有收到响应，则主动发送下一个分片，最多重试10次
func (cm *ConnectionManager) startDownloadRetryMechanism(ctx context.Context, taskID uint64, path string, chunkToRetry int64, totalChunks int64) {
	if chunkToRetry >= totalChunks-1 {
		return
	}

	retryTimer := time.NewTimer(10 * time.Second)
	defer retryTimer.Stop()

	for retryCount := 0; retryCount < 10; retryCount++ {
		select {
		case <-ctx.Done():
			return

		case <-retryTimer.C:
			// 检查任务是否已经进展（被新的 handleFileDownload 更新了）
			downloadTaskTrackers.RLock()
			tracker, exists := downloadTaskTrackers.tasks[taskID]
			// 如果tracker不存在，或者LastChunk已经大于我们要重试的块，说明已经收到新响应
			if !exists || tracker.LastChunk > chunkToRetry {
				downloadTaskTrackers.RUnlock()
				log.Printf("文件下载重试机制: 任务ID=%d, 块=%d 的任务已有进展，停止重试。", taskID, chunkToRetry)
				return
			}
			downloadTaskTrackers.RUnlock()

			log.Printf("文件下载重试机制: 任务ID=%d, 路径=%s, 正在重试分片=%d, 重试次数=%d",
				taskID, path, chunkToRetry, retryCount+1)

			// 构建重试请求
			retryReq := &FileDownloadRequest{
				TaskID:     taskID,
				Path:       path,
				StartChunk: chunkToRetry, // 明确我们要重试哪个块
				ChunkSize:  32 * 1024,    // 或者从配置中获取
			}

			fileMutex.RLock()
			retryResp := downloadFile(retryReq)
			fileMutex.RUnlock()

			// 只发送，不在这里处理响应或启动新的重试goroutine
			if retryResp.Success {
				cm.sendResp(File, FileDownload, retryResp)
			} else {
				log.Printf("重试时读取文件块失败: 任务ID=%d, 块=%d, 错误: %s", taskID, chunkToRetry, retryResp.Error)
				// 读取失败，可能文件被删了，这个重试goroutine可以退出了
				return
			}

			retryTimer.Reset(10 * time.Second)
		}
	}

	log.Printf("文件下载达到最大重试次数: 任务ID=%d, 块=%d", taskID, chunkToRetry)
}

// downloadFile 执行文件下载的核心逻辑
func downloadFile(req *FileDownloadRequest) *FileDownloadResponse {
	// 参数验证
	taskID := req.TaskID
	if req.Path == "" {
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   "文件路径不能为空",
		}
	}

	// 获取绝对路径
	absPath, err := getAbsolutePath(req.Path)
	if err != nil {
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   fmt.Sprintf("路径解析失败: %v", err),
		}
	}

	// 检查文件是否存在且是普通文件
	fileInfo, err := os.Stat(absPath)
	if err != nil {
		if os.IsNotExist(err) {
			return &FileDownloadResponse{
				TaskID:   taskID,
				NotExist: true,
				Error:    "文件不存在",
			}
		}
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   fmt.Sprintf("文件状态检查失败: %v", err),
		}
	}

	if fileInfo.IsDir() {
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   "路径是目录而非文件",
		}
	}

	// 设置默认分块大小
	chunkSize := req.ChunkSize
	if chunkSize == 0 {
		chunkSize = 32 * 1024 // 默认32KB
	}

	fileSize := fileInfo.Size()
	totalChunks := (fileSize + chunkSize - 1) / chunkSize
	// 校验起始分块
	if req.StartChunk >= totalChunks {
		return &FileDownloadResponse{
			TaskID:     taskID,
			Success:    false,
			TotalChunk: totalChunks,
			FileSize:   fileSize,
			Error:      "起始分块超出范围",
		}
	}

	// 打开文件
	file, err := os.Open(absPath)
	if err != nil {
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   fmt.Sprintf("打开文件失败: %v", err),
		}
	}
	defer file.Close()

	// 计算读取位置
	offset := req.StartChunk * chunkSize
	if req.RangeStart > 0 {
		offset = req.RangeStart
	}

	// 定位到指定位置
	if _, err = file.Seek(offset, io.SeekStart); err != nil {
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   fmt.Sprintf("文件定位失败: %v", err),
		}
	}
	if req.RangeEnd > 0 {
		chunkSize = req.RangeEnd - req.RangeStart
	}
	// 使用内存池读取分块内容
	buf := cm.memoryPool.GetChunkBuffer(int(chunkSize))
	defer cm.memoryPool.PutChunkBuffer(buf)

	n, err := file.Read(buf)
	if err != nil && err != io.EOF {
		return &FileDownloadResponse{
			TaskID:  taskID,
			Success: false,
			Error:   fmt.Sprintf("读取文件失败: %v", err),
		}
	}

	// 构建响应 - 创建数据副本以避免内存池冲突
	chunkData := make([]byte, n)
	copy(chunkData, buf[:n])

	resp := &FileDownloadResponse{
		TaskID:              taskID,
		Success:             true,
		CurrentChunk:        req.StartChunk,
		TotalChunk:          totalChunks,
		CurrentChunkContent: chunkData,
		FileSize:            fileSize,
		TransferredBytes:    offset + int64(n),
		Completed:           req.StartChunk == totalChunks-1,
	}

	// 如果是最后一个分块，计算文件哈希值
	if resp.Completed {
		if hash, err := calculateFileHash(absPath); err == nil {
			resp.FileHash = hash
		} else {
			log.Printf("计算文件哈希失败: %v", err)
			// 不影响下载，只是没有哈希验证
		}
	}

	return resp

}
