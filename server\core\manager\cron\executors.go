package cron

import (
	"context"
	"encoding/json"
	"fmt"
	"server/core/manager/cache"
	"server/factory"
	"server/global"
	"server/model/request/screenshot"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/service"
	"time"
)

// CommandExecutor 命令执行器
type CommandExecutor struct{}

func (e *CommandExecutor) GetType() string {
	return "command"
}

func (e *CommandExecutor) Validate(cronTask *task.CronTask) error {
	if cronTask.Command == "" {
		return fmt.Errorf("命令不能为空")
	}
	return nil
}

func (e *CommandExecutor) Execute(ctx context.Context, cronTask *task.CronTask) (*ExecutionResult, error) {
	startTime := time.Now()

	// 获取客户端信息
	var client sys.Client
	if err := global.DB.First(&client, cronTask.ClientID).Error; err != nil {
		return nil, fmt.Errorf("找不到客户端: %w", err)
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return &ExecutionResult{
			Success:   false,
			Error:     "客户端离线",
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 通过TLV协议向客户端发送命令执行请求
	result, err := e.executeCommandOnClient(ctx, &client, cronTask)
	if err != nil {
		return &ExecutionResult{
			Success:   false,
			Error:     err.Error(),
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	}

	return result, nil
}

// executeCommandOnClient 在客户端执行命令
func (e *CommandExecutor) executeCommandOnClient(ctx context.Context, client *sys.Client, cronTask *task.CronTask) (*ExecutionResult, error) {
	startTime := time.Now()

	// 构建命令请求
	commandRequest := map[string]interface{}{
		"task_id":   cronTask.ID,
		"command":   cronTask.Command,
		"timeout":   cronTask.Timeout,
		"directory": "", // 可以后续扩展工作目录配置
	}

	// 序列化请求数据
	requestData, err := json.Marshal(commandRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化命令请求失败: %w", err)
	}

	// 创建TLV包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.RunCommand,      // RunCommand
			Code: tlv.ExecCronCommand, // ExecCronCommand
		},
		PacketData: &tlv.PacketData{
			Data: requestData,
		},
	}

	// 发送命令到客户端
	err = factory.SendPacketFactory(*client, packet)
	if err != nil {
		return nil, fmt.Errorf("发送命令到客户端失败: %w", err)
	}

	// 等待命令执行结果（设置超时）
	timeout := time.Duration(cronTask.Timeout) * time.Second
	if timeout <= 0 {
		timeout = 5 * time.Minute
	}

	resultChan := make(chan *ExecutionResult, 1)
	errorChan := make(chan error, 1)

	// 启动结果监听器
	go e.waitForCommandResult(cronTask.ID, resultChan, errorChan)

	// 等待结果或超时
	select {
	case result := <-resultChan:
		result.StartTime = startTime
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, nil
	case err := <-errorChan:
		return &ExecutionResult{
			Success:   false,
			Error:     err.Error(),
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	case <-time.After(timeout):
		return &ExecutionResult{
			Success:   false,
			Error:     "命令执行超时",
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	case <-ctx.Done():
		return &ExecutionResult{
			Success:   false,
			Error:     "任务被取消",
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	}
}

// waitForCommandResult 等待命令执行结果
func (e *CommandExecutor) waitForCommandResult(taskID uint64, resultChan chan<- *ExecutionResult, errorChan chan<- error) {
	// 轮询等待结果，最多等待5分钟
	maxWaitTime := 5 * time.Minute
	pollInterval := 500 * time.Millisecond
	startTime := time.Now()

	for {
		// 检查是否超时
		if time.Since(startTime) > maxWaitTime {
			errorChan <- fmt.Errorf("等待命令执行结果超时")
			return
		}

		// 尝试从缓存获取结果
		if responseData, exists := cache.ResponseMgr.GetResponse(taskID); exists {
			// 解析结果
			if responseData.Data != nil {
				if cronResponse, ok := responseData.Data.(map[string]interface{}); ok {
					result := &ExecutionResult{
						Success: cronResponse["success"].(bool),
						Output:  cronResponse["output"].(string),
					}

					if errorStr, hasError := cronResponse["error"].(string); hasError && errorStr != "" {
						result.Error = errorStr
					}

					resultChan <- result
					return
				}
			}
		}

		// 等待一段时间后重试
		time.Sleep(pollInterval)
	}
}

// ScreenshotExecutor 截图执行器
type ScreenshotExecutor struct{}

func (e *ScreenshotExecutor) GetType() string {
	return "screenshot"
}

func (e *ScreenshotExecutor) Validate(cronTask *task.CronTask) error {
	// 解析截图参数
	var params ScreenshotParams
	if cronTask.Params != "" {
		if err := json.Unmarshal([]byte(cronTask.Params), &params); err != nil {
			return fmt.Errorf("截图参数格式错误: %w", err)
		}
	}

	// 验证参数
	if params.Type < 0 || params.Type > 2 {
		return fmt.Errorf("无效的截图类型: %d", params.Type)
	}

	if params.Quality < 1 || params.Quality > 100 {
		return fmt.Errorf("无效的图片质量: %d", params.Quality)
	}

	return nil
}

func (e *ScreenshotExecutor) Execute(ctx context.Context, cronTask *task.CronTask) (*ExecutionResult, error) {
	startTime := time.Now()

	// 获取客户端信息
	var client sys.Client
	if err := global.DB.First(&client, cronTask.ClientID).Error; err != nil {
		return nil, fmt.Errorf("找不到客户端: %w", err)
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return &ExecutionResult{
			Success:   false,
			Error:     "客户端离线",
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 解析截图参数
	var params ScreenshotParams
	if cronTask.Params != "" {
		if err := json.Unmarshal([]byte(cronTask.Params), &params); err != nil {
			return &ExecutionResult{
				Success:   false,
				Error:     fmt.Sprintf("截图参数解析失败: %v", err),
				StartTime: startTime,
				EndTime:   time.Now(),
				Duration:  time.Since(startTime).Milliseconds(),
			}, nil
		}
	} else {
		// 使用默认参数
		params = ScreenshotParams{
			Type:         0,
			Quality:      80,
			Format:       "png",
			MonitorIndex: 0,
		}
	}

	// 构建截图请求
	req := screenshot.ScreenshotRequest{
		Type:         params.Type,
		MonitorIndex: params.MonitorIndex,
		X:            params.X,
		Y:            params.Y,
		Width:        params.Width,
		Height:       params.Height,
		Format:       params.Format,
		Quality:      params.Quality,
	}

	// 调用截图服务
	screenshotService := service.ServiceGroupManagerAPP.C2ServiceGroup.ScreenshotService
	taskID, err := screenshotService.TakeScreenshot(cronTask.ClientID, req)

	endTime := time.Now()

	if err != nil {
		return &ExecutionResult{
			Success:   false,
			Error:     fmt.Sprintf("截图执行失败: %v", err),
			StartTime: startTime,
			EndTime:   endTime,
			Duration:  endTime.Sub(startTime).Milliseconds(),
		}, nil
	}

	return &ExecutionResult{
		Success:   true,
		Output:    fmt.Sprintf("截图任务创建成功，任务ID: %d", taskID),
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  endTime.Sub(startTime).Milliseconds(),
	}, nil
}

// ScriptExecutor 脚本执行器
type ScriptExecutor struct{}

func (e *ScriptExecutor) GetType() string {
	return "script"
}

func (e *ScriptExecutor) Validate(cronTask *task.CronTask) error {
	if cronTask.Command == "" {
		return fmt.Errorf("脚本内容不能为空")
	}

	// 解析脚本参数
	var params ScriptParams
	if cronTask.Params != "" {
		if err := json.Unmarshal([]byte(cronTask.Params), &params); err != nil {
			return fmt.Errorf("脚本参数格式错误: %w", err)
		}
	}

	// 验证脚本类型
	validTypes := []string{"shell", "sh", "powershell", "ps1", "batch", "bat", "cmd", "python", "py", "pl"}
	isValid := false
	for _, validType := range validTypes {
		if params.ScriptType == validType {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("不支持的脚本类型: %s", params.ScriptType)
	}

	return nil
}

func (e *ScriptExecutor) Execute(ctx context.Context, cronTask *task.CronTask) (*ExecutionResult, error) {
	startTime := time.Now()

	// 获取客户端信息
	var client sys.Client
	if err := global.DB.First(&client, cronTask.ClientID).Error; err != nil {
		return nil, fmt.Errorf("找不到客户端: %w", err)
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return &ExecutionResult{
			Success:   false,
			Error:     "客户端离线",
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	}

	// 解析脚本参数
	var params ScriptParams
	if cronTask.Params != "" {
		if err := json.Unmarshal([]byte(cronTask.Params), &params); err != nil {
			return &ExecutionResult{
				Success:   false,
				Error:     fmt.Sprintf("脚本参数解析失败: %v", err),
				StartTime: startTime,
				EndTime:   time.Now(),
				Duration:  time.Since(startTime).Milliseconds(),
			}, nil
		}
	} else {
		// 使用默认参数
		params = ScriptParams{
			ScriptType: "shell",
		}
	}

	// 通过TLV协议向客户端发送脚本执行请求
	result, err := e.executeScriptOnClient(ctx, &client, cronTask, &params)
	if err != nil {
		return &ExecutionResult{
			Success:   false,
			Error:     err.Error(),
			StartTime: startTime,
			EndTime:   time.Now(),
			Duration:  time.Since(startTime).Milliseconds(),
		}, nil
	}

	return result, nil
}

// executeScriptOnClient 在客户端执行脚本
func (e *ScriptExecutor) executeScriptOnClient(ctx context.Context, client *sys.Client, cronTask *task.CronTask, params *ScriptParams) (*ExecutionResult, error) {
	startTime := time.Now()

	// 构建脚本请求
	scriptRequest := map[string]interface{}{
		"task_id":        cronTask.ID,
		"script_type":    params.ScriptType,
		"script_content": cronTask.Command,
		"timeout":        cronTask.Timeout,
		"directory":      params.WorkDir,
		"arguments":      "", // 将Args数组转换为字符串
	}

	// 如果有参数，将其转换为字符串
	if len(params.Args) > 0 {
		scriptRequest["arguments"] = fmt.Sprintf("%v", params.Args)
	}

	// 序列化请求数据
	requestData, err := json.Marshal(scriptRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化脚本请求失败: %w", err)
	}

	// 创建TLV包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.RunCommand,     // RunCommand
			Code: tlv.ExecCronScript, // ExecCronScript
		},
		PacketData: &tlv.PacketData{
			Data: requestData,
		},
	}

	// 发送脚本到客户端
	err = factory.SendPacketFactory(*client, packet)
	if err != nil {
		return nil, fmt.Errorf("发送脚本到客户端失败: %w", err)
	}

	// 等待脚本执行结果（设置超时）
	timeout := time.Duration(cronTask.Timeout) * time.Second
	if timeout <= 0 {
		timeout = 5 * time.Minute
	}

	resultChan := make(chan *ExecutionResult, 1)
	errorChan := make(chan error, 1)

	// 启动结果监听器
	go e.waitForScriptResult(cronTask.ID, resultChan, errorChan)

	// 等待结果或超时
	select {
	case result := <-resultChan:
		result.StartTime = startTime
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, nil
	case err := <-errorChan:
		return nil, err
	case <-time.After(timeout):
		return nil, fmt.Errorf("脚本执行超时")
	case <-ctx.Done():
		return nil, fmt.Errorf("脚本执行被取消")
	}
}

// waitForScriptResult 等待脚本执行结果
func (e *ScriptExecutor) waitForScriptResult(taskID uint64, resultChan chan<- *ExecutionResult, errorChan chan<- error) {
	defer close(resultChan)
	defer close(errorChan)

	startTime := time.Now()
	maxWaitTime := 10 * time.Minute // 最大等待时间
	pollInterval := 500 * time.Millisecond

	for {
		// 检查是否超时
		if time.Since(startTime) > maxWaitTime {
			errorChan <- fmt.Errorf("等待脚本执行结果超时")
			return
		}

		// 尝试从缓存获取结果
		if responseData, exists := cache.ResponseMgr.GetResponse(taskID); exists {
			// 解析结果
			if responseData.Data != nil {
				if cronResponse, ok := responseData.Data.(map[string]interface{}); ok {
					result := &ExecutionResult{
						Success: cronResponse["success"].(bool),
						Output:  cronResponse["output"].(string),
					}

					if errorStr, hasError := cronResponse["error"].(string); hasError && errorStr != "" {
						result.Error = errorStr
					}

					resultChan <- result
					return
				}
			}
		}

		// 等待一段时间后重试
		time.Sleep(pollInterval)
	}
}

// 参数结构体定义

// ScreenshotParams 截图参数
type ScreenshotParams struct {
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	Quality      int    `json:"quality"`       // 图片质量
	Format       string `json:"format"`        // 图片格式
	MonitorIndex int    `json:"monitor_index"` // 显示器索引
	X            int    `json:"x"`             // 区域截图X坐标
	Y            int    `json:"y"`             // 区域截图Y坐标
	Width        int    `json:"width"`         // 区域截图宽度
	Height       int    `json:"height"`        // 区域截图高度
}

// ScriptParams 脚本参数
type ScriptParams struct {
	ScriptType string            `json:"script_type"` // 脚本类型: shell, powershell, batch, python
	Args       []string          `json:"args"`        // 脚本参数
	Env        map[string]string `json:"env"`         // 环境变量
	WorkDir    string            `json:"work_dir"`    // 工作目录
}

// CommandParams 命令参数
type CommandParams struct {
	Args    []string          `json:"args"`     // 命令参数
	Env     map[string]string `json:"env"`      // 环境变量
	WorkDir string            `json:"work_dir"` // 工作目录
}
