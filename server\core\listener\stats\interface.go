package stats

import "time"

// ListenerStats 通用监听器统计接口
type ListenerStats interface {
	GetListenerID() uint
	GetListenerType() string
	GetAddress() string
	IsActive() bool
	GetStartTime() time.Time
	GetTotalConnections() int64
	GetActiveConnections() int64
	GetDataTransferred() int64
	GetPacketsProcessed() int64
	GetErrorCount() int64
	GetLastActivity() time.Time
}

// ConnectionStats 通用连接统计
type ConnectionStats struct {
	TotalConnections    int64     `json:"total_connections"`
	ActiveConnections   int64     `json:"active_connections"`
	ConnectionsPerHour  float64   `json:"connections_per_hour"`
	AverageConnTime     float64   `json:"average_conn_time"`
	LastConnectionTime  time.Time `json:"last_connection_time"`
	DisconnectionsToday int64     `json:"disconnections_today"`
}

// TrafficStats 通用流量统计
type TrafficStats struct {
	BytesReceived    int64     `json:"bytes_received"`
	BytesSent        int64     `json:"bytes_sent"`
	PacketsReceived  int64     `json:"packets_received"`
	PacketsSent      int64     `json:"packets_sent"`
	AverageSpeed     float64   `json:"average_speed_mbps"`
	PeakSpeed        float64   `json:"peak_speed_mbps"`
	LastResetTime    time.Time `json:"last_reset_time"`
}

// ListenerStatsProvider 监听器统计提供者接口
type ListenerStatsProvider interface {
	GetAllListenerStats() []ListenerStats
	GetConnectionStats() ConnectionStats
	GetTrafficStats() TrafficStats
	GetListenerByID(id uint) ListenerStats
	GetActiveListenerCount() int
	ResetStats()
}

// BasicListenerStats 基础监听器统计实现
type BasicListenerStats struct {
	ListenerID        uint      `json:"listener_id"`
	ListenerType      string    `json:"listener_type"`
	Address           string    `json:"address"`
	Active            bool      `json:"is_active"`
	StartTime         time.Time `json:"start_time"`
	TotalConnections  int64     `json:"total_connections"`
	ActiveConnections int64     `json:"active_connections"`
	DataTransferred   int64     `json:"data_transferred"`
	PacketsProcessed  int64     `json:"packets_processed"`
	ErrorCount        int64     `json:"error_count"`
	LastActivity      time.Time `json:"last_activity"`
}

// 实现 ListenerStats 接口
func (b *BasicListenerStats) GetListenerID() uint        { return b.ListenerID }
func (b *BasicListenerStats) GetListenerType() string    { return b.ListenerType }
func (b *BasicListenerStats) GetAddress() string         { return b.Address }
func (b *BasicListenerStats) IsActive() bool             { return b.Active }
func (b *BasicListenerStats) GetStartTime() time.Time    { return b.StartTime }
func (b *BasicListenerStats) GetTotalConnections() int64 { return b.TotalConnections }
func (b *BasicListenerStats) GetActiveConnections() int64 { return b.ActiveConnections }
func (b *BasicListenerStats) GetDataTransferred() int64  { return b.DataTransferred }
func (b *BasicListenerStats) GetPacketsProcessed() int64 { return b.PacketsProcessed }
func (b *BasicListenerStats) GetErrorCount() int64       { return b.ErrorCount }
func (b *BasicListenerStats) GetLastActivity() time.Time { return b.LastActivity }
