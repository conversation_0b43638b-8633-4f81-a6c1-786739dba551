<template>
  <span class="country-flag-wrapper" :class="{ 'with-name': showName }">
    <img 
      v-if="flagSvg" 
      :src="flagSvg" 
      :alt="`${displayName}国旗`"
      class="country-flag"
      :class="sizeClass"
      @error="onImageError"
    />
    <span v-else class="flag-fallback" :class="sizeClass">🌍</span>
    <span v-if="showName" class="country-name">{{ displayName }}</span>
  </span>
</template>

<script setup>
import { computed, ref } from 'vue';
import { getCountryFlagSvg, getCountryDisplayName } from '@/utils/countryUtils';

const props = defineProps({
  country: {
    type: String,
    required: true
  },
  showName: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
});

const imageError = ref(false);

const flagSvg = computed(() => {
  if (imageError.value) return null;
  return getCountryFlagSvg(props.country);
});

const displayName = computed(() => {
  return getCountryDisplayName(props.country);
});

const sizeClass = computed(() => {
  return `size-${props.size}`;
});

const onImageError = () => {
  imageError.value = true;
};
</script>

<style scoped>
.country-flag-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.country-flag {
  border-radius: 2px;
  object-fit: cover;
  border: 1px solid #e8e8e8;
}

.country-flag.size-small {
  width: 16px;
  height: 12px;
}

.country-flag.size-medium {
  width: 20px;
  height: 15px;
}

.country-flag.size-large {
  width: 24px;
  height: 18px;
}

.flag-fallback {
  display: inline-block;
}

.flag-fallback.size-small {
  font-size: 12px;
}

.flag-fallback.size-medium {
  font-size: 16px;
}

.flag-fallback.size-large {
  font-size: 20px;
}

.country-name {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
}

.country-flag-wrapper.with-name {
  min-width: 0;
}
</style>
