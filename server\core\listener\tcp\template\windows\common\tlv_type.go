//go:build windows
// +build windows

package common

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"errors"
	"fmt"
	"io"
	"sync"
	"unsafe"
)

// TLV结构体定义
type Header struct {
	Type      uint8
	Code      uint8
	Flags     uint16
	Label     uint32
	FragIndex uint32
	Length    uint32
}

type PacketData struct {
	IV       [IVSize]byte       // 16字节初始化向量
	Checksum [ChecksumSize]byte // 32字节HMAC-SHA256校验和
	Data     []byte             // 加密数据
}

type Packet struct {
	Header     *Header
	PacketData *PacketData
}

var (
	packetDataPool = sync.Pool{
		New: func() interface{} {
			return &PacketData{}
		},
	}

	bufferPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 4096) // 预分配4KB缓冲区
		},
	}

	fragmentPool = sync.Pool{
		New: func() interface{} {
			return make([][]byte, 0, 16) // 预分配16个分片槽位
		},
	}
)

// 🚀 BOOST优化：零拷贝序列化 - 使用内存池
func (h *Header) Marshal() []byte {
	// 从内存池获取小缓冲区（HeaderSize = 16字节）
	buf := bufferPool.Get().([]byte)[:HeaderSize]
	defer bufferPool.Put(buf[:cap(buf)])

	// 🚀 优化：直接内存操作，避免函数调用开销
	buf[0] = h.Type
	buf[1] = h.Code

	// 🚀 优化：使用位运算替代binary.BigEndian调用
	flags := h.Flags
	buf[2] = byte(flags >> 8)
	buf[3] = byte(flags)

	label := h.Label
	buf[4] = byte(label >> 24)
	buf[5] = byte(label >> 16)
	buf[6] = byte(label >> 8)
	buf[7] = byte(label)

	fragIndex := h.FragIndex
	buf[8] = byte(fragIndex >> 24)
	buf[9] = byte(fragIndex >> 16)
	buf[10] = byte(fragIndex >> 8)
	buf[11] = byte(fragIndex)

	length := h.Length
	buf[12] = byte(length >> 24)
	buf[13] = byte(length >> 16)
	buf[14] = byte(length >> 8)
	buf[15] = byte(length)

	// 创建返回副本
	result := make([]byte, HeaderSize)
	copy(result, buf)
	return result
}

// 🚀 BOOST优化：零拷贝反序列化 - 直接内存映射
func (h *Header) Unmarshal(headerData []byte) error {
	if len(headerData) < HeaderSize {
		return errors.New("header headerData too short")
	}

	// 🚀 优化：直接内存操作，避免函数调用开销
	h.Type = headerData[0]
	h.Code = headerData[1]

	// 🚀 优化：使用位运算替代binary.BigEndian调用
	h.Flags = uint16(headerData[2])<<8 | uint16(headerData[3])
	h.Label = uint32(headerData[4])<<24 | uint32(headerData[5])<<16 | uint32(headerData[6])<<8 | uint32(headerData[7])
	h.FragIndex = uint32(headerData[8])<<24 | uint32(headerData[9])<<16 | uint32(headerData[10])<<8 | uint32(headerData[11])
	h.Length = uint32(headerData[12])<<24 | uint32(headerData[13])<<16 | uint32(headerData[14])<<8 | uint32(headerData[15])

	return nil
}

// 🚀 BOOST优化：零拷贝序列化
func (p *PacketData) Marshal() []byte {
	// 从内存池获取缓冲区
	buf := bufferPool.Get().([]byte)
	defer bufferPool.Put(buf[:0]) // 归还时重置长度

	// 预分配足够的容量
	totalSize := IVSize + ChecksumSize + len(p.Data)
	if cap(buf) < totalSize {
		buf = make([]byte, 0, totalSize)
	}
	buf = buf[:0]

	// 🚀 优化：使用unsafe进行批量内存拷贝
	buf = append(buf, (*[IVSize]byte)(unsafe.Pointer(&p.IV[0]))[:]...)
	buf = append(buf, (*[ChecksumSize]byte)(unsafe.Pointer(&p.Checksum[0]))[:]...)
	buf = append(buf, p.Data...)

	// 创建返回副本
	result := make([]byte, len(buf))
	copy(result, buf)
	return result
}

// 🚀 BOOST优化：零拷贝反序列化
func (p *PacketData) Unmarshal(PacketDataBytes []byte) error {
	if len(PacketDataBytes) < MinPacketDataSize {
		return errors.New("PacketData is too short")
	}

	// 🚀 优化：使用unsafe进行批量内存拷贝
	copy((*[IVSize]byte)(unsafe.Pointer(&p.IV[0]))[:], PacketDataBytes[:IVSize])
	copy((*[ChecksumSize]byte)(unsafe.Pointer(&p.Checksum[0]))[:], PacketDataBytes[IVSize:MinPacketDataSize])

	// 🚀 优化：避免额外的内存分配
	dataLen := len(PacketDataBytes) - MinPacketDataSize
	if cap(p.Data) < dataLen {
		p.Data = make([]byte, dataLen)
	} else {
		p.Data = p.Data[:dataLen]
	}
	copy(p.Data, PacketDataBytes[MinPacketDataSize:])

	return nil
}

// Serialize 序列化整个数据包（使用内存池优化）
func (p *Packet) Serialize() []byte {
	header := *p.Header
	packetData := *p.PacketData

	// 预计算总大小
	headerBytes := header.Marshal()
	packetDataBytes := packetData.Marshal()
	totalSize := 8 + len(headerBytes) + len(packetDataBytes)

	// 使用内存池获取合适大小的缓冲区
	var buf []byte
	if totalSize <= 1024 {
		buf = bufferPool.Get().([]byte)[:0]
		defer bufferPool.Put(buf[:0])
	} else {
		buf = make([]byte, 0, totalSize)
	}

	// 生成随机前缀
	ran, err := RandomString(8)
	if err != nil {
		ran = "aabbccdd"
	}

	// 组装数据包
	buf = append(buf, []byte(ran)...)
	buf = append(buf, headerBytes...)
	buf = append(buf, packetDataBytes...)

	// 创建返回副本
	result := make([]byte, len(buf))
	copy(result, buf)
	return result
}

// DeserializePacket 反序列化数据包
func (p *Packet) DeserializePacket(serializedPacketWithPrefix []byte) error {
	serializedPacket := serializedPacketWithPrefix[8:]
	if len(serializedPacket) < MinPacketSize {
		return fmt.Errorf("packet too small: %d < %d", len(serializedPacket), MinPacketSize)
	}
	headerBytes := serializedPacket[:HeaderSize]
	dataBytes := serializedPacket[HeaderSize:]
	var header Header
	var packetData PacketData
	err := header.Unmarshal(headerBytes)
	if err != nil {
		return err
	}
	err = packetData.Unmarshal(dataBytes)
	if err != nil {
		return err
	}
	p.Header = &header
	p.PacketData = &packetData
	return nil
}

// 🚀 BOOST优化：使用AES-GCM替代AES-CTR+HMAC (一步完成加密+认证)
func (p *Packet) EncryptPacket(metadata *METADATA) error {
	// 1. 准备明文数据
	plaintext := p.PacketData.Data

	// 2. 生成随机IV (AES-GCM使用12字节nonce)
	nonce := make([]byte, 12)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return err
	}

	// 将nonce存储在IV的前12字节
	copy(p.PacketData.IV[:12], nonce)

	// 3. 🚀 使用AES-GCM进行认证加密
	block, err := aes.NewCipher(metadata.EncryptionKey[:32]) // AES需要32字节密钥
	if err != nil {
		return err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	// 4. 🚀 一步完成加密+认证，比AES-CTR+HMAC更快更安全
	ciphertext := aead.Seal(nil, nonce, plaintext, nil)

	// 5. 分离密文和认证标签
	if len(ciphertext) < aead.Overhead() {
		return errors.New("ciphertext too short")
	}

	authTagLen := aead.Overhead() // GCM认证标签长度
	actualCiphertext := ciphertext[:len(ciphertext)-authTagLen]
	authTag := ciphertext[len(ciphertext)-authTagLen:]

	p.PacketData.Data = actualCiphertext
	copy(p.PacketData.Checksum[:authTagLen], authTag) // 存储认证标签

	// 6. 更新长度
	serializedSize := HeaderSize + MinPacketDataSize + len(actualCiphertext)
	p.Header.Length = uint32(serializedSize)

	return nil
}

// 🚀 BOOST优化：使用AES-GCM解密
func (p *Packet) DecryptPacket(metadata *METADATA) error {
	// 1. 提取nonce
	nonce := p.PacketData.IV[:12]

	// 2. 🚀 使用AES-GCM进行认证解密
	block, err := aes.NewCipher(metadata.EncryptionKey[:32])
	if err != nil {
		return err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	// 3. 重构完整的密文（数据+认证标签）
	authTagLen := aead.Overhead()
	authTag := p.PacketData.Checksum[:authTagLen]
	fullCiphertext := make([]byte, len(p.PacketData.Data)+authTagLen)
	copy(fullCiphertext, p.PacketData.Data)
	copy(fullCiphertext[len(p.PacketData.Data):], authTag)

	// 4. 🚀 一步完成解密+验证，比AES-CTR+HMAC更快更安全
	plaintext, err := aead.Open(nil, nonce, fullCiphertext, nil)
	if err != nil {
		return errors.New("decryption or authentication failed")
	}

	// 5. 更新数据
	p.PacketData.Data = plaintext
	return nil
}
