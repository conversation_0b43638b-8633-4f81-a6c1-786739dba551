<template>
  <div class="network-monitor">
    <div class="network-header">
      <h3>网络监控</h3>
      <div class="network-actions">
        <a-button 
          @click="refreshNetworkInfo"
          :loading="loading"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button 
          type="primary"
          @click="exportNetworkData"
          :disabled="!networkData.length"
        >
          <template #icon>
            <DownloadOutlined />
          </template>
          导出数据
        </a-button>
      </div>
    </div>

    <div class="network-content">
      <!-- 网络概览 -->
      <div class="network-overview">
        <div class="section-header">
          <h4>网络统计</h4>
          <a-button
            size="small"
            @click="refreshNetworkStats"
            :loading="statsLoading"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新统计
          </a-button>
        </div>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="上传速度"
                :value="networkStats.uploadSpeed"
                suffix="KB/s"
                :value-style="{ color: '#3f8600' }"
              >
                <template #prefix>
                  <ArrowUpOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="下载速度"
                :value="networkStats.downloadSpeed"
                suffix="KB/s"
                :value-style="{ color: '#cf1322' }"
              >
                <template #prefix>
                  <ArrowDownOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="活跃连接"
                :value="networkStats.activeConnections"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <LinkOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="数据包丢失率"
                :value="networkStats.packetLoss"
                suffix="%"
                :value-style="{ color: networkStats.packetLoss > 5 ? '#cf1322' : '#3f8600' }"
              >
                <template #prefix>
                  <ExclamationCircleOutlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 网络接口信息 -->
      <div class="network-interfaces">
        <div class="section-header">
          <h4>网络接口</h4>
          <a-button
            size="small"
            @click="refreshNetworkInterfaces"
            :loading="interfacesLoading"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新接口
          </a-button>
        </div>
        <!-- 接口加载进度 -->
        <div v-if="interfaceProgress.visible" class="interface-progress">
          <a-progress
            :percent="interfaceProgress.percent"
            :status="interfaceProgress.status"
            :show-info="true"
          />
          <p class="progress-text">{{ interfaceProgress.message }}</p>
        </div>

        <a-table
          :columns="interfaceColumns"
          :data-source="networkInterfaces"
          :loading="loading"
          :pagination="false"
          size="small"
          rowKey="name"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="record.status === 'Up' ? 'green' : 'red'">
                {{ record.status }}
              </a-tag>
            </template>
            
            <template v-if="column.dataIndex === 'speed'">
              <span>{{ formatSpeed(record.speed) }}</span>
            </template>
            
            <template v-if="column.dataIndex === 'traffic'">
              <div>
                <div style="color: #3f8600;">↑ {{ formatBytes(record.bytes_sent) }}</div>
                <div style="color: #cf1322;">↓ {{ formatBytes(record.bytes_received) }}</div>
              </div>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 网络连接 -->
      <div class="network-connections">
        <div class="connections-header">
          <h4>网络连接</h4>
          <div class="connections-controls">
            <a-button
              size="small"
              @click="refreshNetworkConnections"
              :loading="connectionsLoading"
              style="margin-right: 8px;"
            >
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新连接
            </a-button>
            <div class="connections-filters">
            <a-select 
              v-model:value="connectionFilter" 
              placeholder="过滤连接类型"
              style="width: 150px"
              allowClear
            >
              <a-select-option value="TCP">TCP</a-select-option>
              <a-select-option value="UDP">UDP</a-select-option>
              <a-select-option value="ESTABLISHED">已建立</a-select-option>
              <a-select-option value="LISTENING">监听中</a-select-option>
            </a-select>
            </div>
          </div>
        </div>
        
        <a-table
          :columns="connectionColumns"
          :data-source="filteredConnections"
          :loading="loading"
          :pagination="{
            current: connectionPagination.current,
            pageSize: connectionPagination.pageSize,
            total: connectionPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个连接`
          }"
          @change="handleConnectionTableChange"
          size="small"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'protocol'">
              <a-tag :color="record.protocol === 'TCP' ? 'blue' : 'green'">
                {{ record.protocol }}
              </a-tag>
            </template>
            
            <template v-if="column.dataIndex === 'state'">
              <a-tag :color="getConnectionStateColor(record.state)">
                {{ record.state }}
              </a-tag>
            </template>
            
            <template v-if="column.dataIndex === 'process'">
              <div>
                <div>{{ record.process_name }}</div>
                <small style="color: #999;">PID: {{ record.pid }}</small>
              </div>
            </template>
            
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button 
                  type="link" 
                  size="small"
                  @click="viewConnectionDetails(record)"
                >
                  详情
                </a-button>
                <a-popconfirm
                  title="确定要关闭这个连接吗？"
                  @confirm="closeConnection(record)"
                  :disabled="record.state === 'LISTENING'"
                >
                  <a-button 
                    type="link" 
                    danger 
                    size="small"
                    :disabled="record.state === 'LISTENING'"
                  >
                    关闭
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 网络流量趋势图 -->
      <div class="network-traffic-card">
        <div class="card-header">
          <div class="header-left">
            <DashboardOutlined />
            <h3>网络流量趋势</h3>
          </div>
          <div class="traffic-controls">
            <a-button size="small" @click="addTestData">
              <template #icon><DashboardOutlined /></template>
              添加测试数据
            </a-button>
            <a-button size="small" @click="clearTrafficDataAndRedraw">
              <template #icon><ClearOutlined /></template>
              清空数据
            </a-button>
          </div>
        </div>
        <div class="traffic-chart-container">
          <div class="traffic-stats">
            <div class="stat-item">
              <span class="stat-label">当前下载速度:</span>
              <span class="stat-value download">{{ formatSpeed(networkDataStore.currentSpeeds.download) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">当前上传速度:</span>
              <span class="stat-value upload">{{ formatSpeed(networkDataStore.currentSpeeds.upload) }}</span>
            </div>
          </div>
          <div class="chart-wrapper">
            <canvas ref="trafficChart" width="800" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 连接详情弹窗 -->
    <a-modal
      v-model:open="connectionDetailModalVisible"
      :title="`连接详情 - ${currentConnection?.local_address}:${currentConnection?.local_port}`"
      width="70%"
      :footer="null"
    >
      <div v-if="currentConnection" class="connection-details">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="协议">{{ currentConnection.protocol }}</a-descriptions-item>
          <a-descriptions-item label="状态">{{ currentConnection.state }}</a-descriptions-item>
          <a-descriptions-item label="本地地址">{{ currentConnection.local_address }}:{{ currentConnection.local_port }}</a-descriptions-item>
          <a-descriptions-item label="远程地址">{{ currentConnection.remote_address }}:{{ currentConnection.remote_port }}</a-descriptions-item>
          <a-descriptions-item label="进程名">{{ currentConnection.process_name }}</a-descriptions-item>
          <a-descriptions-item label="进程ID">{{ currentConnection.pid }}</a-descriptions-item>
          <a-descriptions-item label="建立时间">{{ currentConnection.established_time }}</a-descriptions-item>
          <a-descriptions-item label="持续时间">{{ currentConnection.duration }}</a-descriptions-item>
          <a-descriptions-item label="发送字节" :span="2">{{ formatBytes(currentConnection.bytes_sent) }}</a-descriptions-item>
          <a-descriptions-item label="接收字节" :span="2">{{ formatBytes(currentConnection.bytes_received) }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { networkDataStore, updateNetworkStats, clearTrafficData, addTestTrafficData, formatSpeed } from '@/stores/networkData';
import {
  ReloadOutlined,
  DownloadOutlined,
  DashboardOutlined,
  ClearOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  LinkOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import * as networkApi from '@/api/network';
import { getApiBaseUrl } from '@/utils/serverConfig';

// 接收属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  clientInfo: {
    type: Object,
    default: () => ({})
  }
});

// 状态变量
const loading = ref(false);
const statsLoading = ref(false);
const interfacesLoading = ref(false);
const connectionsLoading = ref(false);
const connectionFilter = ref(undefined);

// 网络统计数据（初始化为空，从服务端获取真实数据）
const networkStats = reactive({
  uploadSpeed: 0,
  downloadSpeed: 0,
  activeConnections: 0,
  packetLoss: 0,
  totalBytesSent: 0,
  totalBytesRecv: 0,
  timestamp: 0
});

// 网络接口数据（初始化为空，从服务端获取真实数据）
const networkInterfaces = ref([]);

// 网络流量趋势数据 - 使用共享数据
const trafficChart = ref(null);

// 网络连接数据（初始化为空，从服务端获取真实数据）
const networkConnections = ref([]);

// 网络数据（用于导出）
const networkData = ref([]);

// 分页
const connectionPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 弹窗状态
const connectionDetailModalVisible = ref(false);
const currentConnection = ref(null);

// 接口加载进度
const interfaceProgress = reactive({
  visible: false,
  percent: 0,
  status: 'active',
  message: '正在获取网络接口信息...'
});

// SSE连接管理
let interfaceProgressSSE = null;

// 移除自动刷新功能，改为手动刷新

// 网络接口表格列
const interfaceColumns = [
  {
    title: '接口名称',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: 'IP地址',
    dataIndex: 'ip_address',
    key: 'ip_address',
    width: 140
  },
  {
    title: 'MAC地址',
    dataIndex: 'mac_address',
    key: 'mac_address',
    width: 150
  },
  {
    title: '速度',
    dataIndex: 'speed',
    key: 'speed',
    width: 100
  },
  {
    title: '流量统计',
    dataIndex: 'traffic',
    key: 'traffic',
    width: 150
  }
];

// 网络连接表格列
const connectionColumns = [
  {
    title: '协议',
    dataIndex: 'protocol',
    key: 'protocol',
    width: 80
  },
  {
    title: '本地地址',
    dataIndex: 'local_address',
    key: 'local_address',
    width: 140,
    customRender: ({ record }) => `${record.local_address}:${record.local_port}`
  },
  {
    title: '远程地址',
    dataIndex: 'remote_address',
    key: 'remote_address',
    width: 140,
    customRender: ({ record }) => `${record.remote_address}:${record.remote_port}`
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
    width: 120
  },
  {
    title: '进程',
    dataIndex: 'process',
    key: 'process',
    width: 120
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
];

// 过滤后的连接列表
const filteredConnections = computed(() => {
  let filtered = networkConnections.value;
  
  if (connectionFilter.value) {
    if (['TCP', 'UDP'].includes(connectionFilter.value)) {
      filtered = filtered.filter(conn => conn.protocol === connectionFilter.value);
    } else {
      filtered = filtered.filter(conn => conn.state === connectionFilter.value);
    }
  }
  
  connectionPagination.total = filtered.length;
  return filtered;
});

// 获取连接状态颜色
const getConnectionStateColor = (state) => {
  const colorMap = {
    'ESTABLISHED': 'green',
    'LISTENING': 'blue',
    'TIME_WAIT': 'orange',
    'CLOSE_WAIT': 'red',
    'SYN_SENT': 'purple',
    'SYN_RECEIVED': 'cyan'
  };
  return colorMap[state] || 'default';
};

// 格式化字节数
const formatBytes = (bytes) => {
  // 🔧 修复：处理空值、零值和无效值
  if (bytes === null || bytes === undefined || isNaN(bytes) || bytes < 0) {
    return '0 B';
  }
  if (bytes === 0) {
    return '0 B';
  }

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const safeIndex = Math.max(0, Math.min(i, sizes.length - 1)); // 确保索引在有效范围内
  return parseFloat((bytes / Math.pow(k, safeIndex)).toFixed(1)) + ' ' + sizes[safeIndex];
};

// 删除重复的formatSpeed函数，使用从store导入的版本

// 刷新网络统计信息
const refreshNetworkStats = async () => {
  try {
    statsLoading.value = true;
    console.log('🔄 开始获取网络统计信息...');
    const statsRes = await networkApi.getNetworkStats(props.clientId);
    console.log('📊 网络统计API响应:', statsRes);

    // 更新网络统计信息
    // 🔧 修复：适配服务端响应管理器的双层嵌套结构 {code: 200, data: {data: {task_id, success, stats}}}
    const responseData = statsRes.data?.data || statsRes.data; // 兼容双层嵌套
    if (statsRes.code === 200 && responseData && responseData.success && responseData.stats) {
      const stats = responseData.stats;

      // 更新共享网络数据
      console.log('网络监控页面收到统计数据:', stats);
      updateNetworkStats(stats);

      networkStats.uploadSpeed = stats.upload_speed || 0;
      networkStats.downloadSpeed = stats.download_speed || 0;
      networkStats.activeConnections = stats.active_connections || 0;
      networkStats.packetLoss = stats.packet_loss || 0;
      networkStats.totalBytesSent = stats.total_bytes_sent || 0;
      networkStats.totalBytesRecv = stats.total_bytes_recv || 0;
      networkStats.timestamp = stats.timestamp || 0;
      console.log('✅ 网络统计信息获取成功:', stats);
      message.success('网络统计信息刷新成功');

      // 重新绘制图表
      drawTrafficChart();
    } else {
      console.warn('⚠️ 网络统计信息获取失败:', statsRes);
      // 检查是否是客户端返回的错误
      const errorMsg = responseData?.error || statsRes.data?.error || statsRes.message || '未知错误';
      const fullErrorMsg = '网络统计信息获取失败: ' + errorMsg;
      message.error(fullErrorMsg);
      throw new Error(fullErrorMsg); // 抛出异常，让全局刷新知道失败了
    }
  } catch (error) {
    console.error('❌ 获取网络统计信息失败:', error);
    message.error('获取网络统计信息失败: ' + (error.response?.data?.message || error.message));
  } finally {
    statsLoading.value = false;
  }
};

// 启动接口进度SSE连接
const startInterfaceProgressSSE = () => {
  if (interfaceProgressSSE) {
    interfaceProgressSSE.close();
  }

  // 🔧 修复：使用正确的API基础URL构建SSE连接，并添加token认证
  const baseUrl = getApiBaseUrl();
  const token = localStorage.getItem('token');
  const sseUrl = `${baseUrl}/network/${props.clientId}/interfaces/progress-stream?token=${encodeURIComponent(token || '')}`;
  console.log('🔗 启动接口进度SSE连接:', sseUrl);

  interfaceProgressSSE = new EventSource(sseUrl);

  interfaceProgressSSE.onopen = () => {
    console.log('✅ 接口进度SSE连接已建立');
  };

  interfaceProgressSSE.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log('📊 收到SSE消息:', data);
    } catch (error) {
      console.error('❌ 解析SSE消息失败:', error);
    }
  };

  interfaceProgressSSE.addEventListener('connected', (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log('🔗 SSE连接确认:', data);
    } catch (error) {
      console.error('❌ 解析连接确认消息失败:', error);
    }
  });

  interfaceProgressSSE.addEventListener('interface_progress', (event) => {
    try {
      const data = JSON.parse(event.data);
      console.log('📊 收到接口进度更新:', data);

      if (data.data) {
        const progress = data.data;
        interfaceProgress.visible = true;
        interfaceProgress.percent = Math.round(progress.progress || 0);
        interfaceProgress.status = 'active';
        interfaceProgress.message = progress.message || '正在处理网络接口...';
      }
    } catch (error) {
      console.error('❌ 解析进度消息失败:', error);
    }
  });

  interfaceProgressSSE.addEventListener('keepalive', (event) => {
    console.log('💓 收到SSE保活信号');
  });

  interfaceProgressSSE.onerror = (error) => {
    console.error('❌ SSE连接错误:', error);
    interfaceProgressSSE.close();
    interfaceProgressSSE = null;
  };
};

// 停止接口进度SSE连接
const stopInterfaceProgressSSE = () => {
  if (interfaceProgressSSE) {
    console.log('🔌 关闭接口进度SSE连接');
    interfaceProgressSSE.close();
    interfaceProgressSSE = null;
  }
};

// 刷新网络接口信息
const refreshNetworkInterfaces = async () => {
  try {
    interfacesLoading.value = true;

    // 启动SSE连接监听进度
    startInterfaceProgressSSE();

    // 显示进度条
    interfaceProgress.visible = true;
    interfaceProgress.percent = 0;
    interfaceProgress.status = 'active';
    interfaceProgress.message = '正在获取网络接口信息...';

    console.log('🔄 开始获取网络接口信息...');

    const interfacesRes = await networkApi.getNetworkInterfaces(props.clientId);

    // 更新网络接口信息
    // 🔧 修复：适配服务端响应管理器的双层嵌套结构 {code: 200, data: {data: {task_id, success, interfaces}}}
    const responseData = interfacesRes.data?.data || interfacesRes.data; // 兼容双层嵌套
    if (interfacesRes.code === 200 && responseData && responseData.success && responseData.interfaces) {
      networkInterfaces.value = responseData.interfaces;

      // 🔍 调试：打印接口数据结构
      console.log('🔍 网络接口原始数据:', responseData.interfaces);
      console.log('🔍 第一个接口的详细数据:', responseData.interfaces[0]);
      if (responseData.interfaces[0]) {
        console.log('🔍 第一个接口的字段:', Object.keys(responseData.interfaces[0]));
        console.log('🔍 speed字段值:', responseData.interfaces[0].speed);
        console.log('🔍 bytes_sent字段值:', responseData.interfaces[0].bytes_sent);
        console.log('🔍 BytesSent字段值:', responseData.interfaces[0].BytesSent);
        console.log('🔍 bytes_received字段值:', responseData.interfaces[0].bytes_received);
        console.log('🔍 BytesReceived字段值:', responseData.interfaces[0].BytesReceived);
      }

      console.log('✅ 网络接口信息获取成功');
      interfaceProgress.percent = 100;
      interfaceProgress.status = 'success';
      interfaceProgress.message = '网络接口信息获取完成';
      message.success('网络接口信息刷新成功');

      // 延迟隐藏进度条和关闭SSE
      setTimeout(() => {
        interfaceProgress.visible = false;
        stopInterfaceProgressSSE();
      }, 2000);
    } else {
      console.warn('⚠️ 网络接口信息获取失败:', interfacesRes);
      interfaceProgress.status = 'exception';
      interfaceProgress.message = '网络接口信息获取失败';
      // 检查是否是客户端返回的错误
      const errorMsg = responseData?.error || interfacesRes.data?.error || interfacesRes.message || '未知错误';
      const fullErrorMsg = '网络接口信息获取失败: ' + errorMsg;
      message.error(fullErrorMsg);

      // 延迟隐藏进度条和关闭SSE
      setTimeout(() => {
        interfaceProgress.visible = false;
        stopInterfaceProgressSSE();
      }, 3000);

      throw new Error(fullErrorMsg); // 抛出异常，让全局刷新知道失败了
    }
  } catch (error) {
    console.error('❌ 获取网络接口信息失败:', error);
    interfaceProgress.status = 'exception';
    interfaceProgress.message = '网络接口信息获取失败: ' + (error.response?.data?.message || error.message);
    message.error('获取网络接口信息失败: ' + (error.response?.data?.message || error.message));

    // 延迟隐藏进度条和关闭SSE
    setTimeout(() => {
      interfaceProgress.visible = false;
      stopInterfaceProgressSSE();
    }, 3000);
  } finally {
    interfacesLoading.value = false;
  }
};

// 刷新网络连接信息
const refreshNetworkConnections = async () => {
  try {
    connectionsLoading.value = true;
    console.log('🔄 开始获取网络连接信息...');
    const connectionsRes = await networkApi.getNetworkConnections(props.clientId, {
      protocol: connectionFilter.value || 'ALL',
      state: ''
    });

    // 更新网络连接信息
    // 🔧 修复：适配服务端响应管理器的双层嵌套结构 {code: 200, data: {data: {task_id, success, connections}}}
    const responseData = connectionsRes.data?.data || connectionsRes.data; // 兼容双层嵌套
    if (connectionsRes.code === 200 && responseData && responseData.success && responseData.connections) {
      networkConnections.value = responseData.connections;
      connectionPagination.total = responseData.total || responseData.connections.length;
      console.log('✅ 网络连接信息获取成功，连接数:', responseData.connections.length);
      message.success('网络连接信息刷新成功');
    } else {
      console.warn('⚠️ 网络连接信息获取失败:', connectionsRes);
      // 检查是否是客户端返回的错误
      const errorMsg = responseData?.error || connectionsRes.data?.error || connectionsRes.message || '未知错误';
      const fullErrorMsg = '网络连接信息获取失败: ' + errorMsg;
      message.error(fullErrorMsg);
      throw new Error(fullErrorMsg); // 抛出异常，让全局刷新知道失败了
    }
  } catch (error) {
    console.error('❌ 获取网络连接信息失败:', error);
    message.error('获取网络连接信息失败: ' + (error.response?.data?.message || error.message));
  } finally {
    connectionsLoading.value = false;
  }
};

// 删除了refreshNetworkCharts函数，使用流量趋势图的测试数据功能

// 刷新所有网络信息（保留原函数用于全局刷新按钮）
const refreshNetworkInfo = async () => {
  try {
    loading.value = true;
    console.log('🔄 开始获取所有网络信息...');

    let successCount = 0;
    let errorCount = 0;

    // 串行获取网络信息，避免并发问题
    console.log('📊 1/3 获取网络统计信息...');
    try {
      await refreshNetworkStats();
      successCount++;
      console.log('✅ 网络统计信息获取成功');
    } catch (error) {
      errorCount++;
      console.error('❌ 网络统计信息获取失败:', error);
    }

    // 短暂延迟，避免请求冲突
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('🌐 2/3 获取网络接口信息...');
    try {
      await refreshNetworkInterfaces();
      successCount++;
      console.log('✅ 网络接口信息获取成功');
    } catch (error) {
      errorCount++;
      console.error('❌ 网络接口信息获取失败:', error);
    }

    // 短暂延迟，避免请求冲突
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('🔗 3/3 获取网络连接信息...');
    try {
      await refreshNetworkConnections();
      successCount++;
      console.log('✅ 网络连接信息获取成功');
    } catch (error) {
      errorCount++;
      console.error('❌ 网络连接信息获取失败:', error);
    }

    // 显示结果
    if (successCount === 3) {
      message.success('所有网络信息刷新成功');
    } else if (successCount > 0) {
      message.warning(`部分网络信息刷新成功 (${successCount}/3)`);
    } else {
      message.error('所有网络信息刷新失败');
    }

    console.log(`✅ 网络信息获取完成: 成功${successCount}个，失败${errorCount}个`);
  } catch (error) {
    console.error('❌ 获取网络信息失败:', error);
    message.error('获取网络信息失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 导出网络数据
const exportNetworkData = () => {
  try {
    const data = {
      timestamp: new Date().toISOString(),
      clientId: props.clientId,
      stats: networkStats,
      interfaces: networkInterfaces.value,
      connections: networkConnections.value
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `network_data_${props.clientId}_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    message.success('网络数据导出成功');
  } catch (error) {
    console.error('导出网络数据失败:', error);
    message.error('导出网络数据失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  }
};

// 查看连接详情
const viewConnectionDetails = (connection) => {
  currentConnection.value = connection;
  connectionDetailModalVisible.value = true;
};

// 关闭连接
const closeConnection = async (connection) => {
  try {
    const res = await networkApi.closeConnection(props.clientId, {
      connection_id: connection.id,
      local_addr: connection.local_address,
      local_port: connection.local_port,
      remote_addr: connection.remote_address,
      remote_port: connection.remote_port,
      protocol: connection.protocol
    });

    if (res.code === 200) {
      message.success(`连接 ${connection.local_address}:${connection.local_port} 已关闭`);
      refreshNetworkInfo();
    } else {
      message.error('关闭连接失败: ' + res.message);
    }
  } catch (error) {
    console.error('关闭连接失败:', error);
    message.error('关闭连接失败: ' + (error.response?.data?.message || error.message));
  }
};

// 处理连接表格变化
const handleConnectionTableChange = (pag) => {
  connectionPagination.current = pag.current;
  connectionPagination.pageSize = pag.pageSize;
};

// 网络流量趋势图相关方法 - 使用共享数据

const drawTrafficChart = () => {
  if (!trafficChart.value) {
    console.log('Canvas元素未找到');
    return;
  }

  const canvas = trafficChart.value;
  const ctx = canvas.getContext('2d');
  const width = canvas.width;
  const height = canvas.height;

  // 使用共享数据的简化引用
  const trafficData = networkDataStore.traffic;

  console.log('绘制图表:', { width, height, dataPoints: trafficData.timestamps.length });

  // 清空画布
  ctx.clearRect(0, 0, width, height);

  // 绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 0, height);
  gradient.addColorStop(0, '#f8fafc');
  gradient.addColorStop(1, '#ffffff');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);

  // 绘制圆角边框
  ctx.strokeStyle = '#e2e8f0';
  ctx.lineWidth = 2;
  ctx.beginPath();
  ctx.roundRect(0, 0, width, height, 8);
  ctx.stroke();

  if (networkDataStore.traffic.timestamps.length === 0) {
    // 显示无数据提示
    ctx.fillStyle = '#64748b';
    ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('📊 暂无数据', width / 2, height / 2 - 10);
    ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    ctx.fillStyle = '#94a3b8';
    ctx.fillText('点击"刷新"或"添加测试数据"开始监控', width / 2, height / 2 + 15);
    return;
  }

  // 计算最大值用于缩放
  const maxDownload = Math.max(...trafficData.downloadSpeeds, 1);
  const maxUpload = Math.max(...trafficData.uploadSpeeds, 1);
  const maxSpeed = Math.max(maxDownload, maxUpload);

  // 图表区域
  const padding = { top: 30, right: 30, bottom: 50, left: 80 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // 绘制网格线
  ctx.strokeStyle = '#e2e8f0';
  ctx.lineWidth = 1;
  ctx.setLineDash([2, 2]);

  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = padding.top + (chartHeight * i / 5);
    ctx.beginPath();
    ctx.moveTo(padding.left, y);
    ctx.lineTo(padding.left + chartWidth, y);
    ctx.stroke();
  }

  // 垂直网格线
  const dataLength = trafficData.timestamps.length;
  const gridCount = Math.min(8, dataLength);
  for (let i = 0; i <= gridCount; i++) {
    const x = padding.left + (chartWidth * i / gridCount);
    ctx.beginPath();
    ctx.moveTo(x, padding.top);
    ctx.lineTo(x, padding.top + chartHeight);
    ctx.stroke();
  }

  ctx.setLineDash([]); // 重置虚线

  // 绘制下载速度线（蓝色）
  if (trafficData.downloadSpeeds.length > 1) {
    // 绘制渐变填充区域
    const downloadGradient = ctx.createLinearGradient(0, padding.top, 0, padding.top + chartHeight);
    downloadGradient.addColorStop(0, 'rgba(59, 130, 246, 0.1)');
    downloadGradient.addColorStop(1, 'rgba(59, 130, 246, 0.01)');

    ctx.fillStyle = downloadGradient;
    ctx.beginPath();

    // 绘制填充区域
    for (let i = 0; i < trafficData.downloadSpeeds.length; i++) {
      const x = padding.left + (chartWidth * i / (trafficData.downloadSpeeds.length - 1));
      const y = padding.top + chartHeight - (chartHeight * trafficData.downloadSpeeds[i] / maxSpeed);

      if (i === 0) {
        ctx.moveTo(x, padding.top + chartHeight);
        ctx.lineTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
    ctx.closePath();
    ctx.fill();

    // 绘制线条
    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.beginPath();

    for (let i = 0; i < trafficData.downloadSpeeds.length; i++) {
      const x = padding.left + (chartWidth * i / (trafficData.downloadSpeeds.length - 1));
      const y = padding.top + chartHeight - (chartHeight * trafficData.downloadSpeeds[i] / maxSpeed);

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // 绘制数据点
    ctx.fillStyle = '#3b82f6';
    for (let i = 0; i < trafficData.downloadSpeeds.length; i++) {
      const x = padding.left + (chartWidth * i / (trafficData.downloadSpeeds.length - 1));
      const y = padding.top + chartHeight - (chartHeight * trafficData.downloadSpeeds[i] / maxSpeed);

      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();

      // 白色内圈
      ctx.fillStyle = '#ffffff';
      ctx.beginPath();
      ctx.arc(x, y, 2, 0, 2 * Math.PI);
      ctx.fill();
      ctx.fillStyle = '#3b82f6';
    }
  }

  // 绘制上传速度线（绿色）
  if (trafficData.uploadSpeeds.length > 1) {
    // 绘制渐变填充区域
    const uploadGradient = ctx.createLinearGradient(0, padding.top, 0, padding.top + chartHeight);
    uploadGradient.addColorStop(0, 'rgba(34, 197, 94, 0.1)');
    uploadGradient.addColorStop(1, 'rgba(34, 197, 94, 0.01)');

    ctx.fillStyle = uploadGradient;
    ctx.beginPath();

    // 绘制填充区域
    for (let i = 0; i < trafficData.uploadSpeeds.length; i++) {
      const x = padding.left + (chartWidth * i / (trafficData.uploadSpeeds.length - 1));
      const y = padding.top + chartHeight - (chartHeight * trafficData.uploadSpeeds[i] / maxSpeed);

      if (i === 0) {
        ctx.moveTo(x, padding.top + chartHeight);
        ctx.lineTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight);
    ctx.closePath();
    ctx.fill();

    // 绘制线条
    ctx.strokeStyle = '#22c55e';
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.beginPath();

    for (let i = 0; i < trafficData.uploadSpeeds.length; i++) {
      const x = padding.left + (chartWidth * i / (trafficData.uploadSpeeds.length - 1));
      const y = padding.top + chartHeight - (chartHeight * trafficData.uploadSpeeds[i] / maxSpeed);

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // 绘制数据点
    ctx.fillStyle = '#22c55e';
    for (let i = 0; i < trafficData.uploadSpeeds.length; i++) {
      const x = padding.left + (chartWidth * i / (trafficData.uploadSpeeds.length - 1));
      const y = padding.top + chartHeight - (chartHeight * trafficData.uploadSpeeds[i] / maxSpeed);

      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();

      // 白色内圈
      ctx.fillStyle = '#ffffff';
      ctx.beginPath();
      ctx.arc(x, y, 2, 0, 2 * Math.PI);
      ctx.fill();
      ctx.fillStyle = '#22c55e';
    }
  }

  // 绘制Y轴标签
  ctx.fillStyle = '#64748b';
  ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  ctx.textAlign = 'right';
  ctx.textBaseline = 'middle';

  for (let i = 0; i <= 5; i++) {
    const y = padding.top + (chartHeight * i / 5);
    const value = maxSpeed * (5 - i) / 5;
    ctx.fillText(formatSpeed(value), padding.left - 10, y);
  }

  // 绘制X轴标签（时间）
  ctx.textAlign = 'center';
  ctx.textBaseline = 'top';
  const timeLabels = Math.min(6, trafficData.timestamps.length);
  for (let i = 0; i < timeLabels; i++) {
    const x = padding.left + (chartWidth * i / (timeLabels - 1));
    const timeIndex = Math.floor((trafficData.timestamps.length - 1) * i / (timeLabels - 1));
    const timeStr = trafficData.timestamps[timeIndex] || '';
    ctx.fillText(timeStr, x, padding.top + chartHeight + 10);
  }

  // 绘制图例
  const legendY = padding.top - 15;
  ctx.textAlign = 'left';
  ctx.textBaseline = 'middle';

  // 下载图例
  ctx.fillStyle = '#3b82f6';
  ctx.fillRect(padding.left, legendY - 3, 12, 6);
  ctx.fillStyle = '#64748b';
  ctx.font = '11px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  ctx.fillText('下载速度', padding.left + 20, legendY);

  // 上传图例
  ctx.fillStyle = '#22c55e';
  ctx.fillRect(padding.left + 100, legendY - 3, 12, 6);
  ctx.fillStyle = '#64748b';
  ctx.fillText('上传速度', padding.left + 120, legendY);
};

const clearTrafficDataAndRedraw = () => {
  clearTrafficData();
  drawTrafficChart();
  message.success('已清空数据');
};

const addTestData = () => {
  addTestTrafficData();
  drawTrafficChart();
  message.success('已添加测试数据');
};

// 组件挂载时不自动获取网络信息，改为手动刷新
onMounted(() => {
  console.log('🔧 网络监控组件已挂载，请手动点击刷新按钮获取数据');
  // 初始化图表
  nextTick(() => {
    drawTrafficChart();
  });
});

// 组件卸载时清理SSE连接
onUnmounted(() => {
  console.log('🔧 网络监控组件卸载，清理SSE连接');
  stopInterfaceProgressSSE();
});
</script>

<style scoped>
.network-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.network-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.network-header h3 {
  margin: 0;
}

.network-actions {
  display: flex;
  gap: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
}

.connections-controls {
  display: flex;
  align-items: center;
}

.interface-progress {
  margin: 16px 0;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.progress-text {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.network-content {
  flex: 1;
  overflow: auto;
  padding: 16px 0;
}

.network-overview {
  margin-bottom: 24px;
}

.network-interfaces {
  margin-bottom: 24px;
}

.network-interfaces h4 {
  margin-bottom: 16px;
}

.network-connections {
  margin-bottom: 24px;
}

.connections-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.connections-header h4 {
  margin: 0;
}

.connections-filters {
  display: flex;
  align-items: center;
  gap: 16px;
}

.network-charts {
  margin-bottom: 24px;
}

.network-charts h4 {
  margin-bottom: 16px;
}

.chart-container {
  height: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.connection-details {
  margin-top: 16px;
}

/* 网络流量趋势图卡片 */
.network-traffic-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  margin-top: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.traffic-controls {
  display: flex;
  gap: 8px;
}

.traffic-chart-container {
  margin-top: 16px;
}

.traffic-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.traffic-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.traffic-stats .stat-label {
  font-size: 12px;
  color: #666;
}

.traffic-stats .stat-value {
  font-size: 16px;
  font-weight: 600;
}

.traffic-stats .stat-value.download {
  color: #1890ff;
}

.traffic-stats .stat-value.upload {
  color: #52c41a;
}

.chart-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;
}

.chart-wrapper canvas {
  width: 100%;
  height: auto;
}
</style>