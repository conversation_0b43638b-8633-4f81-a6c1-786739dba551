<template>
  <div class="modern-terminal">
    <!-- 终端头部 -->
    <div class="terminal-header">
      <div class="header-left">
        <div class="terminal-tabs">
          <!-- 主终端类型切换 -->
          <div v-if="backupTerminals.length === 0">
            <div
              class="tab main-tab"
              :class="{ active: terminalType === 'mock' }"
              @click="switchTerminalType('mock', $event)"
            >
              <span class="tab-icon">💻</span>
              <span>模拟终端</span>
            </div>
            <div
              class="tab main-tab"
              :class="{ active: terminalType === 'interactive' }"
              @click="switchTerminalType('interactive', $event)"
            >
              <span class="tab-icon">⚡</span>
              <span>交互式终端</span>
            </div>
          </div>

          <!-- 多终端标签 -->
          <div v-else class="multi-terminal-tabs">
            <!-- 主终端标签 -->
            <div
              class="tab main-tab"
              :class="{ active: activeTerminalId === 0 }"
              @click="switchToTerminal(0)"
            >
              <span class="tab-icon">🏠</span>
              <span>主终端</span>
              <span class="terminal-id">ID:0</span>
              <!-- 主终端内的类型切换 -->
              <div v-if="activeTerminalId === 0" class="main-terminal-type-switch" @click.stop>
                <a-button
                  size="small"
                  :type="terminalType === 'mock' ? 'primary' : 'default'"
                  @click="switchTerminalType('mock', $event)"
                >
                  模拟
                </a-button>
                <a-button
                  size="small"
                  :type="terminalType === 'interactive' ? 'primary' : 'default'"
                  @click="switchTerminalType('interactive', $event)"
                >
                  交互
                </a-button>
              </div>
            </div>

            <!-- 备用终端标签 -->
            <div
              v-for="terminal in backupTerminals"
              :key="terminal.id"
              class="tab backup-tab"
              :class="{ active: activeTerminalId === terminal.id }"
              @click="switchToTerminal(terminal.id)"
            >
              <span class="tab-icon">💻</span>
              <span>终端{{ terminal.id }}</span>
              <span class="terminal-id">ID:{{ terminal.id }}</span>
              <a-button
                type="text"
                size="small"
                class="close-terminal-btn"
                @click.stop="closeBackupTerminal(terminal.id)"
              >
                <template #icon><CloseOutlined /></template>
              </a-button>
            </div>
          </div>

          <!-- 添加终端按钮 -->
          <div class="tab add-tab" @click="createBackupTerminal">
            <span class="tab-icon">➕</span>
            <span>新建终端</span>
          </div>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 状态信息面板 -->
        <div class="status-panel">
          <div class="status-item">
            <span class="status-icon" :class="getConnectionStatusClass()">●</span>
            <span class="status-text">{{ getConnectionStatusText() }}</span>
          </div>
          
          <div class="status-item">
            <span class="status-icon client-icon">👤</span>
            <span class="status-text">客户端: {{ clientId }}</span>
          </div>
          
          <div class="status-item">
            <span class="status-icon mode-icon">{{ terminalType === 'interactive' ? '⚡' : '💻' }}</span>
            <span class="status-text">{{ terminalType === 'interactive' ? '交互式' : '模拟' }}</span>
          </div>
          
          <div class="status-item">
            <span class="status-icon os-icon">{{ getOSIcon() }}</span>
            <span class="status-text">{{ getOSDisplayName() }}</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button 
            v-if="!isCurrentTerminalConnected()" 
            type="primary" 
            size="small"
            @click="connectCurrentTerminal"
            :loading="isCurrentTerminalConnecting()"
            class="connect-btn"
          >
            <template #icon><LinkOutlined /></template>
            连接
          </a-button>
          
          <a-button 
            v-if="isCurrentTerminalConnected()" 
            danger 
            size="small"
            @click="disconnectCurrentTerminal"
            class="disconnect-btn"
          >
            <template #icon><DisconnectOutlined /></template>
            断开
          </a-button>
          
          <a-button size="small" @click="clearTerminal" class="clear-btn">
            <template #icon><ClearOutlined /></template>
          </a-button>
          
          <a-button size="small" @click="closeTerminal" danger class="close-btn">
            <template #icon><CloseOutlined /></template>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 终端主体 -->
    <div class="terminal-body">
      <!-- 主终端 (ID=0) -->
      <div v-if="activeTerminalId === 0" class="main-terminal">
        <!-- 模拟终端 -->
        <div v-if="terminalType === 'mock'" class="mock-terminal">
          <div class="terminal-screen">
            <div class="terminal-output" ref="mockOutputRef">
              <div v-for="(line, index) in terminalOutput" :key="index" class="output-line">
                <span class="line-content">{{ line }}</span>
              </div>
            </div>
          </div>

          <div class="terminal-input-area">
            <div class="input-wrapper">
              <span class="prompt">{{ currentPrompt.trim() }}</span>
              <input
                v-model="terminalInput"
                class="command-input"
                placeholder="输入命令并按回车执行..."
                @keydown.enter="sendCommand"
                :disabled="!mockConnected"
                ref="mockInputRef"
              />
              <button
                class="send-btn"
                @click="sendCommand"
                :disabled="!mockConnected || !terminalInput.trim()"
              >
                <span>发送</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 交互式终端 -->
        <div v-if="terminalType === 'interactive'" class="interactive-terminal">
          <div id="xterm-container" ref="xtermContainer" class="xterm-wrapper"></div>
        </div>
      </div>

      <!-- 备用终端 -->
      <div v-for="terminal in backupTerminals" :key="terminal.id"
           v-show="activeTerminalId === terminal.id"
           class="backup-terminal">
        <div class="interactive-terminal">
          <div :id="`xterm-container-${terminal.id}`" class="xterm-wrapper backup-xterm"></div>
        </div>
      </div>
    </div>
    
    <!-- 底部连接状态区域 -->
    <div class="terminal-footer">
      <!-- 模拟终端连接状态 -->
      <div v-if="terminalType === 'mock'" class="connection-status-area">
        <div v-if="!mockConnected" class="disconnected-panel" @click="connectMockTerminal">
          <div class="status-indicator">
            <div class="status-dot disconnected"></div>
            <span class="status-text">终端已断开连接</span>
          </div>
          <div class="reconnect-hint">
            <span class="hint-text">点击重新连接</span>
            <div class="connect-icon">🔌</div>
          </div>
        </div>
        <div v-else class="connected-panel">
          <div class="status-indicator">
            <div class="status-dot connected"></div>
            <span class="status-text">终端连接正常</span>
          </div>
          <div class="session-info">
            <span class="session-text">会话活跃 | {{ isWindows ? 'Windows' : 'Unix' }} 系统</span>
          </div>
        </div>
      </div>
      
      <!-- 交互式终端连接状态 -->
      <div v-if="terminalType === 'interactive'" class="connection-status-area">
        <div v-if="!isConnected" class="disconnected-panel" @click="connectTerminal">
          <div class="status-indicator">
            <div class="status-dot disconnected"></div>
            <span class="status-text">交互式终端已断开</span>
          </div>
          <div class="reconnect-hint">
            <span class="hint-text">点击重新连接</span>
            <div class="connect-icon">⚡</div>
          </div>
        </div>
        <div v-else class="connected-panel">
          <div class="status-indicator">
            <div class="status-dot connected"></div>
            <span class="status-text">交互式终端连接正常</span>
          </div>
          <div class="session-info">
            <span class="session-text">实时会话 | {{ sessionType }} 模式</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { SearchAddon } from 'xterm-addon-search';
import 'xterm/css/xterm.css';
import { getWsBaseUrl } from '@/utils/serverConfig.js';
import { clientApi } from '@/api';
import {
  LinkOutlined,
  DisconnectOutlined,
  ClearOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';

// 定义属性
const props = defineProps({
  clientId: {
    type: String,
    required: true
  },
  active: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['close', 'openInteractiveTerminal', 'sendCommand']);

// 终端类型
const terminalType = ref('mock');

// 模拟终端相关状态
const terminalOutput = ref([]);
const terminalInput = ref('');
const currentPrompt = ref('$ ');
const sessionType = ref('interactive');
const isWindows = ref(false);
const currentSessionOS = ref('unix');
const mockOutputRef = ref(null);
const mockConnected = ref(false);
const mockConnecting = ref(false);

// 交互式终端相关状态
const xtermContainer = ref(null);
const terminal = ref(null);
const fitAddon = ref(null);
const pluginsLoaded = ref(false);
const webSocket = ref(null);
const connectionStatus = ref('disconnected');
const connecting = ref(false);
const isConnected = ref(false);
const listenerType = ref('pipe');
const globalPrompt = ref("");

// 多终端管理状态
const activeTerminalId = ref(0); // 当前活跃的终端ID，0为主终端
const backupTerminals = ref([]); // 备用终端列表 [{id: 1000, active: true}, ...]
const terminals = ref(new Map()); // 终端实例映射 Map<terminalId, {terminal, fitAddon, webSocket, ...}>

// 非交互式模式状态
const commandHistory = ref([]);
const historyPointer = ref(-1);
const currentInput = ref('');
const tempInput = ref('');
const currentSessionShellPrompt = ref('');
const leftCount = ref(0);
const currentInputLeft = ref('');
const currentInputRight = ref('');
const lastInput = ref('');

// 初始化模拟终端
const initMockTerminal = () => {
  terminalOutput.value = [
    `模拟终端已初始化`,
    '点击连接按钮连接到客户端...',
    ''
  ];
  terminalInput.value = '';
  mockConnected.value = false;
  mockConnecting.value = false;
  
  nextTick(() => {
    scrollTerminalToBottom();
  });
};

// 模拟终端的WebSocket连接
const mockWebSocket = ref(null);

// 连接模拟终端
const connectMockTerminal = async () => {
  if (mockConnecting.value || mockConnected.value) return;
  
  mockConnecting.value = true;
  
  try {
    // 建立真正的WebSocket连接
    const wsBaseUrl = getWsBaseUrl();
    const token = localStorage.getItem('token');
    const wsUrl = `${wsBaseUrl}/client/ws/${props.clientId}?token=${token}`;
    
    mockWebSocket.value = new WebSocket(wsUrl);
    
    mockWebSocket.value.onopen = () => {
      mockConnected.value = true;
      mockConnecting.value = false;
      
      terminalOutput.value = [
        `连接到客户端 ${props.clientId}`,
        'WebSocket连接已建立，可以接收命令执行结果',
        '输入命令并按回车发送...',
        ''
      ];
      
      scrollTerminalToBottom();
    };
    
    mockWebSocket.value.onmessage = (event) => {
      handleMockTerminalMessage(event);
    };
    
    mockWebSocket.value.onclose = () => {
      mockConnected.value = false;
      mockConnecting.value = false;
      terminalOutput.value.push('WebSocket连接已关闭');
      scrollTerminalToBottom();
    };
    
    mockWebSocket.value.onerror = (error) => {
      console.error('模拟终端WebSocket错误:', error);
      mockConnected.value = false;
      mockConnecting.value = false;
      terminalOutput.value.push('WebSocket连接错误');
      scrollTerminalToBottom();
      message.error('连接失败');
    };
    
  } catch (error) {
    console.error('创建模拟终端WebSocket连接失败:', error);
    mockConnecting.value = false;
    message.error('连接失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  }
};

// 断开模拟终端连接
const disconnectMockTerminal = () => {
  if (mockWebSocket.value) {
    mockWebSocket.value.close();
    mockWebSocket.value = null;
  }
  
  mockConnected.value = false;
  terminalOutput.value = [
    `模拟终端已断开连接`,
    '点击连接按钮重新连接...',
    ''
  ];
  scrollTerminalToBottom();
};

// 处理模拟终端WebSocket消息
const handleMockTerminalMessage = (event) => {
  console.log('模拟终端收到WebSocket消息:', event.data);
  
  try {
    // 尝试解析JSON
    const data = JSON.parse(event.data);
    
    // 清理ANSI转义序列
    if (data.content) {
      data.content = cleanANSI(data.content);
    }
    
    // 处理不同类型的消息
    if (data.type === 'command_output') {
      // 命令输出
      const lines = data.content.split('\n');
      lines.forEach(line => {
        if (line.trim() || lines.length === 1) {
          terminalOutput.value.push(line);
        }
      });
    } else if (data.type === 'status') {
      // 状态更新
      terminalOutput.value.push(`[状态] ${data.content}`);
    } else if (data.type === 'error') {
      // 错误信息
      terminalOutput.value.push(`[错误] ${data.content}`);
    } else {
      // 其他类型的消息
      terminalOutput.value.push(`[消息] ${data.content || event.data}`);
    }
  } catch (error) {
    // 如果不是JSON格式，直接显示文本
    console.error('解析模拟终端WebSocket消息失败:', error);
    terminalOutput.value.push(event.data);
  }
  
  // 滚动到底部
  nextTick(() => {
    scrollTerminalToBottom();
  });
};

// 清理ANSI转义序列
function cleanANSI(str) {
  return str
    // 移除OSC窗口标题序列（如\x1b]0;...\x07）
    .replace(/\x1B\]0;[^\x07]*\x07/g, '')
    // 移除CSI控制序列（如颜色代码\x1b[31m）
    .replace(/\x1B\[[0-?]*[ -/]*[@-~]/g, '')
    // 移除其它控制字符
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // 移除回车符，保留换行符
    .replace(/\r/g, '');
}

// 获取会话信息
const fetchSessionInfo = async (clientId) => {
  try {
    const response = await clientApi.getClient(clientId);
    if (response) {
      const client = response.data;
      sessionType.value = client.sessionType;
      isWindows.value = client.os === "windows";
      
      const sessionInfo = {
        type: sessionType.value,
        isWindows: false,
        listenerType: client.listenerType,
        os: client.os, // 添加原始操作系统信息
      };
      console.log(sessionInfo);
      if (client.os.toLowerCase().includes('windows')) {
        sessionInfo.isWindows = true;
      }
      sessionInfo.type = client.sessionType;
      return sessionInfo;
    }
  } catch (error) {
    console.error('Failed to fetch session info:', error);
  }
  
  return {
    type: 'interactive',
    isWindows: false,
    listenerType: 'pipe',
    os: 'unix',
  };
};

// 新的UI辅助方法
const getConnectionStatusClass = () => {
  if (terminalType.value === 'mock') {
    return mockConnected.value ? 'status-connected' : 'status-disconnected';
  } else {
    return isConnected.value ? 'status-connected' : 'status-disconnected';
  }
};

const getConnectionStatusText = () => {
  if (terminalType.value === 'mock') {
    return mockConnected.value ? '已连接' : '未连接';
  } else {
    return isConnected.value ? '已连接' : '未连接';
  }
};

const isCurrentTerminalConnected = () => {
  return terminalType.value === 'mock' ? mockConnected.value : isConnected.value;
};

const isCurrentTerminalConnecting = () => {
  return terminalType.value === 'mock' ? mockConnecting.value : connecting.value;
};

const connectCurrentTerminal = () => {
  if (terminalType.value === 'mock') {
    connectMockTerminal();
  } else {
    connectTerminal();
  }
};

const disconnectCurrentTerminal = () => {
  if (terminalType.value === 'mock') {
    disconnectMockTerminal();
  } else {
    disconnectTerminal();
  }
};

const switchTerminalType = (type, event) => {
  if (event) {
    event.stopPropagation();
  }
  
  if (terminalType.value === type) return;
  
  // 断开当前终端连接
  if (terminalType.value === 'mock' && mockConnected.value) {
    disconnectMockTerminal();
  } else if (terminalType.value === 'interactive' && isConnected.value) {
    disconnectTerminal();
  }
  
  terminalType.value = type;
  
  nextTick(() => {
    if (type === 'mock') {
      initMockTerminal();
    } else {
      initTerminal();
    }
  });
};

// 模拟终端发送命令 (修复版本)
const sendCommand = async () => {
  if (!terminalInput.value.trim()) {
    return;
  }
  
  if (!mockConnected.value) {
    message.warning('请先连接模拟终端');
    return;
  }

  // 显示用户输入的命令
  const userCommand = `${currentPrompt.value.trim()} ${terminalInput.value}`;
  terminalOutput.value.push(userCommand);
  
  try {
    // 获取会话信息
    await fetchSessionInfo(props.clientId);
    
    // 构造命令
    let command = terminalInput.value;
    command += '\r';
    // 通过clientApi直接发送命令，而不是emit事件
    const response = await clientApi.sendCommand({
      id: parseInt(props.clientId),
      command: command
    });
    
    // 处理响应
    if (response && response.data) {
      if (response.data.output) {
        // 分割输出为多行
        const outputLines = response.data.output.split('\n');
        outputLines.forEach(line => {
          if (line.trim()) {
            terminalOutput.value.push(line);
          }
        });
      }
      
      // 更新提示符
      if (response.data.prompt) {
        currentPrompt.value = response.data.prompt;
      }
    }
    
  } catch (error) {
    console.error('发送命令失败:', error);
    terminalOutput.value.push(`错误: ${error.response?.data?.error || error.response?.data?.message || error.message || '命令发送失败'}`);
    message.error('命令发送失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  }

  // 清空输入
  terminalInput.value = '';
  scrollTerminalToBottom();
};

// 滚动终端到底部
const scrollTerminalToBottom = () => {
  nextTick(() => {
    if (mockOutputRef.value) {
      mockOutputRef.value.scrollTop = mockOutputRef.value.scrollHeight;
    }
  });
};

// 清空模拟终端输出
const clearOutput = () => {
  terminalOutput.value = [];
  terminalOutput.value.push(`连接到客户端 ${props.clientId}`);
  terminalOutput.value.push('输入命令并按回车发送...');
  terminalOutput.value.push('');
  scrollTerminalToBottom();
};

// 打开交互式终端
const openInteractiveTerminal = () => {
  terminalType.value = 'interactive';
  nextTick(() => {
    initTerminal();
    setTimeout(() => {
      connectToClient();
    }, 100);
  });
};

// 初始化交互式终端
const initTerminal = () => {
  if (!xtermContainer.value) {
    console.error('Terminal container not found');
    return;
  }
  
  // 清理旧终端和插件
  if (terminal.value) {
    try {
      // 先清理插件引用（只有在插件成功加载时）
      if (pluginsLoaded.value && fitAddon.value) {
        fitAddon.value = null;
      }
      // 安全地dispose终端
      terminal.value.dispose();
    } catch (error) {
      console.warn('Terminal disposal warning:', error);
    }
    terminal.value = null;
    pluginsLoaded.value = false;
  }
  
  // 清空容器
  xtermContainer.value.innerHTML = '';
  
  // 重置连接状态
  isConnected.value = false;
  connecting.value = false;
  
  // 创建新终端实例
  terminal.value = new Terminal({
    cursorBlink: true,
    fontSize: 14,
    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
    theme: {
      background: '#1a1a1a',
      foreground: '#e0e0e0',
      cursor: '#ffffff',
      selection: 'rgba(255, 255, 255, 0.3)',
      black: '#000000',
      red: '#e06c75',
      green: '#98c379',
      yellow: '#e5c07b',
      blue: '#61afef',
      magenta: '#c678dd',
      cyan: '#56b6c2',
      white: '#dcdfe4',
    },
    scrollback: 1000,
    disableStdin: false,
    convertEol: true,
    allowProposedApi: true,
  });

  // 添加插件
  try {
    fitAddon.value = new FitAddon();
    terminal.value.loadAddon(fitAddon.value);
    terminal.value.loadAddon(new WebLinksAddon());
    terminal.value.loadAddon(new SearchAddon());
    pluginsLoaded.value = true;
  } catch (error) {
    console.warn('Plugin loading warning:', error);
    pluginsLoaded.value = false;
  }

  // 打开终端
  terminal.value.open(xtermContainer.value);

  // 强制设置容器样式
  if (xtermContainer.value) {
    xtermContainer.value.style.height = '100%';
    xtermContainer.value.style.width = '100%';
    xtermContainer.value.style.minHeight = '400px';
  }

  // 等待DOM渲染完成后再调整大小
  nextTick(() => {
    // 多次调用fit确保大小正确
    setTimeout(() => {
      if (fitAddon.value && terminal.value) {
        fitAddon.value.fit();
        console.log('First fit:', terminal.value.cols, 'x', terminal.value.rows);
        console.log('Container size:', xtermContainer.value?.offsetWidth, 'x', xtermContainer.value?.offsetHeight);
      }
    }, 50);

    setTimeout(() => {
      if (fitAddon.value && terminal.value) {
        fitAddon.value.fit();
        console.log('Second fit:', terminal.value.cols, 'x', terminal.value.rows);
      }
    }, 200);

    setTimeout(() => {
      if (fitAddon.value && terminal.value) {
        fitAddon.value.fit();
        console.log('Third fit:', terminal.value.cols, 'x', terminal.value.rows);
      }
    }, 500);
  });

  // 显示初始化信息
  terminal.value.writeln('交互式终端已初始化，点击连接按钮连接到客户端...');
  terminal.value.writeln('');

  // 设置键盘事件处理
  setupKeyboardHandlers();

  // 窗口大小变化处理
  window.addEventListener('resize', handleResize);

  // 聚焦终端
  terminal.value.focus();
  console.log(terminal.value.cols);
  console.log(terminal.value.rows);
  console.log(JSON.stringify({
    action: 'resize',
    cols: terminal.value.cols,
    rows: terminal.value.rows
  }))
};

// 设置键盘事件处理
const setupKeyboardHandlers = () => {
  if (!terminal.value) return;
  
  terminal.value.onKey(async (e) => {
    if (!webSocket.value || webSocket.value.readyState !== WebSocket.OPEN) {
      return;
    }
    const ev = e.domEvent;
    const key = ev.key;
    
    // 非交互式会话的特殊处理
    if (listenerType.value === "pipe" && isWindows.value) {
      switch (key) {
        case 'ArrowUp':
          ev.preventDefault();
          if (commandHistory.value.length === 0) break;

          if (historyPointer.value === -1) {
            tempInput.value = currentInput.value;
            historyPointer.value = commandHistory.value.length;
          }
          if (historyPointer.value > 0) {
            historyPointer.value--;
            showHistoryCommand();
          }
          break;

        case 'ArrowDown':
          ev.preventDefault();
          if (historyPointer.value === -1) {
            historyPointer.value = commandHistory.value.length;
          }
          historyPointer.value++;
          if (historyPointer.value >= commandHistory.value.length) {
            historyPointer.value = -1;
            currentInput.value = tempInput.value;
            tempInput.value = '';
            clearCurrentLine();
            if (currentSessionOS.value === 'windows') {
              terminal.value.write(`\r${currentSessionShellPrompt.value}`);
            }
            terminal.value.write(currentInput.value);
          } else {
            showHistoryCommand();
          }
          break;

        case 'ArrowLeft':
          ev.preventDefault();
          if (currentInput.value.length > 0 && leftCount.value <= currentInput.value.length) {
            leftCount.value++;
            currentInputLeft.value = currentInput.value.slice(0, -1);
            currentInputRight.value = currentInput.value.slice(-1);
            terminal.value.write('\x1B[D');
          }
          break;

        case 'ArrowRight':
          ev.preventDefault();
          if (currentInput.value.length > 0) {
            terminal.value.write('\x1B[C');
          }
          break;

        case 'Enter':
          handleNonInteractiveEnter();
          break;

        case 'Backspace':
          if (currentInput.value.length > 0 && terminal.value._core.buffer.x > 2) {
            currentInput.value = currentInput.value.slice(0, -1);
            terminal.value.write('\x08 \x08');
          }
          break;

        default:
          if (e.ctrlKey && key === 'c') {
            sendToCurrentTerminal('\x03');
          } else if (e.ctrlKey && key === 'd') {
            sendToCurrentTerminal('\x04');
          } else if (e.key.length === 1) {
            handleNonInteractiveCharInput(e.key);
          }
      }
    } else if (listenerType.value !== "pipe"){
      // 交互式会话直接转发所有按键
      switch (key) {
        case 'Enter':
          sendToCurrentTerminal('\r');
          break
        case 'Backspace':
          sendToCurrentTerminal('\x7F');
          break;
        case 'ArrowUp':
          sendToCurrentTerminal('\x1B[A');
          break;
        case 'ArrowDown':
          sendToCurrentTerminal('\x1B[B');
          break;
        case 'ArrowRight':
          sendToCurrentTerminal('\x1B[C');
          break
        case 'ArrowLeft':
          sendToCurrentTerminal('\x1B[D');
          break;
        case 'Tab':
          sendToCurrentTerminal('\t');
          e.domEvent.preventDefault();
          break;
        default:
          if (e.ctrlKey && key === 'c') {
            sendToCurrentTerminal('\x03');
          } else if (e.ctrlKey && key === 'd') {
            sendToCurrentTerminal('\x04');
          } else if (e.key.length === 1) {
            sendToCurrentTerminal(e.key);
          }
      }
    } else if (listenerType.value === "pipe"){
      // pipe监听器使用命令模式，不逐字符发送
      if (e.ctrlKey && key === "c"){
        // 发送中断信号
        sendToCurrentTerminal('\x03');
        currentInput.value = '';
        terminal.value.writeln('^C');
      } else if (e.ctrlKey && key === 'd') {
        // 发送EOF信号
        sendToCurrentTerminal('\x04');
      } else if(key.length === 1 && key !== "Enter") {
        // 本地缓存字符，不立即发送
        currentInput.value += e.key;
        lastInput.value += e.key;
        // 在终端显示字符
        terminal.value.write(key);
      }
      if (key === "Enter"){
        // 发送完整命令
        if (currentInput.value.trim()) {
          const command = currentInput.value + "\n";
          sendToCurrentTerminal(command);
          console.log('🚀 发送pipe命令:', currentInput.value);
        } else {
          // 空命令也发送换行符
          sendToCurrentTerminal("\n");
        }
        currentInput.value = '';
        terminal.value.writeln('');
      }
      if (key === "Backspace"){
        if (currentInput.value.length > 0) {
          currentInput.value = currentInput.value.slice(0, -1);
          sendToCurrentTerminal('\x7F');
          terminal.value.write('\x08 \x08');

        }
      }
    }
  });
};

// 显示历史命令
const showHistoryCommand = () => {
  currentInput.value = commandHistory.value[historyPointer.value];
  clearCurrentLine();
  terminal.value.write(currentSessionShellPrompt.value+currentInput.value);
};

// 清除当前行
const clearCurrentLine = () => {
  if (currentSessionOS.value === 'windows'){
    terminal.value.write(' ');
  } else {
    terminal.value.write('\x1B[2K\r');// 清除整行并回到行首
  }
};

// 发送命令到服务器
const sendCommandToServer = async (command, clientId) => {
  const params = {
    id: parseInt(clientId),
    command: command
  };

  try {
    const res = await clientApi.sendCommand(params);
    if (res.code !== 200 || res.msg !== '操作成功' ) {
      message.error(res.msg || '命令发送失败');
    }
  } catch (error) {
    console.error('发送命令错误:', error);
    const errorMsg = error.response?.data?.error || error.response?.data?.message || error.message;
    message.error('发送命令失败: ' + errorMsg);
    if (terminal.value) {
      terminal.value.write(`\r\n错误: 发送命令失败 - ${errorMsg}\r\n`);
    }
  }
};

// 非交互式模式处理回车
const handleNonInteractiveEnter = () => {
  const command = currentInput.value.replace(/\r?\n?$/, '');
  
  // 保存非空命令到历史
  if (command.trim()) {
    commandHistory.value.push(command);
    if (commandHistory.value.length > 100) {
      commandHistory.value.shift();
    }
    
    if (isWindows.value) {
      sendCommandToServer(command+"\r\n", props.clientId);
    } else {
      sendCommandToServer(command.trim(), props.clientId);
    }
  }
  
  currentInputLeft.value = currentInputRight.value = '';
  leftCount.value = 0;
  terminal.value.write('\r\n');
  historyPointer.value = -1;
  currentInput.value = '';
  tempInput.value = '';
};

// 非交互式模式处理字符输入
const handleNonInteractiveCharInput = (char) => {
  if (historyPointer.value !== -1) {
    historyPointer.value = -1;
    currentInput.value = tempInput.value;
    tempInput.value = '';
    clearCurrentLine();
  }
  
  if (currentInputLeft.value !== '' || currentInputRight.value !== '') {
    currentInput.value = currentInputLeft.value + char + currentInputRight.value;
    currentInputLeft.value = currentInputRight.value = '';
    terminal.value.write(char);
  } else {
    currentInput.value += char;
    terminal.value.write(char);
  }
};

// 连接到客户端
const connectToClient = async () => {
  if (!props.clientId) {
    message.error('无效的客户端ID');
    return;
  }
  
  connecting.value = true;
  
  try {
    // 首先获取会话信息
    const sessionInfo = await fetchSessionInfo(props.clientId);
    if (sessionInfo) {
      listenerType.value = sessionInfo.listenerType;
      sessionType.value = sessionInfo.type || 'interactive';
      isWindows.value = sessionInfo.isWindows || false;
      
      // 正确设置操作系统类型
      if (sessionInfo.os) {
        const osLower = sessionInfo.os.toLowerCase();
        if (osLower.includes('windows')) {
          currentSessionOS.value = 'windows';
        } else if (osLower.includes('darwin') || osLower.includes('macos')) {
          currentSessionOS.value = 'darwin';
        } else {
          currentSessionOS.value = 'unix';
        }
      } else {
        currentSessionOS.value = isWindows.value ? 'windows' : 'unix';
      }
    }
    // 建立WebSocket连接
    const wsBaseUrl = getWsBaseUrl();
    const token = localStorage.getItem('token');
    const wsUrl = `${wsBaseUrl}/client/ws/${props.clientId}?token=${token}`;

    webSocket.value = new WebSocket(wsUrl);
    webSocket.value.binaryType = 'arraybuffer';

    webSocket.value.onopen = async () => {
      connectionStatus.value = 'connected';
      connecting.value = false;
      isConnected.value = true;

      // 确保终端大小正确 - 多次调用确保生效
      if (fitAddon.value && terminal.value) {
        fitAddon.value.fit();
        setTimeout(() => {
          if (fitAddon.value && terminal.value) {
            fitAddon.value.fit();
            console.log('Connected fit:', terminal.value.cols, 'x', terminal.value.rows);
          }
        }, 100);
      }

      terminal.value.focus();

      // 延迟发送resize确保终端大小已正确设置
      setTimeout(() => {
        notifyResize();
      }, 200);
    };

    webSocket.value.onmessage = (event) => {
      handleWebSocketMessage(event);
    };
    
    webSocket.value.onclose = () => {
      connectionStatus.value = 'disconnected';
      connecting.value = false;
      isConnected.value = false;
      if (terminal.value) {
        terminal.value.writeln('\r\n连接已断开');
      }
    };
    
    webSocket.value.onerror = (error) => {
      console.error('WebSocket error:', error);
      connectionStatus.value = 'disconnected';
      connecting.value = false;
      isConnected.value = false;
      message.error('连接失败');
    };
    
  } catch (error) {
    console.error('Connection error:', error);
    connecting.value = false;
    message.error('连接失败: ' + error.message);
  }
};

// 处理WebSocket消息
const handleWebSocketMessage = (event) => {
  const rawData = event.data;
  let processedData = rawData instanceof ArrayBuffer
      ? new Uint8Array(rawData)
      : rawData;

  // 过滤特殊错误消息
  if (typeof processedData === 'string' && processedData.includes('action:resize: 未找到命令')) {
    return;
  }

  // 尝试解析JSON消息
  if (typeof processedData === 'string') {
    try {
      const jsonData = JSON.parse(processedData);

      // 处理创建终端响应
      if (jsonData.action === 'create_terminal_response') {
        handleCreateTerminalResponse(jsonData);
        return;
      }

      // 处理多终端消息
      if (jsonData.type === 'command_output' && jsonData.content) {
        const terminalId = jsonData.terminal_id || 0; // 默认主终端
        console.log(`📨 收到终端${terminalId}的输出:`, jsonData.content);

        if (terminalId === 0) {
          // 主终端输出
          console.log('✅ 写入主终端');
          if (terminal.value) {
            terminal.value.write(jsonData.content);
          }
        } else {
          // 备用终端输出
          console.log(`✅ 写入备用终端${terminalId}`);
          const terminalInstance = terminals.value.get(terminalId);
          if (terminalInstance && terminalInstance.terminal) {
            terminalInstance.terminal.write(jsonData.content);
            console.log(`✅ 成功写入备用终端${terminalId}`);
          } else {
            console.log(`❌ 备用终端${terminalId}实例不存在`);
          }
        }
        return;
      }
    } catch (e) {
      // 非JSON数据，继续处理
    }
  }

  // 写入处理后的数据到当前活跃终端
  const currentTerminal = activeTerminalId.value === 0
    ? terminal.value
    : terminals.value.get(activeTerminalId.value)?.terminal;

  if (currentTerminal && processedData) {
    if (processedData instanceof Uint8Array) {
      const text = new TextDecoder('utf-8').decode(processedData);
      currentTerminal.write(text);
    } else {
      currentTerminal.write(processedData);
    }
  }
};

// 睡眠函数
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 通知终端大小变化
const notifyResize = () => {
  let currentTerminal = null;
  let cols = 0;
  let rows = 0;

  if (activeTerminalId.value === 0) {
    // 主终端
    if (terminal.value && webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
      currentTerminal = terminal.value;
      cols = terminal.value.cols;
      rows = terminal.value.rows;
    }
  } else {
    // 备用终端
    const terminalInstance = terminals.value.get(activeTerminalId.value);
    if (terminalInstance && terminalInstance.terminal && webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
      currentTerminal = terminalInstance.terminal;
      cols = terminalInstance.terminal.cols;
      rows = terminalInstance.terminal.rows;
    }
  }

  if (currentTerminal && cols > 0 && rows > 0) {
    try {
      sendToCurrentTerminal({
        action: 'resize',
        terminal_id: activeTerminalId.value,
        cols: cols,
        rows: rows
      });
      console.log(`发送resize到终端${activeTerminalId.value}: ${cols}x${rows}`);
    } catch (error) {
      console.warn('Failed to send resize notification:', error);
    }
  }
};

// 处理窗口大小变化
const handleResize = () => {
  try {
    if (activeTerminalId.value === 0) {
      // 主终端
      if (fitAddon.value && terminal.value) {
        fitAddon.value.fit();
        notifyResize();
      }
    } else {
      // 备用终端
      const terminalInstance = terminals.value.get(activeTerminalId.value);
      if (terminalInstance && terminalInstance.fitAddon && terminalInstance.terminal) {
        terminalInstance.fitAddon.fit();
        notifyResize();
      }
    }
  } catch (error) {
    console.warn('Resize handling error:', error);
  }
};

// 连接交互式终端
const connectTerminal = () => {
  if (connecting.value || isConnected.value) return;
  
  nextTick(() => {
    initTerminal();
    setTimeout(() => {
      connectToClient();
    }, 100);
  });
};

// 断开交互式终端连接
const disconnectTerminal = () => {
  if (webSocket.value) {
    webSocket.value.close();
    webSocket.value = null;
  }
  connectionStatus.value = 'disconnected';
  connecting.value = false;
  isConnected.value = false;
};

// 重连
const reconnect = () => {
  disconnectTerminal();
  setTimeout(() => {
    connectTerminal();
  }, 1000);
};

// 清空终端
const clearTerminal = () => {
  if (terminalType.value === 'mock') {
    clearOutput();
  } else if (terminal.value) {
    terminal.value.clear();
  }
};

// 关闭终端
const closeTerminal = () => {
  if (terminalType.value === 'interactive') {
    disconnectTerminal();
  }
  emit('close');
};

// 添加引用
const mockInputRef = ref(null);

// 暴露方法给父组件 (用于模拟终端)
defineExpose({
  addOutput: (line) => {
    terminalOutput.value.push(line);
    scrollTerminalToBottom();
  },
  clearOutput: () => {
    terminalOutput.value = [];
  },
  setPrompt: (prompt) => {
    currentPrompt.value = prompt;
  }
});

// 发送数据到当前活跃终端
const sendToCurrentTerminal = (data) => {
  console.log(`📤 发送数据到终端${activeTerminalId.value}:`, data);

  if (activeTerminalId.value === 0) {
    // 主终端：保持原有的直接发送逻辑
    console.log('📤 主终端发送模式');
    if (webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
      if (typeof data === 'string') {
        // 字符级发送，保持原有逻辑
        console.log('📤 主终端字符级发送:', data);
        webSocket.value.send(data);
      } else if (data.action === 'resize') {
        // resize命令需要特殊处理
        console.log('📤 主终端resize发送:', data);
        webSocket.value.send(JSON.stringify(data));
      } else {
        // 其他命令也直接发送
        console.log('📤 主终端其他命令发送:', data);
        webSocket.value.send(data.command || JSON.stringify(data));
      }
    }
  } else {
    // 备用终端：使用主WebSocket连接，但包含终端ID
    console.log(`📤 备用终端${activeTerminalId.value}发送模式`);
    if (webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
      if (typeof data === 'string') {
        // 字符级发送需要包装成多终端格式
        const message = {
          terminal_id: activeTerminalId.value,
          command: data,
          action: 'command'
        };
        console.log('📤 备用终端字符级发送:', message);
        webSocket.value.send(JSON.stringify(message));
      } else if (data.action === 'resize') {
        // resize命令已经包含terminal_id
        console.log('📤 备用终端resize发送:', data);
        webSocket.value.send(JSON.stringify(data));
      } else {
        // 其他命令包装成多终端格式
        const message = {
          terminal_id: activeTerminalId.value,
          command: data.command || data,
          action: data.action || 'command'
        };
        console.log('📤 备用终端其他命令发送:', message);
        webSocket.value.send(JSON.stringify(message));
      }
    }
  }
};

// 多终端管理方法
const switchToTerminal = (terminalId) => {
  console.log(`切换到终端 ${terminalId}`);
  activeTerminalId.value = terminalId;

  nextTick(() => {
    if (terminalId === 0) {
      // 主终端逻辑：确保主终端正确挂载到DOM
      const mainContainer = document.getElementById('xterm-container');
      if (mainContainer && terminal.value && fitAddon.value) {
        // 如果主终端没有挂载到DOM，重新挂载
        if (!mainContainer.hasChildNodes()) {
          terminal.value.open(mainContainer);
        }
        fitAddon.value.fit();
        terminal.value.focus();
      }
    } else {
      // 备用终端逻辑
      const terminalInstance = terminals.value.get(terminalId);
      console.log(`🔍 切换到备用终端${terminalId}，实例存在:`, !!terminalInstance);

      if (terminalInstance) {
        // 将备用终端挂载到DOM
        const container = document.getElementById(`xterm-container-${terminalId}`);
        console.log(`🔍 DOM容器存在:`, !!container, `已有子节点:`, container?.hasChildNodes());

        if (container) {
          if (!container.hasChildNodes()) {
            console.log(`📦 挂载备用终端${terminalId}到DOM`);
            terminalInstance.terminal.open(container);
            terminalInstance.fitAddon.fit();
          }

          // 确保终端获得焦点
          setTimeout(() => {
            if (terminalInstance.terminal) {
              terminalInstance.terminal.focus();
              console.log(`🎯 备用终端${terminalId}已获得焦点`);
            }
          }, 100);
        } else {
          console.error(`❌ 找不到备用终端${terminalId}的DOM容器`);
        }

        if (terminalInstance.fitAddon) {
          terminalInstance.fitAddon.fit();
        }
      } else {
        console.error(`❌ 找不到备用终端${terminalId}的实例`);
      }
    }
  });
};

const createBackupTerminal = async () => {
  try {
    console.log('创建备用终端...');
    message.info('正在创建新终端...');

    // 直接调用创建终端API
    const response = await clientApi.createBackupTerminal(props.clientId);
    console.log('=== 创建终端API完整响应 ===');
    console.log('response:', response);
    console.log('response.code:', response?.code);
    console.log('response.data:', response?.data);
    console.log('response.msg:', response?.msg);
    console.log('typeof response:', typeof response);
    console.log('JSON.stringify(response):', JSON.stringify(response, null, 2));

    if (response && response.code === 200 && response.data && response.data.data) {
      console.log('✅ 响应成功，调用handleCreateTerminalResponse');
      console.log('传递给handleCreateTerminalResponse的数据:', response.data.data);
      handleCreateTerminalResponse(response.data.data);
    } else {
      console.log('❌ 响应失败或数据格式不正确');
      console.log('条件检查结果:');
      console.log('- response存在:', !!response);
      console.log('- response.code === 200:', response?.code === 200);
      console.log('- response.data存在:', !!response?.data);
      console.log('- response.data.data存在:', !!response?.data?.data);
      message.error('创建终端失败: ' + (response?.msg || '未知错误'));
    }

  } catch (error) {
    console.error('=== 创建备用终端异常 ===');
    console.error('error:', error);
    console.error('error.message:', error.message);
    console.error('error.response:', error.response);
    console.error('error.response?.data:', error.response?.data);
    message.error('创建备用终端失败: ' + error.message);
  }
};

// 处理创建终端的响应
const handleCreateTerminalResponse = (response) => {
  console.log('=== handleCreateTerminalResponse 被调用 ===');
  console.log('response:', response);
  console.log('response.success:', response?.success);
  console.log('response.terminal_id:', response?.terminal_id);
  console.log('response.error:', response?.error);
  console.log('typeof response:', typeof response);
  console.log('JSON.stringify(response):', JSON.stringify(response, null, 2));

  try {
    if (response.success) {
      console.log('✅ 终端创建成功，开始处理...');
      const newTerminalId = response.terminal_id || (1000 + backupTerminals.value.length);

      // 添加到备用终端列表
      backupTerminals.value.push({
        id: newTerminalId,
        active: true,
        created: new Date()
      });

      // 创建新的终端实例
      const newTerminal = new Terminal({
        cursorBlink: true,
        fontSize: 14,
        fontFamily: 'Consolas, "Courier New", monospace',
        theme: {
          background: '#1e1e1e',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#3a3a3a'
        }
      });

      const newFitAddon = new FitAddon();
      newTerminal.loadAddon(newFitAddon);

      // 备用终端的键盘事件将在DOM挂载后绑定，这里先不绑定

      // 存储终端实例（备用终端使用主WebSocket连接）
      const terminalInstance = {
        terminal: newTerminal,
        fitAddon: newFitAddon,
        webSocket: null, // 备用终端不需要独立的WebSocket连接
        connected: true  // 通过主WebSocket连接
      };

      terminals.value.set(newTerminalId, terminalInstance);

      // 切换到新终端
      switchToTerminal(newTerminalId);

      // 等待DOM更新后调整终端大小并重新绑定键盘事件
      nextTick(() => {
        const terminalInstance = terminals.value.get(newTerminalId);
        if (terminalInstance && terminalInstance.fitAddon) {
          try {
            terminalInstance.fitAddon.fit();

            // 绑定键盘事件（首次绑定）
            const terminal = terminalInstance.terminal;
            if (terminal) {
              console.log(`🎹 绑定备用终端${newTerminalId}的键盘事件`);

              // 绑定键盘事件
              terminal.onKey(async (e) => {
                if (!webSocket.value || webSocket.value.readyState !== WebSocket.OPEN) {
                  return;
                }
                const ev = e.domEvent;
                const key = ev.key;

                console.log(`🎹 备用终端${newTerminalId}按键:`, key);

                // 直接发送到指定的备用终端
                const sendToBackupTerminal = (data) => {
                  const message = {
                    terminal_id: newTerminalId,
                    command: data,
                    action: 'command'
                  };
                  console.log('📤 备用终端直接发送:', message);
                  webSocket.value.send(JSON.stringify(message));
                };

                // 处理按键
                switch (key) {
                  case 'Enter':
                    sendToBackupTerminal('\r');
                    break
                  case 'Backspace':
                    sendToBackupTerminal('\x7F');
                    break;
                  case 'ArrowUp':
                    sendToBackupTerminal('\x1B[A');
                    break;
                  case 'ArrowDown':
                    sendToBackupTerminal('\x1B[B');
                    break;
                  case 'ArrowRight':
                    sendToBackupTerminal('\x1B[C');
                    break
                  case 'ArrowLeft':
                    sendToBackupTerminal('\x1B[D');
                    break;
                  case 'Tab':
                    sendToBackupTerminal('\t');
                    e.domEvent.preventDefault();
                    break;
                  default:
                    if (e.ctrlKey && key === 'c') {
                      sendToBackupTerminal('\x03');
                    } else if (e.ctrlKey && key === 'd') {
                      sendToBackupTerminal('\x04');
                    } else if (e.key.length === 1) {
                      sendToBackupTerminal(e.key);
                    }
                }
              });
            }

            // 发送resize通知到服务器
            notifyResize();
          } catch (error) {
            console.warn('新终端resize失败:', error);
          }
        }
      });

      message.success(`备用终端 ${newTerminalId} 创建成功`);
    } else {
      console.log('❌ 终端创建失败');
      console.log('失败原因:', response.error || '未知错误');
      message.error(response.error || '创建终端失败');
    }
  } catch (error) {
    console.error('=== handleCreateTerminalResponse 异常 ===');
    console.error('error:', error);
    console.error('处理创建终端响应失败:', error);
    message.error('创建终端失败: ' + error.message);
  }
};

const closeBackupTerminal = async (terminalId) => {
  try {
    console.log(`关闭备用终端 ${terminalId}`);

    // 调用后端API关闭终端
    await clientApi.closeBackupTerminal(props.clientId, terminalId);

    // 从备用终端列表中移除
    const index = backupTerminals.value.findIndex(t => t.id === terminalId);
    if (index !== -1) {
      backupTerminals.value.splice(index, 1);
    }

    // 清理终端实例
    const terminalInstance = terminals.value.get(terminalId);
    if (terminalInstance) {
      if (terminalInstance.terminal) {
        terminalInstance.terminal.dispose();
      }
      terminals.value.delete(terminalId);
    }

    // 如果关闭的是当前活跃终端，切换到主终端
    if (activeTerminalId.value === terminalId) {
      switchToTerminal(0);
    }

    message.success(`终端 ${terminalId} 已关闭`);
  } catch (error) {
    console.error('关闭备用终端失败:', error);
    message.error('关闭终端失败: ' + error.message);
  }
};

// 创建终端实例（用于恢复状态）
const createTerminalInstance = async (terminalId, createBackend = true) => {
  console.log(`🔧 创建终端实例 ${terminalId}，创建后端: ${createBackend}`);

  // 创建新的终端实例
  const newTerminal = new Terminal({
    cursorBlink: true,
    fontSize: 14,
    fontFamily: 'Consolas, "Courier New", monospace',
    theme: {
      background: '#1e1e1e',
      foreground: '#ffffff',
      cursor: '#ffffff',
      selection: '#3a3a3a'
    }
  });

  const newFitAddon = new FitAddon();
  newTerminal.loadAddon(newFitAddon);

  // 存储终端实例
  const terminalInstance = {
    terminal: newTerminal,
    fitAddon: newFitAddon,
    webSocket: null, // 备用终端不需要独立的WebSocket连接
    connected: true  // 通过主WebSocket连接
  };

  terminals.value.set(terminalId, terminalInstance);

  // 等待DOM更新后绑定键盘事件
  nextTick(() => {
    const container = document.getElementById(`xterm-container-${terminalId}`);
    if (container && terminalInstance.terminal) {
      // 挂载终端到DOM
      terminalInstance.terminal.open(container);
      terminalInstance.fitAddon.fit();

      // 绑定键盘事件
      terminalInstance.terminal.onKey(async (e) => {
        if (!webSocket.value || webSocket.value.readyState !== WebSocket.OPEN) {
          return;
        }
        const key = e.domEvent.key;
        console.log(`🎹 恢复终端${terminalId}按键:`, key);

        // 发送到指定终端
        const sendToTerminal = (data) => {
          const message = {
            terminal_id: terminalId,
            command: data,
            action: 'command'
          };
          webSocket.value.send(JSON.stringify(message));
        };

        // 处理按键
        switch (key) {
          case 'Enter': sendToTerminal('\r'); break;
          case 'Backspace': sendToTerminal('\x7F'); break;
          case 'ArrowUp': sendToTerminal('\x1B[A'); break;
          case 'ArrowDown': sendToTerminal('\x1B[B'); break;
          case 'ArrowRight': sendToTerminal('\x1B[C'); break;
          case 'ArrowLeft': sendToTerminal('\x1B[D'); break;
          case 'Tab': sendToTerminal('\t'); e.domEvent.preventDefault(); break;
          default:
            if (e.ctrlKey && key === 'c') sendToTerminal('\x03');
            else if (e.ctrlKey && key === 'd') sendToTerminal('\x04');
            else if (e.key.length === 1) sendToTerminal(e.key);
        }
      });

      console.log(`✅ 终端实例 ${terminalId} 创建完成`);
    }
  });
};

// 恢复终端状态
const restoreTerminalState = async () => {
  try {
    console.log('🔄 尝试恢复终端状态...');
    const response = await clientApi.getTerminalList(props.clientId);

    if (response && response.code === 200 && response.data && response.data.data && response.data.data.terminals) {
      const terminals = response.data.data.terminals;
      console.log('📋 获取到终端列表:', terminals);

      // 过滤出备用终端（ID != 0）
      const backupTerminalList = terminals.filter(t => t.id !== 0);

      if (backupTerminalList.length > 0) {
        console.log(`🔄 发现 ${backupTerminalList.length} 个备用终端，正在恢复...`);

        // 重建备用终端列表
        backupTerminals.value = backupTerminalList.map(t => ({
          id: t.id,
          active: t.active,
          created: new Date() // 使用当前时间作为创建时间
        }));

        // 为每个备用终端创建前端实例（但不重新创建后端终端）
        for (const terminalInfo of backupTerminalList) {
          await createTerminalInstance(terminalInfo.id, false); // false表示不创建后端终端
        }

        message.success(`恢复了 ${backupTerminalList.length} 个备用终端`);
      } else {
        console.log('✅ 没有发现备用终端');
      }
    }
  } catch (error) {
    console.error('❌ 恢复终端状态失败:', error);
    // 不显示错误消息，静默失败
  }
};

// 组件挂载时初始化
onMounted(async () => {
  // 首先尝试恢复终端状态
  await restoreTerminalState();

  if (terminalType.value === 'mock') {
    initMockTerminal();
  } else {
    nextTick(() => {
      initTerminal();

      // 延迟调整终端大小，确保DOM完全渲染
      setTimeout(() => {
        if (fitAddon.value && terminal.value) {
          fitAddon.value.fit();
          console.log('Terminal size after mount:', terminal.value.cols, 'x', terminal.value.rows);
        }
      }, 200);
    });
  }
});

// 组件卸载时清理
onBeforeUnmount(() => {
  try {
    if (webSocket.value) {
      webSocket.value.close();
    }
    if (mockWebSocket.value) {
      mockWebSocket.value.close();
    }
    if (terminal.value) {
      // 先清理插件引用（只有在插件成功加载时）
      if (pluginsLoaded.value && fitAddon.value) {
        fitAddon.value = null;
      }
      // 安全地dispose终端
      terminal.value.dispose();
      terminal.value = null;
      pluginsLoaded.value = false;
    }
  } catch (error) {
    console.warn('Terminal cleanup warning:', error);
  }
  window.removeEventListener('resize', handleResize);
});

// 获取操作系统图标
const getOSIcon = () => {
  switch (currentSessionOS.value) {
    case 'windows':
      return '🪟';
    case 'darwin':
      return '🍎';
    case 'linux':
      return '🐧';
    case 'unix':
      return '🖥️';
    default:
      return '💻';
  }
};

// 获取操作系统显示名称
const getOSDisplayName = () => {
  switch (currentSessionOS.value) {
    case 'windows':
      return 'Windows';
    case 'darwin':
      return 'macOS';
    case 'linux':
      return 'Linux';
    case 'unix':
      return 'Unix';
    default:
      return 'Unknown';
  }
};
</script>

<style scoped>
/* 现代化终端设计 */
.modern-terminal {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px); /* 使用视口高度减去页面其他元素的高度 */
  min-height: 600px; /* 最小高度确保可用性 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 终端头部 */
.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.terminal-tabs {
  display: flex;
  gap: 4px;
  background: rgba(0, 0, 0, 0.05);
  padding: 4px;
  border-radius: 12px;
}

/* 多终端标签容器 - 横向排布 */
.multi-terminal-tabs {
  display: flex;
  gap: 8px;
  align-items: center;
  background: rgba(0, 0, 0, 0.05);
  padding: 4px;
  border-radius: 12px;
  flex-wrap: nowrap; /* 不换行，保持横向 */
  max-width: 100%;
  overflow-x: auto; /* 横向滚动 */
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #666;
  user-select: none;
  white-space: nowrap; /* 防止文字换行 */
  min-width: 120px; /* 最小宽度 */
  max-width: 200px; /* 最大宽度 */
  flex-shrink: 1; /* 允许缩小 */
}

.tab:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #333;
}

.tab.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 16px;
}

/* 主终端标签样式 */
.main-tab {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.2), rgba(24, 144, 255, 0.1));
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.main-tab.active {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 主终端类型切换按钮 */
.main-terminal-type-switch {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.main-terminal-type-switch .ant-btn {
  height: 24px;
  padding: 0 8px;
  font-size: 11px;
  border-radius: 4px;
}

/* 主终端类型切换按钮 */
.main-terminal-type-switch {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.main-terminal-type-switch .ant-btn {
  height: 24px;
  padding: 0 8px;
  font-size: 11px;
  border-radius: 4px;
}

/* 备用终端标签样式 */
.backup-tab {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.2), rgba(82, 196, 26, 0.1));
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.backup-tab.active {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

/* 添加终端标签样式 */
.add-tab {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
  border: 1px dashed rgba(255, 193, 7, 0.5);
  color: #faad14;
}

.add-tab:hover {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.2));
  border-color: rgba(255, 193, 7, 0.7);
  color: #d48806;
}

/* 终端ID显示 */
.terminal-id {
  font-size: 10px;
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 4px;
}

/* 关闭按钮 */
.close-terminal-btn {
  margin-left: 8px;
  padding: 2px;
  border-radius: 4px;
  opacity: 0.6;
  transition: all 0.2s ease;
  min-width: 20px;
  height: 20px;
}

.close-terminal-btn:hover {
  opacity: 1;
  background: rgba(255, 77, 79, 0.2);
  color: #ff4d4f;
}

/* 备用终端容器 */
.backup-terminal {
  height: 100%;
  width: 100%;
}

.backup-xterm {
  height: 100%;
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 状态面板 */
.status-panel {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-icon {
  font-size: 14px;
}

.status-icon.status-connected {
  color: #52c41a;
  animation: pulse 2s infinite;
}

.status-icon.status-disconnected {
  color: #ff4d4f;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  color: #000000;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.connect-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3) !important;
}

.disconnect-btn {
  background: linear-gradient(135deg, #ff4d4f, #ff7875) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3) !important;
  color: #333;
}

/* 终端主体 */
.terminal-body {
  position: relative;
  flex: 1;
  min-height: 400px !important;
  height: calc(100% - 80px) !important; /* 减去头部高度 */
  background-color: #1e1e1e;
  display: flex;
  flex-direction: column;
  padding: 10px;
  padding-bottom: 0;
}

/* 模拟终端样式 */
.mock-terminal {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.terminal-screen {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.terminal-output {
  flex: 1;
  background: linear-gradient(135deg, #282c34 0%, #21252b 100%);
  color: #abb2bf;
  padding: 20px;
  overflow-y: auto;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
  position: relative;
  border: 1px solid #3e4451;
  border-radius: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.terminal-output::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(97, 175, 239, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(152, 195, 121, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.output-line {
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

.line-content {
  color: #abb2bf;
  font-weight: 400;
}

/* 终端输入区域 */
.terminal-input-area {
  padding: 16px 20px;
  background: linear-gradient(135deg, #2c313c 0%, #252a33 100%);
  border-top: 1px solid #3e4451;
  border-radius: 0 0 8px 8px;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(40, 44, 52, 0.8);
  border: 1px solid #4b5263;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.input-wrapper:focus-within {
  border-color: #61afef;
  box-shadow: 0 0 0 2px rgba(97, 175, 239, 0.2);
}

.prompt {
  color: #98c379;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-weight: 600;
  white-space: nowrap;
}

.command-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #abb2bf;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  padding: 8px 0;
}

.command-input::placeholder {
  color: rgba(171, 178, 191, 0.5);
}

.command-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-btn {
  background: linear-gradient(135deg, #61afef, #528bff);
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-weight: 500;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  box-shadow: 0 2px 8px rgba(97, 175, 239, 0.3);
}

.send-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #528bff, #4078f2);
  box-shadow: 0 4px 12px rgba(97, 175, 239, 0.4);
  transform: translateY(-1px);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 主终端容器 */
.main-terminal {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 交互式终端样式 */
.interactive-terminal {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
  min-height: 400px !important;
}

.xterm-wrapper {
  flex-grow: 1;
  background-color: #1a1a1a;
  border-radius: 4px;
  overflow: auto;
  height: 100% !important;
  min-height: 400px !important;
  width: 100% !important;
}

/* 强制xterm.js占用全部空间 */
.xterm-wrapper .xterm {
  height: 100% !important;
  width: 100% !important;
}

.xterm-wrapper .xterm .xterm-viewport {
  height: 100% !important;
  width: 100% !important;
}

.xterm-wrapper .xterm .xterm-screen {
  height: 100% !important;
  width: 100% !important;
}

/* 交互式终端滚动条样式 */
.xterm-wrapper ::-webkit-scrollbar {
  width: 8px;
}

.xterm-wrapper ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.xterm-wrapper ::-webkit-scrollbar-thumb {
  background: rgba(100, 149, 237, 0.6);
  border-radius: 4px;
}

.xterm-wrapper ::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 149, 237, 0.8);
}

/* 滚动条样式 */
.terminal-output::-webkit-scrollbar {
  width: 8px;
}

.terminal-output::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.terminal-output::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 136, 0.3);
  border-radius: 4px;
}

.terminal-output::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 136, 0.5);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .status-panel {
    display: none;
  }
  
  .terminal-header {
    padding: 12px 16px;
  }
}

@media (max-width: 768px) {
  .modern-terminal {
    height: calc(100vh - 80px); /* 移动端减少更少的高度 */
    min-height: 500px;
    margin: 8px;
    border-radius: 12px;
  }
  
  .terminal-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-right {
    justify-content: center;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .terminal-body {
    margin: 8px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.output-line {
  animation: fadeIn 0.3s ease;
}

/* 终端启动动画 */
@keyframes terminalBoot {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.terminal-screen {
  animation: terminalBoot 0.5s ease;
}

/* 底部连接状态区域 */
.terminal-footer {
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.connection-status-area {
  width: 100%;
}

.disconnected-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 120, 117, 0.1));
  border: 1px solid rgba(255, 77, 79, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.disconnected-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.disconnected-panel:hover {
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.2), rgba(255, 120, 117, 0.2));
  border-color: rgba(255, 77, 79, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 77, 79, 0.2);
}

.disconnected-panel:hover::before {
  left: 100%;
}

.connected-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.1));
  border: 1px solid rgba(82, 196, 26, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
}

.status-dot.connected {
  background: #52c41a;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
  animation: connectedPulse 2s infinite;
}

.status-dot.disconnected {
  background: #ff4d4f;
  box-shadow: 0 0 10px rgba(255, 77, 79, 0.5);
  animation: disconnectedBlink 1.5s infinite;
}

@keyframes connectedPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes disconnectedBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.reconnect-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.hint-text {
  font-size: 12px;
  font-style: italic;
}

.connect-icon {
  font-size: 18px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.session-info {
  color: rgba(255, 255, 255, 0.7);
}

.session-text {
  font-size: 12px;
  font-style: italic;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .disconnected-panel,
  .connected-panel {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .reconnect-hint {
    justify-content: center;
  }
}
</style>