//go:build linux && 386

package main

import "syscall"

// Register access functions for i386 architecture on Linux

func getRIP(regs *syscall.PtraceRegs) uint64 {
return uint64(regs.Eip)  // EIP on i386
}

func setRIP(regs *syscall.PtraceRegs, addr uint64) {
regs.Eip = int32(addr)
}

func getRSP(regs *syscall.PtraceRegs) uint64 {
return uint64(regs.Esp)  // ESP on i386
}

func setRSP(regs *syscall.PtraceRegs, addr uint64) {
regs.Esp = int32(addr)
}
