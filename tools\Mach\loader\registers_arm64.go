//go:build darwin && arm64

package main

import (
	"unsafe"
)

// ARM64 register structure for macOS (different from Linux)
type PtraceRegsArm64Darwin struct {
	X    [29]uint64 // General purpose registers x0-x28
	Fp   uint64     // Frame pointer (x29)
	Lr   uint64     // Link register (x30)
	Sp   uint64     // Stack pointer
	Pc   uint64     // Program counter
	Cpsr uint64     // Current program status register
}

// Register access functions for ARM64 architecture on macOS

func getRIP(regs interface{}) uint64 {
	// Convert to ARM64 specific structure for macOS
	arm64Regs := (*PtraceRegsArm64Darwin)(unsafe.Pointer(&regs))
	return arm64Regs.Pc
}

func setRIP(regs interface{}, addr uint64) {
	// Convert to ARM64 specific structure for macOS
	arm64Regs := (*PtraceRegsArm64Darwin)(unsafe.Pointer(&regs))
	arm64Regs.Pc = addr
}

func getRSP(regs interface{}) uint64 {
	// Convert to ARM64 specific structure for macOS
	arm64Regs := (*PtraceRegsArm64Darwin)(unsafe.Pointer(&regs))
	return arm64Regs.Sp
}

func setRSP(regs interface{}, addr uint64) {
	// Convert to ARM64 specific structure for macOS
	arm64Regs := (*PtraceRegsArm64Darwin)(unsafe.Pointer(&regs))
	arm64Regs.Sp = addr
}
