#!/bin/bash

# Cross-compilation script for ELF and Mach-O tools
# Builds all platforms and architectures with maximum obfuscation and compression

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Build parameters
LDFLAGS="-w -s"
GCFLAGS="all=-trimpath=${PWD}"
ASMFLAGS="all=-trimpath=${PWD}"
GARBLE_FLAGS="-literals -tiny -seed=random"

# Output directory
BIN_DIR="tools/bin"

echo -e "${BLUE}🚀 Starting cross-compilation build process...${NC}"

# Create bin directory
mkdir -p "${BIN_DIR}"

# Check if garble is installed
GARBLE_AVAILABLE=false
if command -v garble &> /dev/null; then
    # Test if garble works with current Go version
    if garble version &> /dev/null; then
        GARBLE_AVAILABLE=true
        echo -e "${GREEN}✅ garble found and working${NC}"
    else
        echo -e "${YELLOW}⚠️  garble found but incompatible with current Go version${NC}"
        echo -e "${YELLOW}⚠️  Falling back to standard go build${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  garble not found, using standard go build${NC}"
fi

# Check if upx is installed
UPX_AVAILABLE=false
if command -v upx &> /dev/null; then
    UPX_AVAILABLE=true
    echo -e "${GREEN}✅ UPX found, will compress binaries${NC}"
else
    echo -e "${YELLOW}⚠️  UPX not found, skipping compression${NC}"
fi

# Function to build with garble or standard go build
build_binary() {
    local goos=$1
    local goarch=$2
    local source_dir=$3
    local output_name=$4
    local build_tags=$5

    echo -e "${YELLOW}🔨 Building ${output_name} for ${goos}/${goarch}...${NC}"

    cd "${source_dir}"

    if [ "${GARBLE_AVAILABLE}" = true ]; then
        # Try garble for obfuscation, fallback to go build if it fails
        if [ -n "${build_tags}" ]; then
            if ! GOOS=${goos} GOARCH=${goarch} garble ${GARBLE_FLAGS} build \
                -tags="${build_tags}" \
                -ldflags="${LDFLAGS}" \
                -gcflags="${GCFLAGS}" \
                -asmflags="${ASMFLAGS}" \
                -o "../../../${BIN_DIR}/${output_name}" . 2>/dev/null; then
                echo -e "${YELLOW}⚠️  garble failed, falling back to go build${NC}"
                GOOS=${goos} GOARCH=${goarch} go build \
                    -tags="${build_tags}" \
                    -ldflags="${LDFLAGS}" \
                    -gcflags="${GCFLAGS}" \
                    -asmflags="${ASMFLAGS}" \
                    -o "../../../${BIN_DIR}/${output_name}" .
            fi
        else
            if ! GOOS=${goos} GOARCH=${goarch} garble ${GARBLE_FLAGS} build \
                -ldflags="${LDFLAGS}" \
                -gcflags="${GCFLAGS}" \
                -asmflags="${ASMFLAGS}" \
                -o "../../../${BIN_DIR}/${output_name}" . 2>/dev/null; then
                echo -e "${YELLOW}⚠️  garble failed, falling back to go build${NC}"
                GOOS=${goos} GOARCH=${goarch} go build \
                    -ldflags="${LDFLAGS}" \
                    -gcflags="${GCFLAGS}" \
                    -asmflags="${ASMFLAGS}" \
                    -o "../../../${BIN_DIR}/${output_name}" .
            fi
        fi
    else
        # Use standard go build
        if [ -n "${build_tags}" ]; then
            GOOS=${goos} GOARCH=${goarch} go build \
                -tags="${build_tags}" \
                -ldflags="${LDFLAGS}" \
                -gcflags="${GCFLAGS}" \
                -asmflags="${ASMFLAGS}" \
                -o "../../../${BIN_DIR}/${output_name}" .
        else
            GOOS=${goos} GOARCH=${goarch} go build \
                -ldflags="${LDFLAGS}" \
                -gcflags="${GCFLAGS}" \
                -asmflags="${ASMFLAGS}" \
                -o "../../../${BIN_DIR}/${output_name}" .
        fi
    fi

    cd - > /dev/null

    if [ -f "${BIN_DIR}/${output_name}" ]; then
        echo -e "${GREEN}✅ Successfully built ${output_name}${NC}"
    else
        echo -e "${RED}❌ Failed to build ${output_name}${NC}"
        return 1
    fi
}

# Function to compress with UPX
compress_with_upx() {
    local file_path=$1
    local goos=$2
    
    if [ "${UPX_AVAILABLE}" = true ] && [ "${goos}" != "darwin" ]; then
        echo -e "${YELLOW}🗜️  Compressing ${file_path}...${NC}"
        upx --best --lzma "${file_path}" 2>/dev/null || {
            echo -e "${YELLOW}⚠️  UPX compression failed for ${file_path}, continuing...${NC}"
        }
    fi
}

echo -e "${BLUE}📦 Building ELF Converters...${NC}"

# ELF Converter builds
build_binary "linux" "amd64" "tools/ELF/converter" "elf2shellcode-linux-amd64"
compress_with_upx "${BIN_DIR}/elf2shellcode-linux-amd64" "linux"

build_binary "linux" "arm64" "tools/ELF/converter" "elf2shellcode-linux-arm64"
compress_with_upx "${BIN_DIR}/elf2shellcode-linux-arm64" "linux"

build_binary "linux" "386" "tools/ELF/converter" "elf2shellcode-linux-386"
compress_with_upx "${BIN_DIR}/elf2shellcode-linux-386" "linux"

build_binary "linux" "arm" "tools/ELF/converter" "elf2shellcode-linux-arm"
compress_with_upx "${BIN_DIR}/elf2shellcode-linux-arm" "linux"

build_binary "windows" "amd64" "tools/ELF/converter" "elf2shellcode-windows-amd64.exe"
compress_with_upx "${BIN_DIR}/elf2shellcode-windows-amd64.exe" "windows"

build_binary "windows" "386" "tools/ELF/converter" "elf2shellcode-windows-386.exe"
compress_with_upx "${BIN_DIR}/elf2shellcode-windows-386.exe" "windows"

build_binary "darwin" "amd64" "tools/ELF/converter" "elf2shellcode-darwin-amd64"

build_binary "darwin" "arm64" "tools/ELF/converter" "elf2shellcode-darwin-arm64"

echo -e "${BLUE}📦 Building Mach-O Converters...${NC}"

# Mach-O Converter builds
build_binary "darwin" "amd64" "tools/Mach/converter" "macho2shellcode-darwin-amd64"

build_binary "darwin" "arm64" "tools/Mach/converter" "macho2shellcode-darwin-arm64"

build_binary "linux" "amd64" "tools/Mach/converter" "macho2shellcode-linux-amd64"
compress_with_upx "${BIN_DIR}/macho2shellcode-linux-amd64" "linux"

build_binary "linux" "arm64" "tools/Mach/converter" "macho2shellcode-linux-arm64"
compress_with_upx "${BIN_DIR}/macho2shellcode-linux-arm64" "linux"

build_binary "windows" "amd64" "tools/Mach/converter" "macho2shellcode-windows-amd64.exe"
compress_with_upx "${BIN_DIR}/macho2shellcode-windows-amd64.exe" "windows"

build_binary "windows" "386" "tools/Mach/converter" "macho2shellcode-windows-386.exe"
compress_with_upx "${BIN_DIR}/macho2shellcode-windows-386.exe" "windows"

echo -e "${BLUE}📦 Building ELF Loaders (Linux only)...${NC}"

# ELF Loader builds (Linux only)
build_binary "linux" "amd64" "tools/ELF/loader" "elf-loader-linux-amd64"
compress_with_upx "${BIN_DIR}/elf-loader-linux-amd64" "linux"

build_binary "linux" "arm64" "tools/ELF/loader" "elf-loader-linux-arm64"
compress_with_upx "${BIN_DIR}/elf-loader-linux-arm64" "linux"

build_binary "linux" "386" "tools/ELF/loader" "elf-loader-linux-386"
compress_with_upx "${BIN_DIR}/elf-loader-linux-386" "linux"

build_binary "linux" "arm" "tools/ELF/loader" "elf-loader-linux-arm"
compress_with_upx "${BIN_DIR}/elf-loader-linux-arm" "linux"

echo -e "${BLUE}📦 Building Mach-O Loaders (Darwin only)...${NC}"

# Mach-O Loader builds (Darwin only)
build_binary "darwin" "amd64" "tools/Mach/loader" "macho-loader-darwin-amd64"

build_binary "darwin" "arm64" "tools/Mach/loader" "macho-loader-darwin-arm64"

echo -e "${GREEN}🎉 Build process completed!${NC}"
echo -e "${BLUE}📊 Build Summary:${NC}"

# Count and display built files
total_files=$(find "${BIN_DIR}" -type f | wc -l)
echo -e "${GREEN}✅ Total binaries built: ${total_files}${NC}"

echo -e "${BLUE}📁 Built files in ${BIN_DIR}/:${NC}"
ls -la "${BIN_DIR}/" | grep -v "^total" | while read -r line; do
    echo -e "${GREEN}  ${line}${NC}"
done

# Calculate total size
total_size=$(du -sh "${BIN_DIR}" | cut -f1)
echo -e "${BLUE}💾 Total size: ${total_size}${NC}"

echo -e "${GREEN}🚀 All builds completed successfully!${NC}"
echo -e "${YELLOW}💡 Usage examples:${NC}"
echo -e "  ${GREEN}ELF Converter:${NC} ./${BIN_DIR}/elf2shellcode-linux-amd64 -i input.elf -o output.bin"
echo -e "  ${GREEN}Mach-O Converter:${NC} ./${BIN_DIR}/macho2shellcode-darwin-arm64 -i input.macho -o output.bin"
echo -e "  ${GREEN}ELF Loader:${NC} ./${BIN_DIR}/elf-loader-linux-amd64 -f shellcode.bin"
echo -e "  ${GREEN}Mach-O Loader:${NC} ./${BIN_DIR}/macho-loader-darwin-arm64 -f shellcode.bin"
