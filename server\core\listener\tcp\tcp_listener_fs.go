package tcp

import (
	"fmt"
	"io"
	"os"
	"server/core/manager/cache"
	"server/core/manager/downloader"
	"server/core/manager/filetransferpool"
	"server/core/manager/uploader"
	"server/global"
	"server/model/request/fs"
	fs2 "server/model/response/fs"
	"server/model/tlv"
	"server/utils"
	"strings"
	"time"

	"github.com/minio/sha256-simd"

	"go.uber.org/zap"
)

// handleFilePacket 处理文件数据包
func (l *TCPListener) handleFilePacket(remoteAddr string, packet *tlv.Packet) error {
	//global.LOG.Info("收到文件数据包", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))

	// 根据Code字段判断文件操作类型
	switch packet.Header.Code {
	case tlv.FileInfo:
		// 处理文件信息响应
		return l.handleFileOperationResponse(remoteAddr, packet, "info_file")
	case tlv.FileCopy:
		// 处理文件复制响应
		return l.handleFileOperationResponse(remoteAddr, packet, "copy_file")
	case tlv.FileDelete:
		// 处理文件删除响应
		return l.handleFileOperationResponse(remoteAddr, packet, "delete_file")
	case tlv.FileMove:
		// 处理文件移动响应
		return l.handleFileOperationResponse(remoteAddr, packet, "move_file")
	case tlv.FileUpload:
		// 处理文件上传响应
		return l.handleFileTransferResponse(remoteAddr, packet, "upload_file")
	case tlv.FileDownload:
		// 处理文件下载响应
		return l.handleFileTransferResponse(remoteAddr, packet, "download_file")
	case tlv.FileRead:
		// 处理文件内容读取响应
		return l.handleFileOperationResponse(remoteAddr, packet, "read_file")
	case tlv.FileWrite:
		// 处理文件内容写入响应
		return l.handleFileOperationResponse(remoteAddr, packet, "write_file")
	case tlv.FileCreate:
		// 处理文件创建响应
		return l.handleFileOperationResponse(remoteAddr, packet, "create_file")
	default:
		global.LOG.Warn("未知的文件操作类型", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))
		return nil
	}
}

// handleDirPacket 处理目录数据包
func (l *TCPListener) handleDirPacket(remoteAddr string, packet *tlv.Packet) error {
	global.LOG.Info("收到目录数据包", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))

	// 根据Code字段判断目录操作类型
	switch packet.Header.Code {
	case tlv.DirCreate:
		// 处理目录创建响应
		return l.handleDirOperationResponse(remoteAddr, packet, "create_dir")
	case tlv.DirList:
		// 处理目录列表响应
		return l.handleDirOperationResponse(remoteAddr, packet, "list_dir")
	case tlv.DirMove:
		// 处理目录移动响应
		return l.handleDirOperationResponse(remoteAddr, packet, "move_dir")
	case tlv.DirDelete:
		// 处理目录删除响应
		return l.handleDirOperationResponse(remoteAddr, packet, "delete_dir")
	case tlv.DirCopy:
		// 处理目录复制响应
		return l.handleDirOperationResponse(remoteAddr, packet, "copy_dir")
	case tlv.DiskList:
		// 处理磁盘列表响应
		return l.handleDirOperationResponse(remoteAddr, packet, "list_disk")
	default:
		global.LOG.Warn("未知的目录操作类型", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))
		return nil
	}
}

// handleFileOperationResponse 处理文件操作响应
func (l *TCPListener) handleFileOperationResponse(remoteAddr string, packet *tlv.Packet, operation string) error {
	switch operation {
	case "info_file":
		// 反序列化文件信息响应
		var response fs2.FileInfoResponse

		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件信息响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "info_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端文件信息操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("path", response.Path),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("文件信息响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.String("path", response.Path),
				zap.Bool("exist", response.Exist),
				zap.String("name", response.Name),
				zap.Int64("size", response.Size),
				zap.Bool("isDir", response.IsDir))
		}

	case "copy_file":
		// 处理文件复制响应
		var response fs2.FileCopyResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件复制响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("文件复制响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("source", response.ActualSource),
			zap.String("destination", response.ActualDestination),
			zap.Int64("bytesCopied", response.BytesCopied),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "copy_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "delete_file":
		// 处理文件删除响应
		var response fs2.FileDeleteResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件删除响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("文件删除响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("path", response.ActualPath),
			zap.Int("deletedCount", response.DeletedCount),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "delete_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}
	case "move_file":
		// 处理文件移动响应
		var response fs2.FileMoveResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件移动响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("文件移动响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("source", response.ActualSource),
			zap.String("destination", response.ActualDestination),
			zap.Int64("bytesMoved", response.BytesMoved),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "move_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "read_file":
		// 处理文件内容读取响应
		var response fs2.FileReadResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件读取响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("文件读取响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("path", response.ActualPath),
			zap.Int64("fileSize", response.FileSize),
			zap.Int64("readSize", response.ReadSize),
			zap.Bool("isBinary", response.IsBinary),
			zap.Bool("tooLarge", response.TooLarge),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "read_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "write_file":
		// 处理文件内容写入响应
		var response fs2.FileWriteResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件写入响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("文件写入响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("path", response.ActualPath),
			zap.Int64("bytesWritten", response.BytesWritten),
			zap.String("backupPath", response.BackupPath),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "write_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "create_file":
		// 处理文件创建响应
		var response fs2.FileCreateResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件创建响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("文件创建响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("actualPath", response.ActualPath),
			zap.String("createdPath", response.CreatedPath),
			zap.Int64("bytesWritten", response.BytesWritten),
			zap.Bool("notAllow", response.NotAllow),
			zap.Bool("exists", response.Exists),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "create_file", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	default:
		global.LOG.Warn("未知的文件操作类型", zap.String("remoteAddr", remoteAddr), zap.String("operation", operation))
	}

	return nil
}

func (l *TCPListener) processUploadResponse(remoteAddr string, resp *fs2.FileUploadResponse) {
	// --- 步骤 1: 获取任务状态和基本信息 ---
	state, exists := uploader.UploadManager.GetTask(resp.TaskID)
	if !exists {
		global.LOG.Warn("上传任务状态不存在，可能已被取消或完成", zap.Uint64("taskID", resp.TaskID))
		return
	}

	// 将不变量读取到局部变量
	taskID := state.TaskID
	destPath := state.DestPath
	chunkSize := state.ChunkSize

	// --- 步骤 2: 处理 Agent 发来的错误响应 ---
	if !resp.Success || resp.Error != "" {
		global.LOG.Error("Agent报告上传失败",
			zap.Uint64("taskID", taskID),
			zap.String("error", resp.Error))

		// 执行清理并发送取消信令
		l.cleanupUploadTask(remoteAddr, state, "failed", resp.Error)
		return
	}

	// --- 步骤 3: 更新共享状态和DB ---
	state.Mutex.Lock()
	state.Transferred = resp.ReceivedSize
	state.NextChunk = resp.CurrentChunk + 1
	state.LastUpdated = time.Now()
	if state.TotalChunks == 0 {
		state.TotalChunks = resp.TotalChunk
	}

	// 读取需要用于后续逻辑的变量
	nextChunkToSend := state.NextChunk
	isCompleted := resp.Completed || state.NextChunk >= state.TotalChunks
	transferredBytes := state.Transferred
	fileSize := state.FileSize
	state.Mutex.Unlock()

	// DB操作在锁外执行
	if fileSize > 0 {
		progress := float32(transferredBytes) / float32(fileSize) * 100
		utils.UpdateTaskProgressWithFileSize(taskID, int(progress), transferredBytes, fileSize)
	}

	// --- 步骤 4: 检查是否完成 ---
	if isCompleted {
		global.LOG.Info("文件上传完成",
			zap.Uint64("taskID", taskID),
			zap.String("path", destPath))

		// 任务完成，执行最终清理
		l.cleanupUploadTask(remoteAddr, state, "completed", "")

		// 存储最终响应结果
		if resp.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(resp.TaskID, "upload_file", resp, "")
		} else {
			global.LOG.Warn("响应中缺少TaskID以存储最终响应", zap.String("remoteAddr", remoteAddr))
		}
		return
	}

	// --- 步骤 5: 发送下一个分块数据 ---
	if state.File != nil {
		// 使用文件传输内存池读取下一个分块
		buf := filetransferpool.GetChunkBuffer(int(chunkSize))
		defer filetransferpool.PutChunkBuffer(buf)

		n, err := state.File.Read(buf)
		if err != nil && err != io.EOF {
			global.LOG.Error("读取上传文件失败", zap.Uint64("taskID", taskID), zap.Error(err))
			l.cleanupUploadTask(remoteAddr, state, "failed", "读取文件失败")
			return
		}

		// 记录传输字节数
		filetransferpool.AddTransferredBytes(int64(n))

		// 构建下一个分块请求 - 创建数据副本以避免内存池冲突
		chunkData := make([]byte, n)
		copy(chunkData, buf[:n])

		nextReq := fs.FileUploadRequest{
			TaskID:       taskID,
			Destination:  destPath,
			StartChunk:   nextChunkToSend,
			CurrentChunk: nextChunkToSend,
			TotalChunk:   state.TotalChunks,
			ChunkContent: chunkData,
			Force:        true,
			FileSize:     fileSize,
			FileHash:     state.FileHash,
		}

		reqBytes, err := utils.SerializerManager.Serialize(nextReq)
		if err != nil {
			global.LOG.Error("序列化下一个分块请求失败", zap.Uint64("taskID", taskID), zap.Error(err))
			l.cleanupUploadTask(remoteAddr, state, "failed", "序列化失败")
			return
		}

		nextPacket := &tlv.Packet{
			Header: &tlv.Header{
				Type: tlv.File,
				Code: tlv.FileUpload,
			},
			PacketData: &tlv.PacketData{
				Data: reqBytes,
			},
		}

		if err = l.SendPacket(remoteAddr, nextPacket); err != nil {
			global.LOG.Error("发送下一个分块请求失败",
				zap.Uint64("taskID", taskID),
				zap.Error(err))
			l.cleanupUploadTask(remoteAddr, state, "failed", "网络发送失败")
		}
	}
}

func (l *TCPListener) cleanupUploadTask(remoteAddr string, state *uploader.UploadTaskState, status string, errorMsg string) {
	// 确保文件被关闭
	if state.File != nil {
		state.File.Close()
	}

	// 发送结束信令给Agent
	stopReq := fs.FileUploadRequest{
		TaskID:     state.TaskID,
		StartChunk: -1, // -1 作为结束/取消信令
	}
	reqBytes, err := utils.SerializerManager.Serialize(stopReq)
	if err == nil {
		stopPacket := &tlv.Packet{
			Header:     &tlv.Header{Type: tlv.File, Code: tlv.FileUpload},
			PacketData: &tlv.PacketData{Data: reqBytes},
		}
		if err := l.SendPacket(remoteAddr, stopPacket); err != nil {
			global.LOG.Warn("发送上传结束信令失败", zap.Uint64("taskID", state.TaskID), zap.Error(err))
		}
	}

	// 更新数据库状态
	utils.UpdateTaskStatus(state.TaskID, status, errorMsg)

	// 从管理器中移除任务
	uploader.UploadManager.RemoveTask(state.TaskID)

	global.LOG.Info("上传任务已清理",
		zap.Uint64("taskID", state.TaskID),
		zap.String("status", status),
		zap.String("reason", errorMsg))
}

// handleFileTransferResponse 处理文件传输响应
func (l *TCPListener) handleFileTransferResponse(remoteAddr string, packet *tlv.Packet, transferType string) error {
	switch transferType {
	case "upload_file":
		// 处理文件上传响应
		var response fs2.FileUploadResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件上传响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}
		go l.processUploadResponse(remoteAddr, &response)
	case "download_file":
		// 处理文件下载响应
		var response fs2.FileDownloadResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化文件下载响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}
		go l.processDownloadResponse(remoteAddr, &response)
	default:
		global.LOG.Warn("未知的文件传输类型", zap.String("remoteAddr", remoteAddr), zap.String("transferType", transferType))
	}

	return nil
}

func (l *TCPListener) processDownloadResponse(remoteAddr string, resp *fs2.FileDownloadResponse) {
	// --- 步骤 1: 获取任务状态和基本信息 (无锁或短时读锁) ---
	state, exists := downloader.DownloadManager.GetTask(resp.TaskID)
	if !exists {
		global.LOG.Warn("下载任务状态不存在，可能已被取消或完成", zap.Uint64("taskID", resp.TaskID))
		return
	}

	// 将不变量读取到局部变量，减少后续加锁
	taskID := state.TaskID
	filePath := state.Path
	chunkSize := state.ChunkSize
	destPath := state.DestPath

	// --- 步骤 2: 处理 Agent 发来的错误响应 (短时写锁) ---
	if !resp.Success || resp.Error != "" {
		global.LOG.Error("Agent报告下载失败",
			zap.Uint64("taskID", taskID),
			zap.String("error", resp.Error))

		// 执行清理并发送取消信令
		l.cleanupDownloadTask(remoteAddr, state, "failed", resp.Error)
		return
	}

	// --- 步骤 3: 写入文件块 (耗时IO，无锁) ---
	// 注意：os.File.Write 本身不是线程安全的。但由于我们是串行处理每个块的响应，
	// 所以同一个任务的goroutine不会并发调用Write。这里可以不加锁。
	bytesWritten, err := state.File.Write(resp.CurrentChunkContent)
	if err != nil {
		global.LOG.Error("写入文件失败",
			zap.Uint64("taskID", taskID),
			zap.String("path", destPath),
			zap.Error(err))

		// 写入失败，清理任务
		l.cleanupDownloadTask(remoteAddr, state, "failed", err.Error())
		return
	}

	// 记录传输字节数到文件传输池统计
	filetransferpool.AddTransferredBytes(int64(bytesWritten))

	// --- 步骤 4: 更新共享状态和DB (短时写锁) ---
	state.Mutex.Lock()
	state.Transferred += int64(len(resp.CurrentChunkContent))
	state.NextChunk = resp.CurrentChunk + 1
	state.LastUpdated = time.Now()
	if state.TotalChunks == 0 {
		state.TotalChunks = resp.TotalChunk
		state.FileSize = resp.FileSize
	}

	// 读取需要用于后续逻辑的变量
	nextChunkToSend := state.NextChunk
	isCompleted := resp.Completed || state.NextChunk >= state.TotalChunks
	transferredBytes := state.Transferred
	fileSize := state.FileSize
	state.Mutex.Unlock() // <--- 立刻释放锁！

	// DB操作在锁外执行
	if fileSize > 0 {
		progress := float32(transferredBytes) / float32(fileSize) * 100
		utils.UpdateTaskProgressWithFileSize(taskID, int(progress), transferredBytes, fileSize)
	}

	// --- 步骤 5: 检查是否完成 ---
	if isCompleted {
		// 文件下载完成，进行完整性校验
		if resp.FileHash != "" {
			if !l.verifyDownloadedFileIntegrity(destPath, resp.FileHash) {
				global.LOG.Error("下载文件完整性校验失败",
					zap.Uint64("taskID", taskID),
					zap.String("path", destPath),
					zap.String("expectedHash", resp.FileHash))
				// 删除损坏的文件
				os.Remove(destPath)
				l.cleanupDownloadTask(remoteAddr, state, "failed", "文件完整性校验失败")
				return
			}
			global.LOG.Info("文件完整性校验通过",
				zap.Uint64("taskID", taskID),
				zap.String("path", destPath),
				zap.String("fileHash", resp.FileHash))
		} else {
			global.LOG.Warn("客户端未提供文件哈希值，跳过完整性校验",
				zap.Uint64("taskID", taskID),
				zap.String("path", destPath))
		}

		global.LOG.Info("文件下载完成",
			zap.Uint64("taskID", taskID),
			zap.String("path", destPath))

		// 任务完成，执行最终清理
		l.cleanupDownloadTask(remoteAddr, state, "completed", "")

		// (可选) 存储最终响应结果，用于其他需要同步等待结果的API
		cache.ResponseMgr.StoreResponse(resp.TaskID, "download_file", resp, "")
		return
	}

	// --- 步骤 6: 发送下一个分块请求 (网络IO，无锁) ---
	nextReq := fs.FileDownloadRequest{
		TaskID:     taskID,
		Path:       filePath,
		StartChunk: nextChunkToSend,
		ChunkSize:  chunkSize,
	}

	reqBytes, err := utils.SerializerManager.Serialize(nextReq)
	if err != nil {
		global.LOG.Error("序列化下一个分块请求失败", zap.Uint64("taskID", taskID), zap.Error(err))
		// 序列化失败，也应终止任务
		l.cleanupDownloadTask(remoteAddr, state, "failed", "序列化失败")
		return
	}

	nextPacket := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.File,
			Code: tlv.FileDownload,
		},
		PacketData: &tlv.PacketData{
			Data: reqBytes,
		},
	}

	if err = l.SendPacket(remoteAddr, nextPacket); err != nil {
		global.LOG.Error("发送下一个分块请求失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		// 发送失败，也应终止任务
		l.cleanupDownloadTask(remoteAddr, state, "failed", "网络发送失败")
	}
}

// 新增辅助函数：cleanupDownloadTask，用于统一处理任务的终止和清理
// 这样可以避免在多个地方重复写清理逻辑
// verifyDownloadedFileIntegrity 验证下载文件的完整性
func (l *TCPListener) verifyDownloadedFileIntegrity(filePath, expectedHash string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		global.LOG.Error("打开文件进行完整性校验失败", zap.String("path", filePath), zap.Error(err))
		return false
	}
	defer file.Close()

	hash := sha256.New()
	if _, err = io.Copy(hash, file); err != nil {
		global.LOG.Error("计算文件哈希失败", zap.String("path", filePath), zap.Error(err))
		return false
	}

	actualHash := fmt.Sprintf("%x", hash.Sum(nil))
	isValid := strings.EqualFold(actualHash, expectedHash)

	if !isValid {
		global.LOG.Error("文件哈希不匹配",
			zap.String("path", filePath),
			zap.String("expected", expectedHash),
			zap.String("actual", actualHash))
	}

	return isValid
}

func (l *TCPListener) cleanupDownloadTask(remoteAddr string, state *downloader.DownloadTaskState, status string, errorMsg string) {
	// 确保文件被关闭
	state.File.Close()

	// 发送结束信令给Agent，让其停止重试等活动
	// 即使是Agent报告错误导致的中止，也最好再发一个确认中止的包，保证状态同步
	stopReq := fs.FileDownloadRequest{
		TaskID:     state.TaskID,
		Path:       state.Path,
		StartChunk: -1, // -1 作为结束/取消信令
		ChunkSize:  state.ChunkSize,
	}
	reqBytes, err := utils.SerializerManager.Serialize(stopReq)
	if err == nil {
		stopPacket := &tlv.Packet{
			Header:     &tlv.Header{Type: tlv.File, Code: tlv.FileDownload},
			PacketData: &tlv.PacketData{Data: reqBytes},
		}
		// 发送失败也只是记录日志，不影响Server端的清理
		if err := l.SendPacket(remoteAddr, stopPacket); err != nil {
			global.LOG.Warn("发送下载结束信令失败", zap.Uint64("taskID", state.TaskID), zap.Error(err))
		}
	}

	// 更新数据库状态
	utils.UpdateTaskStatus(state.TaskID, status, errorMsg)

	// 从管理器中移除任务
	downloader.DownloadManager.RemoveTask(state.TaskID)

	global.LOG.Info("下载任务已清理",
		zap.Uint64("taskID", state.TaskID),
		zap.String("status", status),
		zap.String("reason", errorMsg))
}

// handleDirOperationResponse 处理目录操作响应
func (l *TCPListener) handleDirOperationResponse(remoteAddr string, packet *tlv.Packet, operation string) error {
	switch operation {
	case "list_disk":
		// 反序列化磁盘列表响应
		var response fs2.DiskListResponse

		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化磁盘列表响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "list_disk", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端磁盘列表操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("磁盘列表响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Bool("success", response.Success),
				zap.Int("diskCount", len(response.DiskInfos)))

			// 记录磁盘信息
			for _, diskInfo := range response.DiskInfos {
				global.LOG.Info("磁盘信息",
					zap.String("remoteAddr", remoteAddr),
					zap.String("mountPoint", diskInfo.MountPoint),
					zap.String("fileSystem", diskInfo.FileSystem),
					zap.String("label", diskInfo.Label),
					zap.Int64("totalSize", diskInfo.TotalSize),
					zap.Float64("usagePercent", diskInfo.UsagePercent),
					zap.String("icon", diskInfo.Icon))
			}
		}
	case "list_dir":
		// 反序列化目录列表响应
		var response fs2.DirListResponse

		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化目录列表响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "list_dir", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端目录列表操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("path", response.Path),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("目录列表响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.String("path", response.Path),
				zap.String("actualPath", response.ActualPath),
				zap.Int("totalCount", response.TotalCount),
				zap.Int("fileCount", len(response.FileInfoResponses)))

			// 记录目录中的文件信息
			for i, fileInfo := range response.FileInfoResponses {
				if i < 10 { // 只记录前10个文件的详细信息，避免日志过多
					global.LOG.Info("目录文件信息",
						zap.String("remoteAddr", remoteAddr),
						zap.String("fileName", fileInfo.Name),
						zap.Int64("size", fileInfo.Size),
						zap.Bool("isDir", fileInfo.IsDir))
				}
			}
		}
	case "create_dir":
		// 处理目录创建响应
		var response fs2.DirCreateResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化目录创建响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "create_dir", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		global.LOG.Info("目录创建响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Bool("alreadyExist", response.Exists),
			zap.Bool("notAllow", response.NotAllow),
			zap.String("error", response.Error))

	case "move_dir":
		// 处理目录移动响应
		var response fs2.DirMoveResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化目录移动响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "move_dir", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		global.LOG.Info("目录移动响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Bool("destinationExists", response.DestinationExists),
			zap.String("error", response.Error))

	case "delete_dir":
		// 处理目录删除响应
		var response fs2.DirDeleteResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化目录删除响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "delete_dir", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}
		global.LOG.Info("目录删除响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Bool("notAllow", response.NotAllow),
			zap.Bool("notEmpty", response.NotEmpty),
			zap.String("error", response.Error))

	case "copy_dir":
		// 处理目录复制响应
		var response fs2.DirCopyResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化目录复制响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			cache.ResponseMgr.StoreResponse(response.TaskID, "copy_dir", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		global.LOG.Info("目录复制响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Bool("destinationExists", response.DestinationExists),
			zap.Int("copiedFiles", response.CopiedFiles),
			zap.String("error", response.Error))

	default:
		global.LOG.Warn("未知的目录操作类型", zap.String("remoteAddr", remoteAddr), zap.String("operation", operation))
	}

	return nil
}
