import { ref, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { fileApi, dirApi } from '@/api';
import { buildPath } from './fileUtils.js';

/**
 * 文件操作组合式函数
 * 提供文件管理的核心业务逻辑
 */
export function useFileOperations(props, { getFileList, refreshFileList, clearSelection }) {

  // 剪切板状态
  const clipboard = ref({ files: [], operation: null }); // operation可以是'copy'或'cut'
  const loading = ref(false);

  // 复制选中文件
  const copySelectedFiles = (selectedFiles, fileList, currentPath) => {
    if (selectedFiles.length === 0) {
      message.warning('请先选择要复制的文件');
      return;
    }
    clipboard.value = {
      files: selectedFiles.map(name => {
        const file = fileList.find(f => f.name === name);
        return { ...file, sourcePath: currentPath };
      }),
      operation: 'copy'
    };
    message.success(`已复制 ${selectedFiles.length} 个项目到剪切板`);
  };

  // 剪切选中文件
  const cutSelectedFiles = (selectedFiles, fileList, currentPath) => {
    if (selectedFiles.length === 0) {
      message.warning('请先选择要剪切的文件');
      return;
    }
    clipboard.value = {
      files: selectedFiles.map(name => {
        const file = fileList.find(f => f.name === name);
        return { ...file, sourcePath: currentPath };
      }),
      operation: 'cut'
    };
    message.success(`已剪切 ${selectedFiles.length} 个项目到剪切板`);
  };

  // 粘贴文件
  const pasteFiles = async (currentPath) => {
    if (clipboard.value.files.length === 0) {
      message.warning('剪切板为空');
      return;
    }

    try {
      loading.value = true;
      const promises = clipboard.value.files.map(async (file) => {
        const sourcePath = buildPath(file.sourcePath, file.name);
        const targetPath = buildPath(currentPath, file.name);
        
        if (file.type === 'directory') {
          if (clipboard.value.operation === 'copy') {
            await dirApi.copyDir(props.clientId, {
              source_path: sourcePath,
              destination_path: targetPath
            });
          } else {
            await dirApi.moveDir(props.clientId, {
              source: sourcePath,
              destination: targetPath
            });
          }
        } else {
          if (clipboard.value.operation === 'copy') {
            await fileApi.copyFile(props.clientId, {
              source: sourcePath,
              destination: targetPath
            });
          } else {
            await fileApi.moveFile(props.clientId, {
              source_path: sourcePath,
              target_path: targetPath
            });
          }
        }
      });

      await Promise.all(promises);
      
      // 无论是复制还是剪切操作，粘贴完成后都清空剪切板
      clipboard.value = { files: [], operation: '' };
      
      message.success('操作完成');
      getFileList();
      clearSelection();
    } catch (error) {
      console.error('粘贴操作失败:', error);
      message.error('粘贴失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
    } finally {
      loading.value = false;
    }
  };

  // 复制单个文件到剪切板
  const copyFile = (file, currentPath) => {
    clipboard.value = {
      files: [{ ...file, sourcePath: currentPath }],
      operation: 'copy'
    };
    message.success('已复制到剪切板');
  };

  // 剪切单个文件到剪切板
  const cutFile = (file, currentPath) => {
    clipboard.value = {
      files: [{ ...file, sourcePath: currentPath }],
      operation: 'cut'
    };
    message.success('已剪切到剪切板');
  };

  // 删除单个文件或目录
  const deleteFile = async (file, currentPath) => {
    try {
      const filePath = buildPath(currentPath, file.name);
      
      let res;
      if (file.type === 'directory') {
        res = await dirApi.deleteDir(props.clientId, { 
          path: filePath,
          recursive: true,
          force: true
        });
      } else {
        res = await fileApi.deleteFile(props.clientId, { 
          path: filePath,
          recursive: true,
          force_write: true
        });
      }
      
      if (res.code === 200) {
        message.success(`删除${file.type === 'directory' ? '目录' : '文件'}成功`);
        refreshFileList();
      } else {
        console.error('删除失败响应:', res);
        message.error(res.data?.error || res.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
    }
  };

  // 批量删除功能
  const deleteSelectedFiles = async (selectedFiles, fileList, currentPath) => {
    if (selectedFiles.length === 0) {
      message.warning('请先选择要删除的文件');
      return;
    }

    try {
      loading.value = true;
      const promises = selectedFiles.map(async (fileName) => {
        const file = fileList.find(f => f.name === fileName);
        const filePath = buildPath(currentPath, fileName);
        
        if (file.type === 'directory') {
          await dirApi.deleteDir(props.clientId, {
            path: filePath,
            recursive: true,
            force: true
          });
        } else {
          await fileApi.deleteFile(props.clientId, {
            path: filePath,
            recursive: true,
            force_write: true
          });
        }
      });

      await Promise.all(promises);
      message.success('删除成功');
      getFileList();
      clearSelection();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
    } finally {
      loading.value = false;
    }
  };

  // 下载文件
  const downloadFile = async (file, currentPath, fileTransferModalRef) => {
    if (file.type === 'directory') {
      message.warning('不能下载目录');
      return;
    }
    
    try {
      const remotePath = buildPath(currentPath, file.name);
      const res = await fileApi.downloadFileFromClient(props.clientId, { path: remotePath });
      if (res.code === 200) {
        message.success('文件下载任务已创建');
        // 通过ref调用FileTransferModal的getTransferTasks方法
        if (fileTransferModalRef && fileTransferModalRef.value) {
          fileTransferModalRef.value.getTransferTasks();
        }
      } else {
        message.error(res.message || '下载文件失败');
      }
    } catch (error) {
      console.error('下载文件失败:', error);
      message.error('下载文件失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
    }
  };

  // 确认删除对话框
  const confirmDelete = (file, currentPath) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除 "${file.name}" 吗？`,
      onOk: () => deleteFile(file, currentPath)
    });
  };

  // 确认批量删除对话框
  const confirmDeleteSelected = (selectedFiles, fileList, currentPath) => {
    if (selectedFiles.length === 0) {
      message.warning('请先选择要删除的文件');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedFiles.length} 个项目吗？`,
      onOk: () => deleteSelectedFiles(selectedFiles, fileList, currentPath)
    });
  };

  // 处理右键菜单点击事件
  const handleMenuClick = async (key, record, currentPath, startRename) => {
    switch (key) {
      case 'copy':
        copyFile(record, currentPath);
        break;
      case 'cut':
        cutFile(record, currentPath);
        break;
      case 'rename':
        startRename(record);
        break;
      case 'delete':
        confirmDelete(record, currentPath);
        break;
    }
  };

  return {
    // 状态
    clipboard,
    loading,
    
    // 工具函数
    buildPath,
    
    // 剪切板操作
    copySelectedFiles,
    cutSelectedFiles,
    pasteFiles,
    copyFile,
    cutFile,
    
    // 删除操作
    deleteFile,
    deleteSelectedFiles,
    confirmDelete,
    confirmDeleteSelected,
    
    // 下载操作
    downloadFile,
    
    // 菜单处理
    handleMenuClick
  };
}