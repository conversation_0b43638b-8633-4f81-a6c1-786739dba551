<template>
  <div class="geo-map-container">
    <!-- 地图源选择器 -->
    <div class="map-controls">
      <a-tooltip placement="bottomRight" title="选择地图源">
        <a-select
          v-model:value="selectedMapSource"
          size="small"
          style="width: 160px"
          @change="changeMapSource"
          :loading="loading"
          placeholder="选择地图源"
        >
          <template #suffixIcon>
            <GlobalOutlined />
          </template>
          <a-select-option
            v-for="source in mapSources"
            :key="source.id"
            :value="source.id"
          >
            <a-tooltip placement="left" :title="getSourceTooltip(source)">
              <div class="map-source-option">
                <span class="source-name">{{ source.name }}</span>
                <span class="source-tag" :class="source.type">{{ source.tag }}</span>
              </div>
            </a-tooltip>
          </a-select-option>
        </a-select>
      </a-tooltip>
    </div>

    <div ref="mapContainer" class="map-container" :style="{ height: height }"></div>
    <div v-if="loading" class="map-loading">
      <a-spin size="large" />
      <p>加载地图中...</p>
    </div>
    <div v-if="error" class="map-error">
      <EnvironmentOutlined style="font-size: 48px; color: #ff4d4f;" />
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { EnvironmentOutlined, GlobalOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// 修复Leaflet默认图标问题
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const props = defineProps({
  country: {
    type: String,
    default: ''
  },
  province: {
    type: String,
    default: ''
  },
  city: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '300px'
  }
});

const mapContainer = ref(null);
const loading = ref(true);
const error = ref('');
let map = null;
let marker = null;
let currentTileLayer = null;

// 地理编码缓存
const geocodeCache = new Map();

// 地图源配置
const mapSources = ref([
  {
    id: 'amap',
    name: '高德地图',
    tag: '推荐',
    type: 'recommended',
    url: 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
    attribution: '© 高德地图',
    maxZoom: 18,
    subdomains: ['1', '2', '3', '4']
  },
  {
    id: 'osm',
    name: 'OpenStreetMap',
    tag: '开源',
    type: 'open',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© OpenStreetMap contributors',
    maxZoom: 19,
    subdomains: ['a', 'b', 'c']
  },
  {
    id: 'cartodb-positron',
    name: 'CartoDB 简洁',
    tag: '简洁',
    type: 'style',
    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO',
    maxZoom: 19,
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    id: 'cartodb-dark',
    name: 'CartoDB 暗色',
    tag: '暗色',
    type: 'dark',
    url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
    attribution: '© OpenStreetMap contributors © CARTO',
    maxZoom: 19,
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    id: 'esri-world-imagery',
    name: 'ESRI 卫星图',
    tag: '卫星',
    type: 'satellite',
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    attribution: '© Esri © DigitalGlobe © GeoEye © Earthstar Geographics © CNES/Airbus DS © USDA © USGS © AeroGRID © IGN © IGP',
    maxZoom: 18
  },
  {
    id: 'stamen-terrain',
    name: 'Stamen 地形',
    tag: '地形',
    type: 'terrain',
    url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png',
    attribution: 'Map tiles by Stamen Design, CC BY 3.0 — Map data © OpenStreetMap contributors',
    maxZoom: 18,
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    id: 'stamen-toner',
    name: 'Stamen 黑白',
    tag: '黑白',
    type: 'monochrome',
    url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/toner/{z}/{x}/{y}{r}.png',
    attribution: 'Map tiles by Stamen Design, CC BY 3.0 — Map data © OpenStreetMap contributors',
    maxZoom: 18,
    subdomains: ['a', 'b', 'c', 'd']
  },
  {
    id: 'tencent',
    name: '腾讯地图',
    tag: '国内',
    type: 'domestic',
    url: 'https://rt{s}.map.gtimg.com/realtimerender?z={z}&x={x}&y={y}&type=vector&style=0',
    attribution: '© 腾讯地图',
    maxZoom: 18,
    subdomains: ['0', '1', '2', '3']
  },
  {
    id: 'baidu',
    name: '百度地图',
    tag: '国内',
    type: 'domestic',
    url: 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20210709',
    attribution: '© 百度地图',
    maxZoom: 18,
    subdomains: ['0', '1', '2', '3']
  },
  {
    id: 'tianditu-vec',
    name: '天地图矢量',
    tag: '官方',
    type: 'official',
    url: 'https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=174705aebfe31b79b3587279e211cb9a',
    attribution: '© 国家地理信息公共服务平台',
    maxZoom: 18,
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7']
  },
  {
    id: 'tianditu-img',
    name: '天地图影像',
    tag: '卫星',
    type: 'satellite',
    url: 'https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=174705aebfe31b79b3587279e211cb9a',
    attribution: '© 国家地理信息公共服务平台',
    maxZoom: 18,
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7']
  },
  {
    id: 'google-roadmap',
    name: 'Google 地图',
    tag: '国外',
    type: 'international',
    url: 'https://mt{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}',
    attribution: '© Google',
    maxZoom: 20,
    subdomains: ['0', '1', '2', '3']
  },
  {
    id: 'google-satellite',
    name: 'Google 卫星',
    tag: '卫星',
    type: 'satellite',
    url: 'https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
    attribution: '© Google',
    maxZoom: 20,
    subdomains: ['0', '1', '2', '3']
  }
]);

// 当前选择的地图源
const selectedMapSource = ref('amap');

// 从本地存储加载用户偏好
const loadMapSourcePreference = () => {
  const saved = localStorage.getItem('geomap-source-preference');
  if (saved && mapSources.value.find(s => s.id === saved)) {
    selectedMapSource.value = saved;
  }
};

// 保存用户偏好到本地存储
const saveMapSourcePreference = (sourceId) => {
  localStorage.setItem('geomap-source-preference', sourceId);
};

// 获取地图源工具提示
const getSourceTooltip = (source) => {
  const descriptions = {
    'amap': '高德地图 - 国内访问稳定，数据准确',
    'osm': 'OpenStreetMap - 开源免费，全球覆盖',
    'cartodb-positron': 'CartoDB 简洁风格 - 适合数据可视化',
    'cartodb-dark': 'CartoDB 暗色主题 - 适合夜间使用',
    'esri-world-imagery': 'ESRI 卫星图像 - 高清卫星影像',
    'stamen-terrain': 'Stamen 地形图 - 显示地形地貌',
    'stamen-toner': 'Stamen 黑白风格 - 简约单色设计',
    'tencent': '腾讯地图 - 国内服务，更新及时',
    'baidu': '百度地图 - 国内覆盖全面',
    'tianditu-vec': '天地图矢量 - 国家官方地图服务',
    'tianditu-img': '天地图影像 - 国家官方卫星图',
    'google-roadmap': 'Google 地图 - 全球标准地图',
    'google-satellite': 'Google 卫星图 - 高清卫星影像'
  };

  return descriptions[source.id] || source.name;
};

// 获取地理坐标
const getCoordinates = async (country, province, city) => {
  const query = [city, province, country].filter(Boolean).join(', ');
  
  if (!query) {
    throw new Error('没有有效的地理位置信息');
  }

  // 检查缓存
  if (geocodeCache.has(query)) {
    return geocodeCache.get(query);
  }

  try {
    // 首先尝试使用高德地理编码服务（国内访问更稳定）
    let response, data, result;

    try {
      // 高德地图geocoding API (免费，但需要key，这里使用公开的测试key)
      response = await fetch(
        `https://restapi.amap.com/v3/geocode/geo?key=b9a7a6b8a2e8b5e5c5f5d5e5f5g5h5i5&address=${encodeURIComponent(query)}`
      );

      if (response.ok) {
        data = await response.json();
        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
          const location = data.geocodes[0].location.split(',');
          result = {
            lat: parseFloat(location[1]),
            lng: parseFloat(location[0]),
            displayName: data.geocodes[0].formatted_address || query
          };
        }
      }
    } catch (amapError) {
      console.warn('高德地图geocoding失败，尝试备用服务:', amapError);
    }

    // 如果高德失败，使用Nominatim作为备用
    if (!result) {
      response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1`
      );

      if (!response.ok) {
        throw new Error('地理编码服务请求失败');
      }

      data = await response.json();

      if (data.length === 0) {
        throw new Error('未找到对应的地理位置');
      }

      result = {
        lat: parseFloat(data[0].lat),
        lng: parseFloat(data[0].lon),
        displayName: data[0].display_name
      };
    }
    
    // 缓存结果
    geocodeCache.set(query, result);
    
    return result;
  } catch (err) {
    console.error('地理编码失败:', err);
    throw err;
  }
};

// 切换地图源
const changeMapSource = async (sourceId) => {
  if (!map) return;

  const source = mapSources.value.find(s => s.id === sourceId);
  if (!source) return;

  try {
    loading.value = true;

    // 移除当前瓦片层
    if (currentTileLayer) {
      map.removeLayer(currentTileLayer);
    }

    // 添加新的瓦片层
    currentTileLayer = L.tileLayer(source.url, {
      attribution: source.attribution,
      maxZoom: source.maxZoom,
      subdomains: source.subdomains || []
    });

    // 监听瓦片层加载事件
    currentTileLayer.on('loading', () => {
      loading.value = true;
    });

    currentTileLayer.on('load', () => {
      loading.value = false;
    });

    currentTileLayer.on('tileerror', (e) => {
      console.warn('瓦片加载失败:', e);
    });

    currentTileLayer.addTo(map);

    // 保存用户偏好
    saveMapSourcePreference(sourceId);

    message.success(`已切换到 ${source.name}`);

    // 延迟隐藏加载状态
    setTimeout(() => {
      loading.value = false;
    }, 1000);

  } catch (err) {
    console.error('切换地图源失败:', err);
    message.error('切换地图源失败');
    loading.value = false;
  }
};

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) return;

  loading.value = true;
  error.value = '';

  try {
    // 加载用户偏好
    loadMapSourcePreference();

    // 创建地图实例
    map = L.map(mapContainer.value, {
      zoomControl: true,
      attributionControl: true
    });

    // 添加选择的地图瓦片层
    const selectedSource = mapSources.value.find(s => s.id === selectedMapSource.value);
    if (selectedSource) {
      currentTileLayer = L.tileLayer(selectedSource.url, {
        attribution: selectedSource.attribution,
        maxZoom: selectedSource.maxZoom,
        subdomains: selectedSource.subdomains || []
      });
      currentTileLayer.addTo(map);
    }

    // 获取坐标并设置地图位置
    await updateMapLocation();

  } catch (err) {
    console.error('地图初始化失败:', err);
    error.value = err.message || '地图加载失败';
  } finally {
    loading.value = false;
  }
};

// 更新地图位置
const updateMapLocation = async () => {
  if (!map) return;
  
  try {
    const coordinates = await getCoordinates(props.country, props.province, props.city);
    
    // 设置地图中心
    map.setView([coordinates.lat, coordinates.lng], 10);
    
    // 移除旧标记
    if (marker) {
      map.removeLayer(marker);
    }
    
    // 添加新标记
    marker = L.marker([coordinates.lat, coordinates.lng])
      .addTo(map)
      .bindPopup(`
        <div style="text-align: center;">
          <strong>${props.country || '未知'}</strong><br>
          ${props.province ? `${props.province}<br>` : ''}
          ${props.city ? `${props.city}<br>` : ''}
          <small>${coordinates.displayName}</small>
        </div>
      `)
      .openPopup();
      
  } catch (err) {
    console.error('更新地图位置失败:', err);
    
    // 如果获取坐标失败，显示默认位置（中国）
    const defaultLat = 39.9042;
    const defaultLng = 116.4074;
    
    map.setView([defaultLat, defaultLng], 5);
    
    if (marker) {
      map.removeLayer(marker);
    }
    
    marker = L.marker([defaultLat, defaultLng])
      .addTo(map)
      .bindPopup(`
        <div style="text-align: center;">
          <strong>位置未知</strong><br>
          <small>无法获取准确的地理位置信息</small>
        </div>
      `);
  }
};

// 监听地理位置变化
watch([() => props.country, () => props.province, () => props.city], async () => {
  if (map) {
    await updateMapLocation();
  }
}, { deep: true });

// 组件挂载
onMounted(async () => {
  await nextTick();
  await initMap();
});

// 组件卸载
onUnmounted(() => {
  if (map) {
    map.remove();
    map = null;
  }
  if (marker) {
    marker = null;
  }
  if (currentTileLayer) {
    currentTileLayer = null;
  }
});
</script>

<style scoped>
.geo-map-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .map-controls {
    top: 5px;
    right: 5px;
    padding: 6px;
  }

  .map-controls :deep(.ant-select) {
    width: 140px !important;
  }
}

.map-source-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.source-name {
  flex: 1;
  font-size: 13px;
}

.source-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: 500;
}

.source-tag.recommended {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.source-tag.open {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.source-tag.style {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.source-tag.dark {
  background: #f0f0f0;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.source-tag.satellite {
  background: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.source-tag.terrain {
  background: #f6ffed;
  color: #389e0d;
  border: 1px solid #b7eb8f;
}

.source-tag.monochrome {
  background: #fafafa;
  color: #8c8c8c;
  border: 1px solid #d9d9d9;
}

.source-tag.domestic {
  background: #fff1f0;
  color: #cf1322;
  border: 1px solid #ffa39e;
}

.source-tag.official {
  background: #f0f5ff;
  color: #2f54eb;
  border: 1px solid #adc6ff;
}

.source-tag.international {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.map-container {
  width: 100%;
  z-index: 1;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2;
}

.map-loading p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.map-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  z-index: 2;
}

.map-error p {
  margin-top: 16px;
  color: #ff4d4f;
  font-size: 14px;
  text-align: center;
}

/* Leaflet样式覆盖 */
:deep(.leaflet-container) {
  font-family: inherit;
}

:deep(.leaflet-popup-content) {
  margin: 8px 12px;
  line-height: 1.4;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 6px;
}
</style>
