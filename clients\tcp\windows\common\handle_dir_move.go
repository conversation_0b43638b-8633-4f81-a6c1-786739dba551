//go:build windows
// +build windows

package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// DirMoveRequest 目录移动请求
type DirMoveRequest struct {
	TaskID      uint64 `json:"task_id"`     // 任务ID
	Source      string `json:"source"`      // 源目录路径
	Destination string `json:"destination"` // 目标目录路径
	Force       bool   `json:"force"`       // 是否强制覆盖
}

// DirMoveResponse 目录移动响应
type DirMoveResponse struct {
	TaskID            uint64 `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	SourceExists      bool   `json:"source_exists"`      // 源目录是否存在
	DestinationExists bool   `json:"destination_exists"` // 目标目录是否存在
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	ActualSource      string `json:"actual_source"`      // 实际源路径
	ActualDestination string `json:"actual_destination"` // 实际目标路径
	Error             string `json:"error"`              // 错误信息
}

// handleDirMove 处理目录移动请求
func (cm *ConnectionManager) handleDirMove(packet *Packet) {
	var req DirMoveRequest
	// 创建错误响应结构体，TaskID初始化为0
	respErr := &DirMoveResponse{
		TaskID:  0,
		Success: false,
		Error:   "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("目录移动请求反序列化失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		respErr.Error = fmt.Sprintf("目录移动请求反序列化失败: %v", err)
		cm.sendResp(Dir, DirMove, respErr)
		return
	}
	// 反序列化成功后，更新错误响应的TaskID
	respErr.TaskID = req.TaskID

	// 参数验证
	source := strings.TrimSpace(req.Source)
	destination := strings.TrimSpace(req.Destination)

	log.Printf("开始移动DirMove: ")
	log.Printf("source: %v", source)
	log.Printf("destination: %v", destination)

	if source == "" || destination == "" {
		log.Printf("源路径或目标路径为空")
		respErr.Error = "源路径或目标路径为空"
		cm.sendResp(Dir, DirMove, respErr)
		return
	}

	// 路径安全检查
	if !isValidPath(source) || !isValidPath(destination) {
		log.Printf("无效的路径: %s -> %s", source, destination)
		respErr.Error = fmt.Sprintf("无效的路径: %s -> %s", source, destination)
		cm.sendResp(Dir, DirMove, respErr)
		return
	}

	// 获取绝对路径
	absSource, err := getAbsolutePath(source)
	if err != nil {
		log.Printf("获取源路径绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取源路径绝对路径失败: %v", err)
		cm.sendResp(Dir, DirMove, respErr)
		return
	}

	absDestination, err := getAbsolutePath(destination)
	if err != nil {
		log.Printf("获取目标路径绝对路径失败: %v", err)
		respErr.Error = fmt.Sprintf("获取目标路径绝对路径失败: %v", err)
		cm.sendResp(Dir, DirMove, respErr)
		return
	}

	// 使用互斥锁防止并发操作冲突
	dirOpMutex.Lock()
	defer dirOpMutex.Unlock()

	resp := cm.moveDirectory(absSource, absDestination, &req)
	cm.sendResp(Dir, DirMove, resp)
}

// moveDirectory 移动目录的核心逻辑
func (cm *ConnectionManager) moveDirectory(source, destination string, req *DirMoveRequest) *DirMoveResponse {
	resp := &DirMoveResponse{
		TaskID:            req.TaskID,
		Success:           false,
		SourceExists:      false,
		DestinationExists: false,
		NotAllow:          false,
		ActualSource:      source,
		ActualDestination: destination,
		Error:             "",
	}

	// 检查源路径是否存在且是目录
	sourceExists, err := isDirectory(source)
	if err != nil {
		log.Printf("检查源路径出错: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "源路径权限不足"
		} else {
			resp.Error = fmt.Sprintf("源路径检查失败: %v", err)
		}
		return resp
	}

	if !sourceExists {
		resp.SourceExists = false
		resp.Error = "源目录不存在"
		return resp
	}
	resp.SourceExists = true

	// 检查目标路径是否存在
	destExists, err := PathExists(destination)
	if err != nil {
		log.Printf("检查目标路径出错: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "目标路径权限不足"
		} else {
			resp.Error = fmt.Sprintf("目标路径检查失败: %v", err)
		}
		return resp
	}

	if destExists && !req.Force {
		resp.DestinationExists = true
		resp.Error = "目标路径已存在且未启用强制覆盖"
		return resp
	}

	// 如果强制覆盖且目标存在，先删除目标
	if destExists && req.Force {
		if err := os.RemoveAll(destination); err != nil {
			log.Printf("删除目标路径失败: %v", err)
			if os.IsPermission(err) {
				resp.NotAllow = true
				resp.Error = "删除目标路径权限不足"
			} else {
				resp.Error = fmt.Sprintf("删除目标路径失败: %v", err)
			}
			return resp
		}
	}

	// 确保目标目录的父目录存在
	destDir := filepath.Dir(destination)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		log.Printf("创建目标父目录失败: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "创建目标父目录权限不足"
		} else {
			resp.Error = fmt.Sprintf("创建目标父目录失败: %v", err)
		}
		return resp
	}

	// 执行移动操作
	if err := os.Rename(source, destination); err != nil {
		log.Printf("目录移动失败: %v", err)
		if os.IsPermission(err) {
			resp.NotAllow = true
			resp.Error = "移动操作权限不足"
		} else if os.IsNotExist(err) {
			resp.SourceExists = false
			resp.Error = "源目录不存在"
		} else {
			resp.Error = fmt.Sprintf("移动失败: %v", err)
		}
		return resp
	}

	// 验证移动结果
	if moved, _ := isDirectory(destination); moved {
		resp.Success = true
	} else {
		resp.Error = "移动验证失败"
	}

	return resp
}
