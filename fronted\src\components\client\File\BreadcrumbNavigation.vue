<template>
  <div class="path-navigation">
    <div class="breadcrumb-container">
      <a-breadcrumb>
        <!-- 磁盘根目录 -->
        <a-breadcrumb-item v-if="currentDisk">
          <a @click="navigateToSegment(-1)">
            <span class="disk-icon-small">{{ currentDisk.icon }}</span>
            {{ currentDisk.displayName }}
          </a>
        </a-breadcrumb-item>
        <a-breadcrumb-item v-else>
          <a @click="navigateToSegment(-1)">
            <HomeOutlined />
            根目录
          </a>
        </a-breadcrumb-item>
        <!-- 路径分段 -->
        <a-breadcrumb-item v-for="(segment, index) in pathSegments" :key="index">
          <a @click="navigateToSegment(index)">{{ segment }}</a>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <div class="path-input-container">
      <a-input 
        v-model:value="inputPath" 
        placeholder="输入路径" 
        class="path-input"
        @pressEnter="navigateToPath"
      >
        <template #prefix>
          <FolderOutlined />
        </template>
      </a-input>
      <a-button 
        type="primary" 
        :disabled="!clientInfo || clientInfo.status !== 1 || !inputPath.trim()" 
        @click="navigateToPath"
      >
        转到
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { HomeOutlined, FolderOutlined } from '@ant-design/icons-vue';

// Props
const props = defineProps({
  currentPath: {
    type: String,
    required: true
  },
  currentDisk: {
    type: Object,
    default: null
  },
  clientInfo: {
    type: Object,
    default: null
  },
  pathHistory: {
    type: Array,
    required: true
  }
});

// Emits
const emit = defineEmits(['navigate-to-path', 'navigate-to-segment']);

// 响应式数据
const inputPath = ref('');

// 通用路径处理函数
const buildPath = (basePath, ...segments) => {
  // 过滤掉空字符串和null/undefined
  const validSegments = segments.filter(segment => segment && segment.trim());
  
  if (!validSegments.length) {
    return basePath;
  }
  
  let result = basePath;
  
  for (const segment of validSegments) {
    // 移除segment开头的斜杠
    const cleanSegment = segment.replace(/^\/+/, '');
    
    if (cleanSegment) {
      // 确保basePath以斜杠结尾
      if (!result.endsWith('/')) {
        result += '/';
      }
      result += cleanSegment;
    }
  }
  
  return result;
};

// 计算属性：路径分段
const pathSegments = computed(() => {
  let path = props.currentPath;
  
  // 如果有当前磁盘，移除磁盘前缀
  if (props.currentDisk && path.startsWith(props.currentDisk.name)) {
    path = path.substring(props.currentDisk.name.length);
    // 移除开头的斜杠
    if (path.startsWith('/')) {
      path = path.substring(1);
    }
  } else if (path === '/') {
    return [];
  }
  
  return path.split('/').filter(segment => segment);
});

// 导航到输入的路径
const navigateToPath = () => {
  if (!inputPath.value.trim()) {
    message.warning('请输入路径');
    return;
  }
  
  let targetPath = inputPath.value.trim();
  
  // Windows路径处理
  if (props.clientInfo?.os === 'windows') {
    // 检查是否是磁盘路径（如 C:, D: 等）
    if (/^[A-Za-z]:$/.test(targetPath)) {
      targetPath = targetPath + '/';
    }
    // 检查是否是完整的Windows路径
    else if (/^[A-Za-z]:[\\/]/.test(targetPath)) {
      // 将反斜杠转换为正斜杠
      targetPath = targetPath.replace(/\\/g, '/');
      // 确保以 / 结尾（如果是目录）
      if (!targetPath.endsWith('/')) {
        targetPath += '/';
      }
    }
    // 相对路径处理
    else if (!targetPath.startsWith('/') && props.currentDisk) {
      const diskPath = props.currentDisk.name.endsWith('/') ? props.currentDisk.name.slice(0, -1) : props.currentDisk.name;
      targetPath = buildPath(diskPath, targetPath);
    }
  } else {
    // Linux路径处理
    // 确保路径以 / 开头
    if (!targetPath.startsWith('/')) {
      targetPath = '/' + targetPath;
    }
  }
  
  // 移除末尾的 /（除非是根目录或磁盘根目录）
  if (targetPath !== '/' && !(/^[A-Za-z]:\/$/.test(targetPath)) && targetPath.endsWith('/')) {
    targetPath = targetPath.slice(0, -1);
  }
  
  // 清空输入框
  inputPath.value = '';
  
  // 触发导航事件
  emit('navigate-to-path', targetPath);
};

// 导航到面包屑中的特定路径段
const navigateToSegment = (index) => {
  if (!props.clientInfo || props.clientInfo.status !== 1) {
    message.warning('客户端未连接或离线');
    return;
  }
  
  let targetPath;
  
  if (index === -1) {
    // 点击根目录
    if (props.currentDisk) {
      // 确保磁盘路径格式正确，避免双斜杠
      targetPath = props.currentDisk.name.endsWith('/') ? props.currentDisk.name : props.currentDisk.name + '/';
    } else {
      targetPath = '/';
    }
  } else {
    // 构建目标路径
    const segments = pathSegments.value.slice(0, index + 1);
    if (props.currentDisk) {
      const diskPath = props.currentDisk.name.endsWith('/') ? props.currentDisk.name.slice(0, -1) : props.currentDisk.name;
      targetPath = buildPath(diskPath, segments.join('/'));
    } else {
      targetPath = buildPath('/', segments.join('/'));
    }
  }
  
  // 触发导航事件
  emit('navigate-to-segment', { targetPath, index });
};
</script>

<style scoped>
.path-navigation {
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.breadcrumb-container {
  flex: 1;
  min-width: 200px;
}

.disk-icon-small {
  font-size: 14px;
  margin-right: 4px;
}

.path-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.path-input {
  flex: 1;
  max-width: 400px;
}

/* 面包屑样式优化 */
:deep(.ant-breadcrumb) {
  font-size: 14px;
}

:deep(.ant-breadcrumb a) {
  color: #1890ff;
  transition: color 0.2s;
}

:deep(.ant-breadcrumb a:hover) {
  color: #40a9ff;
}

/* 输入框样式优化 */
:deep(.ant-input) {
  border-radius: 6px;
}

/* 按钮样式优化 */
:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
}
</style>