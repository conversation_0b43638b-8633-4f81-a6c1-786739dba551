package cache

import (
	"fmt"
	"server/core/manager/dbpool"
	"server/global"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ResponseManager 响应管理器
type ResponseManager struct {
	mu sync.RWMutex

	// 🚀 统计信息
	stats *ResponseManagerStats
}

// ResponseManagerStats 响应管理器统计信息
type ResponseManagerStats struct {
	// 操作统计 - 使用原子操作
	TotalStores   int64 `json:"total_stores"`   // 总存储次数
	TotalGets     int64 `json:"total_gets"`     // 总获取次数
	TaskStores    int64 `json:"task_stores"`    // 任务存储次数
	CommandStores int64 `json:"command_stores"` // 命令存储次数
	StreamStores  int64 `json:"stream_stores"`  // 流存储次数

	// 性能统计
	CacheHits    int64 `json:"cache_hits"`    // 缓存命中次数
	CacheMisses  int64 `json:"cache_misses"`  // 缓存未命中次数
	WaitTimeouts int64 `json:"wait_timeouts"` // 等待超时次数

	// 时间统计
	StartTime     int64 `json:"start_time"`
	LastStoreTime int64 `json:"last_store_time"`
}

var ResponseMgr = &ResponseManager{
	stats: &ResponseManagerStats{
		StartTime: time.Now().UnixNano(),
	},
}

// ResponseData 响应数据结构
type ResponseData struct {
	ClientID    uint        `json:"client_id"`
	TaskID      uint64      `json:"task_id"`      // 任务ID
	RequestType string      `json:"request_type"` // file_info, dir_list, file_copy, etc.
	RequestPath string      `json:"request_path"` // 请求的路径（保留用于兼容性）
	Data        interface{} `json:"data"`         // 实际响应数据
	Error       string      `json:"error"`        // 错误信息
	Timestamp   time.Time   `json:"timestamp"`    // 响应时间
	Status      string      `json:"status"`       // success, error
}

// StoreResponse 存储响应数据（基于TaskID）
func (rm *ResponseManager) StoreResponse(taskID uint64, requestType string, data interface{}, err string) {
	response := &ResponseData{
		TaskID:      taskID,
		RequestType: requestType,
		Data:        data,
		Error:       err,
		Timestamp:   time.Now(),
		Status:      "success",
	}
	if err != "" {
		response.Status = "error"
	}

	// 生成缓存键：taskID
	cacheKey := fmt.Sprintf("task_%d", taskID)

	// 🚀 优先使用智能缓存，任务响应使用较长的TTL且不会被LRU淘汰
	if GlobalSmartCache != nil {
		GlobalSmartCache.Set(cacheKey, response, CacheTypeTask, time.Minute*30) // 任务缓存30分钟
	} else {
		// 降级到原有缓存
		global.ResponseCache.Set(cacheKey, response, time.Minute*5)
	}

	// 更新统计
	atomic.AddInt64(&rm.stats.TotalStores, 1)
	atomic.AddInt64(&rm.stats.TaskStores, 1)
	atomic.StoreInt64(&rm.stats.LastStoreTime, time.Now().UnixNano())

	global.LOG.Debug("存储响应数据",
		zap.String("cacheKey", cacheKey),
		zap.Uint64("taskID", taskID),
		zap.String("requestType", requestType),
		zap.String("status", response.Status))
}

// GetResponse 获取响应数据（基于TaskID）
func (rm *ResponseManager) GetResponse(taskID uint64) (*ResponseData, bool) {
	// 生成缓存键
	cacheKey := fmt.Sprintf("task_%d", taskID)

	// 更新统计
	atomic.AddInt64(&rm.stats.TotalGets, 1)

	// 🚀 优先从智能缓存获取
	if GlobalSmartCache != nil {
		if value, ok := GlobalSmartCache.Get(cacheKey); ok {
			if response, ok := value.(*ResponseData); ok {
				atomic.AddInt64(&rm.stats.CacheHits, 1)
				return response, true
			}
		}
	} else {
		// 降级到原有缓存
		if value, ok := global.ResponseCache.Get(cacheKey); ok {
			if response, ok := value.(*ResponseData); ok {
				atomic.AddInt64(&rm.stats.CacheHits, 1)
				return response, true
			}
		}
	}

	atomic.AddInt64(&rm.stats.CacheMisses, 1)
	return nil, false
}

// WaitForResponse 等待响应数据（基于TaskID，带超时）
func (rm *ResponseManager) WaitForResponse(taskID uint64, timeout time.Duration) (*ResponseData, error) {
	// 🚀 直接执行等待逻辑，避免双重工作池嵌套问题
	start := time.Now()
	ticker := time.NewTicker(100 * time.Millisecond) // 每100ms检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if response, found := rm.GetResponse(taskID); found {
				return response, nil
			}
			if time.Since(start) >= timeout {
				atomic.AddInt64(&rm.stats.WaitTimeouts, 1)
				return nil, fmt.Errorf("等待响应超时")
			}
		case <-time.After(timeout):
			atomic.AddInt64(&rm.stats.WaitTimeouts, 1)
			return nil, fmt.Errorf("等待响应超时")
		}
	}
}

// StoreStreamData 存储流数据（基于自定义键）
func (rm *ResponseManager) StoreStreamData(streamKey string, requestType string, data interface{}, err string) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	response := &ResponseData{
		RequestType: requestType,
		Data:        data,
		Error:       err,
		Timestamp:   time.Now(),
		Status:      "success",
	}
	if err != "" {
		response.Status = "error"
	}

	// 直接使用 streamKey 作为缓存键
	cacheKey := streamKey

	// 存储到缓存中，流数据保持较短时间
	global.ResponseCache.Set(cacheKey, response, time.Minute*2)

	global.LOG.Debug("存储流数据",
		zap.String("cacheKey", cacheKey),
		zap.String("requestType", requestType),
		zap.String("status", response.Status))
}

// GetStreamData 获取流数据（基于自定义键）
func (rm *ResponseManager) GetStreamData(streamKey string, requestType string) (interface{}, bool) {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	// 直接使用 streamKey 作为缓存键
	cacheKey := streamKey

	// 从缓存中获取
	if value, ok := global.ResponseCache.Get(cacheKey); ok {
		if response, ok := value.(*ResponseData); ok {
			// 检查请求类型是否匹配
			if response.RequestType == requestType {
				return response.Data, true
			}
		}
	}

	return nil, false
}

// GetClientIDFromRemoteAddr 从远程地址获取客户端ID
func (rm *ResponseManager) GetClientIDFromRemoteAddr(remoteAddr string) (uint, error) {
	// 从数据库中查询客户端ID
	var client struct {
		ID uint `json:"id"`
	}

	// 🚀 使用数据库连接池查询客户端ID
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_id_by_addr", func(db *gorm.DB) error {
		return db.Table("sys_client").Select("id").Where("remote_addr = ? AND status = 1", remoteAddr).First(&client).Error
	})
	if err != nil {
		return 0, fmt.Errorf("未找到对应的客户端: %v", err)
	}

	return client.ID, nil
}

// 🚀 新增：命令输出数据结构
type CommandOutputData struct {
	ClientID  uint      `json:"client_id"`
	Output    string    `json:"output"`
	Type      string    `json:"type"` // command_output, command_error, etc.
	Timestamp time.Time `json:"timestamp"`
}

// 🚀 新增：存储命令输出到客户端缓存
func (rm *ResponseManager) StoreCommandOutput(clientID uint, output string, outputType string) {
	outputData := &CommandOutputData{
		ClientID:  clientID,
		Output:    output,
		Type:      outputType,
		Timestamp: time.Now(),
	}

	// 生成缓存键：client_output_clientID
	cacheKey := fmt.Sprintf("client_output_%d", clientID)

	// 🚀 使用智能缓存，命令输出可以被LRU淘汰
	var outputQueue []*CommandOutputData

	// 获取现有的输出队列
	if GlobalSmartCache != nil {
		if value, ok := GlobalSmartCache.Get(cacheKey); ok {
			if queue, ok := value.([]*CommandOutputData); ok {
				outputQueue = queue
			}
		}
	} else {
		// 降级到原有缓存
		if value, ok := global.ResponseCache.Get(cacheKey); ok {
			if queue, ok := value.([]*CommandOutputData); ok {
				outputQueue = queue
			}
		}
	}

	// 添加新的输出到队列
	outputQueue = append(outputQueue, outputData)

	// 🚀 智能队列长度管理：根据输出大小动态调整
	maxQueueSize := rm.calculateMaxQueueSize(outputQueue)
	if len(outputQueue) > maxQueueSize {
		outputQueue = outputQueue[len(outputQueue)-maxQueueSize:]
	}

	// 存储到缓存中
	if GlobalSmartCache != nil {
		GlobalSmartCache.Set(cacheKey, outputQueue, CacheTypeCommand, time.Minute*15)
	} else {
		global.ResponseCache.Set(cacheKey, outputQueue, time.Minute*10)
	}

	// 更新统计
	atomic.AddInt64(&rm.stats.TotalStores, 1)
	atomic.AddInt64(&rm.stats.CommandStores, 1)
	atomic.StoreInt64(&rm.stats.LastStoreTime, time.Now().UnixNano())

	global.LOG.Info("存储命令输出",
		zap.String("cacheKey", cacheKey),
		zap.Uint("clientID", clientID),
		zap.String("output", output),
		zap.Bool("useSmartCache", GlobalSmartCache != nil),
		zap.Uint("clientID", clientID),
		zap.String("type", outputType),
		zap.Int("queueLength", len(outputQueue)))
}

// calculateMaxQueueSize 根据输出内容计算最大队列大小
func (rm *ResponseManager) calculateMaxQueueSize(queue []*CommandOutputData) int {
	if len(queue) == 0 {
		return 100 // 默认值
	}

	// 计算平均输出长度
	totalLength := 0
	for _, item := range queue {
		totalLength += len(item.Output)
	}
	avgLength := totalLength / len(queue)

	// 根据平均长度动态调整队列大小
	if avgLength > 1000 { // 大输出
		return 50
	} else if avgLength > 100 { // 中等输出
		return 100
	} else { // 小输出
		return 200
	}
}

// 🚀 新增：获取客户端的所有未读命令输出
func (rm *ResponseManager) GetCommandOutputs(clientID uint) ([]*CommandOutputData, bool) {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	// 生成缓存键
	cacheKey := fmt.Sprintf("client_output_%d", clientID)

	// 从缓存中获取
	if value, ok := global.ResponseCache.Get(cacheKey); ok {
		if outputQueue, ok := value.([]*CommandOutputData); ok {
			return outputQueue, true
		}
	}

	return nil, false
}

// 🚀 新增：清空客户端的命令输出缓存
func (rm *ResponseManager) ClearCommandOutputs(clientID uint) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	cacheKey := fmt.Sprintf("client_output_%d", clientID)

	// 🚀 修复：同时清空智能缓存和原有缓存
	if GlobalSmartCache != nil {
		GlobalSmartCache.Delete(cacheKey)
	}
	global.ResponseCache.Delete(cacheKey)

	global.LOG.Debug("清空命令输出缓存",
		zap.String("cacheKey", cacheKey),
		zap.Uint("clientID", clientID))
}

// 🚀 新增：获取并清空客户端的命令输出（原子操作）
func (rm *ResponseManager) PopCommandOutputs(clientID uint) ([]*CommandOutputData, bool) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 生成缓存键
	cacheKey := fmt.Sprintf("client_output_%d", clientID)

	// 🚀 修复：优先从智能缓存获取，与存储逻辑保持一致
	if GlobalSmartCache != nil {
		if value, ok := GlobalSmartCache.Get(cacheKey); ok {
			if outputQueue, ok := value.([]*CommandOutputData); ok {
				// 清空智能缓存
				GlobalSmartCache.Delete(cacheKey)

				global.LOG.Debug("从智能缓存弹出命令输出",
					zap.String("cacheKey", cacheKey),
					zap.Uint("clientID", clientID),
					zap.Int("outputCount", len(outputQueue)))

				return outputQueue, true
			}
		}
	} else {
		// 降级到原有缓存
		if value, ok := global.ResponseCache.Get(cacheKey); ok {
			if outputQueue, ok := value.([]*CommandOutputData); ok {
				// 清空缓存
				global.ResponseCache.Delete(cacheKey)

				global.LOG.Debug("从原有缓存弹出命令输出",
					zap.String("cacheKey", cacheKey),
					zap.Uint("clientID", clientID),
					zap.Int("outputCount", len(outputQueue)))

				return outputQueue, true
			}
		}
	}

	return nil, false
}

// GetStats 获取ResponseManager统计信息
func (rm *ResponseManager) GetStats() *ResponseManagerStats {
	stats := &ResponseManagerStats{
		TotalStores:   atomic.LoadInt64(&rm.stats.TotalStores),
		TotalGets:     atomic.LoadInt64(&rm.stats.TotalGets),
		TaskStores:    atomic.LoadInt64(&rm.stats.TaskStores),
		CommandStores: atomic.LoadInt64(&rm.stats.CommandStores),
		StreamStores:  atomic.LoadInt64(&rm.stats.StreamStores),
		CacheHits:     atomic.LoadInt64(&rm.stats.CacheHits),
		CacheMisses:   atomic.LoadInt64(&rm.stats.CacheMisses),
		WaitTimeouts:  atomic.LoadInt64(&rm.stats.WaitTimeouts),
		StartTime:     atomic.LoadInt64(&rm.stats.StartTime),
		LastStoreTime: atomic.LoadInt64(&rm.stats.LastStoreTime),
	}

	return stats
}

// GetCompatibleStats 获取兼容格式的统计信息
func (rm *ResponseManager) GetCompatibleStats() map[string]interface{} {
	stats := rm.GetStats()

	hitRate := float64(0)
	if stats.TotalGets > 0 {
		hitRate = float64(stats.CacheHits) / float64(stats.TotalGets)
	}

	return map[string]interface{}{
		"total_stores":    stats.TotalStores,
		"total_gets":      stats.TotalGets,
		"task_stores":     stats.TaskStores,
		"command_stores":  stats.CommandStores,
		"stream_stores":   stats.StreamStores,
		"cache_hits":      stats.CacheHits,
		"cache_misses":    stats.CacheMisses,
		"wait_timeouts":   stats.WaitTimeouts,
		"hit_rate":        hitRate,
		"start_time":      time.Unix(0, stats.StartTime),
		"last_store_time": time.Unix(0, stats.LastStoreTime),
	}
}
