package fs

// FileUploadToClientRequest 上传文件到客户端请求
type FileUploadToClientRequest struct {
	TaskID          uint64 `json:"task_id"`
	SourcePath      string `json:"source_path" binding:"required"`      // 服务器上的源文件路径
	DestinationPath string `json:"destination_path" binding:"required"` // 客户端目标路径
	Force           bool   `json:"force"`                               // 是否强制覆盖
	ChunkSize       int64  `json:"chunk_size"`                          // 分块大小，默认64KB
}
