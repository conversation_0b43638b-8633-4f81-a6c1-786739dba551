package sys

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type AuthRoute struct{}

func (r *AuthRoute) InitAuthRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	authRouter := Router.Group("/auth").Use(middleware.OperationRecord())
	{
		authRouter.POST("login", authApi.Login)
		authRouter.POST("captcha", authApi.Captcha)
		authRouter.GET("captcha", authApi.Captcha)
	}

	return authRouter
}

func (r *AuthRoute) InitUpdateUserInfoRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {

	updateUserInfoRouter := Router.Group("/auth").Use(middleware.OperationRecord())

	updateUserInfoRouter.POST("update-user-info", authApi.UpdateUserInfo)
	updateUserInfoRouter.GET("user-info", authApi.GetUserInfo) // 获取用户信息

	return updateUserInfoRouter
}
