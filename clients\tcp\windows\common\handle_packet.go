//go:build windows
// +build windows

package common

import (
	"log"
	"strings"
)

// ProcessIncomingPacket 处理接收到的数据包
func (cm *ConnectionManager) ProcessIncomingPacket(packetBytes []byte) (*Packet, bool, error) {
	//packet := &Packet{}
	packet := cm.PacketPool.Get().(*Packet)
	defer cm.PacketPool.Put(packet)
	if err := packet.DeserializePacket(packetBytes); err != nil {
		return nil, false, err
	}

	if err := packet.DecryptPacket(cm.metadata); err != nil {
		return nil, false, err
	}

	// 处理分片
	if packet.Header.Flags == MoreFrag || packet.Header.Flags == NoMoreFrag {
		return cm.handleFragmentedPacket(packet)
	}

	return packet, true, nil
}

func (cm *ConnectionManager) processPacket(packet *Packet) {
	switch {
	case packet.Header.Type == RunCommand && packet.Header.Code == ExecInput:
		// 尝试解析为CommandRequest结构体
		var cmdRequest CommandRequest
		if err := cm.serializer.Deserialize(packet.PacketData.Data, &cmdRequest); err != nil {
			// 如果解析失败，按旧格式处理（向后兼容）
			log.Printf("解析CommandRequest失败，使用旧格式: %v", err)
			cm.ptyInputChan <- packet.PacketData.Data
		} else {
			// 新格式：CommandRequest结构体
			log.Printf("收到命令请求: TerminalID=%d, Command=%s", cmdRequest.TerminalID, cmdRequest.Command)

			if cmdRequest.TerminalID == 0 {
				// 主终端：检查是否是RESIZE命令
				if strings.HasPrefix(cmdRequest.Command, "RESIZE:") {
					log.Printf("主终端收到RESIZE命令: %s", cmdRequest.Command)
					cm.handleMainTerminalResize(cmdRequest.Command)
				} else {
					// 普通命令使用原有的处理方式
					log.Printf("主终端命令，使用原有处理方式")
					cm.ptyInputChan <- []byte(cmdRequest.Command)
				}
			} else {
				// 备用终端：使用多终端管理器
				if err := cm.multiTerminalManager.SendCommand(cmdRequest.TerminalID, cmdRequest.Command); err != nil {
					log.Printf("发送命令到终端%d失败: %v", cmdRequest.TerminalID, err)
				}
			}
		}

	case packet.Header.Type == RunCommand && packet.Header.Code == ExecCronCommand:
		// 定时任务命令执行（使用exec.Command，非PTY）
		log.Println("收到定时任务命令执行请求")
		cm.handleCronCommand(packet.PacketData.Data)

	case packet.Header.Type == RunCommand && packet.Header.Code == ExecCronScript:
		// 定时任务脚本执行（支持bat/ps1/cmd/py等）
		log.Println("收到定时任务脚本执行请求")
		cm.handleCronScript(packet.PacketData.Data)

	case packet.Header.Type == RunCommand && packet.Header.Code == CreateTerminal:
		// 创建新终端的TLV命令
		log.Println("收到创建新终端的TLV命令")
		cm.handleCreateTerminalCommand()

	case packet.Header.Type == RunCommand && packet.Header.Code == CloseTerminal:
		// 关闭终端的TLV命令
		log.Println("收到关闭终端的TLV命令")
		cm.handleCloseTerminalCommand(packet.PacketData.Data)

	case packet.Header.Type == RunCommand && packet.Header.Code == GetTerminalList:
		// 获取终端列表的TLV命令
		log.Println("收到获取终端列表的TLV命令")
		cm.handleGetTerminalListCommand()

	case packet.Header.Type == Registration && packet.Header.Code == RegResponse:
		log.Println("已成功注册!")

	case packet.Header.Type == File:
		go cm.handleFileRequest(packet)
	case packet.Header.Type == Dir:
		go cm.handleDirRequest(packet)
	case packet.Header.Type == Heartbeat && packet.Header.Code == PING:
		// 收到服务器的PING，回复结构化PONG
		heartbeatReq := cm.createClientHeartbeatRequest()
		heartbeatReq.Type = PONG // 设置为PONG类型

		heartbeatData, err := cm.serializer.Serialize(heartbeatReq)
		if err != nil {
			log.Printf("序列化PONG数据失败: %v", err)
			return
		}

		pong, err := cm.createAdvancedHeartbeatPacket(heartbeatData, PONG)
		if err != nil {
			log.Printf("创建PONG包失败: %v", err)
			return
		}

		pongBytes := pong.Serialize()
		_, err = cm.conn.Write(pongBytes)
		if err != nil {
			log.Println("发送PONG响应失败: ", err)
		} else {
			log.Println("已回复服务器PING（结构化数据）")
		}
	case packet.Header.Type == Heartbeat && packet.Header.Code == PONG:
		// 收到服务器对客户端PING的PONG响应（可能包含结构化数据）
		log.Println("收到服务器PONG响应，心跳正常")

		// 尝试解析结构化响应数据
		if len(packet.PacketData.Data) > 0 {
			cm.parseServerHeartbeatResponse(packet.PacketData.Data)
		}
	case packet.Header.Type == TermResize && packet.Header.Code == WinResize: // 新增终端调整类型
		go cm.handleTermResize(packet)
	case packet.Header.Type == Process:
		go cm.handleProcessRequest(packet)
	case packet.Header.Type == Screenshot:
		go cm.handleScreenshotRequest(packet)
	case packet.Header.Type == Network:
		go cm.handleNetworkRequest(packet)
	case packet.Header.Type == Proxy:
		go cm.handleProxyRequest(packet)
	default:
		log.Printf("未知的数据包类型: Type=%d, Code=%d", packet.Header.Type, packet.Header.Code)
	}
}

// handleFragmentedPacket 处理分片数据包
func (cm *ConnectionManager) handleFragmentedPacket(packet *Packet) (*Packet, bool, error) {
	cm.fragMutex.Lock()
	defer cm.fragMutex.Unlock()

	label := packet.Header.Label
	buffer, exists := cm.fragmentBuffer[label]
	if !exists {
		buffer = NewFragmentBuffer()
		cm.fragmentBuffer[label] = buffer
	}

	data, complete := buffer.AddFragment(packet.Header.FragIndex, packet.PacketData.Data, packet.Header.Flags == NoMoreFrag)
	if complete {
		delete(cm.fragmentBuffer, label)
		packet.PacketData.Data = data
		packet.Header.Flags = NoMoreFrag
		packet.Header.FragIndex = 0
		return packet, true, nil
	}

	return nil, false, nil
}
