//go:build windows
// +build windows

package common

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
	"github.com/minio/sha256-simd"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

// 🚀 阶段1优化：使用高性能JSON库
var json = jsoniter.ConfigCompatibleWithStandardLibrary

// Serializer 通用序列化器
type Serializer struct{}

func (s *Serializer) Serialize(data interface{}) ([]byte, error) {
	result, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("json序列化失败: %w", err)
	}
	return result, nil
}

func (s *Serializer) Deserialize(data []byte, target interface{}) error {
	if err := json.Unmarshal(data, target); err != nil {
		return fmt.Errorf("json反序列化失败: %w", err)
	}
	return nil
}

var (
	charset  = []byte("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	randPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 8)
		},
	}
)

func RandomString(length int) (string, error) {
	if length <= 0 {
		return "", nil
	}

	// 从内存池获取缓冲区
	b := randPool.Get().([]byte)
	defer randPool.Put(b)

	if cap(b) < length {
		b = make([]byte, length)
	}
	b = b[:length]

	if _, err := rand.Read(b); err != nil {
		return "", err
	}

	// 🚀 优化：使用位运算替代取模操作
	charsetLen := len(charset)
	mask := byte(charsetLen - 1)

	for i := range b {
		b[i] = charset[b[i]&mask]
	}

	return string(b), nil
}

func DecodePublicKeyFromPEM(pemString string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(pemString))
	if block == nil {
		return nil, errors.New("failed to parse PEM block containing public key")
	}
	if block.Type != "RSA PUBLIC KEY" {
		return nil, errors.New("invalid PEM block type, expected PUBLIC KEY")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("not an RSA public key")
	}
	return rsaPub, nil
}

// EncryptOAEP 使用 RSA-OAEP 加密
func EncryptOAEP(publicKey *rsa.PublicKey, text string) (string, error) {
	ciphertext, err := rsa.EncryptOAEP(
		sha256.New(),
		rand.Reader,
		publicKey,
		[]byte(text),
		nil, // label
	)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// RemoveBetween 删除 data 中从 prefix 到 suffix 的整段内容（包含 prefix 和 suffix）
func RemoveBetween(data, start, end []byte) []byte {
	if len(data) == 0 || len(start) == 0 || len(end) == 0 {
		return data
	}

	// 🚀 使用bytes.Index进行快速查找
	startIdx := findBytes(data, start)
	if startIdx == -1 {
		return data
	}

	endIdx := findBytes(data[startIdx+len(start):], end)
	if endIdx == -1 {
		return data
	}

	endIdx += startIdx + len(start) + len(end)

	// 🚀 零拷贝拼接
	result := make([]byte, 0, len(data)-(endIdx-startIdx))
	result = append(result, data[:startIdx]...)
	result = append(result, data[endIdx:]...)

	return result
}

func findBytes(data, pattern []byte) int {
	if len(pattern) == 0 {
		return 0
	}
	if len(data) < len(pattern) {
		return -1
	}

	// 简化的Boyer-Moore算法
	for i := 0; i <= len(data)-len(pattern); i++ {
		match := true
		for j := 0; j < len(pattern); j++ {
			if data[i+j] != pattern[j] {
				match = false
				break
			}
		}
		if match {
			return i
		}
	}
	return -1
}

// RetainMiddle 保留 data 中 prefix 之后和 suffix 之前的内容（包含中间随机部分）
// 如果找不到前缀或后缀，返回空切片
func RetainMiddle(data, prefix, suffix []byte) []byte {
	// 查找前缀位置
	start := bytes.Index(data, prefix)
	if start == -1 {
		return data // 没有找到前缀
	}

	// 从前缀之后开始搜索后缀
	searchStart := start + len(prefix)
	end := bytes.Index(data[searchStart:], suffix)
	if end == -1 {
		return data // 没有找到后缀
	}

	// 计算中间内容的起始和结束位置
	middleStart := searchStart
	middleEnd := searchStart + end

	// 返回中间部分（包含随机内容）
	return data[middleStart:middleEnd]
}

// copyStats 复制操作的统计信息
type copyStats struct {
	copiedFiles  int
	copiedDirs   int
	skippedFiles int
}

// isDirectory 检查是否是目录的增强函数
func isDirectory(path string) (bool, error) {
	fi, err := os.Stat(path)
	if err != nil {
		return false, err
	}
	return fi.IsDir(), nil
}

// PathExists 检查路径是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// isValidPath 验证路径是否安全
func isValidPath(path string) bool {
	if path == "" {
		return false
	}

	// 检查路径遍历攻击
	cleanPath := filepath.Clean(path)
	if strings.Contains(cleanPath, "..") {
		return false
	}

	return true
}

// isDirEmpty 检查目录是否为空
func isDirEmpty(path string) (bool, error) {
	f, err := os.Open(path)
	if err != nil {
		return false, err
	}
	defer func(f *os.File) {
		_ = f.Close()
	}(f)

	_, err = f.Readdirnames(1)
	if err == nil {
		return false, nil // 目录不为空
	}
	if err.Error() == "EOF" {
		return true, nil // 目录为空
	}
	return false, err
}

// removeReadOnlyAttribute 移除只读属性
func removeReadOnlyAttribute(path string) error {
	return filepath.WalkDir(path, func(filePath string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil // 忽略错误，继续处理
		}

		info, err := d.Info()
		if err != nil {
			return nil
		}

		// 如果是只读文件，移除只读属性
		if info.Mode()&0200 == 0 {
			if err = os.Chmod(filePath, info.Mode()|0200); err != nil {
				return nil
			}
		}

		return nil
	})
}

// 安全删除文件（带权限检查）
func secureDelete(path string) error {
	// 先检查写权限
	if !isWritable(path) {
		return os.ErrPermission
	}
	return os.Remove(path)
}

func isWritable(path string) bool {
	// 尝试以只写模式打开文件
	file, err := os.OpenFile(path, os.O_WRONLY, 0666)
	if err != nil {
		return false
	}
	_ = file.Close()
	return true
}

// appendChunkToFile 将分块内容追加到文件中
func appendChunkToFile(filePath string, chunkContent []byte, chunkIndex int64) error {
	// 打开或创建文件
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 计算写入位置
	// 对于第一个分块，直接写入到文件开头
	// 对于后续分块，追加到文件末尾
	var offset int64
	if chunkIndex == 0 {
		// 第一个分块，从文件开头写入
		offset = 0
	} else {
		// 后续分块，获取当前文件大小作为偏移量
		fileInfo, err := file.Stat()
		if err != nil {
			return fmt.Errorf("获取文件信息失败: %v", err)
		}
		offset = fileInfo.Size()
	}

	if _, err = file.Seek(offset, 0); err != nil {
		return fmt.Errorf("文件定位失败: %v", err)
	}

	if _, err = file.Write(chunkContent); err != nil {
		return fmt.Errorf("写入分块失败: %v", err)
	}

	return file.Sync()
}

// verifyFileHash 验证文件的哈希值
func verifyFileHash(filePath, expectedHash string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	hash := sha256.New()
	if _, err = io.Copy(hash, file); err != nil {
		return false
	}

	actualHash := fmt.Sprintf("%x", hash.Sum(nil))
	return strings.EqualFold(actualHash, expectedHash)
}

// calculateFileHash 计算文件的SHA256哈希值
func calculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha256.New()
	if _, err = io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// verifyFileCopy 验证文件复制的完整性
func verifyFileCopy(source, destination string) bool {
	srcInfo, err := os.Stat(source)
	if err != nil {
		return false
	}

	destInfo, err := os.Stat(destination)
	if err != nil {
		return false
	}

	// 比较文件大小
	if srcInfo.Size() != destInfo.Size() {
		return false
	}

	// 可以添加更多验证，如哈希比较
	return true
}

// getAbsolutePath 获取绝对路径并进行安全验证
func getAbsolutePath(path string) (string, error) {
	if path == "" {
		return "", fmt.Errorf("路径不能为空")
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(path)
	if err != nil {
		return "", fmt.Errorf("获取绝对路径失败: %v", err)
	}

	// 验证路径安全性
	if !isValidPath(absPath) {
		return "", fmt.Errorf("路径不安全: %s", absPath)
	}

	// 标准化路径格式：将Windows的反斜杠转换为正斜杠，保持与Linux一致
	standardPath := filepath.ToSlash(absPath)

	return standardPath, nil
}

// deleteDirectoryRecursive 递归删除目录
func deleteDirectoryRecursive(dirPath string, secure bool) (int, error) {
	var deletedCount int

	// 遍历目录内容
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return 0, fmt.Errorf("读取目录失败: %v", err)
	}

	// 删除目录中的所有内容
	for _, entry := range entries {
		entryPath := filepath.Join(dirPath, entry.Name())

		if entry.IsDir() {
			// 递归删除子目录
			subCount, err := deleteDirectoryRecursive(entryPath, secure)
			if err != nil {
				return deletedCount, err
			}
			deletedCount += subCount
		} else {
			// 删除文件
			var deleteErr error
			if secure {
				deleteErr = secureDelete(entryPath)
			} else {
				deleteErr = DelFile(entryPath)
			}

			if deleteErr != nil {
				return deletedCount, fmt.Errorf("删除文件 %s 失败: %v", entryPath, deleteErr)
			}
			deletedCount++
		}
	}

	// 删除空目录
	if err = os.Remove(dirPath); err != nil {
		return deletedCount, fmt.Errorf("删除目录 %s 失败: %v", dirPath, err)
	}
	deletedCount++

	return deletedCount, nil
}

// 检查是否是普通文件（非目录）
func isRegularFile(path string) (bool, error) {
	fi, err := os.Stat(path)
	if err != nil {
		return false, err
	}
	return fi.Mode().IsRegular(), nil
}

// copyFileWithAttributes 复制文件并保留属性
func copyFileWithAttributes(src, dst string, preserveAttrs bool) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = srcFile.WriteTo(dstFile)
	if err != nil {
		return err
	}

	if preserveAttrs {
		srcInfo, err := srcFile.Stat()
		if err != nil {
			return err
		}

		// 复制权限
		if err = os.Chmod(dst, srcInfo.Mode()); err != nil {
			return err
		}

		// 复制时间戳
		accessTime := srcInfo.ModTime()
		modTime := srcInfo.ModTime()
		return os.Chtimes(dst, accessTime, modTime)
	}

	return nil
}

// 高性能文件内容复制
func copyFileContents(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	// 使用32KB缓冲区提高性能
	buf := make([]byte, 32*1024)
	_, err = io.CopyBuffer(dstFile, srcFile, buf)
	if err != nil {
		return err
	}

	// 确保数据写入磁盘
	return dstFile.Sync()
}

func MoveFile(src string, dst string) (err error) {
	if dst == "" {
		return nil
	}
	// 使用标准化路径处理
	src, err = getAbsolutePath(src)
	if err != nil {
		return err
	}
	dst, err = getAbsolutePath(dst)
	if err != nil {
		return err
	}
	revoke := false
	dir := filepath.Dir(dst)
Redirect:
	_, err = os.Stat(dir)
	if err != nil {
		err = os.MkdirAll(dir, 0o755)
		if err != nil {
			return err
		}
		if !revoke {
			revoke = true
			goto Redirect
		}
	}
	return os.Rename(src, dst)
}

func DelFile(filePath string) error {
	return os.RemoveAll(filePath)
}

// encryptAES 使用AES-GCM加密数据
func encryptAES(data []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}
