package sys

import (
	"github.com/gin-gonic/gin"
	"net"
	"server/global"
	"time"
)

func ClearToken(c *gin.Context) {
	host, _, err := net.SplitHostPort(c.Request.Host)
	if err != nil {
		host = c.Request.Host
	}
	if net.ParseIP(host) != nil {
		c.<PERSON>("X-Token", "", -1, "/", "", false, true)
	} else {
		c.<PERSON><PERSON><PERSON>ie("X-Token", "", -1, "/", host, false, true)
	}

}

func SetToken(c *gin.Context, token string, maxAge int) {
	host, _, err := net.SplitHostPort(c.Request.Host)
	if err != nil {
		host = c.Request.Host
	}

	if net.ParseIP(host) != nil {
		c.SetCookie("X-Token", token, maxAge, "/", "", false, true)
	} else {
		c.SetCookie("X-Token", token, maxAge, "/", host, false, true)
	}
}

func GetToken(c *gin.Context) string {
	// 首先从请求头获取token
	token := c.Request.Header.Get("X-Token")
	if token == "" {
		// 然后从URL参数获取token
		token = c.Query("token")
	}
	if token == "" {
		// 最后从Cookie获取token
		j := NewJWT()
		token, _ = c.Cookie("X-Token")
		claims, err := j.ParseToken(token)
		if err != nil {
			global.LOG.Error("重新写入cookie token失败,未能成功解析token,请检查请求头是否存在X-Token且claims是否为规定结构")
			return token
		}
		SetToken(c, token, int((claims.ExpiresAt.Unix()-time.Now().Unix())/60))
	}
	return token
}

func GetClaims(c *gin.Context) (*CustomClaims, error) {
	token := GetToken(c)
	j := NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		global.LOG.Error("从Gin的Context中获取从jwt解析信息失败, 请检查请求头是否存在X-Token且claims是否为规定结构")
	}
	return claims, err
}

func LoginToken(user Login) (token string, claims CustomClaims, err error) {
	j := NewJWT()
	claims = j.CreateClaims(BaseClaims{
		UUID:     user.GetUUID(),
		ID:       user.GetUserID(),
		Username: user.GetUsername(),
	})
	token, err = j.CreateToken(claims)
	return
}
