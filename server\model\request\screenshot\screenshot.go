package screenshot

// ScreenshotRequest 截图请求
type ScreenshotRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	MonitorID    int    `json:"monitor_id"`    // 显示器ID (多显示器支持)
	MonitorIndex int    `json:"monitor_index"` // 显示器索引 (前端兼容)
	X            int    `json:"x"`             // 区域截图的X坐标
	Y            int    `json:"y"`             // 区域截图的Y坐标
	Width        int    `json:"width"`         // 区域截图的宽度
	Height       int    `json:"height"`        // 区域截图的高度
	Format       string `json:"format"`        // 图片格式: "png", "jpeg", "bmp"
	Quality      int    `json:"quality"`       // JPEG质量 (1-100)

}

// ExtendedScreenshotRequest 扩展的截图请求，添加流相关字段
type ExtendedScreenshotRequest struct {
	ScreenshotRequest
	FPS                      int  `json:"fps,omitempty"`                        // 帧率
	EnableDiffDetection      bool `json:"enable_diff_detection,omitempty"`      // 启用差异检测
	DiffThreshold            int  `json:"diff_threshold,omitempty"`             // 差异阈值百分比
	CPULimit                 int  `json:"cpu_limit,omitempty"`                  // CPU使用限制百分比
	BandwidthLimit           int  `json:"bandwidth_limit,omitempty"`            // 带宽限制 KB/s
	EnableMemoryOptimization bool `json:"enable_memory_optimization,omitempty"` // 启用内存优化
}

type MonitorListRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
}

// TimedScreenshotRequest 定时截图请求
type TimedScreenshotRequest struct {
	IntervalSeconds    int  `json:"interval_seconds"`     // 间隔秒数
	MaxCount           int  `json:"max_count"`            // 最大次数，0表示无限制
	EnableSmartTrigger bool `json:"enable_smart_trigger"` // 启用智能触发
	ScreenshotRequest       // 嵌入基础截图请求
}

// TimedScreenshotStatusResponse 定时截图状态响应
type TimedScreenshotStatusResponse struct {
	ID              uint64 `json:"id"`
	ClientID        uint   `json:"client_id"`
	Status          string `json:"status"`
	IntervalSeconds int    `json:"interval_seconds"`
	MaxCount        int    `json:"max_count"`
	CurrentCount    int    `json:"current_count"`
	IsActive        bool   `json:"is_active"`
	CreatedAt       string `json:"created_at"`
	LastExecutedAt  string `json:"last_executed_at,omitempty"`
}
