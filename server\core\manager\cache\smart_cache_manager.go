package cache

import (
	"container/list"
	"server/core/manager/workerpool"
	"server/global"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// CacheType 缓存类型枚举
type CacheType int

const (
	CacheTypeTask    CacheType = iota // 任务响应缓存（不会被LRU淘汰）
	CacheTypeCommand                  // 命令输出缓存（可以被LRU淘汰）
	CacheTypeStream                   // 流数据缓存（短期缓存）
	CacheTypeGeneral                  // 通用缓存（可以被LRU淘汰）
)

// CacheItem 缓存项
type CacheItem struct {
	Key         string      `json:"key"`
	Value       interface{} `json:"value"`
	Type        CacheType   `json:"type"`
	ExpireTime  time.Time   `json:"expire_time"`
	AccessTime  time.Time   `json:"access_time"`
	CreateTime  time.Time   `json:"create_time"`
	AccessCount int64       `json:"access_count"`

	// LRU链表节点
	element *list.Element
}

// SmartCacheManager 智能缓存管理器
type SmartCacheManager struct {
	// 缓存存储
	items map[string]*CacheItem
	mu    sync.RWMutex

	// LRU链表（只用于可淘汰的缓存类型）
	lruList *list.List

	// 配置
	maxSize       int           // 最大缓存项数
	maxMemory     int64         // 最大内存使用（字节）
	cleanInterval time.Duration // 清理间隔

	// 统计信息
	stats *CacheStats

	// 控制
	stopChan chan struct{}
}

// CacheStats 缓存统计信息
type CacheStats struct {
	// 基础统计 - 使用原子操作
	TotalItems   int64 `json:"total_items"`
	TaskItems    int64 `json:"task_items"`    // 任务缓存项数
	CommandItems int64 `json:"command_items"` // 命令缓存项数
	StreamItems  int64 `json:"stream_items"`  // 流缓存项数
	GeneralItems int64 `json:"general_items"` // 通用缓存项数

	// 性能统计
	HitCount    int64 `json:"hit_count"`    // 命中次数
	MissCount   int64 `json:"miss_count"`   // 未命中次数
	EvictCount  int64 `json:"evict_count"`  // 淘汰次数
	ExpireCount int64 `json:"expire_count"` // 过期清理次数

	// 内存统计
	MemoryUsage    int64 `json:"memory_usage"`     // 当前内存使用
	MaxMemoryUsage int64 `json:"max_memory_usage"` // 最大内存使用

	// 时间统计
	StartTime     int64 `json:"start_time"`
	LastCleanTime int64 `json:"last_clean_time"`
}

// NewSmartCacheManager 创建智能缓存管理器
func NewSmartCacheManager(maxSize int, maxMemory int64) *SmartCacheManager {
	manager := &SmartCacheManager{
		items:         make(map[string]*CacheItem),
		lruList:       list.New(),
		maxSize:       maxSize,
		maxMemory:     maxMemory,
		cleanInterval: 5 * time.Minute,
		stopChan:      make(chan struct{}),
		stats: &CacheStats{
			StartTime: time.Now().UnixNano(),
		},
	}

	// 启动清理器
	go manager.startCleaner()

	global.LOG.Info("智能缓存管理器创建成功",
		zap.Int("maxSize", maxSize),
		zap.Int64("maxMemory", maxMemory))

	return manager
}

// Set 设置缓存项
func (scm *SmartCacheManager) Set(key string, value interface{}, cacheType CacheType, ttl time.Duration) {
	scm.mu.Lock()
	defer scm.mu.Unlock()

	now := time.Now()
	expireTime := now.Add(ttl)

	// 检查是否已存在
	if existingItem, exists := scm.items[key]; exists {
		// 更新现有项
		existingItem.Value = value
		existingItem.ExpireTime = expireTime
		existingItem.AccessTime = now
		atomic.AddInt64(&existingItem.AccessCount, 1)

		// 如果是LRU管理的类型，移到链表头部
		if scm.isLRUManaged(cacheType) && existingItem.element != nil {
			scm.lruList.MoveToFront(existingItem.element)
		}
		return
	}

	// 创建新项
	item := &CacheItem{
		Key:         key,
		Value:       value,
		Type:        cacheType,
		ExpireTime:  expireTime,
		AccessTime:  now,
		CreateTime:  now,
		AccessCount: 1,
	}

	// 如果是LRU管理的类型，添加到链表头部
	if scm.isLRUManaged(cacheType) {
		item.element = scm.lruList.PushFront(item)
	}

	scm.items[key] = item

	// 更新统计
	atomic.AddInt64(&scm.stats.TotalItems, 1)
	scm.updateTypeStats(cacheType, 1)

	// 检查是否需要淘汰
	scm.evictIfNeeded()
}

// Get 获取缓存项
func (scm *SmartCacheManager) Get(key string) (interface{}, bool) {
	scm.mu.RLock()
	item, exists := scm.items[key]
	scm.mu.RUnlock()

	if !exists {
		atomic.AddInt64(&scm.stats.MissCount, 1)
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.ExpireTime) {
		scm.Delete(key)
		atomic.AddInt64(&scm.stats.MissCount, 1)
		return nil, false
	}

	// 更新访问信息
	scm.mu.Lock()
	item.AccessTime = time.Now()
	atomic.AddInt64(&item.AccessCount, 1)

	// 如果是LRU管理的类型，移到链表头部
	if scm.isLRUManaged(item.Type) && item.element != nil {
		scm.lruList.MoveToFront(item.element)
	}
	scm.mu.Unlock()

	atomic.AddInt64(&scm.stats.HitCount, 1)
	return item.Value, true
}

// Delete 删除缓存项
func (scm *SmartCacheManager) Delete(key string) {
	scm.mu.Lock()
	defer scm.mu.Unlock()

	if item, exists := scm.items[key]; exists {
		// 从LRU链表中移除
		if item.element != nil {
			scm.lruList.Remove(item.element)
		}

		delete(scm.items, key)

		// 更新统计
		atomic.AddInt64(&scm.stats.TotalItems, -1)
		scm.updateTypeStats(item.Type, -1)
	}
}

// isLRUManaged 判断缓存类型是否由LRU管理
func (scm *SmartCacheManager) isLRUManaged(cacheType CacheType) bool {
	switch cacheType {
	case CacheTypeTask:
		return false // 任务缓存不被LRU淘汰
	case CacheTypeCommand, CacheTypeStream, CacheTypeGeneral:
		return true // 其他类型可以被LRU淘汰
	default:
		return true
	}
}

// updateTypeStats 更新类型统计
func (scm *SmartCacheManager) updateTypeStats(cacheType CacheType, delta int64) {
	switch cacheType {
	case CacheTypeTask:
		atomic.AddInt64(&scm.stats.TaskItems, delta)
	case CacheTypeCommand:
		atomic.AddInt64(&scm.stats.CommandItems, delta)
	case CacheTypeStream:
		atomic.AddInt64(&scm.stats.StreamItems, delta)
	case CacheTypeGeneral:
		atomic.AddInt64(&scm.stats.GeneralItems, delta)
	}
}

// evictIfNeeded 根据需要进行淘汰
func (scm *SmartCacheManager) evictIfNeeded() {
	// 检查数量限制
	if len(scm.items) > scm.maxSize {
		scm.evictLRU()
	}

	// TODO: 检查内存限制（需要实现内存使用计算）
}

// evictLRU 淘汰LRU项
func (scm *SmartCacheManager) evictLRU() {
	// 从链表尾部开始淘汰（最少使用的）
	for scm.lruList.Len() > 0 && len(scm.items) > scm.maxSize {
		element := scm.lruList.Back()
		if element == nil {
			break
		}

		item := element.Value.(*CacheItem)

		// 不淘汰任务类型的缓存
		if item.Type == CacheTypeTask {
			// 如果链表中都是任务类型，停止淘汰
			break
		}

		scm.lruList.Remove(element)
		delete(scm.items, item.Key)

		// 更新统计
		atomic.AddInt64(&scm.stats.TotalItems, -1)
		atomic.AddInt64(&scm.stats.EvictCount, 1)
		scm.updateTypeStats(item.Type, -1)

		global.LOG.Debug("LRU淘汰缓存项",
			zap.String("key", item.Key),
			zap.Int("type", int(item.Type)))
	}
}

// startCleaner 启动清理器
func (scm *SmartCacheManager) startCleaner() {
	ticker := time.NewTicker(scm.cleanInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			scm.cleanExpired()
		case <-scm.stopChan:
			return
		}
	}
}

// cleanExpired 清理过期项
func (scm *SmartCacheManager) cleanExpired() {
	// 🚀 使用工作池执行清理任务，避免阻塞
	task := workerpool.NewGeneralTask("cache_clean_expired", func() error {
		scm.mu.Lock()
		defer scm.mu.Unlock()

		now := time.Now()
		expiredKeys := make([]string, 0)

		// 收集过期的键
		for key, item := range scm.items {
			if now.After(item.ExpireTime) {
				expiredKeys = append(expiredKeys, key)
			}
		}

		// 删除过期项
		for _, key := range expiredKeys {
			if item, exists := scm.items[key]; exists {
				// 从LRU链表中移除
				if item.element != nil {
					scm.lruList.Remove(item.element)
				}

				delete(scm.items, key)

				// 更新统计
				atomic.AddInt64(&scm.stats.TotalItems, -1)
				atomic.AddInt64(&scm.stats.ExpireCount, 1)
				scm.updateTypeStats(item.Type, -1)
			}
		}

		atomic.StoreInt64(&scm.stats.LastCleanTime, now.UnixNano())

		if len(expiredKeys) > 0 {
			global.LOG.Debug("清理过期缓存项",
				zap.Int("expiredCount", len(expiredKeys)))
		}

		return nil
	})

	if err := workerpool.SubmitGeneralTask(task); err != nil {
		global.LOG.Error("提交缓存清理任务失败", zap.Error(err))
	}
}

// GetStats 获取缓存统计信息
func (scm *SmartCacheManager) GetStats() *CacheStats {
	scm.mu.RLock()
	defer scm.mu.RUnlock()

	stats := &CacheStats{
		TotalItems:     atomic.LoadInt64(&scm.stats.TotalItems),
		TaskItems:      atomic.LoadInt64(&scm.stats.TaskItems),
		CommandItems:   atomic.LoadInt64(&scm.stats.CommandItems),
		StreamItems:    atomic.LoadInt64(&scm.stats.StreamItems),
		GeneralItems:   atomic.LoadInt64(&scm.stats.GeneralItems),
		HitCount:       atomic.LoadInt64(&scm.stats.HitCount),
		MissCount:      atomic.LoadInt64(&scm.stats.MissCount),
		EvictCount:     atomic.LoadInt64(&scm.stats.EvictCount),
		ExpireCount:    atomic.LoadInt64(&scm.stats.ExpireCount),
		MemoryUsage:    atomic.LoadInt64(&scm.stats.MemoryUsage),
		MaxMemoryUsage: atomic.LoadInt64(&scm.stats.MaxMemoryUsage),
		StartTime:      atomic.LoadInt64(&scm.stats.StartTime),
		LastCleanTime:  atomic.LoadInt64(&scm.stats.LastCleanTime),
	}

	return stats
}

// Stop 停止缓存管理器
func (scm *SmartCacheManager) Stop() {
	close(scm.stopChan)
	global.LOG.Info("智能缓存管理器已停止")
}

// 全局智能缓存管理器
var GlobalSmartCache *SmartCacheManager

// InitGlobalSmartCache 初始化全局智能缓存管理器
func InitGlobalSmartCache() {
	startTime := time.Now()

	// 🚀 优化：根据系统内存动态调整缓存大小
	maxSize, maxMemory := calculateOptimalCacheSize()

	GlobalSmartCache = NewSmartCacheManager(maxSize, maxMemory)

	global.LOG.Info("✅ 全局智能缓存管理器初始化完成",
		zap.Int("maxSize", maxSize),
		zap.Int64("maxMemory", maxMemory),
		zap.Duration("耗时", time.Since(startTime)))
}

// calculateOptimalCacheSize 根据系统资源计算最优缓存大小
func calculateOptimalCacheSize() (maxSize int, maxMemory int64) {
	// 默认值
	maxSize = 10000
	maxMemory = 100 * 1024 * 1024 // 100MB

	// 🚀 优化：可以根据系统内存动态调整
	// 这里可以添加系统内存检测逻辑
	// 例如：如果系统内存 > 8GB，增加缓存大小

	return maxSize, maxMemory
}
