<template>
  <div class="user-manage-container">
    <!-- 搜索表单 -->
    <div class="search-form">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="用户名">
          <a-input v-model:value="searchForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="角色">
          <a-select v-model:value="searchForm.roleName" placeholder="请选择角色" allowClear>
            <a-select-option value="superadmin">超级管理员</a-select-option>
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="user">普通用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model:value="searchForm.enable" placeholder="请选择状态" allowClear>
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="2">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            <SearchOutlined />
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            <ReloadOutlined />
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-button type="primary" @click="showCreateModal">
        <PlusOutlined />
        新增用户
      </a-button>
    </div>

    <!-- 用户列表 -->
    <a-table
      :columns="columns"
      :data-source="userList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'roleName'">
          <a-tag :color="getRoleColor(record.role?.roleName)">
            {{ getRoleDisplayName(record.role?.roleName) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'enable'">
          <a-tag :color="record.enable === 1 ? 'green' : 'red'">
            {{ record.enable === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <!-- 超级管理员不显示任何操作按钮 -->
            <template v-if="record.role?.roleName !== 'superadmin'">
              <a-button type="link" size="small" @click="showEditModal(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                :danger="record.enable === 1"
                @click="toggleUserStatus(record)"
              >
                {{ record.enable === 1 ? '禁用' : '启用' }}
              </a-button>
              <a-popconfirm
                title="确定要删除这个用户吗？"
                @confirm="handleDelete(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </template>
            <!-- 超级管理员显示提示 -->
            <template v-else>
              <a-tooltip title="超级管理员不可操作">
                <a-tag color="gold">系统保护</a-tag>
              </a-tooltip>
            </template>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 创建/编辑用户弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="formData.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="密码" name="password">
          <a-input-password 
            v-model:value="formData.password" 
            :placeholder="isEdit ? '留空表示不修改密码' : '请输入密码'" 
          />
        </a-form-item>
        <a-form-item label="角色" name="roleName">
          <a-select v-model:value="formData.roleName" placeholder="请选择角色">
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="user">普通用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="enable">
          <a-radio-group v-model:value="formData.enable">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="2">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import * as userManageApi from '@/api/userManage'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const searchForm = reactive({
  username: '',
  roleName: '',
  enable: null
})

const userList = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

const formData = reactive({
  id: null,
  username: '',
  password: '',
  roleName: '',
  enable: 1
})

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '角色',
    dataIndex: ['role', 'roleName'],
    key: 'roleName'
  },
  {
    title: '状态',
    dataIndex: 'enable',
    key: 'enable',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符' }
  ],
  password: [
    { 
      validator: (rule, value) => {
        if (!isEdit.value && !value) {
          return Promise.reject('请输入密码')
        }
        if (value && (value.length < 6 || value.length > 50)) {
          return Promise.reject('密码长度为6-50个字符')
        }
        return Promise.resolve()
      }
    }
  ],
  roleName: [
    { required: true, message: '请选择角色' }
  ]
}

// 获取用户列表
const getUserList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      username: searchForm.username || '',
      roleName: searchForm.roleName || '',
      enable: searchForm.enable
    }
    
    const res = await userManageApi.getUserList(params)
    userList.value = res.data.list
    pagination.total = res.data.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getUserList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    roleName: '',
    enable: null
  })
  pagination.current = 1
  getUserList()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  getUserList()
}

// 显示创建弹窗
const showCreateModal = () => {
  isEdit.value = false
  modalVisible.value = true
  resetFormData()
}

// 显示编辑弹窗
const showEditModal = (record) => {
  // 禁止编辑超级管理员
  if (record.role?.roleName === 'superadmin') {
    message.warning('超级管理员不可编辑')
    return
  }

  isEdit.value = true
  modalVisible.value = true
  Object.assign(formData, {
    id: record.id,
    username: record.username,
    password: '',
    roleName: record.role?.roleName || '',
    enable: record.enable
  })
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: null,
    username: '',
    password: '',
    roleName: '',
    enable: 1
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 前端验证：禁止创建或修改为超级管理员
    if (formData.roleName === 'superadmin') {
      message.error('不允许创建或修改为超级管理员')
      return
    }

    submitLoading.value = true

    if (isEdit.value) {
      await userManageApi.updateUser(formData)
      message.success('更新用户成功')
    } else {
      await userManageApi.createUser(formData)
      message.success('创建用户成功')
    }

    modalVisible.value = false
    getUserList()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      message.error(error.response.data.message)
    }
  } finally {
    submitLoading.value = false
  }
}

// 取消弹窗
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 切换用户状态
const toggleUserStatus = async (record) => {
  try {
    const newStatus = record.enable === 1 ? 2 : 1
    await userManageApi.changeUserStatus({
      id: record.id,
      enable: newStatus
    })
    message.success(`${newStatus === 1 ? '启用' : '禁用'}用户成功`)
    getUserList()
  } catch (error) {
    console.error('修改用户状态失败:', error)
    message.error('修改用户状态失败')
  }
}

// 删除用户
const handleDelete = async (id) => {
  try {
    await userManageApi.deleteUser(id)
    message.success('删除用户成功')
    getUserList()
  } catch (error) {
    console.error('删除用户失败:', error)
    message.error('删除用户失败')
  }
}

// 角色显示名称映射
const getRoleDisplayName = (roleName) => {
  const roleMap = {
    'superadmin': '超级管理员',
    'admin': '管理员',
    'user': '普通用户'
  }
  return roleMap[roleName] || roleName || '-'
}

// 角色颜色映射
const getRoleColor = (roleName) => {
  const colorMap = {
    'superadmin': 'red',
    'admin': 'blue',
    'user': 'green'
  }
  return colorMap[roleName] || 'default'
}

// 组件挂载时获取数据
onMounted(() => {
  getUserList()
})
</script>

<style scoped>
.user-manage-container {
  padding: 24px;
}

.search-form {
  background: #fff;
  padding: 24px;
  border-radius: 6px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  margin-bottom: 16px;
}
</style>
