package sys

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// NotificationType 通知类型枚举
type NotificationType string

const (
	// 客户端相关通知
	NotificationClientOnline  NotificationType = "client_online"
	NotificationClientOffline NotificationType = "client_offline"
	NotificationClientDeleted NotificationType = "client_deleted"

	// 监听器相关通知
	NotificationListenerCreated NotificationType = "listener_created"
	NotificationListenerClosed  NotificationType = "listener_closed"
	NotificationListenerDeleted NotificationType = "listener_deleted"

	// 服务器相关通知（预留）
	NotificationServerStarted NotificationType = "server_started"
	NotificationServerStopped NotificationType = "server_stopped"
	NotificationServerError   NotificationType = "server_error"

	// 系统相关通知（预留）
	NotificationSystemAlert   NotificationType = "system_alert"
	NotificationSystemWarning NotificationType = "system_warning"
)

// NotificationLevel 通知级别
type NotificationLevel string

const (
	NotificationLevelInfo    NotificationLevel = "info"
	NotificationLevelSuccess NotificationLevel = "success"
	NotificationLevelWarning NotificationLevel = "warning"
	NotificationLevelError   NotificationLevel = "error"
)

// SysNotification 系统通知模型
type SysNotification struct {
	ID        uint                 `json:"id" gorm:"primarykey"`
	UserID    uint                 `json:"userId" gorm:"not null;index;comment:接收用户ID"`
	Type      NotificationType     `json:"type" gorm:"not null;index;comment:通知类型"`
	Level     NotificationLevel    `json:"level" gorm:"not null;default:'info';comment:通知级别"`
	Title     string               `json:"title" gorm:"not null;size:255;comment:通知标题"`
	Content   string               `json:"content" gorm:"not null;size:1000;comment:通知内容"`
	Data      string               `json:"data" gorm:"type:text;comment:JSON格式的额外数据"`
	ReadAt    *time.Time           `json:"readAt" gorm:"comment:已读时间"`
	CreatedAt time.Time            `json:"createdAt"`
	UpdatedAt time.Time            `json:"updatedAt"`
	DeletedAt gorm.DeletedAt       `json:"-" gorm:"index"`

	// 关联用户信息（用于查询时加载）
	User SysUser `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (SysNotification) TableName() string {
	return "sys_notifications"
}

// NotificationData 通知数据结构（用于JSON序列化）
type NotificationData struct {
	ClientID     *uint   `json:"clientId,omitempty"`
	ClientName   *string `json:"clientName,omitempty"`
	ClientIP     *string `json:"clientIp,omitempty"`
	ListenerID   *uint   `json:"listenerId,omitempty"`
	ListenerName *string `json:"listenerName,omitempty"`
	ListenerPort *uint16 `json:"listenerPort,omitempty"`
	ExtraInfo    map[string]interface{} `json:"extraInfo,omitempty"`
}

// SetData 设置通知数据
func (n *SysNotification) SetData(data NotificationData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	n.Data = string(jsonData)
	return nil
}

// GetData 获取通知数据
func (n *SysNotification) GetData() (NotificationData, error) {
	var data NotificationData
	if n.Data == "" {
		return data, nil
	}
	err := json.Unmarshal([]byte(n.Data), &data)
	return data, err
}

// IsRead 检查是否已读
func (n *SysNotification) IsRead() bool {
	return n.ReadAt != nil
}

// MarkAsRead 标记为已读
func (n *SysNotification) MarkAsRead() {
	now := time.Now()
	n.ReadAt = &now
}

// GetLevelIcon 获取级别对应的图标
func (n *SysNotification) GetLevelIcon() string {
	switch n.Level {
	case NotificationLevelSuccess:
		return "check-circle"
	case NotificationLevelWarning:
		return "exclamation-circle"
	case NotificationLevelError:
		return "close-circle"
	default:
		return "info-circle"
	}
}

// GetLevelColor 获取级别对应的颜色
func (n *SysNotification) GetLevelColor() string {
	switch n.Level {
	case NotificationLevelSuccess:
		return "#52c41a"
	case NotificationLevelWarning:
		return "#faad14"
	case NotificationLevelError:
		return "#ff4d4f"
	default:
		return "#1890ff"
	}
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	Type    NotificationType
	Level   NotificationLevel
	Title   string
	Content string
}

// GetNotificationTemplate 获取通知模板
func GetNotificationTemplate(notificationType NotificationType) NotificationTemplate {
	templates := map[NotificationType]NotificationTemplate{
		NotificationClientOnline: {
			Type:    NotificationClientOnline,
			Level:   NotificationLevelSuccess,
			Title:   "客户端上线",
			Content: "客户端 {{.ClientName}} ({{.ClientIP}}) 已成功连接",
		},
		NotificationClientOffline: {
			Type:    NotificationClientOffline,
			Level:   NotificationLevelWarning,
			Title:   "客户端离线",
			Content: "客户端 {{.ClientName}} ({{.ClientIP}}) 已断开连接",
		},
		NotificationClientDeleted: {
			Type:    NotificationClientDeleted,
			Level:   NotificationLevelInfo,
			Title:   "客户端删除",
			Content: "客户端 {{.ClientName}} 已被删除",
		},
		NotificationListenerCreated: {
			Type:    NotificationListenerCreated,
			Level:   NotificationLevelSuccess,
			Title:   "监听器创建",
			Content: "监听器 {{.ListenerName}} (端口: {{.ListenerPort}}) 已创建",
		},
		NotificationListenerClosed: {
			Type:    NotificationListenerClosed,
			Level:   NotificationLevelWarning,
			Title:   "监听器关闭",
			Content: "监听器 {{.ListenerName}} (端口: {{.ListenerPort}}) 已关闭",
		},
		NotificationListenerDeleted: {
			Type:    NotificationListenerDeleted,
			Level:   NotificationLevelInfo,
			Title:   "监听器删除",
			Content: "监听器 {{.ListenerName}} 已被删除",
		},
	}

	if template, exists := templates[notificationType]; exists {
		return template
	}

	// 默认模板
	return NotificationTemplate{
		Type:    notificationType,
		Level:   NotificationLevelInfo,
		Title:   "系统通知",
		Content: "收到新的系统通知",
	}
}

// NotificationStats 通知统计信息
type NotificationStats struct {
	Total  int64 `json:"total"`
	Unread int64 `json:"unread"`
	Read   int64 `json:"read"`
}
