//go:build darwin
// +build darwin

package common

import (
	"bufio"
	"bytes"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"

	"golang.org/x/sys/unix"
)

// DiskListRequest 磁盘列表请求
type DiskListRequest struct {
	TaskID         uint64 `json:"task_id"`         // 任务ID
	IncludeDetails bool   `json:"include_details"` // 是否包含详细信息
}

// DiskInfo 磁盘信息
type DiskInfo struct {
	MountPoint   string  `json:"mount_point"`   // 挂载点
	FileSystem   string  `json:"file_system"`   // 文件系统类型
	Device       string  `json:"device"`        // 设备名称
	TotalSize    int64   `json:"total_size"`    // 总大小（字节）
	UsedSize     int64   `json:"used_size"`     // 已使用大小（字节）
	AvailSize    int64   `json:"avail_size"`    // 可用大小（字节）
	UsagePercent float64 `json:"usage_percent"` // 使用百分比
	IsReadOnly   bool    `json:"is_read_only"`  // 是否只读
	Label        string  `json:"label"`         // 磁盘标签
	Icon         string  `json:"icon"`          // 图标类型
}

// DiskListResponse 磁盘列表响应
type DiskListResponse struct {
	TaskID    uint64     `json:"task_id"`    // 任务ID
	Success   bool       `json:"success"`    // 操作是否成功
	DiskInfos []DiskInfo `json:"disk_infos"` // 磁盘信息列表
	Error     string     `json:"error"`      // 错误信息
}

// MountInfo 挂载信息结构
type MountInfo struct {
	Device     string
	MountPoint string
	FileSystem string
	Options    string
}

// DiskUsage 磁盘使用情况
type DiskUsage struct {
	Total        int64
	Used         int64
	Avail        int64
	UsagePercent float64
	IsReadOnly   bool
}

// handleDiskList 处理磁盘列表请求
func (cm *ConnectionManager) handleDiskList(packet *Packet) {
	log.Printf("[磁盘处理] 收到磁盘列表请求")
	var req DiskListRequest
	respErr := &DiskListResponse{
		TaskID:  0, // 初始化为0，反序列化后更新
		Error:   "",
		Success: false,
	}

	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("[磁盘处理] 磁盘列表请求反序列化失败: %v", err)
		respErr.TaskID = req.TaskID // 反序列化后设置TaskID
		respErr.Error = fmt.Sprintf("磁盘列表请求反序列化失败: %v", err)
		cm.sendResp(Dir, DiskList, respErr)
		return
	}

	log.Printf("[磁盘处理] 请求参数解析成功，包含详细信息: %v", req.IncludeDetails)

	// 获取磁盘列表
	resp := getUnixDiskList(&req)
	log.Printf("[磁盘处理] 磁盘列表获取完成，成功: %v, 磁盘数量: %d", resp.Success, len(resp.DiskInfos))
	if !resp.Success {
		log.Printf("[磁盘处理] 磁盘列表获取失败: %s", resp.Error)
	}

	cm.sendResp(Dir, DiskList, resp)
}

// getUnixDiskList 获取Unix系统的磁盘列表 (已修复)
func getUnixDiskList(req *DiskListRequest) *DiskListResponse {
	log.Printf("[磁盘列表] 开始获取Unix磁盘列表，包含详细信息: %v", req.IncludeDetails)
	resp := &DiskListResponse{
		TaskID:    req.TaskID,
		Success:   true,
		DiskInfos: []DiskInfo{},
		Error:     "",
	}

	// 1. 读取所有挂载信息
	mountInfos, err := readMountInfo()
	if err != nil {
		log.Printf("[磁盘列表] 读取挂载信息失败: %v", err)
		resp.Error = fmt.Sprintf("读取挂载信息失败: %v", err)
		resp.Success = false
		return resp
	}
	log.Printf("[磁盘列表] 成功读取到 %d 个挂载点", len(mountInfos))

	// 2. 过滤和处理挂载点
	for _, mountInfo := range mountInfos {
		// 【已修复】shouldSkipMountPoint 现在需要整个 MountInfo 结构体
		if shouldSkipMountPoint(mountInfo) {
			log.Printf("[磁盘列表] 跳过系统或虚拟挂载点: %s (类型: %s)", mountInfo.MountPoint, mountInfo.FileSystem)
			continue
		}

		log.Printf("[磁盘列表] 处理挂载点: %s, 设备: %s, 文件系统: %s", mountInfo.MountPoint, mountInfo.Device, mountInfo.FileSystem)

		diskInfo := DiskInfo{
			MountPoint: mountInfo.MountPoint,
			FileSystem: mountInfo.FileSystem,
			Device:     mountInfo.Device,
			Icon:       getDiskIcon(mountInfo.MountPoint, mountInfo.FileSystem),
			Label:      getDiskLabel(mountInfo.Device),
		}

		// 如果需要详细信息，获取磁盘使用情况
		if req.IncludeDetails {
			log.Printf("[磁盘列表] 获取挂载点 %s 的使用情况", mountInfo.MountPoint)
			// 【已修复】getDiskUsage 现在需要传入挂载选项 mountInfo.Options
			if usage, err := getDiskUsage(mountInfo.MountPoint, mountInfo.Options); err == nil {
				diskInfo.TotalSize = usage.Total
				diskInfo.UsedSize = usage.Used
				diskInfo.AvailSize = usage.Avail
				diskInfo.UsagePercent = usage.UsagePercent
				diskInfo.IsReadOnly = usage.IsReadOnly
				log.Printf("[磁盘列表] 挂载点 %s 使用情况: 总计=%d MB, 已用=%d MB, 可用=%d MB, 使用率=%.2f%%, 只读=%v",
					mountInfo.MountPoint, usage.Total/(1024*1024), usage.Used/(1024*1024), usage.Avail/(1024*1024), usage.UsagePercent, usage.IsReadOnly)
			} else {
				log.Printf("[磁盘列表] 获取挂载点 %s 使用情况失败: %v", mountInfo.MountPoint, err)
			}
		} else {
			log.Printf("[磁盘列表] 跳过获取挂载点 %s 的详细使用情况", mountInfo.MountPoint)
		}

		resp.DiskInfos = append(resp.DiskInfos, diskInfo)
		log.Printf("[磁盘列表] 成功添加磁盘: %s (%s)", diskInfo.MountPoint, diskInfo.Label)
	}

	// 3. 【已修复】如果过滤后没有磁盘，提供一个更智能的根目录回退
	if len(resp.DiskInfos) == 0 && len(mountInfos) > 0 {
		log.Printf("[磁盘列表] 没有找到任何用户相关的挂载点，尝试添加根目录作为默认磁盘")

		// 从原始列表中找到根 ("/") 的挂载信息
		var rootMountInfo *MountInfo
		for i := range mountInfos {
			if mountInfos[i].MountPoint == "/" {
				rootMountInfo = &mountInfos[i]
				break
			}
		}

		// 如果找到了根目录的信息
		if rootMountInfo != nil {
			log.Printf("[磁盘列表] 找到根目录挂载信息，设备: %s, 文件系统: %s", rootMountInfo.Device, rootMountInfo.FileSystem)
			rootDisk := DiskInfo{
				MountPoint: rootMountInfo.MountPoint,
				FileSystem: rootMountInfo.FileSystem,
				Device:     rootMountInfo.Device,
				Icon:       getDiskIcon(rootMountInfo.MountPoint, rootMountInfo.FileSystem),
				Label:      getDiskLabel(rootMountInfo.Device),
			}

			if req.IncludeDetails {
				log.Printf("[磁盘列表] 获取根目录的使用情况")
				// 使用找到的根目录选项来调用 getDiskUsage
				if usage, err := getDiskUsage(rootMountInfo.MountPoint, rootMountInfo.Options); err == nil {
					rootDisk.TotalSize = usage.Total
					rootDisk.UsedSize = usage.Used
					rootDisk.AvailSize = usage.Avail
					rootDisk.UsagePercent = usage.UsagePercent
					rootDisk.IsReadOnly = usage.IsReadOnly
					log.Printf("[磁盘列表] 根目录使用情况: 总计=%d MB, 已用=%d MB, 可用=%d MB, 使用率=%.2f%%, 只读=%v",
						usage.Total/(1024*1024), usage.Used/(1024*1024), usage.Avail/(1024*1024), usage.UsagePercent, usage.IsReadOnly)
				} else {
					log.Printf("[磁盘列表] 获取根目录使用情况失败: %v", err)
				}
			}
			resp.DiskInfos = append(resp.DiskInfos, rootDisk)
			log.Printf("[磁盘列表] 成功添加根目录磁盘")
		} else {
			log.Printf("[磁盘列表] 警告: 在原始挂载列表中未找到根目录 ('/')")
		}
	}

	log.Printf("[磁盘列表] Unix磁盘列表获取完成，共找到 %d 个磁盘", len(resp.DiskInfos))
	return resp
}

// ----------------------------------------------------------------------------
// 2. 挂载点过滤 (优化)
// ----------------------------------------------------------------------------

// shouldSkipMountPoint 优化为基于文件系统类型和挂载选项进行过滤
func shouldSkipMountPoint(info MountInfo) bool {
	// 基于文件系统类型过滤，这比路径前缀更可靠
	fsTypesToSkip := map[string]bool{
		"autofs":      true,
		"binfmt_misc": true,
		"bpf":         true,
		"cgroup":      true,
		"cgroup2":     true,
		"configfs":    true,
		"debugfs":     true,
		"devpts":      true,
		"devtmpfs":    true,
		"hugetlbfs":   true,
		"mqueue":      true,
		"proc":        true,
		"pstore":      true,
		"securityfs":  true,
		"squashfs":    true, // 通常用于 snap 包
		"sysfs":       true,
		"tmpfs":       true,
		"tracefs":     true,
		"overlay":     true, // Docker a.u.
	}

	if fsTypesToSkip[info.FileSystem] {
		return true
	}

	// 在macOS上，'nobrowse'标志是系统不希望用户看到的明确信号. [2]
	if runtime.GOOS == "darwin" && strings.Contains(info.Options, "nobrowse") {
		return true
	}

	// 过滤掉一些特定的系统路径
	pathsToSkip := []string{"/var/lib/docker"}
	for _, p := range pathsToSkip {
		if strings.HasPrefix(info.MountPoint, p) {
			return true
		}
	}

	// 如果设备名不是以/dev/开头，通常是虚拟的或特殊的挂载点 (例如 'map', 'host')
	if !strings.HasPrefix(info.Device, "/dev/") && info.Device != "tmpfs" {
		if info.FileSystem != "nfs" && info.FileSystem != "cifs" && !strings.Contains(info.FileSystem, "smb") {
			return true
		}
	}

	return false
}

// ----------------------------------------------------------------------------
// 3. 磁盘使用情况 (重构)
// ----------------------------------------------------------------------------

// getDiskUsage 被极大简化，只负责计算，不再获取挂载信息
// 它现在依赖外部传入的挂载选项来判断只读状态
func getDiskUsage(path string, mountOptions string) (*DiskUsage, error) {
	var stat unix.Statfs_t
	if err := unix.Statfs(path, &stat); err != nil {
		return nil, err
	}

	total := int64(stat.Blocks) * int64(stat.Bsize)
	avail := int64(stat.Bavail) * int64(stat.Bsize)
	used := total - (int64(stat.Bfree) * int64(stat.Bsize)) // 使用Bfree更准确计算已用空间

	usagePercent := 0.0
	if total > 0 {
		usagePercent = float64(used) / float64(total) * 100
	}

	return &DiskUsage{
		Total:        total,
		Used:         used,
		Avail:        avail,
		UsagePercent: usagePercent,
		// 只读状态直接从传入的选项判断，不再重新扫描
		IsReadOnly: strings.Contains(","+mountOptions+",", ",ro,"),
	}, nil
}

// ----------------------------------------------------------------------------
// 4. 磁盘标签获取 (优化)
// ----------------------------------------------------------------------------

// getDiskLabel 总入口
func getDiskLabel(device string) string {
	if runtime.GOOS == "darwin" {
		return getMacOSDiskLabel(device)
	}
	return getDarwinDiskLabel(device)
}

// getDarwinDiskLabel 从 /dev/disk/by-label 获取标签
func getDarwinDiskLabel(device string) string {
	labelDir := "/dev/disk/by-label/"
	entries, err := os.ReadDir(labelDir)
	if err != nil {
		return filepath.Base(device) // 回退到设备名
	}

	for _, entry := range entries {
		linkPath := filepath.Join(labelDir, entry.Name())
		target, err := os.Readlink(linkPath)
		if err != nil {
			continue
		}
		// 确保解析的链接是绝对路径
		if !filepath.IsAbs(target) {
			target = filepath.Join(filepath.Dir(linkPath), target)
		}
		// 检查链接目标是否与我们的设备匹配
		if filepath.Clean(target) == filepath.Clean(device) {
			return entry.Name()
		}
	}

	return filepath.Base(device) // 如果没找到标签，回退
}

// getMacOSDiskLabel 使用 diskutil 获取卷标，这是最可靠的方式
func getMacOSDiskLabel(device string) string {
	// diskutil info 是获取 macOS 磁盘详细信息的标准命令
	cmd := exec.Command("diskutil", "info", device)
	output, err := cmd.Output()
	if err != nil {
		return filepath.Base(device) // 失败则回退
	}

	scanner := bufio.NewScanner(bytes.NewReader(output))
	for scanner.Scan() {
		line := scanner.Text()
		// 寻找 "Volume Name:" 这一行
		if strings.Contains(line, "Volume Name:") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				// 清理并返回标签
				label := strings.TrimSpace(parts[1])
				if label != "Not applicable" {
					return label
				}
			}
		}
	}

	return filepath.Base(device)
}

// ----------------------------------------------------------------------------
// 5. 磁盘图标获取 (保持逻辑，但受益于更准的数据)
// ----------------------------------------------------------------------------
// getDiskIcon 函数本身逻辑是合理的，因为它是一个启发式映射。
// 优化的价值在于，我们现在给它传入了更准确的 FileSystem 类型，
// 使得它的判断结果更可靠。此处代码保持不变。
func getDiskIcon(mountPoint, fileSystem string) string {
	// ... (原始代码在这里是OK的)
	if runtime.GOOS == "darwin" {
		return getMacOSDiskIcon(mountPoint, fileSystem)
	}
	return getDarwinDiskIcon(mountPoint, fileSystem)
}

// ... (getDarwinDiskIcon 和 getMacOSDiskIcon 函数也保持不变)

func getDarwinDiskIcon(mountPoint, fileSystem string) string {
	switch {
	case mountPoint == "/":
		return "hdd"
	case strings.HasPrefix(mountPoint, "/home"):
		return "folder-home"
	case strings.HasPrefix(mountPoint, "/media") || strings.HasPrefix(mountPoint, "/mnt"):
		switch fileSystem {
		case "vfat", "ntfs", "exfat":
			return "usb"
		case "iso9660":
			return "cd"
		default:
			return "hdd-external"
		}
	case fileSystem == "tmpfs":
		return "memory"
	case fileSystem == "nfs", fileSystem == "cifs", fileSystem == "sshfs":
		return "network"
	default:
		return "hdd"
	}
}

func getMacOSDiskIcon(mountPoint, fileSystem string) string {
	switch {
	case mountPoint == "/":
		return "hdd"
	case strings.HasPrefix(mountPoint, "/Users"):
		return "folder-home"
	case strings.HasPrefix(mountPoint, "/Volumes"):
		switch fileSystem {
		case "msdos", "ntfs", "exfat":
			return "usb"
		case "cd9660":
			return "cd"
		case "apfs", "hfs":
			return "hdd-external"
		default:
			return "hdd-external"
		}
	case fileSystem == "apfs":
		return "hdd"
	case fileSystem == "hfs":
		return "hdd"
	case fileSystem == "nfs":
		return "network"
	case fileSystem == "cifs":
		return "network"
	case fileSystem == "smbfs":
		return "network"
	default:
		return "hdd"
	}
}

// readMountInfo 在 macOS 上的实现 (最终修复版)
func readMountInfo() ([]MountInfo, error) {
	// 1. 调用 Getfsstat 获取挂载点的数量
	count, err := unix.Getfsstat(nil, 0)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return []MountInfo{}, nil
	}

	// 2. 创建一个足够大的切片来存储数据
	stats := make([]unix.Statfs_t, count)

	// 3. 再次调用 Getfsstat 来填充这个切片
	_, err = unix.Getfsstat(stats, 0)
	if err != nil {
		return nil, err
	}

	var mounts []MountInfo
	for _, stat := range stats {
		// 【已修复】现在调用 cStringToGoString 时，传入的 []byte 类型与函数签名匹配
		mountPoint := cStringToGoString(stat.Mntonname[:])
		fileSystem := cStringToGoString(stat.Fstypename[:])
		device := cStringToGoString(stat.Mntfromname[:])

		var opts []string
		if (stat.Flags & unix.MNT_RDONLY) != 0 {
			opts = append(opts, "ro")
		}
		if (stat.Flags & unix.MNT_SYNCHRONOUS) != 0 {
			opts = append(opts, "sync")
		}
		if (stat.Flags & unix.MNT_NOEXEC) != 0 {
			opts = append(opts, "noexec")
		}
		if (stat.Flags & unix.MNT_NOSUID) != 0 {
			opts = append(opts, "nosuid")
		}
		if (stat.Flags & unix.MNT_NODEV) != 0 {
			opts = append(opts, "nodev")
		}
		if (stat.Flags & unix.MNT_LOCAL) != 0 {
			opts = append(opts, "local")
		}
		if (stat.Flags & unix.MNT_QUOTA) != 0 {
			opts = append(opts, "quota")
		}
		if (stat.Flags & unix.MNT_ROOTFS) != 0 {
			opts = append(opts, "rootfs")
		}
		if (stat.Flags & unix.MNT_DONTBROWSE) != 0 {
			opts = append(opts, "nobrowse")
		}

		mounts = append(mounts, MountInfo{
			Device:     device,
			MountPoint: mountPoint,
			FileSystem: fileSystem,
			Options:    strings.Join(opts, ","),
		})
	}
	return mounts, nil
}

// cStringToGoString 是 macOS 版本需要的辅助函数 (已修复)
// 【已修复】函数签名现在接受 []byte，以匹配 Statfs_t 结构体中的字段类型
func cStringToGoString(c []byte) string {
	var buf bytes.Buffer
	for _, b := range c {
		if b == 0 {
			break
		}
		buf.WriteByte(b)
	}
	return buf.String()
}
