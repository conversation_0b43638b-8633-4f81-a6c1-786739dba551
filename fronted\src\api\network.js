import { get, post } from '@/utils/request'

/**
 * 获取网络统计信息
 * @param {string} clientId 客户端ID
 * @returns {Promise}
 */
export function getNetworkStats(clientId) {
  return get(`/network/${clientId}/stats`)
}

/**
 * 获取网络接口信息
 * @param {string} clientId 客户端ID
 * @returns {Promise}
 */
export function getNetworkInterfaces(clientId) {
  return get(`/network/${clientId}/interfaces`)
}

/**
 * 获取网络连接信息
 * @param {string} clientId 客户端ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getNetworkConnections(clientId, params = {}) {
  const query = new URLSearchParams(params).toString()
  return get(`/network/${clientId}/connections${query ? '?' + query : ''}`)
}

/**
 * 关闭网络连接
 * @param {string} clientId 客户端ID
 * @param {Object} params 请求参数
 * @returns {Promise}
 */
export function closeConnection(clientId, params) {
  return post(`/network/${clientId}/close-connection`, params)
}
