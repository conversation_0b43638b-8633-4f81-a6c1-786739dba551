package workerpool

import (
	"net"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
)

// 跨平台进程检测功能 - 从tcp_listener.go移动过来

// IsLocalConnection 判断连接是否来自本地
func IsLocalConnection(remoteAddr string) bool {
	host, _, err := net.SplitHostPort(remoteAddr)
	if err != nil {
		// 如果解析失败，可能是直接的IP地址格式
		host = remoteAddr
	}

	// 标准本地地址
	localAddresses := map[string]bool{
		"127.0.0.1": true,
		"::1":       true,
		"localhost": true,
	}

	if localAddresses[host] {
		return true
	}

	// 解析主机名为IP地址
	ips, err := net.LookupIP(host)
	if err != nil {
		// 如果解析失败，尝试直接作为IP地址处理
		if ip := net.ParseIP(host); ip != nil {
			ips = []net.IP{ip}
		} else {
			return false
		}
	}

	// 获取本机所有网络接口的IP地址
	localIPs, err := getLocalIPs()
	if err != nil {
		return false
	}

	// 检查远程IP是否匹配本机任何IP
	for _, remoteIP := range ips {
		// 检查IPv4映射的IPv6地址
		remoteIP = remoteIP.To4()
		if remoteIP == nil {
			remoteIP = remoteIP.To16()
		}

		for _, localIP := range localIPs {
			if remoteIP.Equal(localIP) {
				return true
			}
		}
	}

	return false
}

// getLocalIPs 获取本机所有IP地址
func getLocalIPs() ([]net.IP, error) {
	var localIPs []net.IP

	// 添加标准回环地址
	localIPs = append(localIPs, net.ParseIP("127.0.0.1"))
	localIPs = append(localIPs, net.ParseIP("::1"))

	// 获取所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		return localIPs, err
	}

	for _, iface := range interfaces {
		// 跳过down的接口
		if iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过回环接口（我们已经手动添加了）
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		for _, addr := range addrs {
			switch v := addr.(type) {
			case *net.IPNet:
				// 处理IPv4和IPv6
				if ip := v.IP.To4(); ip != nil {
					localIPs = append(localIPs, ip)
				} else {
					localIPs = append(localIPs, v.IP)
				}
			case *net.IPAddr:
				// 处理IPv4和IPv6
				if ip := v.IP.To4(); ip != nil {
					localIPs = append(localIPs, ip)
				} else {
					localIPs = append(localIPs, v.IP)
				}
			}
		}
	}

	return localIPs, nil
}

// getProcessByConnection 通过连接信息获取进程信息（跨平台）
func getProcessByConnection(localAddr, remoteAddr string) (string, string) {
	switch runtime.GOOS {
	case "windows":
		return getProcessByConnectionWindows(localAddr, remoteAddr)
	case "linux":
		return getProcessByConnectionLinux(localAddr, remoteAddr)
	case "darwin":
		return getProcessByConnectionDarwin(localAddr, remoteAddr)
	default:
		return "", ""
	}
}

// getProcessByConnectionWindows Windows平台进程检测
func getProcessByConnectionWindows(localAddr, remoteAddr string) (string, string) {
	// 解析地址获取端口
	_, localPort, err := net.SplitHostPort(localAddr)
	if err != nil {
		return "", ""
	}

	_, _, err = net.SplitHostPort(remoteAddr)
	if err != nil {
		return "", ""
	}

	// 使用netstat命令查找连接对应的PID
	cmd := exec.Command("netstat", "-ano")
	output, err := cmd.Output()
	if err != nil {
		return "", ""
	}

	lines := strings.Split(string(output), "\n")
	var pid string

	for _, line := range lines {
		if strings.Contains(line, "TCP") && strings.Contains(line, localPort) {
			fields := strings.Fields(line)
			if len(fields) >= 5 {
				pid = fields[len(fields)-1]
				break
			}
		}
	}

	if pid == "" {
		return "", ""
	}

	// 通过PID获取进程名
	processName := getProcessNameByPIDWindows(pid)
	return pid, processName
}

// getProcessByConnectionLinux Linux平台进程检测
func getProcessByConnectionLinux(localAddr, remoteAddr string) (string, string) {
	// 解析地址获取端口
	_, localPort, err := net.SplitHostPort(localAddr)
	if err != nil {
		return "", ""
	}

	_, _, err = net.SplitHostPort(remoteAddr)
	if err != nil {
		return "", ""
	}

	// 使用ss命令查找连接对应的PID (更现代的替代netstat)
	cmd := exec.Command("ss", "-tulpn")
	output, err := cmd.Output()
	if err != nil {
		// 如果ss不可用，尝试netstat
		cmd = exec.Command("netstat", "-tulpn")
		output, err = cmd.Output()
		if err != nil {
			return "", ""
		}
	}

	lines := strings.Split(string(output), "\n")
	var pid string

	for _, line := range lines {
		if strings.Contains(line, "tcp") && strings.Contains(line, ":"+localPort) {
			// 查找包含PID的部分
			if strings.Contains(line, "pid=") {
				// ss格式: users:(("process",pid=1234,fd=5))
				start := strings.Index(line, "pid=")
				if start != -1 {
					start += 4
					end := strings.Index(line[start:], ",")
					if end == -1 {
						end = strings.Index(line[start:], ")")
					}
					if end != -1 {
						pid = line[start : start+end]
						break
					}
				}
			} else {
				// netstat格式，PID在最后一列
				fields := strings.Fields(line)
				for _, field := range fields {
					if strings.Contains(field, "/") {
						parts := strings.Split(field, "/")
						if len(parts) >= 1 {
							pid = parts[0]
							break
						}
					}
				}
				if pid != "" {
					break
				}
			}
		}
	}

	if pid == "" {
		return "", ""
	}

	// 通过PID获取进程名
	processName := getProcessNameByPIDLinux(pid)
	return pid, processName
}

// getProcessByConnectionDarwin macOS平台进程检测
func getProcessByConnectionDarwin(localAddr, remoteAddr string) (string, string) {
	// 解析地址获取端口
	_, localPort, err := net.SplitHostPort(localAddr)
	if err != nil {
		return "", ""
	}

	// 使用lsof命令查找连接对应的PID
	cmd := exec.Command("lsof", "-i", ":"+localPort, "-n", "-P")
	output, err := cmd.Output()
	if err != nil {
		return "", ""
	}

	lines := strings.Split(string(output), "\n")
	var pid string
	var processName string

	for _, line := range lines {
		if strings.Contains(line, "TCP") && strings.Contains(line, ":"+localPort) {
			fields := strings.Fields(line)
			if len(fields) >= 3 {
				processName = fields[0]
				pid = fields[1]
				break
			}
		}
	}

	return pid, processName
}

// getProcessNameByPIDWindows 通过PID获取进程名（Windows）
func getProcessNameByPIDWindows(pid string) string {
	cmd := exec.Command("tasklist", "/FI", "PID eq "+pid, "/FO", "CSV", "/NH")
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			// CSV格式解析，第一个字段是进程名
			fields := strings.Split(line, ",")
			if len(fields) > 0 {
				processName := strings.Trim(fields[0], "\"")
				return processName
			}
		}
	}

	return ""
}

// getProcessNameByPIDLinux 通过PID获取进程名（Linux）
func getProcessNameByPIDLinux(pid string) string {
	// 读取/proc/PID/comm文件获取进程名
	commPath := "/proc/" + pid + "/comm"
	content, err := exec.Command("cat", commPath).Output()
	if err != nil {
		// 如果读取comm失败，尝试从cmdline获取
		cmdlinePath := "/proc/" + pid + "/cmdline"
		content, err = exec.Command("cat", cmdlinePath).Output()
		if err != nil {
			return ""
		}
		// cmdline可能包含参数，只取第一部分
		parts := strings.Split(string(content), "\x00")
		if len(parts) > 0 {
			return filepath.Base(parts[0])
		}
		return ""
	}

	return strings.TrimSpace(string(content))
}
