package request

// ClientSearch 客户端搜索请求结构体
type ClientSearch struct {
	ListenerID   uint   `json:"listenerId" form:"listenerId"`
	ListenerType string `json:"listenerType" form:"listenerType"`
	RemoteAddr   string `json:"remoteAddr" form:"remoteAddr"`
	Status       int    `json:"status" form:"status"`
	StatusSet    bool   `json:"statusSet" form:"statusSet"` // 标识是否设置了status字段
	Remark       string `json:"remark" form:"remark"`
	PageInfo
}

// ClientTerminalRequest 打开终端请求结构体
type ClientTerminalRequest struct {
	ID uint `json:"id" form:"id" binding:"required"`
}

// ClientCommandRequest 发送命令请求结构体
type ClientCommandRequest struct {
	ID      uint   `json:"id" form:"id" binding:"required"`
	Command string `json:"command" form:"command" binding:"required"`
}
