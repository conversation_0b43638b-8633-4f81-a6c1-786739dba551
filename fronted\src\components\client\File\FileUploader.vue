<template>
  <div class="file-uploader">
    <!-- 上传文件选择 -->
    <a-upload-dragger
      v-model:fileList="fileList"
      :multiple="true"
      :before-upload="beforeUpload"
      :show-upload-list="false"
    >
      <p class="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
      <p class="ant-upload-hint">支持单个或批量上传，支持断点续传</p>
    </a-upload-dragger>

    <!-- 上传任务列表 -->
    <div v-if="uploadTasks.length > 0" class="upload-tasks">
      <div class="tasks-header">
        <h4>上传任务</h4>
        <a-button size="small" @click="clearCompletedTasks">清除已完成</a-button>
      </div>
      
      <div class="task-list">
        <div 
          v-for="task in uploadTasks" 
          :key="task.id" 
          class="upload-task"
          :class="{ 'task-error': task.status === 'error', 'task-completed': task.status === 'completed' }"
        >
          <div class="task-info">
            <div class="task-name">{{ task.fileName }}</div>
            <div class="task-details">
              <span class="file-size">{{ formatFileSize(task.fileSize) }}</span>
              <span class="task-stage">{{ task.stage }}</span>
            </div>
          </div>
          
          <div class="task-progress">
            <a-progress 
              :percent="task.progress" 
              :status="getProgressStatus(task.status)"
              size="small"
              :show-info="false"
            />
            <div class="progress-text">
              {{ task.progress }}%
              <span v-if="task.status === 'uploading'">
                ({{ formatFileSize(task.uploadedSize) }}/{{ formatFileSize(task.fileSize) }})
              </span>
            </div>
          </div>
          
          <div class="task-actions">
            <a-button 
              v-if="task.status === 'uploading' || task.status === 'paused'"
              size="small"
              @click="task.status === 'uploading' ? pauseTask(task.id) : resumeTask(task.id)"
            >
              {{ task.status === 'uploading' ? '暂停' : '继续' }}
            </a-button>
            <a-button 
              v-if="task.status === 'error'"
              size="small"
              type="primary"
              @click="retryTask(task.id)"
            >
              重试
            </a-button>
            <a-button 
              size="small"
              danger
              @click="removeTask(task.id)"
            >
              删除
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'
import * as fileApi from '@/api/file'

const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    default: '/'
  }
})

const emit = defineEmits(['upload-complete'])

const fileList = ref([])
const uploadTasks = ref([])
let taskIdCounter = 0

// 文件上传前的处理
const beforeUpload = (file) => {
  addUploadTask(file)
  return false // 阻止自动上传
}

// 添加上传任务
const addUploadTask = (file) => {
  const taskId = ++taskIdCounter
  const task = {
    id: taskId,
    fileName: file.name,
    fileSize: file.size,
    uploadedSize: 0,
    file: file,
    status: 'pending', // pending, uploading, paused, completed, error
    progress: 0,
    error: null,
    stage: '等待上传',
    serverPath: null,
    taskId: null // 服务器端传输任务ID
  }
  
  uploadTasks.value.push(task)
  startUpload(taskId)
}

// 开始上传
const startUpload = (taskId) => {
  const task = uploadTasks.value.find(t => t.id === taskId)
  if (!task) return
  
  handleUpload(task)
}

// 主要的上传处理函数
const handleUpload = async (task) => {
  if (task.status === 'uploading') return
  
  task.status = 'uploading'
  task.progress = 0
  task.error = ''
  task.stage = '准备上传'
  task.startTime = Date.now()
  task.lastProgressTime = Date.now()
  
  try {
    // 上传到服务器并开始传输
    const serverUploadSuccess = await uploadToServer(task)
    if (!serverUploadSuccess) {
      throw new Error('上传到服务器失败')
    }
    
    // 如果没有taskId，说明是同步传输，直接标记完成
    if (!task.taskId) {
      task.status = 'completed'
      task.progress = 100
      task.stage = '上传完成'
      message.success(`${task.fileName} 上传完成`)
      emit('upload-complete')
    }
    // 否则轮询逻辑已在uploadToServer中启动
  } catch (error) {
    task.status = 'error'
    task.error = error.response?.data?.error || error.response?.data?.message || error.message
    
    task.stage = '上传失败'
    
    message.error(`${task.fileName} 上传失败: ${task.error}`)
  }
}

// 上传到服务器
const uploadToServer = async (task) => {
  const formData = new FormData()
  formData.append('file', task.file)
  
  // 计算目标路径
  const remotePath = props.currentPath === '/' ? `/${task.fileName}` : `${props.currentPath}/${task.fileName}`
  formData.append('destination_path', remotePath)
  
  try {
    const response = await fileApi.uploadFileToServerAndTransferToClient(props.clientId, formData, {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
        task.progress = progress
        task.uploadedSize = progressEvent.loaded
        task.stage = '上传到服务器'
      }
    })
    
    if (response.code === 200) {
      // 检查响应中的错误信息
      const errorMsg = response.data?.data?.error || response.data?.error || '';
      if (errorMsg && errorMsg !== "") {
        throw new Error(errorMsg)
      }

      task.serverPath = response.data.file_path
      task.taskId = response.data.task_id
      task.progress = 100
      task.stage = '上传完成，服务器正在传输到客户端'
      
      // 开始轮询传输任务状态
      if (task.taskId) {
        startPollingTaskStatus(task)
      }
      
      return true
    } else {
      throw new Error(response.message || '上传到服务器失败')
    }
  } catch (error) {
    throw new Error(`上传到服务器失败: ${error.response?.data?.error || error.response?.data?.message || error.message}`)
  }
}

// 轮询传输任务状态
const startPollingTaskStatus = async (task) => {
  const pollInterval = setInterval(async () => {
    try {
      const response = await fileApi.getTaskStatus(task.taskId)
      
      if (response.code === 200) {
        const taskStatus = response.data
        
        if (taskStatus.status === 'completed') {
          task.status = 'completed'
          task.stage = '传输完成'
          clearInterval(pollInterval)
          message.success(`${task.fileName} 传输完成`)
          emit('upload-complete')
        } else if (taskStatus.status === 'failed') {
          task.status = 'error'
          task.error = taskStatus.error || '传输失败'
          task.stage = '传输失败'
          clearInterval(pollInterval)
          message.error(`${task.fileName} 传输失败: ${task.error}`)
        } else {
          // 任务仍在进行中
          task.stage = taskStatus.message || '正在传输到客户端'
          // 更新传输进度
          if (taskStatus.progress !== undefined) {
            task.progress = taskStatus.progress
          }
          // 计算已传输大小（基于进度百分比）
          if (task.progress > 0) {
            task.uploadedSize = Math.round((task.progress / 100) * task.fileSize)
          }
        }
      }
    } catch (error) {
      console.error('轮询任务状态失败:', error.response?.data?.error || error.response?.data?.message || error.message)
      // 继续轮询，不中断
    }
  }, 2000) // 每2秒轮询一次
  
  // 设置超时，避免无限轮询
  setTimeout(() => {
    clearInterval(pollInterval)
    if (task.status === 'uploading') {
      task.status = 'error'
      task.error = '传输超时'
      task.stage = '传输超时'
    }
  }, 300000) // 5分钟超时
}

// 暂停任务
const pauseTask = (taskId) => {
  const task = uploadTasks.value.find(t => t.id === taskId)
  if (task) {
    task.status = 'paused'
  }
}

// 继续任务
const resumeTask = (taskId) => {
  const task = uploadTasks.value.find(t => t.id === taskId)
  if (task) {
    handleUpload(task)
  }
}

// 重试任务
const retryTask = (taskId) => {
  const task = uploadTasks.value.find(t => t.id === taskId)
  if (task) {
    task.status = 'pending'
    task.progress = 0
    task.uploadedSize = 0
    task.error = null
    task.stage = '准备重试'
    handleUpload(task)
  }
}

// 删除任务
const removeTask = (taskId) => {
  const index = uploadTasks.value.findIndex(t => t.id === taskId)
  if (index > -1) {
    uploadTasks.value.splice(index, 1)
  }
}

// 清除已完成的任务
const clearCompletedTasks = () => {
  uploadTasks.value = uploadTasks.value.filter(task => 
    task.status !== 'completed'
  )
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取进度条状态
const getProgressStatus = (status) => {
  if (status === 'error') return 'exception'
  if (status === 'completed') return 'success'
  return 'active'
}
</script>

<style scoped>
.file-uploader {
  padding: 16px;
}

.upload-tasks {
  margin-top: 24px;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tasks-header h4 {
  margin: 0;
  color: #262626;
}

.task-list {
  max-height: 400px;
  overflow-y: auto;
}

.upload-task {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.upload-task:hover {
  border-color: #d9d9d9;
  background: #f5f5f5;
}

.task-error {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.task-completed {
  border-color: #52c41a;
  background: #f6ffed;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #8c8c8c;
}

.task-progress {
  flex: 1;
  margin: 0 16px;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #595959;
  margin-top: 4px;
}

.task-actions {
  display: flex;
  gap: 8px;
}
</style>