<template>
  <div class="proxy-chain-visualizer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <a-space>
        <a-button type="primary" @click="showCreateChainModal = true">
          <template #icon><PlusOutlined /></template>
          创建代理链
        </a-button>
        <a-button @click="refreshChains">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
        <a-divider type="vertical" />
        <a-button @click="zoomIn">
          <template #icon><ZoomInOutlined /></template>
        </a-button>
        <a-button @click="zoomOut">
          <template #icon><ZoomOutOutlined /></template>
        </a-button>
        <a-button @click="resetZoom">
          <template #icon><ExpandOutlined /></template>
          重置
        </a-button>
        <a-divider type="vertical" />
        <a-switch v-model:checked="autoLayout" checked-children="自动布局" un-checked-children="手动布局" />
      </a-space>
    </div>

    <!-- 代理链列表 -->
    <div class="chain-list">
      <a-card title="代理链列表" size="small" :bordered="false">
        <a-list
          :data-source="proxyChains"
          :loading="loading"
          size="small"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: getChainStatusColor(item.status) }">
                    <template #icon><NodeIndexOutlined /></template>
                  </a-avatar>
                </template>
                <template #title>
                  <a @click="selectChain(item)">{{ item.name }}</a>
                  <a-tag size="small" :color="getChainStatusColor(item.status)" style="margin-left: 8px">
                    {{ getChainStatusText(item.status) }}
                  </a-tag>
                </template>
                <template #description>
                  <div class="chain-info">
                    <span>节点: {{ item.nodes_info?.length || 0 }}</span>
                    <a-divider type="vertical" />
                    <span>连接: {{ item.total_connections || 0 }}</span>
                    <a-divider type="vertical" />
                    <span>{{ formatTime(item.created_at) }}</span>
                  </div>
                </template>
              </a-list-item-meta>
              <template #actions>
                <a @click="editChain(item)">编辑</a>
                <a @click="controlChain(item, item.status === 2 ? 'stop' : 'start')">
                  {{ item.status === 2 ? '停止' : '启动' }}
                </a>
                <a-popconfirm
                  title="确定要删除这个代理链吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteChain(item)"
                >
                  <a style="color: #ff4d4f">删除</a>
                </a-popconfirm>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>

    <!-- 可视化画布 -->
    <div class="visualization-canvas">
      <a-card 
        :title="selectedChain ? `代理链: ${selectedChain.name}` : '选择一个代理链查看拓扑图'"
        size="small" 
        :bordered="false"
      >
        <div v-if="selectedChain" class="canvas-container">
          <svg
            ref="svgCanvas"
            class="chain-canvas"
            :width="canvasWidth"
            :height="canvasHeight"
            @mousedown="handleCanvasMouseDown"
            @mousemove="handleCanvasMouseMove"
            @mouseup="handleCanvasMouseUp"
            @wheel="handleCanvasWheel"
          >
            <!-- 定义箭头标记 -->
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon
                  points="0 0, 10 3.5, 0 7"
                  fill="#1890ff"
                />
              </marker>
            </defs>

            <!-- 背景网格 -->
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" stroke-width="1"/>
            </pattern>
            <rect width="100%" height="100%" fill="url(#grid)" />

            <!-- 连接线 -->
            <g class="connections">
              <line
                v-for="connection in connections"
                :key="`${connection.from}-${connection.to}`"
                :x1="connection.x1"
                :y1="connection.y1"
                :x2="connection.x2"
                :y2="connection.y2"
                stroke="#1890ff"
                stroke-width="2"
                marker-end="url(#arrowhead)"
                :class="{ 'active-connection': connection.active }"
              />
            </g>

            <!-- 节点 -->
            <g class="nodes">
              <g
                v-for="node in nodes"
                :key="node.id"
                :transform="`translate(${node.x}, ${node.y})`"
                class="node"
                :class="{ 'selected': node.id === selectedNodeId, 'dragging': node.id === draggingNodeId }"
                @mousedown="handleNodeMouseDown(node, $event)"
              >
                <!-- 节点背景 -->
                <circle
                  :r="nodeRadius"
                  :fill="getNodeColor(node)"
                  :stroke="getNodeBorderColor(node)"
                  stroke-width="2"
                />
                
                <!-- 节点图标 -->
                <text
                  text-anchor="middle"
                  dy="0.3em"
                  font-size="16"
                  fill="white"
                >
                  {{ getNodeIcon(node) }}
                </text>
                
                <!-- 节点标签 -->
                <text
                  :y="nodeRadius + 20"
                  text-anchor="middle"
                  font-size="12"
                  fill="#262626"
                >
                  {{ node.name }}
                </text>
                
                <!-- 状态指示器 -->
                <circle
                  :cx="nodeRadius - 5"
                  :cy="-nodeRadius + 5"
                  r="4"
                  :fill="getNodeStatusColor(node.status)"
                  stroke="white"
                  stroke-width="1"
                />
              </g>
            </g>

            <!-- 临时连接线（拖拽时显示） -->
            <line
              v-if="tempConnection"
              :x1="tempConnection.x1"
              :y1="tempConnection.y1"
              :x2="tempConnection.x2"
              :y2="tempConnection.y2"
              stroke="#1890ff"
              stroke-width="2"
              stroke-dasharray="5,5"
              opacity="0.6"
            />
          </svg>

          <!-- 节点详情面板 -->
          <div v-if="selectedNode" class="node-detail-panel">
            <a-card title="节点详情" size="small">
              <template #extra>
                <a-button type="text" size="small" @click="selectedNode = null">
                  <template #icon><CloseOutlined /></template>
                </a-button>
              </template>
              
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="节点名称">{{ selectedNode.name }}</a-descriptions-item>
                <a-descriptions-item label="节点类型">{{ getNodeTypeText(selectedNode.type) }}</a-descriptions-item>
                <a-descriptions-item label="状态">
                  <a-tag :color="getNodeStatusColor(selectedNode.status)">
                    {{ getNodeStatusText(selectedNode.status) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="地址">{{ selectedNode.host }}:{{ selectedNode.port }}</a-descriptions-item>
                <a-descriptions-item label="延迟">{{ selectedNode.latency || 0 }}ms</a-descriptions-item>
                <a-descriptions-item label="连接数">{{ selectedNode.connections || 0 }}</a-descriptions-item>
              </a-descriptions>
              
              <a-divider />
              
              <a-space>
                <a-button size="small" @click="testNodeConnection(selectedNode)">测试连接</a-button>
                <a-button size="small" @click="editNode(selectedNode)">编辑</a-button>
                <a-button size="small" danger @click="removeNode(selectedNode)">移除</a-button>
              </a-space>
            </a-card>
          </div>
        </div>
        
        <a-empty v-else description="请从左侧选择一个代理链查看拓扑图" />
      </a-card>
    </div>

    <!-- 创建代理链模态框 -->
    <CreateChainModal
      v-model:visible="showCreateChainModal"
      @success="handleCreateChainSuccess"
    />

    <!-- 编辑代理链模态框 -->
    <EditChainModal
      v-model:visible="showEditChainModal"
      :chain-data="editingChain"
      @success="handleEditChainSuccess"
    />

    <!-- 添加节点模态框 -->
    <AddNodeModal
      v-model:visible="showAddNodeModal"
      :chain-id="selectedChain?.chain_id"
      @success="handleAddNodeSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ExpandOutlined,
  NodeIndexOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
// import { getProxyChainList, controlProxyChain, deleteProxyChain } from '@/api/proxy' // 暂未实现
import { formatTime } from '@/utils/format'
import { getApiBaseUrl } from '@/utils/serverConfig'
import CreateChainModal from './CreateChainModal.vue'
import EditChainModal from './EditChainModal.vue'
import AddNodeModal from './AddNodeModal.vue'

// 响应式数据
const loading = ref(false)
const proxyChains = ref([])
const selectedChain = ref(null)
const selectedNode = ref(null)
const selectedNodeId = ref(null)
const draggingNodeId = ref(null)
const autoLayout = ref(true)
const showCreateChainModal = ref(false)
const showEditChainModal = ref(false)
const showAddNodeModal = ref(false)
const editingChain = ref(null)

// 画布相关
const svgCanvas = ref(null)
const canvasWidth = ref(800)
const canvasHeight = ref(600)
const nodeRadius = 30
const zoomLevel = ref(1)
const panOffset = reactive({ x: 0, y: 0 })

// 拖拽相关
const isDragging = ref(false)
const dragStart = reactive({ x: 0, y: 0 })
const tempConnection = ref(null)

// 节点和连接数据
const nodes = ref([])
const connections = ref([])

// 计算属性
const canvasStyle = computed(() => ({
  transform: `scale(${zoomLevel.value}) translate(${panOffset.x}px, ${panOffset.y}px)`
}))

// 方法
const loadProxyChains = async () => {
  loading.value = true
  try {
    // 暂未实现代理链功能
    proxyChains.value = []
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('获取代理链列表失败:', error)
    message.error('获取代理链列表失败')
  } finally {
    loading.value = false
  }
}

const selectChain = (chain) => {
  selectedChain.value = chain
  selectedNode.value = null
  selectedNodeId.value = null
  loadChainTopology(chain)
}

const loadChainTopology = (chain) => {
  // 模拟加载代理链拓扑数据
  const mockNodes = [
    {
      id: 'client',
      name: '客户端',
      type: 'client',
      status: 1,
      x: 100,
      y: 300,
      host: '*************',
      port: 0,
      latency: 0,
      connections: 0
    },
    {
      id: 'proxy1',
      name: '代理节点1',
      type: 'proxy',
      status: 1,
      x: 300,
      y: 200,
      host: '*********',
      port: 8080,
      latency: 45,
      connections: 5
    },
    {
      id: 'proxy2',
      name: '代理节点2',
      type: 'proxy',
      status: 1,
      x: 300,
      y: 400,
      host: '*********',
      port: 8080,
      latency: 52,
      connections: 3
    },
    {
      id: 'target',
      name: '目标服务器',
      type: 'target',
      status: 1,
      x: 500,
      y: 300,
      host: '*******',
      port: 80,
      latency: 28,
      connections: 0
    }
  ]

  const mockConnections = [
    {
      from: 'client',
      to: 'proxy1',
      active: true,
      x1: 130,
      y1: 280,
      x2: 270,
      y2: 220
    },
    {
      from: 'client',
      to: 'proxy2',
      active: false,
      x1: 130,
      y1: 320,
      x2: 270,
      y2: 380
    },
    {
      from: 'proxy1',
      to: 'target',
      active: true,
      x1: 330,
      y1: 200,
      x2: 470,
      y2: 280
    },
    {
      from: 'proxy2',
      to: 'target',
      active: false,
      x1: 330,
      y1: 400,
      x2: 470,
      y2: 320
    }
  ]

  nodes.value = mockNodes
  connections.value = mockConnections
}

const getChainStatusColor = (status) => {
  const colors = {
    0: '#d9d9d9',   // 停止
    1: '#faad14',   // 启动中
    2: '#52c41a',   // 运行中
    3: '#f5222d',   // 错误
    4: '#faad14'    // 停止中
  }
  return colors[status] || '#d9d9d9'
}

const getChainStatusText = (status) => {
  const texts = {
    0: '停止',
    1: '启动中',
    2: '运行中',
    3: '错误',
    4: '停止中'
  }
  return texts[status] || '未知'
}

const getNodeColor = (node) => {
  const colors = {
    client: '#1890ff',
    proxy: '#52c41a',
    target: '#fa8c16'
  }
  return colors[node.type] || '#d9d9d9'
}

const getNodeBorderColor = (node) => {
  if (node.id === selectedNodeId.value) {
    return '#ff4d4f'
  }
  return getNodeColor(node)
}

const getNodeIcon = (node) => {
  const icons = {
    client: '🖥️',
    proxy: '🔄',
    target: '🎯'
  }
  return icons[node.type] || '⚪'
}

const getNodeStatusColor = (status) => {
  const colors = {
    0: '#d9d9d9',   // 离线
    1: '#52c41a',   // 在线
    2: '#f5222d'    // 错误
  }
  return colors[status] || '#d9d9d9'
}

const getNodeTypeText = (type) => {
  const texts = {
    client: '客户端',
    proxy: '代理节点',
    target: '目标服务器'
  }
  return texts[type] || type
}

const getNodeStatusText = (status) => {
  const texts = {
    0: '离线',
    1: '在线',
    2: '错误'
  }
  return texts[status] || '未知'
}

const handleNodeMouseDown = (node, event) => {
  event.stopPropagation()
  selectedNodeId.value = node.id
  selectedNode.value = node
  
  if (!autoLayout.value) {
    draggingNodeId.value = node.id
    isDragging.value = true
    dragStart.x = event.clientX - node.x
    dragStart.y = event.clientY - node.y
  }
}

const handleCanvasMouseDown = (event) => {
  if (event.target === svgCanvas.value) {
    selectedNodeId.value = null
    selectedNode.value = null
  }
}

const handleCanvasMouseMove = (event) => {
  if (isDragging.value && draggingNodeId.value) {
    const node = nodes.value.find(n => n.id === draggingNodeId.value)
    if (node) {
      node.x = event.clientX - dragStart.x
      node.y = event.clientY - dragStart.y
      
      // 更新连接线位置
      updateConnections()
    }
  }
}

const handleCanvasMouseUp = () => {
  isDragging.value = false
  draggingNodeId.value = null
}

const handleCanvasWheel = (event) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  zoomLevel.value = Math.max(0.1, Math.min(3, zoomLevel.value + delta))
}

const updateConnections = () => {
  connections.value.forEach(conn => {
    const fromNode = nodes.value.find(n => n.id === conn.from)
    const toNode = nodes.value.find(n => n.id === conn.to)
    
    if (fromNode && toNode) {
      conn.x1 = fromNode.x + nodeRadius
      conn.y1 = fromNode.y
      conn.x2 = toNode.x - nodeRadius
      conn.y2 = toNode.y
    }
  })
}

const zoomIn = () => {
  zoomLevel.value = Math.min(3, zoomLevel.value + 0.2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(0.1, zoomLevel.value - 0.2)
}

const resetZoom = () => {
  zoomLevel.value = 1
  panOffset.x = 0
  panOffset.y = 0
}

const refreshChains = () => {
  loadProxyChains()
  if (selectedChain.value) {
    loadChainTopology(selectedChain.value)
  }
}

const editChain = (chain) => {
  editingChain.value = chain
  showEditChainModal.value = true
}

const controlChain = async (chain, action) => {
  try {
    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error(`代理链${action}失败:`, error)
    message.error(`代理链${action === 'start' ? '启动' : '停止'}失败`)
  }
}

const deleteChain = async (chain) => {
  try {
    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')
  } catch (error) {
    console.error('代理链删除失败:', error)
    message.error('代理链删除失败')
  }
}

const testNodeConnection = async (node) => {
  message.info(`正在测试节点 ${node.name} 的连接...`)

  try {
    // 实现节点连接测试
    const baseUrl = getApiBaseUrl()
    const response = await fetch(`${baseUrl}/api/proxy/${node.proxy_id}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.code === 200) {
        message.success(`节点 ${node.name} 连接正常`)
      } else {
        message.warning(`节点 ${node.name} 连接异常: ${result.msg}`)
      }
    } else {
      message.error(`节点 ${node.name} 连接测试失败`)
    }
  } catch (error) {
    console.error('节点连接测试失败:', error)
    message.error(`节点 ${node.name} 连接测试失败`)
  }
}

const editNode = (node) => {
  message.info(`编辑节点 ${node.name}`)

  // 实现节点编辑
  // 这里可以打开一个编辑对话框，允许用户修改节点配置
  // 暂时显示提示信息，实际实现需要根据具体需求设计编辑界面
  message.warning('节点编辑功能正在开发中，请通过代理管理页面进行编辑')
}

const removeNode = async (node) => {
  try {
    // 实现节点移除
    if (!selectedChain.value) {
      message.error('请先选择一个代理链')
      return
    }

    // 确认删除
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '确认移除节点',
        content: `确定要从代理链中移除节点 "${node.name}" 吗？`,
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (!confirmed) return

    // 从当前代理链中移除节点
    const updatedNodes = selectedChain.value.nodes.filter(n => n.id !== node.id)

    // 更新代理链配置
    const updateData = {
      ...selectedChain.value,
      nodes: updatedNodes
    }

    // 这里应该调用API更新代理链
    // const response = await updateProxyChain(selectedChain.value.id, updateData)

    // 暂时直接更新本地数据
    selectedChain.value.nodes = updatedNodes
    loadChainVisualization(selectedChain.value)

    message.success(`节点 ${node.name} 已从代理链中移除`)
  } catch (error) {
    console.error('移除节点失败:', error)
    message.error('移除节点失败')
  }
}

const handleCreateChainSuccess = () => {
  showCreateChainModal.value = false
  loadProxyChains()
}

const handleEditChainSuccess = () => {
  showEditChainModal.value = false
  loadProxyChains()
}

const handleAddNodeSuccess = () => {
  showAddNodeModal.value = false
  if (selectedChain.value) {
    loadChainTopology(selectedChain.value)
  }
}

// 生命周期
onMounted(() => {
  loadProxyChains()
  
  // 监听窗口大小变化
  const resizeObserver = new ResizeObserver(entries => {
    for (const entry of entries) {
      canvasWidth.value = entry.contentRect.width
      canvasHeight.value = entry.contentRect.height
    }
  })
  
  nextTick(() => {
    const canvasContainer = document.querySelector('.canvas-container')
    if (canvasContainer) {
      resizeObserver.observe(canvasContainer)
    }
  })
})
</script>

<style scoped>
.proxy-chain-visualizer {
  display: flex;
  height: 100vh;
  background: #f0f2f5;
}

.toolbar {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  background: white;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chain-list {
  width: 300px;
  padding: 16px;
  background: white;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
}

.visualization-canvas {
  flex: 1;
  padding: 16px;
  position: relative;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: white;
}

.chain-canvas {
  cursor: grab;
}

.chain-canvas:active {
  cursor: grabbing;
}

.node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.node:hover {
  transform: scale(1.1);
}

.node.selected {
  filter: drop-shadow(0 0 8px #1890ff);
}

.node.dragging {
  cursor: grabbing;
}

.connections line {
  transition: all 0.2s ease;
}

.connections .active-connection {
  stroke: #52c41a;
  stroke-width: 3;
}

.node-detail-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 280px;
  z-index: 10;
}

.chain-info {
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
}

:deep(.ant-list-item-action) {
  margin-left: 16px;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}
</style>
