//go:build windows
// +build windows

package common

import (
	"log"
	"sync"
)

// 目录操作的互斥锁，防止并发操作冲突
var (
	dirOpMutex sync.RWMutex
)

func (cm *ConnectionManager) handleDirRequest(packet *Packet) {
	switch packet.Header.Code {
	case DirList:
		cm.handleDirList(packet)
	case DirCreate:
		cm.handleDirCreate(packet)
	case DirMove:
		cm.handleDirMove(packet)
	case DirDelete:
		cm.handleDirDelete(packet)
	case DirCopy:
		cm.handleDirCopy(packet)
	case DiskList:
		cm.handleDiskList(packet)
	default:
		log.Printf("未知的目录操作代码: %d", packet.Header.Code)
	}
}
