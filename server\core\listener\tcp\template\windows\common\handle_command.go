//go:build windows
// +build windows

package common

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"runtime"
	"strconv"
	"strings"
	"time"
)

// CommandRequest 多终端命令请求结构体
type CommandRequest struct {
	TerminalID uint32 `json:"terminal_id"` // 终端ID: 0=主终端, 1000+=备用终端
	Command    string `json:"command"`     // 要执行的命令
	Type       uint8  `json:"type"`        // 命令类型（预留字段）
}

// CommandResponse 多终端命令响应结构体
type CommandResponse struct {
	TerminalID uint32 `json:"terminal_id"` // 对应的终端ID
	Output     string `json:"output"`      // 命令输出
	Success    bool   `json:"success"`     // 是否成功执行
	Error      string `json:"error"`       // 错误信息（如果有）
}

func (cm *ConnectionManager) readPTYOutput(ctx context.Context) {
	// 🚀 修复：使用与Linux/macOS一致的非阻塞读取策略
	// 1. 在独立goroutine中执行阻塞的PTY读取
	// 2. 通过channel传递读取结果
	// 3. 主循环可以正确响应context取消

	type readResult struct {
		data []byte
		err  error
	}
	readChan := make(chan readResult, 1) // 缓冲为1，避免发送者阻塞

	// 在一个专用的goroutine中执行阻塞的 pty.Read()
	go func() {
		defer close(readChan) // 确保channel被关闭
		for {
			buf := make([]byte, 8192)
			if cm.pty == nil {
				log.Println("PTY已关闭，退出读取goroutine")
				return
			}

			n, err := cm.pty.Read(buf)
			if err != nil {
				// 将读取结果（无论是数据还是错误）发送到channel
				select {
				case readChan <- readResult{err: err}:
				case <-ctx.Done():
				}
				return // 读取出错，退出这个IO goroutine
			}

			if n > 0 {
				data := make([]byte, n)
				copy(data, buf[:n])
				select {
				case readChan <- readResult{data: data}:
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	for {
		select {
		case <-ctx.Done():
			log.Println("readPTYOutput收到context取消信号")
			return
		case result, ok := <-readChan:
			if !ok {
				log.Println("readPTYOutput: readChan已关闭")
				return
			}

			// PTY读取操作有了结果
			if result.err != nil {
				if result.err != io.EOF {
					log.Printf("PTY读取错误: %v", result.err)
				} else {
					log.Println("PTY读取到EOF，正常关闭")
				}
				// PTY流已关闭，这个会话的PTY部分结束了。
				// cmd.Wait()的goroutine会检测到进程退出并调用cancel()
				return
			}

			// 成功读取到数据，发送到输出channel
			select {
			case cm.ptyOutputChan <- result.data:
				log.Printf("PTY读取成功: %d字节", len(result.data))
			case <-ctx.Done():
				return
			}
		}
	}
}

func (cm *ConnectionManager) handlePTYInput(ctx context.Context) {
	// 🚀 优化策略：
	// 1. 减少定时器频率，提高响应速度
	// 2. 对单字符输入立即写入，无缓冲
	// 3. 只对批量输入进行缓冲
	// 4. 简化刷新逻辑

	inputBuffer := make([]byte, 0, 256)                  // 减小缓冲区
	flushTimer := time.NewTicker(500 * time.Microsecond) // 0.5ms，更快响应
	defer flushTimer.Stop()

	flush := func() {
		if len(inputBuffer) == 0 {
			return
		}
		writeStart := time.Now()
		_, err := cm.pty.Write(inputBuffer)
		writeDuration := time.Since(writeStart)
		if err != nil {
			log.Printf("写入PTY失败: %v, 耗时: %v", err, writeDuration)
			return
		}

		log.Printf("⏱️ 写入PTY成功: %d字节, 耗时: %v", len(inputBuffer), writeDuration)
		inputBuffer = inputBuffer[:0]
	}

	for {
		select {
		case <-ctx.Done():
			flush()
			return
		case <-flushTimer.C:
			if len(inputBuffer) > 0 {
				log.Printf("⏱️ 输入定时器触发，缓冲区: %d字节", len(inputBuffer))
			}
			flush()
		case input := <-cm.ptyInputChan:
			receiveTime := time.Now()
			log.Printf("⏱️ 收到输入数据: %d字节, 内容: %q", len(input), string(input))

			// 🚀 优化：单字符输入立即写入，无缓冲延迟
			if len(input) == 1 {
				writeStart := time.Now()
				_, err := cm.pty.Write(input)
				writeDuration := time.Since(writeStart)
				if err != nil {
					log.Printf("写入PTY失败: %v, 耗时: %v", err, writeDuration)
				} else {
					processDelay := time.Since(receiveTime)
					log.Printf("⏱️ 单字符立即写入: %q, 写入耗时: %v, 总延迟: %v", string(input), writeDuration, processDelay)
				}
				continue
			}

			// 多字符输入加入缓冲区
			inputBuffer = append(inputBuffer, input...)

			// 如果是回车或者缓冲区满了，立即刷新
			shouldFlush := false
			if bytes.Contains(input, []byte{'\r'}) || bytes.Contains(input, []byte{'\n'}) {
				shouldFlush = true
				log.Printf("⏱️ 检测到回车/换行，立即刷新")
			} else if len(inputBuffer) > 64 { // 进一步降低阈值
				shouldFlush = true
				log.Printf("⏱️ 输入缓冲区满，立即刷新")
			}

			if shouldFlush {
				processDelay := time.Since(receiveTime)
				log.Printf("⏱️ 输入处理延迟: %v", processDelay)
				flush()
			}
		}
	}
}

func (cm *ConnectionManager) startPTY(ctx context.Context) error {
	osName := strings.ToLower(runtime.GOOS)
	var shell string
	switch osName {
	case "windows":
		shell = "powershell.exe"
	case "linux":
		shell = "/bin/bash"
	case "darwin":
		shell = "/bin/zsh"
	default:
		shell = "/bin/sh"
	}

	cm.cmdMutex.Lock()
	defer cm.cmdMutex.Unlock()

	// 启动 PTY
	pty, err := NewWithSize(cm.terminalSize)
	if err != nil {
		return fmt.Errorf("启动PTY失败: %v", err)
	}
	cmd := pty.CommandContext(ctx, shell)
	cm.cmd = cmd
	cm.pty = pty

	return nil
}

func (cm *ConnectionManager) sendPTYOutput(ctx context.Context) {
	cm.sendPTYOutputWithTerminalID(ctx, 0) // 主终端ID=0
}

func (cm *ConnectionManager) sendPTYOutputWithTerminalID(ctx context.Context, terminalID uint32) {
	// 🚀 优化策略：
	// 1. 减少定时器频率，提高响应速度
	// 2. 降低缓冲阈值，更快发送
	// 3. 优化包创建和发送逻辑

	buf := bytes.NewBuffer(nil)
	flushTimer := time.NewTicker(1 * time.Millisecond) // 1ms，更快响应
	defer flushTimer.Stop()

	flush := func() {
		if buf.Len() == 0 {
			return
		}
		flushStart := time.Now()

		// 创建CommandResponse结构体
		cmdResponse := CommandResponse{
			TerminalID: terminalID,
			Output:     buf.String(),
			Success:    true,
			Error:      "",
		}

		// 序列化CommandResponse
		responseData, err := cm.serializer.Serialize(cmdResponse)
		if err != nil {
			log.Printf("序列化CommandResponse失败: %v", err)
			return
		}

		packets, err := cm.CreatePackets(responseData, RunCommand, ExecOutput)
		if err != nil {
			log.Printf("创建输出包失败: %v", err)
			return
		}

		// 批量发送所有包
		for _, packet := range packets {
			packetBytes := packet.Serialize()
			if _, err = cm.conn.Write(packetBytes); err != nil {
				log.Printf("发送输出失败: %v", err)
				return
			}
		}

		totalFlushDuration := time.Since(flushStart)
		log.Printf("⏱️ 批量发送%d个包，总耗时: %v, 数据大小: %d, 终端ID: %d", len(packets), totalFlushDuration, buf.Len(), terminalID)

		buf.Reset()
	}

	for {
		select {
		case <-ctx.Done():
			flush()
			return

		case <-flushTimer.C:
			flush()

		case output, ok := <-cm.ptyOutputChan:
			if !ok {
				flush()
				return
			}

			buf.Write(output)

			// 🚀 优化：更激进的立即刷新策略
			// 1. 降低缓冲阈值到512字节
			// 2. 检测更多触发字符
			shouldFlush := buf.Len() > 512 ||
				bytes.Contains(output, []byte{'\n'}) ||
				bytes.Contains(output, []byte{'\r'}) ||
				bytes.Contains(output, []byte{'\x1b'}) // ANSI转义序列

			if shouldFlush {
				flush()
			}
		}
	}
}

func (cm *ConnectionManager) handleTermResize(packet *Packet) {
	if len(packet.PacketData.Data) < 4 {
		log.Printf("无效的终端大小数据")
		return
	}
	log.Println("开始Resize")
	cols := binary.BigEndian.Uint16(packet.PacketData.Data[:2])
	rows := binary.BigEndian.Uint16(packet.PacketData.Data[2:4])

	err := cm.pty.Resize(int(cols), int(rows))
	if err != nil {
		log.Printf("Resize失败: %v", err)
		return
	}
	log.Println("结束Resize")
}

// handleMainTerminalResize 处理主终端的RESIZE命令
func (cm *ConnectionManager) handleMainTerminalResize(command string) {
	// 解析RESIZE命令: RESIZE:cols:rows
	parts := strings.Split(command, ":")
	if len(parts) != 3 {
		log.Printf("无效的RESIZE命令格式: %s", command)
		return
	}

	cols, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Printf("无效的列数: %s", parts[1])
		return
	}

	rows, err := strconv.ParseInt(parts[2], 10, 32)
	if err != nil {
		log.Printf("无效的行数: %s", parts[2])
		return
	}

	// 调整主终端大小
	if cm.pty != nil {
		err := cm.pty.Resize(int(cols), int(rows))
		if err != nil {
			log.Printf("主终端Resize失败: %v", err)
		} else {
			log.Printf("✅ 主终端大小已调整为: %dx%d", cols, rows)
		}
	}
}

// handleCreateTerminalCommand 处理创建新终端的命令
func (cm *ConnectionManager) handleCreateTerminalCommand() {
	log.Println("收到创建新终端命令")

	// 使用多终端管理器创建新的备用终端
	terminalID, err := cm.multiTerminalManager.CreateBackupTerminal(cm.ctx)
	if err != nil {
		log.Printf("创建备用终端失败: %v", err)

		// 发送错误响应
		errorResponse := CommandResponse{
			TerminalID: 0,
			Output:     "",
			Success:    false,
			Error:      fmt.Sprintf("创建终端失败: %v", err),
		}

		cm.sendCommandResponse(errorResponse)
		return
	}

	log.Printf("✅ 成功创建备用终端，ID: %d", terminalID)

	// 发送成功响应
	successResponse := CommandResponse{
		TerminalID: terminalID,
		Output:     fmt.Sprintf("终端 %d 创建成功", terminalID),
		Success:    true,
		Error:      "",
	}

	cm.sendCommandResponse(successResponse)
}

// handleGetTerminalListCommand 处理获取终端列表的命令
func (cm *ConnectionManager) handleGetTerminalListCommand() {
	log.Println("收到获取终端列表命令")

	// 使用多终端管理器获取终端列表
	terminalIDs := cm.multiTerminalManager.GetTerminalList()

	log.Printf("当前活跃终端列表: %v", terminalIDs)

	// 构建终端列表响应
	var terminals []map[string]interface{}
	for _, terminalID := range terminalIDs {
		terminalType := "main"
		if terminalID != 0 {
			terminalType = "backup"
		}

		terminals = append(terminals, map[string]interface{}{
			"id":     terminalID,
			"type":   terminalType,
			"active": true,
		})
	}

	// 构建终端列表响应数据
	terminalListData := map[string]interface{}{
		"terminals": terminals,
	}

	log.Printf("✅ 成功获取终端列表，终端数量: %d", len(terminals))

	// 发送终端列表响应 - 直接传递Go对象，让sendResp进行序列化
	cm.sendResp(RunCommand, GetTerminalList, terminalListData)
}

// handleCloseTerminalCommand 处理关闭终端的命令
func (cm *ConnectionManager) handleCloseTerminalCommand(data []byte) {
	// 解析终端ID
	if len(data) < 4 {
		log.Printf("关闭终端命令数据长度不足: %d", len(data))
		return
	}

	terminalID := binary.LittleEndian.Uint32(data[:4])
	log.Printf("收到关闭终端命令，终端ID: %d", terminalID)

	// 使用多终端管理器关闭终端
	err := cm.multiTerminalManager.CloseTerminal(terminalID)
	if err != nil {
		log.Printf("关闭备用终端失败: %v", err)

		// 发送错误响应
		errorResponse := CommandResponse{
			TerminalID: terminalID,
			Output:     "",
			Success:    false,
			Error:      fmt.Sprintf("关闭终端失败: %v", err),
		}

		cm.sendCommandResponse(errorResponse)
		return
	}

	log.Printf("✅ 成功关闭备用终端，ID: %d", terminalID)

	// 发送成功响应
	successResponse := CommandResponse{
		TerminalID: terminalID,
		Output:     fmt.Sprintf("终端 %d 关闭成功", terminalID),
		Success:    true,
		Error:      "",
	}

	cm.sendCommandResponse(successResponse)
}

// sendCommandResponse 发送命令响应
func (cm *ConnectionManager) sendCommandResponse(response CommandResponse) {
	// 使用sendResp方法发送响应
	cm.sendResp(RunCommand, ExecOutput, response)
	log.Printf("✅ 命令响应已发送: TerminalID=%d, Success=%t", response.TerminalID, response.Success)
}
