<template>
  <div class="screenshot-gallery">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <FileImageOutlined class="title-icon" />
          截图库
        </h2>
        <span class="page-subtitle">管理和浏览客户端截图</span>
      </div>
      <div class="header-right">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索文件名"
          style="width: 250px; margin-right: 12px;"
          @search="handleSearch"
          allow-clear
        />
        <a-button @click="refreshScreenshots" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
        <a-button 
          @click="clearAllScreenshots" 
          :loading="clearLoading" 
          danger
          style="margin-left: 8px;"
        >
          <template #icon><DeleteOutlined /></template>
          清空
        </a-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-row">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic title="总截图数" :value="totalCount" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic title="总大小" :value="formatFileSize(totalSize || 0)" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic title="客户端数" :value="uniqueClients.length" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" class="stat-card">
            <a-statistic title="今日截图" :value="todayCount" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <a-space>
        <span>筛选:</span>
        <a-select 
          v-model:value="selectedClientId" 
          placeholder="选择客户端"
          style="width: 200px;"
          allow-clear
          @change="handleClientFilter"
        >
          <a-select-option value="">全部客户端</a-select-option>
          <a-select-option 
            v-for="client in uniqueClients" 
            :key="client.id" 
            :value="client.id"
          >
            {{ client.name || `客户端 ${client.id}` }}
          </a-select-option>
        </a-select>
        
        <a-range-picker 
          v-model:value="dateRange"
          @change="handleDateFilter"
          placeholder="[开始时间, 结束时间]"
        />
        
        <a-select v-model:value="sortBy" style="width: 120px;" @change="handleSort">
          <a-select-option value="created_at_desc">最新优先</a-select-option>
          <a-select-option value="created_at_asc">最旧优先</a-select-option>
          <a-select-option value="size_desc">大小降序</a-select-option>
          <a-select-option value="size_asc">大小升序</a-select-option>
        </a-select>
      </a-space>
    </div>

    <!-- 截图列表 -->
    <div class="content-area">
      <a-spin :spinning="loading">
        <div v-if="filteredScreenshots.length === 0" class="empty-state">
          <a-empty description="暂无截图数据" />
        </div>
        
        <div v-else>
          <div class="screenshot-grid">
            <div 
              v-for="screenshot in paginatedScreenshots" 
              :key="screenshot.id"
              class="screenshot-item"
            >
              <a-card 
                hoverable 
                class="screenshot-card"
                :body-style="{ padding: '12px' }"
              >
                <template #cover>
                  <div class="image-container" @click="previewScreenshot(screenshot)">
                    <img 
                      :src="getScreenshotUrl(screenshot)" 
                      :alt="screenshot.filename"
                      class="screenshot-image"
                      @error="handleImageError"
                    />
                    <div class="image-overlay">
                      <a-space>
                        <a-button type="primary" size="small" @click.stop="previewScreenshot(screenshot)">
                          <template #icon><EyeOutlined /></template>
                        </a-button>
                        <a-button size="small" @click.stop="downloadScreenshot(screenshot)">
                          <template #icon><DownloadOutlined /></template>
                        </a-button>
                        <a-button danger size="small" @click.stop="deleteScreenshot(screenshot)">
                          <template #icon><DeleteOutlined /></template>
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </template>
                
                <div class="screenshot-info">
                  <div class="screenshot-name" :title="screenshot.filename">
                    {{ screenshot.filename }}
                  </div>
                  <div class="screenshot-meta">
                    <div class="client-info">
                      <UserOutlined style="margin-right: 4px;" />
                      {{ getClientName(screenshot.client_id) }}
                    </div>
                    <div class="screenshot-time">
                      {{ formatDate(screenshot.created_at) }}
                    </div>
                    <div class="screenshot-size">
                      {{ formatFileSize(screenshot.size || 0) }}
                    </div>
                  </div>
                </div>
              </a-card>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="filteredScreenshots.length > 0">
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="filteredScreenshots.length"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
        class="custom-pagination"
      />
    </div>

    <!-- 预览弹窗 -->
    <a-modal
      v-model:open="previewModalVisible"
      :title="currentScreenshot?.filename"
      width="80%"
      :footer="null"
      centered
    >
      <div v-if="currentScreenshot" class="preview-content">
        <div class="preview-image-container">
          <img 
            :src="getScreenshotUrl(currentScreenshot)" 
            :alt="currentScreenshot.filename"
            class="preview-image"
            @error="handleImageError"
          />
        </div>
        
        <div class="preview-info">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="文件名">{{ currentScreenshot.filename }}</a-descriptions-item>
            <a-descriptions-item label="客户端">{{ getClientName(currentScreenshot.client_id) }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ formatDate(currentScreenshot.created_at) }}</a-descriptions-item>
            <a-descriptions-item label="文件大小">{{ formatFileSize(currentScreenshot.size || 0) }}</a-descriptions-item>
          </a-descriptions>
          
          <div class="preview-actions" style="margin-top: 16px;">
            <a-space>
              <a-button @click="downloadScreenshot(currentScreenshot)">
                <template #icon><DownloadOutlined /></template>
                下载
              </a-button>
              <a-button danger @click="deleteScreenshot(currentScreenshot)">
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  CalendarOutlined,
  UserOutlined,
  FileImageOutlined,
  DatabaseOutlined,
  DesktopOutlined,
  ClockCircleOutlined,
  GlobalOutlined,
  PictureOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
import { getAllScreenshots, deleteScreenshotById } from '@/api/screenshot'
import { formatDate, formatFileSize } from '@/utils/format'
import dayjs from 'dayjs'

// 状态变量
const loading = ref(false)
const clearLoading = ref(false)
const screenshots = ref([])
const searchKeyword = ref('')
const selectedClientId = ref('')
const dateRange = ref([])
const sortBy = ref('created_at_desc')
const currentPage = ref(1)
const pageSize = ref(20)

// 预览相关
const previewModalVisible = ref(false)
const currentScreenshot = ref(null)

// 计算属性
const uniqueClients = computed(() => {
  const clientMap = new Map()
  screenshots.value.forEach(screenshot => {
    if (!clientMap.has(screenshot.client_id)) {
      clientMap.set(screenshot.client_id, {
        id: screenshot.client_id,
        name: screenshot.client_name,
        ip: screenshot.client_ip
      })
    }
  })
  return Array.from(clientMap.values())
})

const filteredScreenshots = computed(() => {
  let result = screenshots.value

  // 搜索过滤
  if (searchKeyword.value) {
    result = result.filter(screenshot => 
      screenshot.filename.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 客户端过滤
  if (selectedClientId.value) {
    result = result.filter(screenshot => screenshot.client_id === selectedClientId.value)
  }

  // 时间范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    result = result.filter(screenshot => {
      const screenshotDate = dayjs(screenshot.created_at)
      return screenshotDate.isAfter(startDate.startOf('day')) && 
             screenshotDate.isBefore(endDate.endOf('day'))
    })
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'created_at_desc':
        return new Date(b.created_at) - new Date(a.created_at)
      case 'created_at_asc':
        return new Date(a.created_at) - new Date(b.created_at)
      case 'size_desc':
        return b.size - a.size
      case 'size_asc':
        return a.size - b.size
      default:
        return 0
    }
  })

  return result
})

const paginatedScreenshots = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredScreenshots.value.slice(start, end)
})

const totalCount = computed(() => screenshots.value.length)
const totalSize = computed(() => screenshots.value.reduce((sum, s) => sum + s.size, 0))
const todayCount = computed(() => {
  const today = dayjs().startOf('day')
  return screenshots.value.filter(s => dayjs(s.created_at).isAfter(today)).length
})

// 方法
const getScreenshotUrl = (screenshot) => {
  // 使用下载API来获取截图文件，和客户端管理页面保持一致
  const token = localStorage.getItem('token') || sessionStorage.getItem('token')
  // 使用file_path作为相对路径
  let relativePath = screenshot.filename
  if (screenshot.file_path) {
    relativePath = screenshot.file_path
  }
  return `/api/download/screenshot?path=${encodeURIComponent(relativePath)}&token=${token}`
}

const getClientName = (clientId) => {
  const client = uniqueClients.value.find(c => c.id === clientId)
  return client ? (client.name || `客户端 ${clientId}`) : `客户端 ${clientId}`
}

const loadScreenshots = async () => {
  loading.value = true
  try {
    const response = await getAllScreenshots()
    if (response.code === 200) {
      console.log(response);
      screenshots.value = response.data || []
    } else {
      message.error(response.message || '获取截图列表失败')
    }
  } catch (error) {
    console.error('获取截图列表失败:', error)
    message.error('获取截图列表失败')
  } finally {
    loading.value = false
  }
}

const refreshScreenshots = () => {
  loadScreenshots()
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleClientFilter = () => {
  currentPage.value = 1
}

const handleDateFilter = () => {
  currentPage.value = 1
}

const handleSort = () => {
  currentPage.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
}

const handlePageSizeChange = (current, size) => {
  pageSize.value = size
  currentPage.value = 1
}

const previewScreenshot = (screenshot) => {
  currentScreenshot.value = screenshot
  previewModalVisible.value = true
}

const downloadScreenshot = (screenshot) => {
  const url = getScreenshotUrl(screenshot)
  const link = document.createElement('a')
  link.href = url
  link.download = screenshot.filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const deleteScreenshot = (screenshot) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除截图 "${screenshot.filename}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const response = await deleteScreenshotById(screenshot.id)
        if (response.code === 200) {
          message.success('删除成功')
          loadScreenshots()
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除截图失败:', error)
        message.error('删除失败')
      }
    }
  })
}

const clearAllScreenshots = () => {
  Modal.confirm({
    title: '确认清空',
    content: '确定要删除所有截图吗？此操作不可恢复！',
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      clearLoading.value = true
      try {
        // 批量删除所有截图
        const deletePromises = screenshots.value.map(screenshot => 
          deleteScreenshotById(screenshot.id)
        )
        await Promise.all(deletePromises)
        message.success('清空成功')
        loadScreenshots()
      } catch (error) {
        console.error('清空截图失败:', error)
        message.error('清空失败')
      } finally {
        clearLoading.value = false
      }
    }
  })
}

const handleImageError = (event) => {
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='
}

// 生命周期
onMounted(() => {
  loadScreenshots()
})
</script>

<style scoped>
.screenshot-gallery {
  padding: 32px;
  background: #f2f2f7;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 32px 40px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
  color: #1d1d1f;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.5px;
  color: #1d1d1f;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #1890ff;
  font-size: 24px;
}

.page-subtitle {
  color: #86868b;
  font-size: 17px;
  font-weight: 400;
  line-height: 1.4;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right .ant-input-search {
  border-radius: 12px;
}

.header-right .ant-input-search .ant-input {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header-right .ant-input-search .ant-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.header-right .ant-btn {
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  color: #007aff;
  font-weight: 500;
  padding: 8px 16px;
  height: auto;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header-right .ant-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: #007aff;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.15);
}

/* 统计信息 */
.stats-row {
  margin-bottom: 32px;
}

.stat-card {
  text-align: center;
  border-radius: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: 24px 20px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.9);
}

.stat-card .ant-statistic-title {
  color: #86868b;
  font-weight: 500;
  font-size: 15px;
  letter-spacing: -0.2px;
}

.stat-card .ant-statistic-content {
  color: #1d1d1f;
  font-weight: 700;
  font-size: 28px;
  letter-spacing: -0.5px;
}

/* 筛选栏 */
.filter-bar {
  margin-bottom: 32px;
  padding: 24px 28px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
}

.filter-bar .ant-select,
.filter-bar .ant-picker {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.filter-bar .ant-select:hover,
.filter-bar .ant-picker:hover {
  border-color: #007aff;
  background: rgba(255, 255, 255, 0.95);
}

.filter-bar .ant-select:focus,
.filter-bar .ant-picker:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* 内容区域 */
.content-area {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
  margin-bottom: 32px;
}

/* 截图网格 */
.screenshot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 24px;
}

.screenshot-item {
  position: relative;
}

.screenshot-card {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 420px;
}

.screenshot-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.image-container {
  position: relative;
  height: 220px;
  overflow: hidden;
  cursor: pointer;
  background: #f2f2f7;
  border-radius: 16px 16px 0 0;
  margin: 0;
}

.screenshot-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px 16px 0 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.screenshot-card:hover .screenshot-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  border-radius: 16px 16px 0 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-overlay .ant-btn {
  border-radius: 12px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  color: #007aff;
  width: 44px;
  height: 44px;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.image-overlay .ant-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.2);
}

/* 截图信息 */
.screenshot-info {
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 12px;
}

.screenshot-name {
  font-weight: 600;
  margin-bottom: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #1d1d1f;
  font-size: 16px;
  letter-spacing: -0.2px;
  line-height: 1.3;
}

.screenshot-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.client-info {
  font-size: 13px;
  color: #86868b;
  display: flex;
  align-items: center;
  font-weight: 500;
  padding: 6px 12px;
  background: rgba(0, 122, 255, 0.08);
  border-radius: 8px;
  width: fit-content;
}

.screenshot-time,
.screenshot-size {
  font-size: 12px;
  color: #86868b;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 6px;
  letter-spacing: -0.1px;
}

/* 分页 */
.pagination-container {
  text-align: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
}

.pagination-container .ant-pagination {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.pagination-container .ant-pagination .ant-pagination-item {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
}

.pagination-container .ant-pagination .ant-pagination-item:hover {
  border-color: #007aff;
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.pagination-container .ant-pagination .ant-pagination-item-active {
  background: #007aff;
  border-color: #007aff;
  color: white;
  font-weight: 600;
}

/* 预览弹窗 */
.preview-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.preview-image-container {
  text-align: center;
  background: #f2f2f7;
  border-radius: 20px;
  padding: 32px;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.preview-info {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  padding: 24px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.preview-info .ant-descriptions-item-label {
  color: #86868b;
  font-weight: 500;
  font-size: 15px;
}

.preview-info .ant-descriptions-item-content {
  color: #1d1d1f;
  font-weight: 500;
  font-size: 15px;
}

.preview-actions .ant-btn {
  border-radius: 12px;
  font-weight: 500;
  padding: 8px 20px;
  height: auto;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.preview-actions .ant-btn-primary {
  background: #007aff;
  border-color: #007aff;
}

.preview-actions .ant-btn-primary:hover {
  background: #0056cc;
  border-color: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
}

.preview-actions .ant-btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(255, 59, 48, 0.3);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  position: relative;
  z-index: 1;
}

.empty-state .ant-empty {
  color: #64748b;
}

.empty-state .ant-empty-description {
  color: #94a3b8;
  font-size: 16px;
  font-weight: 500;
}

/* 加载状态优化 */
.ant-spin-container {
  position: relative;
  z-index: 1;
}

.ant-spin .ant-spin-dot {
  font-size: 24px;
}

.ant-spin .ant-spin-dot-item {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

/* 模态框优化 */
.ant-modal .ant-modal-content {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.ant-modal .ant-modal-header {
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 24px 32px 20px;
  border-radius: 20px 20px 0 0;
}

.ant-modal .ant-modal-title {
  color: #1d1d1f;
  font-weight: 600;
  font-size: 20px;
  letter-spacing: -0.3px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ant-modal .ant-modal-body {
  padding: 24px 32px 32px;
  background: transparent;
}

.ant-modal .ant-modal-close {
  top: 20px;
  right: 20px;
  color: #86868b;
  font-size: 16px;
}

.ant-modal .ant-modal-close:hover {
  color: #1d1d1f;
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .screenshot-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .screenshot-gallery {
    padding: 16px;
  }
  
  .screenshot-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .title-icon {
    font-size: 28px;
  }
  
  .header-right {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .header-right .ant-input-search {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }
  
  .stats-row .ant-col {
    margin-bottom: 12px;
  }
  
  .filter-bar {
    padding: 16px;
  }
  
  .filter-bar .ant-space {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  .filter-bar .ant-select,
  .filter-bar .ant-picker {
    width: 100% !important;
  }
  
  .content-area {
    padding: 20px;
  }
  
  .image-container {
    height: 180px;
  }
  
  .screenshot-info {
    padding: 12px 16px;
  }
  
  .pagination-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .screenshot-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .title-icon {
    font-size: 24px;
  }
  
  .stats-row .ant-col {
    span: 12;
  }
  
  .image-container {
    height: 160px;
  }
  
  .ant-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .preview-image {
    max-height: 40vh;
  }
}

/* 焦点状态优化 */
.ant-btn:focus,
.ant-input:focus,
.ant-select:focus {
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.ant-input:focus,
.ant-select:focus,
.ant-picker:focus {
  border-color: #007aff !important;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1) !important;
}

.ant-btn:focus {
  border-color: #007aff !important;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1) !important;
}
</style>