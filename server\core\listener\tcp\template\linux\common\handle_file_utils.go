//go:build linux
// +build linux

package common

import (
	"fmt"
	"os"
	"os/user"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
)

func handleFileMoveError(err error, resp *FileMoveResponse) {
	switch {
	case os.IsPermission(err):
		resp.NotAllow = true
	case os.IsExist(err):
		resp.DestinationExists = true
	default:
		resp.Success = false
	}
}

// 统一错误处理
func handleDeleteError(err error, resp *FileDeleteResponse) {
	switch {
	case os.IsPermission(err):
		resp.NotAllow = true
	case os.IsNotExist(err):
		resp.NotExist = true
	default:
		resp.Success = false
	}
}

// 处理上传错误
func handleUploadError(err error, resp *FileUploadResponse) {
	switch {
	case os.IsPermission(err):
		resp.Error = "权限不足"
	case os.IsExist(err):
		resp.Error = "目标文件已存在"
	case os.IsNotExist(err):
		resp.Error = "路径不存在"
	default:
		resp.Error = "操作失败"
	}
	resp.Success = false
}

// 处理文件操作错误
func handleFileError(err error, resp *FileCopyResponse) {
	switch {
	case os.IsPermission(err):
		resp.NotAllow = true
	case os.IsNotExist(err):
		resp.SourceExists = false
	default:
		resp.Success = false
	}
}

// formatUnixPermissions 格式化Unix权限为字符串形式（如：-rwxr-xr-x）
func formatUnixPermissions(mode os.FileMode) string {
	var result strings.Builder

	// 文件类型
	switch mode & os.ModeType {
	case os.ModeDir:
		result.WriteString("d")
	case os.ModeSymlink:
		result.WriteString("l")
	case os.ModeNamedPipe:
		result.WriteString("p")
	case os.ModeSocket:
		result.WriteString("s")
	case os.ModeDevice:
		result.WriteString("b")
	case os.ModeCharDevice:
		result.WriteString("c")
	default:
		result.WriteString("-")
	}

	// 所有者权限
	if mode&0400 != 0 {
		result.WriteString("r")
	} else {
		result.WriteString("-")
	}
	if mode&0200 != 0 {
		result.WriteString("w")
	} else {
		result.WriteString("-")
	}
	if mode&0100 != 0 {
		if mode&os.ModeSetuid != 0 {
			result.WriteString("s")
		} else {
			result.WriteString("x")
		}
	} else {
		if mode&os.ModeSetuid != 0 {
			result.WriteString("S")
		} else {
			result.WriteString("-")
		}
	}

	// 组权限
	if mode&0040 != 0 {
		result.WriteString("r")
	} else {
		result.WriteString("-")
	}
	if mode&0020 != 0 {
		result.WriteString("w")
	} else {
		result.WriteString("-")
	}
	if mode&0010 != 0 {
		if mode&os.ModeSetgid != 0 {
			result.WriteString("s")
		} else {
			result.WriteString("x")
		}
	} else {
		if mode&os.ModeSetgid != 0 {
			result.WriteString("S")
		} else {
			result.WriteString("-")
		}
	}

	// 其他用户权限
	if mode&0004 != 0 {
		result.WriteString("r")
	} else {
		result.WriteString("-")
	}
	if mode&0002 != 0 {
		result.WriteString("w")
	} else {
		result.WriteString("-")
	}
	if mode&0001 != 0 {
		if mode&os.ModeSticky != 0 {
			result.WriteString("t")
		} else {
			result.WriteString("x")
		}
	} else {
		if mode&os.ModeSticky != 0 {
			result.WriteString("T")
		} else {
			result.WriteString("-")
		}
	}

	return result.String()
}

// getFileOwnerAndGroup 获取文件的拥有者和组信息
func getFileOwnerAndGroup(fileInfo os.FileInfo) (string, string) {
	// 获取文件的系统信息
	stat, ok := fileInfo.Sys().(*syscall.Stat_t)
	if !ok {
		return "unknown", "unknown"
	}

	// 获取拥有者信息
	owner := "unknown"
	if u, err := user.LookupId(strconv.Itoa(int(stat.Uid))); err == nil {
		owner = u.Username
	} else {
		// 如果无法查找用户名，则显示UID
		owner = strconv.Itoa(int(stat.Uid))
	}

	// 获取组信息
	group := "unknown"
	if g, err := user.LookupGroupId(strconv.Itoa(int(stat.Gid))); err == nil {
		group = g.Name
	} else {
		// 如果无法查找组名，则显示GID
		group = strconv.Itoa(int(stat.Gid))
	}

	return owner, group
}

func getUnixFileInfo(path string, taskID uint64) (*FileInfoResponse, error) {
	// 首先使用 Lstat 获取符号链接本身的信息
	linkInfo, err := os.Lstat(path)
	if err != nil {
		return &FileInfoResponse{
			TaskID: taskID,
		}, fmt.Errorf("failed to get file info: %w", err)
	}

	// 检查是否为符号链接
	isSymlink := linkInfo.Mode()&os.ModeSymlink != 0
	actualPath := path

	// 获取文件的拥有者和组信息（基于符号链接本身）
	owner, group := getFileOwnerAndGroup(linkInfo)

	// 如果是符号链接，尝试解析目标路径
	var targetInfo os.FileInfo
	if isSymlink {
		// 解析符号链接的目标路径
		if target, err := os.Readlink(path); err == nil {
			// 如果目标路径是相对路径，需要基于符号链接的目录来解析
			if !strings.HasPrefix(target, "/") {
				dir := filepath.Dir(path)
				actualPath = filepath.Join(dir, target)
			} else {
				actualPath = target
			}
		}

		// 尝试获取目标文件的信息
		if info, err := os.Stat(path); err == nil {
			targetInfo = info
		} else {
			// 如果目标不存在或无法访问，使用符号链接本身的信息
			targetInfo = linkInfo
		}
	} else {
		targetInfo = linkInfo
	}

	response := &FileInfoResponse{
		TaskID:      taskID,
		Path:        path,
		ActualPath:  actualPath,
		Exist:       true,
		Name:        linkInfo.Name(),
		Size:        targetInfo.Size(),
		Mode:        linkInfo.Mode(),
		ModTime:     targetInfo.ModTime(),
		IsDir:       targetInfo.IsDir(),
		IsSymlink:   isSymlink,
		Permissions: formatUnixPermissions(linkInfo.Mode()),
		Owner:       owner,
		Group:       group,
	}
	return response, nil
}

// getMaxFileSizeForFile 统一文件大小限制为200MB，移除文件类型限制
func getMaxFileSizeForFile(filePath string, fileSize int64) int64 {
	// 移除所有文件类型限制，统一设置为200MB
	// 无论什么类型的文件，只要不超过200MB都可以在编辑器中打开
	return 200 * 1024 * 1024 // 200MB
}

// formatFileSize 格式化文件大小显示
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// isBinaryContent 移除二进制文件限制，允许所有文件在编辑器中打开
func isBinaryContent(content []byte) bool {
	// 移除所有二进制文件检查，允许所有文件类型在编辑器中打开
	// 包括二进制文件、图片、可执行文件等都可以以文本形式显示
	return false
}
