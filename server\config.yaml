admin:
    password: admin6666
    username: admin
captcha:
    img-height: 80
    img-width: 240
    key-long: 6
    open-captcha: true
    open-captcha-timeout: 3600
    max-failed-attempts: 5
disk-list: []
jwt:
    buffer-time: 1d
    expires-time: 7d
    issuer: ezUser
    signing-key: "adsadwadsdawdasdwadawd"
notification:
    clientStatus: true
    displayDuration: 5000
    maxNotifications: 3
    soundEnabled: false
server:
    client-bin-dir: ./clientbin
    db-type: sqlite
    download-dir: ./download
    id: b004d5a5-a739-4afc-ae35-aee11e03aa41
    iplimit-count: 15000
    iplimit-time: 3600
    port: 8888
    router-prefix: /api
    server-end-port: 65535
    server-start-port: 20000
    upload-dir: ./upload
    version: 1.0.0
sqlite:
    config: ""
    db-name: ezdata
    engine: ""
    log-mode: error
    log-zap: false
    max-idle-conns: 10
    max-open-conns: 100
    password: ""
    path: ./
    port: ""
    prefix: ""
    singular: false
    username: ""
zap:
    director: log
    encode-level: LowercaseColorLevelEncoder
    format: console
    level: info
    log-in-console: true
    prefix: '[EzC2]'
    retention-day: -1
    show-line: true
    stacktrace-key: stacktrace
