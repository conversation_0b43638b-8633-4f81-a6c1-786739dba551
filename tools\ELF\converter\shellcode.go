package main

import (
	"bytes"
	"compress/gzip"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"io"
)

// ShellcodeBuilder builds the final shellcode from components
type ShellcodeBuilder struct {
	loaderStub    []byte
	elfData       []byte
	compressed    bool
	encrypted     bool
	silent        bool
	password      string
	// Enhanced obfuscation features (compatible with existing)
	multiLayerEnc bool   // Multi-layer encryption
	codeObfusc    bool   // Code obfuscation
	apiObfusc     bool   // API obfuscation
	memProtect    bool   // Memory protection
}

// NewShellcodeBuilder creates a new shellcode builder
func NewShellcodeBuilder() *ShellcodeBuilder {
	return &ShellcodeBuilder{}
}

// SetLoaderStub sets the loader stub code
func (sb *ShellcodeBuilder) SetLoaderStub(stub []byte) {
	sb.loaderStub = make([]byte, len(stub))
	copy(sb.loaderStub, stub)
}

// SetEnhancedOptions sets enhanced obfuscation options (compatible with existing features)
func (sb *ShellcodeBuilder) SetEnhancedOptions(multiLayer, codeObfusc, apiObfusc, memProtect bool) {
	sb.multiLayerEnc = multiLayer
	sb.codeObfusc = codeObfusc
	sb.apiObfusc = apiObfusc
	sb.memProtect = memProtect
}

// SetELFData sets the ELF data to be embedded with optional compression, encryption, and silent mode
func (sb *ShellcodeBuilder) SetELFData(data []byte, compress bool, encrypt bool, silent bool) error {
	processedData := data

	// Step 1: Compression (if enabled)
	if compress {
		compressed, err := compressData(processedData)
		if err != nil {
			return fmt.Errorf("failed to compress ELF data: %v", err)
		}
		processedData = compressed
		sb.compressed = true
		fmt.Printf("ELF data compressed: %d -> %d bytes\n", len(data), len(processedData))
	} else {
		sb.compressed = false
	}

	// Step 2: Enhanced Multi-layer Encryption (if enabled)
	if encrypt {
		if sb.multiLayerEnc {
			// Multi-layer encryption: XOR + AES + Custom (enhanced obfuscation)
			encrypted, password, err := multiLayerEncrypt(processedData)
			if err != nil {
				return fmt.Errorf("multi-layer encryption failed: %v", err)
			}
			processedData = encrypted
			sb.password = password
			fmt.Printf("ELF data encrypted with Multi-layer (XOR+AES+Custom)\n")
			fmt.Printf("🔑 PASSWORD: %s\n", password)
			fmt.Printf("⚠️  SAVE THIS PASSWORD! You need it to load the shellcode.\n")
		} else {
			// Standard AES encryption (existing functionality preserved)
			encrypted, password, err := encryptData(processedData)
			if err != nil {
				return fmt.Errorf("failed to encrypt ELF data: %v", err)
			}
			processedData = encrypted
			sb.password = password
			fmt.Printf("ELF data encrypted with AES-256-GCM\n")
			fmt.Printf("🔑 PASSWORD: %s\n", password)
			fmt.Printf("⚠️  SAVE THIS PASSWORD! You need it to load the shellcode.\n")
		}
		sb.encrypted = true
	} else {
		sb.encrypted = false
	}

	sb.elfData = processedData
	sb.silent = silent
	return nil
}

// Build creates the final shellcode
func (sb *ShellcodeBuilder) Build() ([]byte, error) {
	if len(sb.loaderStub) == 0 {
		return nil, fmt.Errorf("loader stub not set")
	}
	if len(sb.elfData) == 0 {
		return nil, fmt.Errorf("ELF data not set")
	}

	var result bytes.Buffer

	// Write loader stub
	result.Write(sb.loaderStub)

	// Write ELF data size (8 bytes, little-endian)
	sizeBytes := make([]byte, 8)
	binary.LittleEndian.PutUint64(sizeBytes, uint64(len(sb.elfData)))
	result.Write(sizeBytes)

	// Write compression flag (1 byte)
	if sb.compressed {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write encryption flag (1 byte)
	if sb.encrypted {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write silent flag (1 byte)
	if sb.silent {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write enhanced obfuscation flags (4 bytes) - NEW, backward compatible
	if sb.multiLayerEnc {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	if sb.codeObfusc {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	if sb.apiObfusc {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	if sb.memProtect {
		result.WriteByte(1)
	} else {
		result.WriteByte(0)
	}

	// Write ELF data
	result.Write(sb.elfData)

	// Patch the loader stub with the correct offset to ELF size field
	shellcode := result.Bytes()
	// Calculate relative offset from current position (after pop rsi) to ELF size field
	// pop rsi is at position 6 (call 5 bytes + pop 1 byte)
	currentPos := uint32(6)
	elfSizeAbsolutePos := uint32(len(sb.loaderStub))
	relativeOffset := elfSizeAbsolutePos - currentPos

	// Create ASM generator to patch offsets
	// Only patch if the stub contains the expected "add rsi, offset" instruction
	generator := NewASMGenerator()
	stubBytes := shellcode[:len(sb.loaderStub)]

	// Check if this is the old format that needs patching (contains add rsi instruction)
	// Look for the pattern: 48 81 C6 (add rsi, imm32)
	needsPatch := false
	for i := 0; i < len(stubBytes)-2; i++ {
		if stubBytes[i] == 0x48 && stubBytes[i+1] == 0x81 && stubBytes[i+2] == 0xC6 {
			needsPatch = true
			break
		}
	}

	if needsPatch {
		generator.PatchOffsets(stubBytes, relativeOffset)
	}

	return shellcode, nil
}

// compressData compresses data using gzip
func compressData(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	
	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, err
	}
	
	if err := writer.Close(); err != nil {
		return nil, err
	}
	
	return buf.Bytes(), nil
}

// multiLayerEncrypt applies multiple layers of encryption for enhanced obfuscation
func multiLayerEncrypt(data []byte) ([]byte, string, error) {
	// Layer 1: XOR with random key
	xorKey := make([]byte, 16)
	if _, err := rand.Read(xorKey); err != nil {
		return nil, "", fmt.Errorf("failed to generate XOR key: %v", err)
	}

	xorData := make([]byte, len(data))
	for i := 0; i < len(data); i++ {
		xorData[i] = data[i] ^ xorKey[i%len(xorKey)]
	}

	// Layer 2: Custom byte substitution
	customData := make([]byte, len(xorData))
	for i, b := range xorData {
		// Simple substitution cipher (can be enhanced)
		customData[i] = ((b << 3) | (b >> 5)) ^ 0xAA
	}

	// Layer 3: AES-256-GCM (reuse existing function)
	aesData, password, err := encryptData(customData)
	if err != nil {
		return nil, "", fmt.Errorf("AES encryption failed: %v", err)
	}

	// Prepend XOR key to the encrypted data (will be extracted during decryption)
	result := make([]byte, len(xorKey)+len(aesData))
	copy(result[:len(xorKey)], xorKey)
	copy(result[len(xorKey):], aesData)

	return result, password, nil
}

// encryptData encrypts data using AES-256-GCM and returns encrypted data and password
func encryptData(data []byte) ([]byte, string, error) {
	// Generate random 32-byte key for AES-256
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return nil, "", fmt.Errorf("failed to generate encryption key: %v", err)
	}

	// Convert key to hex string for password
	password := hex.EncodeToString(key)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, "", fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Encrypt data
	ciphertext := gcm.Seal(nil, nonce, data, nil)

	// Combine nonce + ciphertext
	encrypted := append(nonce, ciphertext...)

	return encrypted, password, nil
}

// decryptData decrypts AES-256-GCM encrypted data
func decryptData(encryptedData []byte, password string) ([]byte, error) {
	// Decode password to key
	key, err := hex.DecodeString(password)
	if err != nil {
		return nil, fmt.Errorf("invalid password format: %v", err)
	}

	if len(key) != 32 {
		return nil, fmt.Errorf("invalid key length: expected 32 bytes, got %d", len(key))
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// Extract nonce and ciphertext
	nonce := encryptedData[:nonceSize]
	ciphertext := encryptedData[nonceSize:]

	// Decrypt data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %v", err)
	}

	return plaintext, nil
}

// decompressData decompresses gzip data
func decompressData(data []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()
	
	return io.ReadAll(reader)
}

// ValidateShellcode performs basic validation on generated shellcode
func ValidateShellcode(shellcode []byte) error {
	if len(shellcode) < 20 {
		return fmt.Errorf("shellcode too small: %d bytes", len(shellcode))
	}

	// Check for basic structure
	// With advanced features, shellcode might start with anti-analysis code
	// Look for call instruction (0xE8) within the first 50 bytes
	foundCall := false
	for i := 0; i < len(shellcode) && i < 50; i++ {
		if shellcode[i] == 0xE8 {
			foundCall = true
			break
		}
	}

	if !foundCall {
		return fmt.Errorf("shellcode doesn't contain expected call instruction in first 50 bytes")
	}

	return nil
}

// GetShellcodeInfo extracts information from shellcode for debugging
func GetShellcodeInfo(shellcode []byte) (*ShellcodeInfo, error) {
	if len(shellcode) < 20 {
		return nil, fmt.Errorf("shellcode too small")
	}

	// Find the ELF data size and compression flag
	// We need to parse the loader stub to find where the data starts
	// For now, we'll use a simple heuristic
	
	info := &ShellcodeInfo{
		TotalSize: len(shellcode),
	}

	// Try to find the size field by looking for the pattern
	// Try enhanced format first (15 bytes), then fall back to legacy format (11 bytes)
	for i := 0; i < len(shellcode)-17; i++ {
		// Try enhanced format: 8 bytes size + 3 basic flags + 4 enhanced flags = 15 bytes
		if i+15 < len(shellcode) {
			size := binary.LittleEndian.Uint64(shellcode[i : i+8])
			compressed := shellcode[i+8] != 0
			encrypted := shellcode[i+9] != 0
			silent := shellcode[i+10] != 0

			// Validate that this looks like a reasonable size (enhanced format)
			if size > 0 && size < uint64(len(shellcode)) && i+15+int(size) <= len(shellcode) {
				info.StubSize = i
				info.ELFDataSize = int(size)
				info.Compressed = compressed
				info.Encrypted = encrypted
				info.Silent = silent
				info.ELFDataOffset = i + 15  // Enhanced format with 4 additional flags
				break
			}
		}

		// Fall back to legacy format: 8 bytes size + 3 basic flags = 11 bytes
		if i+11 < len(shellcode) {
			size := binary.LittleEndian.Uint64(shellcode[i : i+8])
			compressed := shellcode[i+8] != 0
			encrypted := shellcode[i+9] != 0
			silent := shellcode[i+10] != 0

			// Validate that this looks like a reasonable size (legacy format)
			if size > 0 && size < uint64(len(shellcode)) && i+11+int(size) <= len(shellcode) {
				info.StubSize = i
				info.ELFDataSize = int(size)
				info.Compressed = compressed
				info.Encrypted = encrypted
				info.Silent = silent
				info.ELFDataOffset = i + 11  // Legacy format
				break
			}
		}
	}

	return info, nil
}

// ShellcodeInfo contains information about shellcode structure
type ShellcodeInfo struct {
	TotalSize     int  // Total shellcode size
	StubSize      int  // Loader stub size
	ELFDataSize   int  // ELF data size
	ELFDataOffset int  // Offset to ELF data
	Compressed    bool // Whether ELF data is compressed
	Encrypted     bool // Whether ELF data is encrypted
	Silent        bool // Whether target program runs in silent mode
}

// String returns a string representation of shellcode info
func (info *ShellcodeInfo) String() string {
	return fmt.Sprintf("Shellcode Info:\n"+
		"  Total Size: %d bytes\n"+
		"  Stub Size: %d bytes\n"+
		"  ELF Data Size: %d bytes\n"+
		"  ELF Data Offset: %d\n"+
		"  Compressed: %v\n"+
		"  Encrypted: %v\n"+
		"  Silent: %v",
		info.TotalSize, info.StubSize, info.ELFDataSize, info.ELFDataOffset, info.Compressed, info.Encrypted, info.Silent)
}

// ExtractELFData extracts the ELF data from shellcode
func ExtractELFData(shellcode []byte) ([]byte, error) {
	info, err := GetShellcodeInfo(shellcode)
	if err != nil {
		return nil, err
	}

	if info.ELFDataOffset+info.ELFDataSize > len(shellcode) {
		return nil, fmt.Errorf("invalid ELF data bounds")
	}

	elfData := shellcode[info.ELFDataOffset : info.ELFDataOffset+info.ELFDataSize]

	if info.Compressed {
		return decompressData(elfData)
	}

	return elfData, nil
}
