<template>
  <div class="file-upload">
    <!-- 上传区域 -->
    <a-upload-dragger
      v-model:fileList="fileList"
      name="file"
      :multiple="true"
      :before-upload="beforeUpload"
      :show-upload-list="false"
      @drop="handleDrop"
      class="upload-dragger"
    >
      <p class="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
      <p class="ant-upload-hint">
        上传到{{ targetDirText }}目录
        <span v-if="uploadConfig.maxFileSize" class="size-hint">
          (最大 {{ formatFileSize(uploadConfig.maxFileSize) }})
        </span>
      </p>
      <p v-if="uploadConfig.allowedExtensions && uploadConfig.allowedExtensions.length > 0" class="ext-hint">
        支持格式: {{ uploadConfig.allowedExtensions.join(', ') }}
      </p>
    </a-upload-dragger>

    <!-- 文件列表 -->
    <div v-if="uploadQueue.length > 0" class="upload-queue">
      <div class="queue-header">
        <span>上传队列 ({{ uploadQueue.length }})</span>
        <a-space>
          <a-button 
            size="small" 
            @click="clearCompleted"
            :disabled="!hasCompleted"
          >
            清除已完成
          </a-button>
          <a-button 
            size="small" 
            danger
            @click="clearAll"
          >
            清空队列
          </a-button>
        </a-space>
      </div>
      
      <div class="upload-list">
        <transition-group name="file-list" tag="div">
          <div 
            v-for="item in uploadQueue" 
            :key="item.id" 
            class="upload-item"
            :class="`status-${item.status}`"
          >
            <div class="file-info">
              <div class="file-name">
                <FileOutlined class="file-icon" />
                {{ item.file.name }}
              </div>
              <div class="file-size">{{ formatFileSize(item.file.size) }}</div>
            </div>
            
            <div class="upload-progress">
              <transition name="status-fade" mode="out-in">
                <a-progress 
                  v-if="item.status === 'uploading'"
                  :percent="item.progress"
                  size="small"
                  :show-info="false"
                  :stroke-color="getProgressColor(item)"
                  class="animated-progress"
                  key="uploading"
                />
                <div v-else-if="item.status === 'completed'" class="status-text success" key="completed">
                  <CheckCircleOutlined class="status-icon" /> 上传成功
                </div>
                <div v-else-if="item.status === 'error'" class="status-text error" key="error">
                  <CloseCircleOutlined class="status-icon" /> {{ item.error }}
                </div>
                <div v-else class="status-text pending" key="pending">
                  <ClockCircleOutlined class="status-icon" /> 等待上传
                </div>
              </transition>
            </div>
            
            <div class="upload-actions">
              <transition name="button-fade">
                <a-button 
                  v-if="item.status === 'error'"
                  size="small"
                  type="primary"
                  @click="retryUpload(item)"
                  class="action-button"
                >
                  <template #icon><ReloadOutlined /></template>
                  重试
                </a-button>
              </transition>
              <transition name="button-fade">
                <a-button 
                  size="small"
                  danger
                  @click="removeFromQueue(item.id)"
                  :disabled="item.status === 'uploading'"
                  class="action-button"
                >
                  <template #icon><DeleteOutlined /></template>
                  移除
                </a-button>
              </transition>
            </div>
          </div>
        </transition-group>
      </div>
      
      <!-- 批量操作 -->
      <div class="batch-actions">
        <a-space>
          <a-button 
            type="primary"
            @click="startBatchUpload"
            :loading="isUploading"
            :disabled="!hasPendingFiles"
          >
            <template #icon><UploadOutlined /></template>
            开始上传 ({{ pendingCount }})
          </a-button>
          <a-button 
            @click="pauseAllUploads"
            :disabled="!isUploading"
          >
            <template #icon><PauseCircleOutlined /></template>
            暂停全部
          </a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  getUploadConfig, 
  uploadFileToUploadDir, 
  uploadFileToDownloadDir, 
  validateFile,
  formatFileSize 
} from '@/api/upload'
import { message } from 'ant-design-vue'
import {
  InboxOutlined,
  FileOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  DeleteOutlined,
  UploadOutlined,
  PauseCircleOutlined
} from '@ant-design/icons-vue'

export default {
  name: 'FileUpload',
  components: {
    InboxOutlined,
    FileOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ClockCircleOutlined,
    ReloadOutlined,
    DeleteOutlined,
    UploadOutlined,
    PauseCircleOutlined
  },
  props: {
    targetDir: {
      type: String,
      default: 'upload', // 'upload' 或 'download'
      validator: (value) => ['upload', 'download'].includes(value)
    }
  },
  emits: ['upload-success', 'upload-error', 'upload-complete'],
  data() {
    return {
      fileList: [],
      uploadQueue: [],
      uploadConfig: {},
      isUploading: false,
      nextId: 1
    }
  },
  computed: {
    targetDirText() {
      return this.targetDir === 'upload' ? '上传' : '下载'
    },
    hasPendingFiles() {
      return this.uploadQueue.some(item => item.status === 'pending')
    },
    hasCompleted() {
      return this.uploadQueue.some(item => item.status === 'completed')
    },
    pendingCount() {
      return this.uploadQueue.filter(item => item.status === 'pending').length
    }
  },
  async mounted() {
    await this.loadUploadConfig()
  },
  methods: {
    /**
     * 加载上传配置
     */
    async loadUploadConfig() {
      try {
        this.uploadConfig = await getUploadConfig()
      } catch (error) {
        console.error('加载上传配置失败:', error)
        message.error('加载上传配置失败')
      }
    },
    
    /**
     * 上传前验证
     */
    beforeUpload(file) {
      // 验证文件
      const validation = validateFile(file, this.uploadConfig)
      if (!validation.valid) {
        message.error(validation.error)
        return false
      }
      
      // 添加到上传队列
      this.addToQueue(file)
      
      // 阻止自动上传
      return false
    },
    
    /**
     * 处理拖拽
     */
    handleDrop(e) {
      console.log('文件拖拽:', e)
    },
    
    /**
     * 添加文件到上传队列
     */
    addToQueue(file) {
      const item = {
        id: this.nextId++,
        file,
        status: 'pending', // pending, uploading, completed, error
        progress: 0,
        error: null,
        result: null
      }
      
      this.uploadQueue.push(item)
    },
    
    /**
     * 从队列中移除文件
     */
    removeFromQueue(id) {
      const index = this.uploadQueue.findIndex(item => item.id === id)
      if (index > -1) {
        this.uploadQueue.splice(index, 1)
      }
    },
    
    /**
     * 清除已完成的文件
     */
    clearCompleted() {
      this.uploadQueue = this.uploadQueue.filter(item => item.status !== 'completed')
    },
    
    /**
     * 清空队列
     */
    clearAll() {
      this.uploadQueue = []
    },
    
    /**
     * 开始批量上传
     */
    async startBatchUpload() {
      const pendingItems = this.uploadQueue.filter(item => item.status === 'pending')
      if (pendingItems.length === 0) {
        message.warning('没有待上传的文件')
        return
      }
      
      this.isUploading = true
      
      try {
        // 并发上传（最多3个同时进行）
        const concurrency = 3
        const chunks = []
        for (let i = 0; i < pendingItems.length; i += concurrency) {
          chunks.push(pendingItems.slice(i, i + concurrency))
        }
        
        for (const chunk of chunks) {
          await Promise.all(chunk.map(item => this.uploadSingleFile(item)))
        }
        
        message.success('批量上传完成')
        this.$emit('upload-complete')
      } catch (error) {
        console.error('批量上传失败:', error)
      } finally {
        this.isUploading = false
      }
    },
    
    /**
     * 上传单个文件
     */
    async uploadSingleFile(item) {
      item.status = 'uploading'
      item.progress = 0
      item.error = null
      
      try {
        const uploadFunction = this.targetDir === 'download' 
          ? uploadFileToDownloadDir 
          : uploadFileToUploadDir
          
        const result = await uploadFunction(item.file, {
          onProgress: (progress) => {
            item.progress = progress.progress
          },
          onSuccess: (result) => {
            item.status = 'completed'
            item.result = result
            this.$emit('upload-success', { item, result })
          },
          onError: (error) => {
            item.status = 'error'
            item.error = error.message
            this.$emit('upload-error', { item, error })
          }
        })
        
      } catch (error) {
        item.status = 'error'
        item.error = error.message
        this.$emit('upload-error', { item, error })
      }
    },
    
    /**
     * 重试上传
     */
    async retryUpload(item) {
      await this.uploadSingleFile(item)
    },
    
    /**
     * 暂停所有上传
     */
    pauseAllUploads() {
      // 这里可以实现暂停逻辑
      message.info('暂停功能待实现')
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize(size) {
      if (size === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(size) / Math.log(k))
      return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    /**
     * 获取进度条颜色
     */
    getProgressColor(item) {
      if (item.status === 'error') {
        return '#ff4d4f'
      } else if (item.status === 'completed') {
        return {
          '0%': '#87d068',
          '100%': '#52c41a'
        }
      } else if (item.status === 'uploading') {
        return {
          '0%': '#1890ff',
          '50%': '#40a9ff',
          '100%': '#69c0ff'
        }
      }
      return '#d9d9d9'
    }
  }
}
</script>

<style scoped>
.file-upload {
  margin-bottom: 24px;
}

.upload-dragger {
  margin-bottom: 16px;
}

.upload-dragger .ant-upload {
  padding: 32px 16px;
}

.size-hint {
  color: #666;
  font-size: 12px;
}

.ext-hint {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}

.upload-queue {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  font-weight: 500;
}

.upload-list {
  max-height: 300px;
  overflow-y: auto;
}

.upload-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.upload-item:hover {
  background: #f8f9fa;
}

.upload-item:last-child {
  border-bottom: none;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-icon {
  color: #007bff;
  font-size: 14px;
  flex-shrink: 0;
}

.file-size {
  font-size: 12px;
  color: #666;
}

.upload-progress {
  flex: 1;
  margin: 0 16px;
  min-width: 120px;
}

.status-text {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.status-text.success {
  color: #28a745;
}

.status-text.error {
  color: #dc3545;
}

.status-text.pending {
  color: #6c757d;
}

.upload-actions {
  display: flex;
  gap: 8px;
}

.batch-actions {
  padding: 12px 16px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  text-align: center;
}

/* 状态样式 */
.status-uploading {
  border-left: 3px solid #007bff;
  background: rgba(0, 123, 255, 0.05);
}

.status-completed {
  border-left: 3px solid #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.status-error {
  border-left: 3px solid #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.status-pending {
  border-left: 3px solid #6c757d;
  background: rgba(108, 117, 125, 0.05);
}

/* 动画效果 */
.animated-progress {
  transition: all 0.3s ease;
}

.animated-progress .ant-progress-bg {
  transition: width 0.3s ease;
}

/* 文件列表动画 */
.file-list-enter-active,
.file-list-leave-active {
  transition: all 0.3s ease;
}

.file-list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.file-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.file-list-move {
  transition: transform 0.3s ease;
}

/* 状态变化动画 */
.status-fade-enter-active,
.status-fade-leave-active {
  transition: all 0.2s ease;
}

.status-fade-enter-from,
.status-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 按钮动画 */
.button-fade-enter-active,
.button-fade-leave-active {
  transition: all 0.2s ease;
}

.button-fade-enter-from,
.button-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.action-button {
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-1px);
}

.status-icon {
  transition: all 0.2s ease;
}

.status-text.success .status-icon {
  animation: bounce 0.6s ease;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .upload-progress {
    margin: 0;
  }
  
  .upload-actions {
    justify-content: center;
  }
}
</style>