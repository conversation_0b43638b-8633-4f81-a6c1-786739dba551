package c2

import "server/api"

type RouteGroup struct {
	ListenerRoute
	ClientRoute
	ClientWSRoute
	FileRoute
	DirRoute
	ProcessRoute
	ScreenshotRoute
	NetworkRoute
	HeartbeatConfigRoute
	ProxyRoute
	CronRoute
}

var (
	listenerApi        = api.ApiGroupManagerAPP.C2ApiGroup.ListenerApi
	clientApi          = api.ApiGroupManagerAPP.C2ApiGroup.ClientApi
	fileApi            = api.ApiGroupManagerAPP.C2ApiGroup.FileApi
	dirApi             = api.ApiGroupManagerAPP.C2ApiGroup.DirApi
	procApi            = api.ApiGroupManagerAPP.C2ApiGroup.ProcApi
	screenshotApi      = api.ApiGroupManagerAPP.C2ApiGroup.ScreenshotApi
	networkApi         = api.ApiGroupManagerAPP.C2ApiGroup.NetworkApi
	heartbeatConfigApi = api.ApiGroupManagerAPP.C2ApiGroup.HeartbeatConfigApi
	proxyApi           = api.ApiGroupManagerAPP.C2ApiGroup.ProxyApi
	cronApi            = api.ApiGroupManagerAPP.C2ApiGroup.CronApi
)
