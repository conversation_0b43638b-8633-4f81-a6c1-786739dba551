package pipe

import (
	"server/core/listener/stats"
	"sync"
	"time"
)

// 全局统计管理器
var (
	globalStats = &GlobalStats{
		listeners: make(map[uint]*PipeListener),
	}
)

// GlobalStats 全局统计管理器
type GlobalStats struct {
	listeners map[uint]*PipeListener
	mutex     sync.RWMutex
}

// RegisterListener 注册监听器到统计管理器
func RegisterListener(pipeListener *PipeListener) {
	globalStats.mutex.Lock()
	defer globalStats.mutex.Unlock()
	globalStats.listeners[pipeListener.ID] = pipeListener
	pipeListener.initStats()
}

// UnregisterListener 从统计管理器中移除监听器
func UnregisterListener(listenerID uint) {
	globalStats.mutex.Lock()
	defer globalStats.mutex.Unlock()
	delete(globalStats.listeners, listenerID)
}

// GetAllListenerStats 获取所有监听器的统计信息
func GetAllListenerStats() []stats.ListenerStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	var stats []stats.ListenerStats
	for _, pipeListener := range globalStats.listeners {
		stats = append(stats, pipeListener.getStats())
	}
	return stats
}

// GetConnectionStats 获取连接统计信息
func GetConnectionStats() stats.ConnectionStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	var totalConnections, activeConnections int64
	var lastConnTime time.Time
	
	for _, listener := range globalStats.listeners {
		stats := listener.getStats()
		totalConnections += stats.GetTotalConnections()
		activeConnections += stats.GetActiveConnections()
		
		if stats.GetLastActivity().After(lastConnTime) {
			lastConnTime = stats.GetLastActivity()
		}
	}
	
	// Pipe连接通常是短连接，统计相对简单
	var connectionsPerHour float64
	if len(globalStats.listeners) > 0 {
		var earliestStart time.Time
		for _, listener := range globalStats.listeners {
			stats := listener.getStats()
			if earliestStart.IsZero() || stats.GetStartTime().Before(earliestStart) {
				earliestStart = stats.GetStartTime()
			}
		}
		
		if !earliestStart.IsZero() {
			hours := time.Since(earliestStart).Hours()
			if hours > 0 {
				connectionsPerHour = float64(totalConnections) / hours
			}
		}
	}
	
	return stats.ConnectionStats{
		TotalConnections:    totalConnections,
		ActiveConnections:   activeConnections,
		ConnectionsPerHour:  connectionsPerHour,
		AverageConnTime:     0.1, // Pipe连接通常很短
		LastConnectionTime:  lastConnTime,
		DisconnectionsToday: 0,
	}
}

// GetTrafficStats 获取流量统计信息
func GetTrafficStats() stats.TrafficStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	var bytesReceived, bytesSent, packetsReceived, packetsSent int64
	var totalDataTransferred int64
	var earliestStart time.Time
	
	for _, listener := range globalStats.listeners {
		listener.stats.mutex.RLock()
		bytesReceived += listener.stats.bytesReceived
		bytesSent += listener.stats.bytesSent
		packetsReceived += listener.stats.packetsReceived
		packetsSent += listener.stats.packetsSent
		totalDataTransferred += listener.stats.dataTransferred
		
		if earliestStart.IsZero() || listener.stats.startTime.Before(earliestStart) {
			earliestStart = listener.stats.startTime
		}
		listener.stats.mutex.RUnlock()
	}
	
	// 计算平均速度（MB/s）
	var averageSpeed float64
	if !earliestStart.IsZero() {
		hours := time.Since(earliestStart).Hours()
		if hours > 0 {
			totalMB := float64(totalDataTransferred) / (1024 * 1024)
			averageSpeed = totalMB / hours / 3600 // MB/s
		}
	}
	
	return stats.TrafficStats{
		BytesReceived:   bytesReceived,
		BytesSent:       bytesSent,
		PacketsReceived: packetsReceived,
		PacketsSent:     packetsSent,
		AverageSpeed:    averageSpeed,
		PeakSpeed:       averageSpeed * 1.5, // 简化的峰值计算
		LastResetTime:   earliestStart,
	}
}

// GetListenerByID 根据ID获取监听器统计
func GetListenerByID(id uint) stats.ListenerStats {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	pipeListener := globalStats.listeners[id]
	if pipeListener == nil {
		return nil
	}
	return pipeListener.getStats()
}

// GetActiveListenerCount 获取活跃监听器数量
func GetActiveListenerCount() int {
	globalStats.mutex.RLock()
	defer globalStats.mutex.RUnlock()
	
	count := 0
	for _, listener := range globalStats.listeners {
		if listener.Status == 1 {
			count++
		}
	}
	return count
}

// ResetStats 重置所有统计信息
func ResetStats() {
	globalStats.mutex.Lock()
	defer globalStats.mutex.Unlock()
	
	for _, listener := range globalStats.listeners {
		listener.stats.mutex.Lock()
		listener.stats.totalConnections = 0
		listener.stats.dataTransferred = 0
		listener.stats.packetsProcessed = 0
		listener.stats.errorCount = 0
		listener.stats.bytesReceived = 0
		listener.stats.bytesSent = 0
		listener.stats.packetsReceived = 0
		listener.stats.packetsSent = 0
		listener.stats.startTime = time.Now()
		listener.stats.lastActivity = time.Now()
		listener.stats.mutex.Unlock()
	}
}

// PipeStatsProvider Pipe统计提供者
type PipeStatsProvider struct{}

// GetAllListenerStats 实现 ListenerStatsProvider 接口
func (p *PipeStatsProvider) GetAllListenerStats() []stats.ListenerStats {
	return GetAllListenerStats()
}

// GetConnectionStats 实现 ListenerStatsProvider 接口
func (p *PipeStatsProvider) GetConnectionStats() stats.ConnectionStats {
	return GetConnectionStats()
}

// GetTrafficStats 实现 ListenerStatsProvider 接口
func (p *PipeStatsProvider) GetTrafficStats() stats.TrafficStats {
	return GetTrafficStats()
}

// GetListenerByID 实现 ListenerStatsProvider 接口
func (p *PipeStatsProvider) GetListenerByID(id uint) stats.ListenerStats {
	return GetListenerByID(id)
}

// GetActiveListenerCount 实现 ListenerStatsProvider 接口
func (p *PipeStatsProvider) GetActiveListenerCount() int {
	return GetActiveListenerCount()
}

// ResetStats 实现 ListenerStatsProvider 接口
func (p *PipeStatsProvider) ResetStats() {
	ResetStats()
}

// GetStatsProvider 获取Pipe统计提供者实例
func GetStatsProvider() stats.ListenerStatsProvider {
	return &PipeStatsProvider{}
}
