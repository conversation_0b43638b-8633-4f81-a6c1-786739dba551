#!/bin/bash

# 设置错误时退出
set -e

# 显示执行的命令
set -x

# 项目根目录
PROJECT_ROOT=$(pwd)
FRONTEND_DIR="$PROJECT_ROOT/fronted"
SERVER_DIR="$PROJECT_ROOT/server"
DIST_DIR="$FRONTEND_DIR/dist"
SERVER_DIST_DIR="$SERVER_DIR/core/static/dist"

mkdir -p release

# 清理旧的构建文件
echo "清理旧的构建文件..."
rm -rf "$DIST_DIR"
rm -rf "$SERVER_DIST_DIR"

# 构建前端
echo "构建前端..."
cd "$FRONTEND_DIR"
npm install
npm install terser
npm run build

# 确保服务器的静态目录存在
echo "准备服务器静态文件目录..."
mkdir -p "$SERVER_DIST_DIR"

# 复制前端构建到服务器的静态目录
echo "复制前端构建到服务器的静态目录..."
cp -r "$DIST_DIR"/* "$SERVER_DIST_DIR"/

# 构建服务器
echo "构建服务器..."
cd "$SERVER_DIR"
CGO_ENABLED=1 CC="zig cc -target x86_64-windows-gnu" GOOS=windows GOARCH=amd64 go build -o server_windows_amd64.exe .
mv server_windows_amd64.exe "$PROJECT_ROOT/release/server_windows_amd64.exe"
CGO_ENABLED=1 CC="zig cc -target aarch64-windows-gnu" GOOS=windows GOARCH=arm64 go build -o server_windows_arm64.exe .
mv server_windows_arm64.exe "$PROJECT_ROOT/release/server_windows_arm64.exe"
CGO_ENABLED=1 CC="zig cc -target x86_64-linux-gnu" CXX="zig c++ -target x86_64-linux-gnu" GOOS=linux GOARCH=amd64 go build -o "server_linux_amd64" .
mv server_linux_amd64 "$PROJECT_ROOT/release/server_linux_amd64"
CGO_ENABLED=1 CC="zig cc -target aarch64-linux-gnu" CXX="zig c++ -target aarch64-linux-gnu" GOOS=linux GOARCH=arm64 go build -o "server_linux_arm64" .
mv server_linux_arm64 "$PROJECT_ROOT/release/server_linux_arm64"
CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 go build -o "server_darwin_amd64" .
mv server_darwin_amd64 "$PROJECT_ROOT/release/server_darwin_amd64"
CGO_ENABLED=1 GOOS=darwin GOARCH=arm64 go build -o "server_darwin_arm64" .
mv server_darwin_arm64 "$PROJECT_ROOT/release/server_darwin_arm64"

cp config.yaml "$PROJECT_ROOT/release/config.yaml"

# 完成
echo "构建完成！"
echo "可执行文件位于: $PROJECT_ROOT/release/server_windows_amd64.exe"
echo "可执行文件位于: $PROJECT_ROOT/release/server_windows_arm64.exe"
echo "可执行文件位于: $PROJECT_ROOT/release/server_linux_amd64"
echo "可执行文件位于: $PROJECT_ROOT/release/server_linux_arm64"
echo "可执行文件位于: $PROJECT_ROOT/release/server_darwin_amd64"
echo "可执行文件位于: $PROJECT_ROOT/release/server_darwin_arm64"
