//go:build darwin
// +build darwin
package common

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// FileCopyRequest 文件复制请求
type FileCopyRequest struct {
	TaskID          uint64 `json:"task_id"`          // 任务ID
	Source          string `json:"source"`           // 源文件路径
	Destination     string `json:"destination"`      // 目标文件路径
	Force           bool   `json:"force"`            // 是否强制覆盖
	PreserveAttrs   bool   `json:"preserve_attrs"`   // 是否保留文件属性
	FollowSymlinks  bool   `json:"follow_symlinks"`  // 是否跟随符号链接
	VerifyIntegrity bool   `json:"verify_integrity"` // 是否验证完整性
}

// FileCopyResponse 文件复制响应
type FileCopyResponse struct {
	TaskID            uint64 `json:"task_id"`            // 任务ID
	Success           bool   `json:"success"`            // 操作是否成功
	SourceExists      bool   `json:"source_exists"`      // 源文件是否存在
	DestinationExists bool   `json:"destination_exists"` // 目标文件是否已存在
	NotAllow          bool   `json:"not_allow"`          // 是否权限不足
	BytesCopied       int64  `json:"bytes_copied"`       // 已复制的字节数
	Error             string `json:"error"`              // 错误信息
	ActualSource      string `json:"actual_source"`      // 实际源路径
	ActualDestination string `json:"actual_destination"` // 实际目标路径
}

// handleFileCopy 处理文件复制请求
func (cm *ConnectionManager) handleFileCopy(packet *Packet) {
	var req FileCopyRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileCopyResponse{
		TaskID:            0,
		Success:           false,
		SourceExists:      false,
		NotAllow:          false,
		DestinationExists: false,
		Error:             "",
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileCopyRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "请求格式错误"
		cm.sendResp(File, FileCopy, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用读锁保护文件复制（读取源文件）
	fileMutex.RLock()
	resp := copyFile(&req)
	fileMutex.RUnlock()

	cm.sendResp(File, FileCopy, resp)
}

// copyFile 执行文件复制的核心逻辑
func copyFile(req *FileCopyRequest) *FileCopyResponse {
	// 参数验证
	if req.Source == "" || req.Destination == "" {
		return &FileCopyResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "源路径或目标路径不能为空",
		}
	}

	// 获取绝对路径
	absSource, err := getAbsolutePath(req.Source)
	if err != nil {
		return &FileCopyResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   fmt.Sprintf("源路径解析失败: %v", err),
		}
	}

	absDestination, err := getAbsolutePath(req.Destination)
	if err != nil {
		return &FileCopyResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   fmt.Sprintf("目标路径解析失败: %v", err),
		}
	}

	// 初始化响应
	resp := &FileCopyResponse{
		TaskID:            req.TaskID,
		Success:           false,
		SourceExists:      false,
		DestinationExists: false,
		NotAllow:          false,
		ActualSource:      absSource,
		ActualDestination: absDestination,
	}

	// 检查源文件是否存在且是普通文件
	sourceExists, err := isRegularFile(absSource)
	if err != nil {
		log.Printf("检查源文件出错: %v", err)
		handleFileError(err, resp)
		return resp
	}

	if !sourceExists {
		resp.SourceExists = false
		resp.Error = "源文件不存在或不是普通文件"
		return resp
	}
	resp.SourceExists = true

	// 检查目标文件是否存在
	destExists, err := PathExists(absDestination)
	if err != nil {
		log.Printf("检查目标路径出错: %v", err)
		handleFileError(err, resp)
		return resp
	}
	resp.DestinationExists = destExists

	// 处理目标文件已存在的情况
	if destExists && !req.Force {
		log.Printf("目标文件已存在且未启用强制覆盖: %s", absDestination)
		resp.Error = "目标文件已存在"
		return resp
	}

	// 确保目标目录存在
	destDir := filepath.Dir(absDestination)
	if err = os.MkdirAll(destDir, 0755); err != nil {
		log.Printf("创建目标目录失败: %v", err)
		resp.Error = fmt.Sprintf("创建目标目录失败: %v", err)
		handleFileError(err, resp)
		return resp
	}

	// 获取源文件大小
	srcInfo, err := os.Stat(absSource)
	if err != nil {
		resp.Error = fmt.Sprintf("获取源文件信息失败: %v", err)
		return resp
	}

	// 执行文件复制
	if req.PreserveAttrs {
		err = copyFileWithAttributes(absSource, absDestination, true)
	} else {
		err = copyFileContents(absSource, absDestination)
	}

	if err != nil {
		log.Printf("文件复制失败: %v", err)
		resp.Error = fmt.Sprintf("文件复制失败: %v", err)
		handleFileError(err, resp)
		return resp
	}

	// 验证复制结果
	if req.VerifyIntegrity {
		if !verifyFileCopy(absSource, absDestination) {
			resp.Error = "文件复制完整性验证失败"
			_ = os.Remove(absDestination) // 清理失败的复制
			return resp
		}
	}

	resp.Success = true
	resp.BytesCopied = srcInfo.Size()
	log.Printf("文件复制成功: %s -> %s (大小: %d 字节)", absSource, absDestination, srcInfo.Size())
	return resp
}
