package config

type Server struct {
	ID              string `mapstructure:"id" json:"id" yaml:"id"`
	Version         string `mapstructure:"version" json:"version" yaml:"version"`
	DbType          string `mapstructure:"db-type" json:"db-type" yaml:"db-type"` // 数据库类型:mysql(默认)|sqlite|sqlserver|postgresql
	PORT            int    `mapstructure:"port" json:"port" yaml:"port"`
	RouterPrefix    string `mapstructure:"router-prefix" json:"router-prefix" yaml:"router-prefix"`
	LimitCountIP    int    `mapstructure:"iplimit-count" json:"iplimit-count" yaml:"iplimit-count"`
	LimitTimeIP     int    `mapstructure:"iplimit-time" json:"iplimit-time" yaml:"iplimit-time"`
	ClientBinDir    string `mapstructure:"client-bin-dir" json:"client-bin-dir" yaml:"client-bin-dir"` // 客户端二进制文件存储目录
	UploadDir       string `mapstructure:"upload-dir" json:"upload-dir" yaml:"upload-dir"`             // 文件上传目录
	DownloadDir     string `mapstructure:"download-dir" json:"download-dir" yaml:"download-dir"`       // 文件下载目录
	ServerStartPort uint16 `mapstructure:"server-start-port" json:"server-start-port" yaml:"server-start-port"`
	ServerEndPort   uint16 `mapstructure:"server-end-port" json:"server-end-port" yaml:"server-end-port"`
}
