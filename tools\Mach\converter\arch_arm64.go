//go:build arm64

package main

import (
	"debug/macho"
)

// setDefaultArchitecture sets the default architecture for the current build
func setDefaultArchitecture(generator *ASMGenerator) {
	generator.arch = macho.CpuArm64
}

// isArchitectureSupported checks if the given architecture is supported on this build
func isArchitectureSupported(arch macho.Cpu) bool {
	switch arch {
	case macho.CpuArm64:
		return true
	case macho.CpuAmd64:
		// Cross-compilation support - can generate x86_64 code on ARM64
		return true
	default:
		return false
	}
}

// getArchitectureName returns the human-readable name for the architecture
func getArchitectureName(arch macho.Cpu) string {
	switch arch {
	case macho.CpuAmd64:
		return "x86_64"
	case macho.CpuArm64:
		return "ARM64"
	default:
		return "Unknown"
	}
}
