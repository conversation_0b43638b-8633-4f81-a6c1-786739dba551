<template>
  <a-modal
    title="连接正向客户端"
    :open="visible"
    :confirm-loading="loading"
    @cancel="handleCancel"
    @ok="handleSubmit"
    width="600px"
  >
    <a-form
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      ref="formRef"
    >
      <a-form-item label="监听器类型" name="listenerType">
        <a-select 
          v-model:value="form.listenerType" 
          placeholder="请选择监听器类型"
          @change="handleListenerTypeChange"
        >
          <a-select-option value="pipe">PIPE</a-select-option>
          <a-select-option value="tcp">TCP</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="选择监听器" name="listenerId">
        <a-select 
          v-model:value="form.listenerId" 
          placeholder="请选择在线的监听器"
          :loading="listenersLoading"
          :disabled="!form.listenerType"
        >
          <a-select-option 
            v-for="listener in onlineListeners" 
            :key="listener.id"
            :value="listener.id"
          >
            {{ listener.localListenAddr }} - {{ listener.remark || '无备注' }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="客户端地址" name="clientAddr">
        <a-input 
          v-model:value="form.clientAddr" 
          placeholder="例如: *************:4444"
        />
        <div class="form-help">
          <small class="text-gray-500">
            请输入正向客户端监听的IP地址和端口，格式为 IP:端口
          </small>
        </div>
      </a-form-item>

      <a-alert
        v-if="form.listenerType === 'tcp'"
        message="TCP正向连接说明"
        description="TCP正向连接不需要输入key和salt，因为正向客户端二进制文件中已包含公钥信息。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />

      <a-alert
        v-if="form.listenerType === 'pipe'"
        message="PIPE正向连接说明"
        description="PIPE正向连接将直接建立TCP连接到目标地址，适用于传统的反弹shell场景。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { listenerApi } from '@/api';

// 接收父组件传递的属性
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 状态变量
const loading = ref(false);
const listenersLoading = ref(false);
const onlineListeners = ref([]);

// 表单数据
const form = reactive({
  listenerType: undefined,
  listenerId: undefined,
  clientAddr: ''
});

// 表单验证规则
const rules = {
  listenerType: [
    { required: true, message: '请选择监听器类型', trigger: 'change' }
  ],
  listenerId: [
    { required: true, message: '请选择监听器', trigger: 'change' }
  ],
  clientAddr: [
    { required: true, message: '请输入客户端地址', trigger: 'blur' },
    { 
      pattern: /^(\d{1,3}\.){3}\d{1,3}:\d{1,5}$/, 
      message: '请输入正确的IP:端口格式', 
      trigger: 'blur' 
    }
  ]
};

// 处理监听器类型变化
const handleListenerTypeChange = async (type) => {
  form.listenerId = undefined;
  onlineListeners.value = [];
  
  if (type) {
    await loadOnlineListeners(type);
  }
};

// 加载在线监听器列表
const loadOnlineListeners = async (type) => {
  try {
    listenersLoading.value = true;
    const res = await listenerApi.getOnlineListeners(type);
    onlineListeners.value = res.data || [];
    
    if (onlineListeners.value.length === 0) {
      message.warning(`当前没有在线的${type.toUpperCase()}监听器`);
    }
  } catch (error) {
    console.error('获取在线监听器列表失败:', error);
    message.error('获取在线监听器列表失败');
  } finally {
    listenersLoading.value = false;
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    const params = {
      listenerId: form.listenerId,
      clientAddr: form.clientAddr
    };

    await listenerApi.connectForwardClient(params);
    message.success('正向连接请求已发送，请等待连接建立');
    
    emit('success');
    handleCancel();
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('连接正向客户端失败:', error);
    message.error(error.response?.data?.msg || '连接正向客户端失败');
  } finally {
    loading.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  formRef.value?.resetFields();
  form.listenerType = undefined;
  form.listenerId = undefined;
  form.clientAddr = '';
  onlineListeners.value = [];
  emit('update:visible', false);
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    handleCancel();
  }
});
</script>

<style scoped>
.form-help {
  margin-top: 4px;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
