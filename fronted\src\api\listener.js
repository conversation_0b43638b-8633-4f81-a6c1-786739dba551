import { get, post, put, del } from '@/utils/request'

/**
 * 获取监听器列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getListenerList(params) {
  return post('/listener/list', params)
}

/**
 * 获取单个监听器
 * @param {Number} id 监听器ID
 * @returns {Promise}
 */
export function getListener(id) {
  return get(`/listener/${id}`)
}

/**
 * 创建监听器
 * @param {Object} data 监听器数据
 * @returns {Promise}
 */
export function createListener(data) {
  return post('/listener', data)
}

/**
 * 更新监听器
 * @param {Object} data 监听器数据
 * @returns {Promise}
 */
export function updateListener(data) {
  return put('/listener', data)
}

/**
 * 删除监听器
 * @param {Number} id 监听器ID
 * @returns {Promise}
 */
export function deleteListener(id) {
  return del(`/listener/${id}`)
}

/**
 * 获取在线监听器列表
 * @param {String} type 监听器类型
 * @returns {Promise}
 */
export function getOnlineListeners(type) {
  return get(`/listener/online?type=${type}`)
}

/**
 * 连接正向客户端
 * @param {Object} data 连接参数
 * @returns {Promise}
 */
export function connectForwardClient(data) {
  return post('/listener/connect-forward', data)
}

/**
 * 更新监听器状态
 * @param {Object} data 状态数据 {id, status}
 * @returns {Promise}
 */
export function updateListenerStatus(data) {
  return put('/listener/status', data)
}

/**
 * 添加正向连接配置
 * @param {Object} data 正向连接配置数据
 * @returns {Promise}
 */
export function addForwardConnectionConfig(data) {
  return post('/listener/forward-config', data)
}

/**
 * 移除正向连接配置
 * @param {Number} listenerId 监听器ID
 * @param {String} clientAddr 客户端地址
 * @returns {Promise}
 */
export function removeForwardConnectionConfig(listenerId, clientAddr) {
  return del(`/listener/forward-config/${listenerId}/${encodeURIComponent(clientAddr)}`)
}

/**
 * 获取正向连接配置列表
 * @param {Number} listenerId 监听器ID
 * @returns {Promise}
 */
export function getForwardConnectionConfigs(listenerId) {
  return get(`/listener/forward-config/${listenerId}`)
}