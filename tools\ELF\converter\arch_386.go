//go:build linux && 386

package main

import (
	"debug/elf"
)

// setDefaultArchitecture sets the default architecture for the current build
func setDefaultArchitecture(generator *ASMGenerator) {
	generator.arch = elf.EM_386
}

// isArchitectureSupported checks if the given architecture is supported on this build
func isArchitectureSupported(arch elf.Machine) bool {
	switch arch {
	case elf.EM_386:
		return true
	case elf.EM_X86_64:
		// Cross-compilation support - can generate x86_64 code on i386
		return true
	case elf.EM_ARM:
		// Cross-compilation support - can generate ARM code on i386
		return true
	default:
		return false
	}
}

// getArchitectureName returns the human-readable name for the architecture
func getArchitectureName(arch elf.Machine) string {
	switch arch {
	case elf.EM_X86_64:
		return "x86_64"
	case elf.EM_AARCH64:
		return "ARM64"
	case elf.EM_386:
		return "i386"
	case elf.EM_ARM:
		return "ARM"
	default:
		return "Unknown"
	}
}
