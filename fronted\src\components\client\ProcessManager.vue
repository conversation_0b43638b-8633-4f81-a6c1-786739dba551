<template>
  <div class="process-manager">
    <!-- 页面头部 -->
    <div class="process-header">
      <div class="header-left">
        <div class="page-title">
          <AppstoreOutlined class="title-icon" />
          <h2>进程管理</h2>
        </div>
        <div class="process-stats">
          <a-statistic 
            title="总进程数" 
            :value="processes.length" 
            :value-style="{ color: '#1890ff', fontSize: '16px' }"
          />
          <a-divider type="vertical" style="height: 40px" />
          <a-statistic 
            title="运行中" 
            :value="runningCount" 
            :value-style="{ color: '#52c41a', fontSize: '16px' }"
          />
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button 
            @click="refreshProcesses"
            :loading="loading"
            size="large"
            class="action-btn"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新进程
          </a-button>
          <a-button 
            type="primary"
            @click="showStartProcessModal"
            :disabled="!currentClientInfo || currentClientInfo.status !== 1"
            size="large"
            class="action-btn"
          >
            <template #icon>
              <PlayCircleOutlined />
            </template>
            启动进程
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 过滤器卡片 -->
    <a-card class="filter-card" :bordered="false">
      <div class="filter-content">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">搜索过滤</label>
            <a-input 
              v-model:value="filterName" 
              placeholder="输入进程名进行搜索"
              class="search-input"
              allowClear
            >
              <template #prefix>
                <SearchOutlined class="search-icon" />
              </template>
            </a-input>
          </div>
          <div class="filter-group">
              <label class="filter-label">用户筛选</label>
              <a-select 
                v-model:value="filterUser" 
                mode="multiple"
                placeholder="选择用户（支持多选）"
                class="user-select"
                allowClear
                :disabled="uniqueUsers.length === 0"
                :dropdown-style="{ minWidth: '250px' }"
                :max-tag-count="2"
                :max-tag-placeholder="(omittedValues) => `+${omittedValues.length}个用户`"
              >
                <a-select-option 
                  v-for="user in uniqueUsers" 
                  :key="user" 
                  :value="user"
                  :title="user"
                >
                  <div class="user-option">
                    <UserOutlined class="user-option-icon" />
                    <span class="user-option-text">{{ user }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </div>
        </div>
        <div class="filter-row">
          <div class="filter-options">
            <a-checkbox v-model:checked="showSystemProcesses" class="filter-checkbox">
              <SettingOutlined /> 显示系统进程
            </a-checkbox>
            <a-checkbox v-model:checked="showEmptyNameProcesses" class="filter-checkbox">
              <QuestionCircleOutlined /> 显示未知进程
            </a-checkbox>
            <a-checkbox v-model:checked="autoRefresh" class="filter-checkbox">
              <SyncOutlined /> 自动刷新 (1秒)
            </a-checkbox>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 进程列表卡片 -->
    <a-card class="table-card" :bordered="false">
      <div class="table-header">
        <div class="table-title">
          <UnorderedListOutlined class="table-icon" />
          <span>进程列表</span>
        </div>
        <div class="table-info">
          <a-tag color="blue">显示 {{ filteredProcesses.length }} 个进程</a-tag>
        </div>
      </div>
      
      <a-table
        :key="tableKey"
        :columns="processColumns"
        :data-source="filteredProcesses"
        :loading="loading"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 个进程`,
          pageSizeOptions: ['10', '20', '50', '100']
        }"
        @change="handleTableChange"
        size="middle"
        rowKey="pid"
        class="process-table"
        :scroll="{ x: 1400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'pid'">
            <div class="pid-cell">
              <a-tag color="geekblue" class="pid-tag">{{ record.pid }}</a-tag>
            </div>
          </template>
          
          <template v-if="column.dataIndex === 'name'">
            <div class="process-name">
              <div class="process-icon">
                <AppstoreOutlined v-if="!record.system" class="app-icon" />
                <SettingOutlined v-else class="system-icon" />
              </div>
              <div class="process-info">
                <div class="name-text">{{ record.name || '未知进程' }}</div>
                <div class="ppid-text" v-if="record.ppid">PPID: {{ record.ppid }}</div>
              </div>
            </div>
          </template>
          
          <template v-if="column.dataIndex === 'user'">
            <div class="user-cell">
              <UserOutlined class="user-icon" />
              <span class="user-name">{{ record.user }}</span>
            </div>
          </template>
          
          <template v-if="column.dataIndex === 'status'">
            <a-tooltip :title="record.status" placement="topLeft">
              <div class="status-container">
                <a-tag :color="getStatusColor(record.status)" class="status-tag">
                  <span class="status-dot"></span>
                  <span class="status-text">{{ getDisplayStatus(record.status) }}</span>
                </a-tag>
                <div v-if="hasStatusDetails(record.status)" class="status-details">
                  {{ getStatusDetails(record.status) }}
                </div>
              </div>
            </a-tooltip>
          </template>
          
          <template v-if="column.dataIndex === 'cpu'">
            <div class="resource-metric cpu-metric">
              <div class="metric-header">
                <div class="metric-icon cpu-icon">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path fill="currentColor" d="M17,17H7V7H17M21,11V9H19V7C19,5.89 18.1,5 17,5H15V3H13V5H11V3H9V5H7C5.89,5 5,5.89 5,7V9H3V11H5V13H3V15H5V17C5,18.1 5.89,19 7,19H9V21H11V19H13V21H15V19H17C18.1,19 19,18.1 19,17V15H21V13H19V11M16,8H8V16H16V8Z" />
                  </svg>
                </div>
                <div class="metric-value">{{ record.cpu }}%</div>
              </div>
              <div class="metric-bar">
                <div 
                  class="metric-fill cpu-fill" 
                  :style="{ width: record.cpu + '%', background: getCpuGradient(record.cpu) }"
                ></div>
              </div>
              <div class="metric-label">CPU</div>
            </div>
          </template>
          
          <template v-if="column.dataIndex === 'memory'">
            <div class="resource-metric memory-metric">
              <div class="metric-header">
                <div class="metric-icon memory-icon">
                  <svg viewBox="0 0 24 24" width="14" height="14">
                    <path fill="currentColor" d="M17,7H22V9H19V15H22V17H17V19H15V17H9V19H7V17H2V15H5V9H2V7H7V5H9V7H15V5H17V7M17,9V15H15V9H17M9,9V15H7V9H9Z" />
                  </svg>
                </div>
                <div class="metric-value">{{ record.memoryPercent }}%</div>
              </div>
              <div class="metric-bar">
                <div 
                  class="metric-fill memory-fill" 
                  :style="{ width: record.memoryPercent + '%', background: getMemoryGradient(record.memoryPercent) }"
                ></div>
              </div>
              <div class="metric-label">{{ formatMemory(record.memory) }}</div>
            </div>
          </template>
          
          <template v-if="column.dataIndex === 'action'">
            <div class="action-cell">
              <a-space size="small">
                <a-tooltip title="查看详情">
                  <a-button 
                    type="text" 
                    size="small"
                    @click="viewProcessDetails(record)"
                    class="action-btn-small"
                  >
                    <EyeOutlined />
                  </a-button>
                </a-tooltip>
                
                <a-tooltip title="挂起进程">
                  <a-button 
                    type="text" 
                    size="small"
                    @click="suspendProcess(record)"
                    :disabled="record.system || record.status === 'Suspended'"
                    class="action-btn-small"
                  >
                    <PauseCircleOutlined />
                  </a-button>
                </a-tooltip>
                
                <a-tooltip title="恢复进程">
                  <a-button 
                    type="text" 
                    size="small"
                    @click="resumeProcess(record)"
                    :disabled="record.system || record.status !== 'Suspended'"
                    class="action-btn-small"
                  >
                    <CaretRightOutlined />
                  </a-button>
                </a-tooltip>
                
                <a-popconfirm
                  title="确定要终止这个进程吗？"
                  @confirm="killProcess(record)"
                  :disabled="record.system"
                  placement="topRight"
                >
                  <a-tooltip title="终止进程">
                    <a-button 
                      type="text" 
                      danger 
                      size="small"
                      :disabled="record.system"
                      class="action-btn-small danger-btn"
                    >
                      <StopOutlined />
                    </a-button>
                  </a-tooltip>
                </a-popconfirm>
              </a-space>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 启动进程弹窗 -->
    <StartProcessModal
      v-model:visible="startProcessModalVisible"
      :client-info="currentClientInfo"
      @submit="handleStartProcess"
    />

    <!-- 进程详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      :title="`进程详情 - ${currentProcess?.name}`"
      width="80%"
      :footer="null"
    >
      <div v-if="currentProcess" class="process-details">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="进程ID">{{ currentProcess.pid }}</a-descriptions-item>
          <a-descriptions-item label="父进程ID">{{ currentProcess.ppid }}</a-descriptions-item>
          <a-descriptions-item label="进程名">{{ currentProcess.name }}</a-descriptions-item>
          <a-descriptions-item label="用户">{{ currentProcess.user }}</a-descriptions-item>
          <a-descriptions-item label="状态">{{ currentProcess.status }}</a-descriptions-item>
          <a-descriptions-item label="优先级">{{ currentProcess.priority }}</a-descriptions-item>
          <a-descriptions-item label="CPU使用率">{{ currentProcess.cpu }}%</a-descriptions-item>
          <a-descriptions-item label="内存使用">{{ formatMemory(currentProcess.memory) }} ({{ currentProcess.memoryPercent }}%)</a-descriptions-item>
          <a-descriptions-item label="启动时间">{{ currentProcess.create_time }}</a-descriptions-item>
          <a-descriptions-item label="运行时间">{{ currentProcess.runTime }}</a-descriptions-item>
          <a-descriptions-item label="命令行" :span="2">
            <code>{{ currentProcess.cmdline }}</code>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { 
  ReloadOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  CaretRightOutlined,
  AppstoreOutlined,
  SearchOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  SyncOutlined,
  UnorderedListOutlined,
  UserOutlined,
  EyeOutlined,
  StopOutlined
} from '@ant-design/icons-vue';
import processApi from '@/api/process';
import { clientApi } from '@/api/client';
import StartProcessModal from './Process/StartProcessModal.vue';

// 接收属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  clientInfo: {
    type: Object,
    default: () => ({})
  }
});

// 状态变量
const loading = ref(false);
const processes = ref([]);
const currentClientInfo = ref(null);
const tableKey = ref(0); // 用于强制重新渲染表格

// 过滤条件
const filterName = ref('');
const filterUser = ref([]);
const showSystemProcesses = ref(false);
const showEmptyNameProcesses = ref(true); // 新增：是否显示空白进程名的进程
const autoRefresh = ref(false);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
});

// 弹窗状态
const startProcessModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentProcess = ref(null);

// 自动刷新定时器
let autoRefreshTimer = null;

// 表格列定义
const processColumns = computed(() => [
  {
    title: 'PID',
    dataIndex: 'pid',
    key: 'pid',
    width: 100,
    sorter: (a, b) => a.pid - b.pid,
    sortDirections: ['descend', 'ascend']
  },
  {
    title: '进程信息',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true,
    sorter: (a, b) => (a.name || '').localeCompare(b.name || '')
  },
  {
    title: '用户',
    dataIndex: 'user',
    key: 'user',
    width: 120,
    sorter: (a, b) => (a.user || '').localeCompare(b.user || '')
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 220,
    ellipsis: {
      showTitle: false
    },
    filters: statusFilters.value,
    onFilter: (value, record) => record.status === value
  },
  {
    title: 'CPU 使用率',
    dataIndex: 'cpu',
    key: 'cpu',
    width: 200,
    sorter: (a, b) => a.cpu - b.cpu,
    sortDirections: ['descend', 'ascend'],
    defaultSortOrder: 'descend'
  },
  {
    title: '内存使用',
    dataIndex: 'memory',
    key: 'memory',
    width: 200,
    sorter: (a, b) => a.memory - b.memory,
    sortDirections: ['descend', 'ascend']
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]);

// 运行中进程数量
const runningCount = computed(() => {
  return processes.value.filter(p => p.status === 'Running').length;
});

// 获取所有唯一用户列表
const uniqueUsers = computed(() => {
  const users = [...new Set(processes.value.map(p => p.user).filter(user => user && user !== 'unknown'))];
  return users.sort();
});

// 获取所有唯一状态列表
const uniqueStatuses = computed(() => {
  const statuses = [...new Set(processes.value.map(p => p.status).filter(status => status && status !== '-'))];
  return statuses.sort();
});

// 动态生成状态过滤器
const statusFilters = computed(() => {
  return uniqueStatuses.value.map(status => ({
    text: status,
    value: status
  }));
});

// 过滤后的进程列表
const filteredProcesses = computed(() => {
  let filtered = processes.value;
  
  // 按进程名过滤
  if (filterName.value) {
    filtered = filtered.filter(p => 
      (p.name || '').toLowerCase().includes(filterName.value.toLowerCase())
    );
  }
  
  // 按用户过滤
  if (filterUser.value && filterUser.value.length > 0) {
    filtered = filtered.filter(p => filterUser.value.includes(p.user || ''));
  }
  
  // 是否显示系统进程
  if (!showSystemProcesses.value) {
    filtered = filtered.filter(p => !p.system);
  }
  
  // 是否显示空白进程名的进程
  if (!showEmptyNameProcesses.value) {
    filtered = filtered.filter(p => p.name && p.name.trim() !== 'unknown');
  }
  
  pagination.total = filtered.length;
  return filtered;
});

// 获取状态颜色
const getStatusColor = (status) => {
  // 处理复合状态（如 "Sleeping (session leader, multi-threaded)"）
  const baseStatus = status.split(' (')[0];
  
  const colorMap = {
    'Running': 'green',
    'Sleeping': 'blue',
    'Stopped': 'red',
    'Zombie': 'orange',
    'Suspended': 'purple',
    'Traced': 'cyan',
    'Disk Sleep': 'geekblue',
    'Idle': 'lime',
    'Waiting': 'gold',
    'Parked': 'magenta',
    'Dead': 'volcano',
    'Unknown': 'default'
  };
  
  // 根据基础状态返回颜色，如果有修饰符则使用稍微不同的色调
  const baseColor = colorMap[baseStatus] || 'default';
  
  // 如果状态包含修饰符，可以考虑使用不同的样式
  if (status.includes('(')) {
    // 对于有修饰符的状态，可以返回相同颜色但可能在UI中显示不同样式
    return baseColor;
  }
  
  return baseColor;
};

// 获取显示状态（简化版本）
const getDisplayStatus = (status) => {
  if (!status) return 'Unknown';
  
  // 如果状态包含详细信息，只显示主要状态
  const baseStatus = status.split(' (')[0];
  return baseStatus;
};

// 检查是否有状态详情
const hasStatusDetails = (status) => {
  return status && status.includes('(');
};

// 获取状态详情
const getStatusDetails = (status) => {
  if (!status || !status.includes('(')) return '';
  
  // 提取括号内的内容
  const match = status.match(/\(([^)]+)\)/);
  if (match && match[1]) {
    // 将详情进行简化处理
    const details = match[1];
    // 如果详情太长，进行截断
    if (details.length > 30) {
      return details.substring(0, 27) + '...';
    }
    return details;
  }
  return '';
};

// 获取CPU使用率颜色
const getCpuColor = (cpu) => {
  if (cpu > 80) return '#f5222d';
  if (cpu > 50) return '#fa8c16';
  if (cpu > 20) return '#fadb14';
  return '#52c41a';
};

// 获取内存使用率颜色
const getMemoryColor = (percent) => {
  if (percent > 80) return '#f5222d';
  if (percent > 60) return '#fa8c16';
  if (percent > 40) return '#fadb14';
  return '#52c41a';
};

// 获取CPU渐变色
const getCpuGradient = (cpu) => {
  if (cpu > 80) return 'linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%)';
  if (cpu > 60) return 'linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)';
  if (cpu > 40) return 'linear-gradient(135deg, #fadb14 0%, #fff566 100%)';
  if (cpu > 20) return 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)';
  return 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)';
};

// 获取内存渐变色
const getMemoryGradient = (percent) => {
  if (percent > 80) return 'linear-gradient(135deg, #722ed1 0%, #9254de 100%)';
  if (percent > 60) return 'linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)';
  if (percent > 40) return 'linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%)';
  if (percent > 20) return 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)';
  return 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)';
};

// 格式化内存大小（假设输入为字节）
const formatMemory = (bytes) => {
  if (!bytes || bytes === 0 || isNaN(bytes)) {
    return '0 B';
  }
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 格式化CPU百分比
const formatCpuPercent = (percent) => {
  if (!percent || percent === 0 || isNaN(percent)) {
    return 0;
  }
  // 保留2位小数
  return parseFloat(percent.toFixed(2));
};

// 格式化内存百分比
const formatMemoryPercent = (percent) => {
  if (!percent || percent === 0 || isNaN(percent)) {
    return 0;
  }
  // 保留2位小数
  return parseFloat(percent.toFixed(2));
};

// 刷新进程列表
const refreshProcesses = async () => {
  try {
    loading.value = true;
    
    const response = await processApi.getProcessList(props.clientId, {
      showSystem: showSystemProcesses.value
    });
    
    if (response.code === 200 && response.data.data) {
      const rawProcesses = response.data.data.processes || [];
      console.log(rawProcesses);
      // 数据字段映射和处理
      processes.value = rawProcesses.map(proc => ({
        pid: proc.pid,
        ppid: proc.ppid,
        name: proc.name || '',
        user: proc.username !== undefined && proc.username !== null && proc.username !== '' ? proc.username : 'unknown',
        status: proc.status || '-',
        cpu: formatCpuPercent(proc.cpu_percent || 0),
        memory: proc.memory_mb || 0,
        memoryPercent: formatMemoryPercent(proc.memoryPercent || 0),
        priority: proc.priority,
        create_time: proc.create_time || '',
        runTime: proc.runTime || '',
        cmdline: proc.cmdline || '',
        system: proc.system || false
      }));
      
      tableKey.value++;
      
      message.success('进程列表已刷新');
    } else {
      message.error(response.data.data.error || '获取进程列表失败');
    }
  } catch (error) {
    console.error('获取进程列表失败:', error);
    message.error('获取进程列表失败: ' + (error.response?.data?.data?.error || error.response?.data?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 显示启动进程弹窗
const showStartProcessModal = () => {
  startProcessModalVisible.value = true;
};

// 处理启动进程
const handleStartProcess = async (processData) => {
  try {
    const response = await processApi.startProcess(props.clientId, {
      command: processData.command,
      args: processData.args,
      workDir: processData.workDir,
      runAsAdmin: processData.runAsAdmin,
      hideWindow: processData.hideWindow
    });
    
    if (response.code === 200 && response.data.data && response.data.data.error === "") {
      message.success('进程启动成功');
      startProcessModalVisible.value = false;
      // 等待1秒让客户端完成操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      refreshProcesses();
    } else {
      message.error(response.data?.data?.error || '启动进程失败');
    }
  } catch (error) {
    console.error('启动进程失败:', error);
    message.error('启动进程失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  }
};

// 查看进程详情
const viewProcessDetails = async (process) => {
  try {
    const response = await processApi.getProcessDetails(props.clientId, {
      pid: process.pid
    });
    
    if (response.code === 200 && response.data.data && response.data.data.error === "") {
      const processData = response.data.data.process || response.data.data;
      currentProcess.value = {
        pid: processData.pid || process.pid,
        ppid: processData.ppid || process.ppid,
        name: processData.name || process.name,
        user: processData.username || process.username || process.user,
        status: processData.status || process.status,
        priority: processData.priority || process.priority,
        cpu: formatCpuPercent(processData.cpu_percent || process.cpu_percent),
        memory: processData.memory_mb || processData.memory || process.memory,
        memoryPercent: processData.memoryPercent || process.memoryPercent,
        create_time: processData.create_time || process.create_time,
        runTime: processData.runTime || process.runTime,
        cmdline: processData.command_line || processData.cmdline || process.cmdline
      };
      detailModalVisible.value = true;
    } else {
      // 如果API调用失败，使用当前进程信息作为详情
      currentProcess.value = { ...process };
      detailModalVisible.value = true;
      message.warning(response.data?.data?.error || '无法获取详细信息，显示基本信息');
    }
  } catch (error) {
    console.error('获取进程详情失败:', error);
    // 如果API调用失败，使用当前进程信息作为详情
    currentProcess.value = { ...process };
    detailModalVisible.value = true;
    message.warning('无法获取详细信息，显示基本信息');
  }
};

// 终止进程
const killProcess = async (process) => {
  try {
    const response = await processApi.killProcess(props.clientId, {
      pid: process.pid,
      force: false
    });
    
    if (response.code === 200 && response.data.data && response.data.data.error === "") {
      message.success(`进程 ${process.name} (PID: ${process.pid}) 已终止`);
      // 等待1秒让客户端完成操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      // 刷新进程列表
      await refreshProcesses();
    } else {
      message.error(response.data.data?.error || '终止进程失败');
    }
  } catch (error) {
    console.error('终止进程失败:', error);
    message.error('终止进程失败: ' + (error.response?.data?.data?.error || error.response?.data?.data?.message || error.message));
  }
};

// 挂起进程
const suspendProcess = async (process) => {
  try {
    const response = await processApi.suspendProcess(props.clientId, {
      pid: process.pid
    });
    
    if (response.code === 200 && response.data.data && response.data.data.error === "") {
      message.success(`进程 ${process.name} (PID: ${process.pid}) 已挂起`);
      // 等待1秒让客户端完成操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      // 刷新进程列表
      await refreshProcesses();
    } else {
      message.error(response.data.data?.error || '挂起进程失败');
    }
  } catch (error) {
    message.error('挂起进程失败: ' + (error.response?.data?.data?.error || error.response?.data?.data?.message || error.message));
  }
};

// 恢复进程
const resumeProcess = async (process) => {
  try {
    const response = await processApi.resumeProcess(props.clientId, {
      pid: process.pid
    });
    
    if (response.code === 200 && response.data.data && response.data.data.error === "") {
      message.success(`进程 ${process.name} (PID: ${process.pid}) 已恢复`);
      // 等待1秒让客户端完成操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      // 刷新进程列表
      await refreshProcesses();
    } else {
      message.error(response.data.data?.error || '恢复进程失败');
    }
  } catch (error) {
    message.error('恢复进程失败: ' + (error.response?.data?.data?.error || error.response?.data?.data?.message || error.message));
  }
};

// 处理表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

// 设置自动刷新
const setupAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
  }
  
  if (autoRefresh.value) {
    autoRefreshTimer = setInterval(() => {
      refreshProcesses();
    }, 1000); // 5秒刷新一次
  }
};

// 获取客户端信息
const fetchClientInfo = async () => {
  try {
    const response = await clientApi.getClient(props.clientId);
    if (response && response.data) {
      currentClientInfo.value = response.data;
    }
  } catch (error) {
    console.error('获取客户端信息失败:', error);
    currentClientInfo.value = null;
  }
};

// 监听自动刷新设置变化
watch(() => autoRefresh.value, () => {
  setupAutoRefresh();
});

// 组件挂载时获取进程列表和客户端信息
onMounted(() => {
  fetchClientInfo();
  refreshProcesses();
  setupAutoRefresh();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
  }
});
</script>

<style scoped>
.process-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 64px);
}

/* 页面头部样式 */
.process-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  color: #1890ff;
}

.page-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.process-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.process-stats :deep(.ant-statistic-title) {
  color: #8c8c8c;
  font-size: 14px;
}

.process-stats :deep(.ant-statistic-content) {
  color: #262626;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 过滤器卡片样式 */
.filter-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
}

.filter-content {
  padding: 8px 0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 16px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.search-input {
  width: 280px;
  border-radius: 8px;
}

.search-icon {
  color: #bfbfbf;
}

.user-select {
  width: 320px;
  border-radius: 8px;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.user-option-icon {
  color: #722ed1;
  font-size: 14px;
  flex-shrink: 0;
}

.user-option-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filter-options {
  display: flex;
  gap: 24px;
  align-items: center;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* 表格卡片样式 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
  flex: 1;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.table-icon {
  font-size: 18px;
  color: #1890ff;
}

.table-info {
  display: flex;
  align-items: center;
}

/* 表格样式 */
.process-table {
  border-radius: 8px;
  overflow: hidden;
}

.process-table :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 2px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
  padding: 16px 12px;
}

.process-table :deep(.ant-table-tbody > tr > td) {
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
}

.process-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8f9ff;
}

/* 单元格样式 */
.pid-cell {
  display: flex;
  align-items: center;
}

.pid-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  border-radius: 6px;
}

.process-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.process-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f0f2f5;
}

.app-icon {
  color: #1890ff;
  font-size: 16px;
}

.system-icon {
  color: #faad14;
  font-size: 16px;
}

.process-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.name-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.ppid-text {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-icon {
  color: #722ed1;
  font-size: 14px;
}

.user-name {
  font-weight: 500;
  color: #262626;
}

.status-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  cursor: pointer;
  min-height: 40px;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 2px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.status-container:hover {
  background: rgba(24, 144, 255, 0.05);
  transform: translateY(-1px);
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 8px;
  width: fit-content;
  max-width: 100%;
}

.status-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  flex-shrink: 0;
}

.status-details {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
  line-height: 1.3;
  padding: 2px 8px;
  margin-top: 2px;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  border-left: 3px solid currentColor;
  opacity: 0.8;
}

/* 新的资源指标样式 */
.resource-metric {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  border-radius: 8px;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  min-height: 60px;
  justify-content: space-between;
}

.resource-metric:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2px;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cpu-icon {
  color: #1890ff;
}

.memory-icon {
  color: #722ed1;
}

.metric-value {
  font-weight: 700;
  color: #262626;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.metric-bar {
  position: relative;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.metric-fill {
  height: 100%;
  border-radius: 3px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.metric-label {
  font-size: 11px;
  color: #8c8c8c;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

/* 旧样式保留以防兼容性问题 */
.metric-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-percent {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.metric-progress {
  margin: 0;
}

.action-cell {
  display: flex;
  justify-content: center;
}

.action-btn-small {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn-small:hover {
  background: #f0f2f5;
  transform: scale(1.1);
}

.danger-btn:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 进程详情样式 */
.process-details {
  margin-top: 16px;
}

.process-details code {
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  font-size: 13px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .process-manager {
    padding: 16px;
  }
  
  .header-left {
    gap: 16px;
  }
  
  .process-stats {
    display: none;
  }
  
  .filter-row {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .search-input {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .process-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-options {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>