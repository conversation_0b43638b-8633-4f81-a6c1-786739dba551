<template>
  <div class="notification-center">
    <!-- 通知中心头部 -->
    <div class="notification-header">
      <div class="notification-title">
        <BellOutlined />
        <span>通知中心</span>
        <a-badge v-if="unreadCount > 0" :count="unreadCount" />
      </div>
      <div class="notification-actions">
        <a-button 
          v-if="unreadCount > 0" 
          type="link" 
          size="small" 
          @click="markAllAsRead"
        >
          全部已读
        </a-button>
        <a-button 
          type="link" 
          size="small" 
          @click="clearAll"
        >
          清空
        </a-button>
      </div>
    </div>

    <!-- 通知过滤器 -->
    <div class="notification-filters">
      <a-radio-group v-model:value="activeFilter" size="small">
        <a-radio-button value="all">全部</a-radio-button>
        <a-radio-button value="unread">未读</a-radio-button>
        <a-radio-button value="client">客户端</a-radio-button>
        <a-radio-button value="listener">监听器</a-radio-button>
      </a-radio-group>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list">
      <a-spin :spinning="loading">
        <div v-if="filteredNotifications.length === 0" class="notification-empty">
          <InboxOutlined />
          <p>暂无通知</p>
        </div>
        <div
          v-for="notification in filteredNotifications"
          :key="notification.id"
          :class="[
            'notification-item',
            { 'notification-unread': !notification.is_read }
          ]"
          @click="handleNotificationClick(notification)"
        >
          <!-- 通知图标 -->
          <div class="notification-icon">
            <component :is="getNotificationIcon(notification)" />
          </div>

          <!-- 通知内容 -->
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.content }}</div>
            <div class="notification-meta">
              <span class="notification-time">{{ formatTime(notification.created_at) }}</span>
              <a-tag v-if="notification.type" size="small" :color="getTypeColor(notification.type)">
                {{ getTypeText(notification.type) }}
              </a-tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="notification-actions" @click.stop>
            <a-dropdown :trigger="['click']">
              <a-button type="text" size="small" @click.stop>
                <MoreOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="!notification.is_read" @click.stop="markAsRead(notification.id)">
                    <CheckOutlined />
                    标记已读
                  </a-menu-item>
                  <a-menu-item @click.stop="deleteNotification(notification.id)">
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 分页 -->
    <div v-if="total > pageSize" class="notification-pagination">
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :show-size-changer="false"
        :show-quick-jumper="false"
        size="small"
        @change="loadNotifications"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import {
  BellOutlined,
  InboxOutlined,
  MoreOutlined,
  CheckOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  ApiOutlined,
  DisconnectOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')
import {
  getNotificationList,
  markAsRead as markAsReadApi,
  markAllAsRead as markAllAsReadApi,
  deleteNotifications
} from '@/api/notification'

export default {
  name: 'NotificationCenter',
  components: {
    BellOutlined,
    InboxOutlined,
    MoreOutlined,
    CheckOutlined,
    DeleteOutlined,
    InfoCircleOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    CloseCircleOutlined,
    UserAddOutlined,
    UserDeleteOutlined,
    ApiOutlined,
    DisconnectOutlined
  },
  emits: ['notification-click'],
  setup(props, { emit }) {
    const loading = ref(false)
    const notifications = ref([])
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)
    const activeFilter = ref('all')

    // 未读数量
    const unreadCount = computed(() => {
      return notifications.value.filter(n => !n.is_read).length
    })

    // 过滤后的通知列表
    const filteredNotifications = computed(() => {
      let filtered = notifications.value

      switch (activeFilter.value) {
        case 'unread':
          filtered = filtered.filter(n => !n.is_read)
          break
        case 'client':
          filtered = filtered.filter(n => n.type && n.type.startsWith('client_'))
          break
        case 'listener':
          filtered = filtered.filter(n => n.type && n.type.startsWith('listener_'))
          break
      }

      return filtered
    })

    // 加载通知列表
    const loadNotifications = async () => {
      try {
        console.log('🔄 开始加载通知列表...')
        loading.value = true
        const params = {
          page: currentPage.value,
          page_size: pageSize.value
        }

        console.log('📤 请求通知列表参数:', params)
        const response = await getNotificationList(params)
        console.log('📥 通知列表响应:', response)

        // 🚨 修复：后端返回的成功状态码是200，不是0
        if (response.code === 200) {
          notifications.value = response.data.list || []
          total.value = response.data.total || 0
          console.log('✅ 通知列表加载成功:', notifications.value.length, '条通知')
        } else {
          console.error('❌ 通知列表响应错误:', response)
          message.error(response.msg || '加载通知列表失败')
        }
      } catch (error) {
        console.error('❌ 加载通知列表失败:', error)
        message.error('加载通知列表失败')
      } finally {
        loading.value = false
      }
    }

    // 标记单个通知为已读
    const markAsRead = async (notificationId) => {
      try {
        // 🚨 修复：字段名从notification_ids改为ids
        await markAsReadApi({ ids: [notificationId] })
        const notification = notifications.value.find(n => n.id === notificationId)
        if (notification) {
          notification.is_read = true
        }
        message.success('已标记为已读')
      } catch (error) {
        console.error('标记已读失败:', error)
        message.error('标记已读失败')
      }
    }

    // 标记所有通知为已读
    const markAllAsRead = async () => {
      try {
        // 🚨 修复：传递空对象以满足后端JSON解析要求
        await markAllAsReadApi({})
        notifications.value.forEach(n => {
          n.is_read = true
        })
        message.success('已全部标记为已读')
      } catch (error) {
        console.error('标记全部已读失败:', error)
        message.error('标记全部已读失败')
      }
    }

    // 删除通知
    const deleteNotification = async (notificationId) => {
      try {
        // 🚨 修复：字段名从notification_ids改为ids
        await deleteNotifications({ ids: [notificationId] })
        const index = notifications.value.findIndex(n => n.id === notificationId)
        if (index > -1) {
          notifications.value.splice(index, 1)
          total.value--
        }
        message.success('删除成功')
      } catch (error) {
        console.error('删除通知失败:', error)
        message.error('删除通知失败')
      }
    }

    // 清空所有通知
    const clearAll = async () => {
      try {
        const notificationIds = notifications.value.map(n => n.id)
        if (notificationIds.length === 0) return

        // 🚨 修复：字段名从notification_ids改为ids
        await deleteNotifications({ ids: notificationIds })
        notifications.value = []
        total.value = 0
        message.success('清空成功')
      } catch (error) {
        console.error('清空通知失败:', error)
        message.error('清空通知失败')
      }
    }

    // 处理通知点击
    const handleNotificationClick = (notification) => {
      if (!notification.is_read) {
        markAsRead(notification.id)
      }
      emit('notification-click', notification)
    }

    // 获取通知图标
    const getNotificationIcon = (notification) => {
      const iconMap = {
        info: InfoCircleOutlined,
        success: CheckCircleOutlined,
        warning: ExclamationCircleOutlined,
        error: CloseCircleOutlined,
        client_online: UserAddOutlined,
        client_offline: UserDeleteOutlined,
        client_deleted: UserDeleteOutlined,
        listener_created: ApiOutlined,
        listener_deleted: ApiOutlined,
        listener_closed: DisconnectOutlined
      }

      return iconMap[notification.type] || iconMap[notification.level] || InfoCircleOutlined
    }

    // 获取类型颜色
    const getTypeColor = (type) => {
      const colorMap = {
        client_online: 'green',
        client_offline: 'orange',
        client_deleted: 'red',
        listener_created: 'blue',
        listener_deleted: 'red',
        listener_closed: 'orange'
      }
      return colorMap[type] || 'default'
    }

    // 获取类型文本
    const getTypeText = (type) => {
      const textMap = {
        client_online: '客户端上线',
        client_offline: '客户端离线',
        client_deleted: '客户端删除',
        listener_created: '监听器创建',
        listener_deleted: '监听器删除',
        listener_closed: '监听器关闭'
      }
      return textMap[type] || type
    }

    // 格式化时间
    const formatTime = (timeString) => {
      try {
        return dayjs(timeString).fromNow()
      } catch (error) {
        return '未知时间'
      }
    }

    // 监听过滤器变化
    watch(activeFilter, () => {
      currentPage.value = 1
    })

    // 组件挂载时加载数据
    onMounted(() => {
      loadNotifications()
    })

    return {
      loading,
      notifications,
      currentPage,
      pageSize,
      total,
      activeFilter,
      unreadCount,
      filteredNotifications,
      loadNotifications,
      markAsRead,
      markAllAsRead,
      deleteNotification,
      clearAll,
      handleNotificationClick,
      getNotificationIcon,
      getTypeColor,
      getTypeText,
      formatTime
    }
  }
}
</script>

<style scoped>
.notification-center {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.notification-filters {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
}

.notification-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #8c8c8c;
}

.notification-empty .anticon {
  font-size: 48px;
  margin-bottom: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #fafafa;
}

.notification-item.notification-unread {
  background-color: #f6ffed;
  border-left: 3px solid #52c41a;
}

.notification-icon {
  flex-shrink: 0;
  font-size: 18px;
  margin-top: 2px;
  color: #1890ff;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #595959;
  line-height: 1.4;
  margin-bottom: 8px;
  word-break: break-word;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-time {
  font-size: 12px;
  color: #8c8c8c;
}

.notification-actions {
  flex-shrink: 0;
}

.notification-pagination {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
</style>
