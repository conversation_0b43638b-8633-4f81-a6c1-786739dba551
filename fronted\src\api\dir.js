import { get, post, put, del } from '@/utils/request'

/**
 * 创建目录
 * @param {Number} clientId 客户端ID
 * @param {Object} data 创建数据 {path}
 * @returns {Promise}
 */
export function createDir(clientId, data) {
  return post(`/dir/${clientId}/create`, data)
}

/**
 * 获取目录列表
 * @param {Number} clientId 客户端ID
 * @param {Object} data 查询参数 {path}
 * @returns {Promise}
 */
export function listDir(clientId, data) {
  return post(`/dir/${clientId}/list`, data)
}

/**
 * 移动目录
 * @param {Number} clientId 客户端ID
 * @param {Object} data 移动数据 {sourcePath, targetPath}
 * @returns {Promise}
 */
export function moveDir(clientId, data) {
  return post(`/dir/${clientId}/move`, data)
}

/**
 * 删除目录
 * @param {Number} clientId 客户端ID
 * @param {Object} data 删除数据 {path}
 * @returns {Promise}
 */
export function deleteDir(clientId, data) {
  return post(`/dir/${clientId}/delete`, data )
}

/**
 * 复制目录
 * @param {Number} clientId 客户端ID
 * @param {Object} data 复制数据 {sourcePath, targetPath}
 * @returns {Promise}
 */
export function copyDir(clientId, data) {
  return post(`/dir/${clientId}/copy`, data)
}

/**
 * 获取磁盘列表
 * @param {Number} clientId 客户端ID
 * @param {Object} data 查询参数 {includeDetails}
 * @returns {Promise}
 */
export function listDisks(clientId, data = {}) {
  return post(`/dir/${clientId}/disks`, data)
}