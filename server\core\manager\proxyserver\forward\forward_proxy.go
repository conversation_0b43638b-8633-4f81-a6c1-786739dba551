package forward

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net"
	"server/core/manager/dbpool"
	"server/global"
	"server/model/basic"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ===== 主要结构体 =====

// ForwardProxyServer 正向代理服务器实例
type ForwardProxyServer struct {
	// 基本配置
	userPort     uint16
	clientPort   uint16
	clientAddr   string
	userListener net.Listener

	// 模块化组件
	config            *ForwardProxyConfig
	accessController  *ForwardAccessController
	stats             *ForwardProxyStats
	connectionManager *ForwardConnectionManager

	// 控制
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	lock    sync.RWMutex
}

// Start 启动正向代理服务
func (s *ForwardProxyServer) Start(proxy *basic.Proxy) error {
	if global.LOG != nil {
		global.LOG.Info("🚀 [FORWARD-SERVER] 开始启动正向代理服务",
			zap.String("proxyID", proxy.ProxyID),
			zap.Uint16("userPort", s.userPort),
			zap.Uint16("clientPort", s.clientPort),
			zap.String("clientAddr", s.clientAddr))
	}

	s.lock.Lock()
	if s.running {
		s.lock.Unlock()
		if global.LOG != nil {
			global.LOG.Warn("⚠️ [FORWARD-SERVER] 服务已在运行", zap.String("proxyID", proxy.ProxyID))
		}
		return errors.New("服务已在运行")
	}
	s.running = true
	s.lock.Unlock()

	// 启动用户监听器
	userAddr := fmt.Sprintf(":%d", s.userPort)
	if global.LOG != nil {
		global.LOG.Info("🌐 [FORWARD-SERVER] 启动用户监听器",
			zap.String("userAddr", userAddr),
			zap.Uint16("userPort", s.userPort))
	}

	userListener, err := net.Listen("tcp", userAddr)
	if err != nil {
		s.lock.Lock()
		s.running = false
		s.lock.Unlock()
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD-SERVER] 启动用户监听器失败",
				zap.String("userAddr", userAddr),
				zap.Error(err))
		}
		return fmt.Errorf("启动用户监听器失败: %v", err)
	}
	s.userListener = userListener

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD-SERVER] 用户监听器启动成功",
			zap.String("userAddr", userAddr))
	}

	// 更新代理状态和启动时间
	s.config.Status = 1 // 运行中
	s.config.StartedAt = time.Now()
	s.config.ErrorCount = 0
	s.config.LastError = ""

	// 注意：这里不需要手动保存，因为统计信息会通过定期更新自动保存

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD-SERVER] 正向代理启动成功",
			zap.Uint16("userPort", s.userPort),
			zap.Uint16("clientPort", s.clientPort),
			zap.String("clientAddr", s.clientAddr),
			zap.Bool("authRequired", s.config.AuthRequired),
			zap.Int("allowedIPs", len(s.config.allowedIPs)),
			zap.Int("blockedIPs", len(s.config.blockedIPs)))
	}

	// 启动用户连接处理goroutine
	if global.LOG != nil {
		global.LOG.Info("🔄 [FORWARD-SERVER] 启动用户连接处理协程")
	}
	go s.handleUserConnections()

	return nil
}

// handleUserConnections 处理用户连接
func (s *ForwardProxyServer) handleUserConnections() {
	defer s.userListener.Close()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.userListener.Accept()
			if err != nil {
				select {
				case <-s.ctx.Done():
					return
				default:
					if global.LOG != nil {
						global.LOG.Error("接受连接失败", zap.Error(err))
					}
					continue
				}
			}
			go s.handleUserConnection(conn)
		}
	}
}

// handleUserConnection 处理单个用户连接，转发到Client的socks5服务
func (s *ForwardProxyServer) handleUserConnection(userConn net.Conn) {
	defer userConn.Close()

	// 获取客户端IP
	clientIP := ""
	if addr, ok := userConn.RemoteAddr().(*net.TCPAddr); ok {
		clientIP = addr.IP.String()
	}

	// 访问控制检查
	if !s.accessController.IsAllowed(clientIP) {
		global.LOG.Warn("拒绝连接：IP不在允许列表中", zap.String("clientIP", clientIP))
		return
	}

	// 统计连接
	s.stats.AddConnection()
	defer s.stats.RemoveConnection()

	// 生成连接ID并添加到连接管理器
	connID := fmt.Sprintf("%s-%d", clientIP, time.Now().UnixNano())
	s.connectionManager.AddConnection(connID, userConn)
	defer s.connectionManager.RemoveConnection(connID)

	global.LOG.Info("正向代理用户连接已建立",
		zap.String("clientIP", clientIP),
		zap.String("connID", connID))

	// 连接到Client的socks5服务
	// 注意：这里需要使用客户端实际启动的SOCKS5端口，而不是服务端分配的clientPort
	// 客户端的SOCKS5端口在代理启动成功后会更新到数据库中
	clientAddr := fmt.Sprintf("%s:%d", s.clientAddr, s.clientPort)

	if global.LOG != nil {
		global.LOG.Info("🔗 [FORWARD] 尝试连接客户端SOCKS5服务",
			zap.String("clientIP", s.clientAddr),
			zap.Uint16("clientPort", s.clientPort),
			zap.String("fullAddr", clientAddr))
	}

	clientConn, err := net.DialTimeout("tcp", clientAddr, 10*time.Second)
	if err != nil {
		if global.LOG != nil {
			global.LOG.Error("连接到客户端socks5服务失败",
				zap.String("clientAddr", clientAddr),
				zap.Error(err),
				zap.String("诊断建议", "请检查: 1)客户端SOCKS5服务是否正常启动 2)防火墙是否阻止端口 3)网络连通性"))
		}
		return
	}
	defer clientConn.Close()

	// 使用带统计功能的数据转发
	done := make(chan struct{}, 2)

	// 用户 -> Client
	go func() {
		defer func() { done <- struct{}{} }()
		sent, err := io.Copy(clientConn, userConn)
		s.stats.AddBytes(sent, 0)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("用户到客户端数据转发结束", zap.Error(err))
		}
	}()

	// Client -> 用户
	go func() {
		defer func() { done <- struct{}{} }()
		received, err := io.Copy(userConn, clientConn)
		s.stats.AddBytes(0, received)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("客户端到用户数据转发结束", zap.Error(err))
		}
	}()

	// 等待任一方向的转发结束
	<-done
}

// Stop 停止正向代理服务
func (s *ForwardProxyServer) Stop() error {
	s.lock.Lock()
	if !s.running {
		s.lock.Unlock()
		return nil
	}
	s.running = false
	s.lock.Unlock()

	// 取消context，停止所有goroutine
	if s.cancel != nil {
		s.cancel()
	}

	// 关闭所有连接
	s.connectionManager.CloseAll()

	// 停止统计信息定期更新
	s.stats.stopPeriodicUpdate()

	// 更新代理状态和统计信息
	s.config.Status = 0 // 停止
	s.config.StoppedAt = time.Now()

	// 获取统计信息并保存到数据库
	totalConn, activeConn, bytesSent, bytesReceived, lastActivity := s.stats.GetStats()
	s.config.TotalConnections = totalConn
	s.config.ActiveConnections = int(activeConn)
	s.config.BytesTransferred = bytesSent
	s.config.BytesReceived = bytesReceived
	s.config.LastActivity = lastActivity

	// 🚀 最终保存统计信息到数据库
	updates := map[string]interface{}{
		"status":             s.config.Status,
		"stopped_at":         s.config.StoppedAt,
		"total_connections":  s.config.TotalConnections,
		"active_connections": s.config.ActiveConnections,
		"bytes_transferred":  s.config.BytesTransferred,
		"bytes_received":     s.config.BytesReceived,
		"last_activity":      s.config.LastActivity,
		"version":            s.config.Version + 1,
	}
	dbpool.ExecuteDBOperationAsyncAndWait("forward_proxy_final_stats", func(db *gorm.DB) error {
		return db.Model(s.config.Proxy).Updates(updates).Error
	})

	// 关闭用户监听器
	if s.userListener != nil {
		if err := s.userListener.Close(); err != nil {
			if global.LOG != nil {
				global.LOG.Error("关闭用户监听器失败", zap.Error(err))
			}
			return fmt.Errorf("关闭用户监听器失败: %v", err)
		}
	}

	if global.LOG != nil {
		global.LOG.Info("正向代理服务已停止", zap.Uint16("userPort", s.userPort))
	}

	return nil
}
