package uploader

import (
	"os"
	"server/global"
	"server/utils"
	"sync"
	"time"

	"go.uber.org/zap"
)

func init() {
	go cleanupUploadTasks()
}

func cleanupUploadTasks() {
	ticker := time.NewTicker(5 * time.Minute)
	for range ticker.C {
		UploadManager.mutex.Lock()
		now := time.Now()
		for taskID, state := range UploadManager.tasks {
			if now.Sub(state.LastUpdated) > 1*time.Minute {
				global.LOG.Warn("清理过期上传任务", zap.Uint64("taskID", taskID))
				if state.File != nil {
					state.File.Close()
				}
				utils.UpdateTaskStatus(taskID, "failed", "任务超时")
				delete(UploadManager.tasks, taskID)
			}
		}
		UploadManager.mutex.Unlock()
	}
}

type UploadTaskState struct {
	TaskID      uint64
	ClientID    uint
	File        *os.File
	Path        string
	DestPath    string
	NextChunk   int64
	TotalChunks int64
	ChunkSize   int64
	FileSize    int64
	Transferred int64
	LastUpdated time.Time
	Mutex       sync.Mutex
	FileHash    string
}

type UploadTaskManager struct {
	tasks map[uint64]*UploadTaskState // taskID -> state
	mutex sync.RWMutex
}

var UploadManager = &UploadTaskManager{
	tasks: make(map[uint64]*UploadTaskState),
}

func (m *UploadTaskManager) AddTask(state *UploadTaskState) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	global.LOG.Info("添加上传任务", zap.Uint64("taskID", state.TaskID))
	m.tasks[state.TaskID] = state
}

func (m *UploadTaskManager) GetTask(taskID uint64) (*UploadTaskState, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	state, exists := m.tasks[taskID]
	return state, exists
}

func (m *UploadTaskManager) RemoveTask(taskID uint64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	if state, exists := m.tasks[taskID]; exists {
		if state.File != nil {
			state.File.Close()
		}
		delete(m.tasks, taskID)
	}
}
