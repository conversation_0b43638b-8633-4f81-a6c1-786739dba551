//go:build windows
// +build windows

package common

import (
	"fmt"
	"log"
	"os"
)

// FileDeleteRequest 文件删除请求
type FileDeleteRequest struct {
	TaskID     uint64 `json:"task_id"`     // 任务ID
	Path       string `json:"path"`        // 文件路径
	Secure     bool   `json:"secure"`      // 是否安全删除（多次覆写）
	Recursive  bool   `json:"recursive"`   // 是否递归删除（如果是目录）
	ForceWrite bool   `json:"force_write"` // 是否强制移除只读属性
}

// FileDeleteResponse 文件删除响应
type FileDeleteResponse struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	NotExist     bool   `json:"not_exist"`     // 文件是否不存在
	Success      bool   `json:"success"`       // 操作是否成功
	NotAllow     bool   `json:"not_allow"`     // 是否权限不足
	Error        string `json:"error"`         // 错误信息
	ActualPath   string `json:"actual_path"`   // 实际删除的路径
	DeletedCount int    `json:"deleted_count"` // 删除的文件数量
}

// handleFileDelete 处理文件删除请求
func (cm *ConnectionManager) handleFileDelete(packet *Packet) {
	var req FileDeleteRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := &FileDeleteResponse{
		TaskID:       0,
		NotExist:     true,
		Success:      false,
		NotAllow:     false,
		Error:        "请求格式错误",
		DeletedCount: 0,
	}
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("反序列化FileDeleteRequest失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendResp(File, FileDelete, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	// 使用写锁保护文件删除
	fileMutex.Lock()
	resp := deleteFile(&req)
	fileMutex.Unlock()

	cm.sendResp(File, FileDelete, resp)
}

// deleteFile 执行文件删除的核心逻辑
func deleteFile(req *FileDeleteRequest) *FileDeleteResponse {
	// 参数验证
	if req.Path == "" {
		return &FileDeleteResponse{
			TaskID:       req.TaskID,
			Success:      false,
			Error:        "文件路径不能为空",
			DeletedCount: 0,
		}
	}

	// 获取绝对路径
	absPath, err := getAbsolutePath(req.Path)
	if err != nil {
		return &FileDeleteResponse{
			TaskID:       req.TaskID,
			Success:      false,
			Error:        fmt.Sprintf("路径解析失败: %v", err),
			DeletedCount: 0,
		}
	}

	// 初始化响应
	resp := &FileDeleteResponse{
		TaskID:       req.TaskID,
		Success:      false,
		NotExist:     false,
		NotAllow:     false,
		ActualPath:   absPath,
		DeletedCount: 0,
		Error:        "",
	}

	// 检查文件是否存在
	fileExists, err := PathExists(absPath)
	if err != nil {
		log.Printf("检查文件是否存在时出错: %v", err)
		resp.Error = fmt.Sprintf("检查文件存在性失败: %v", err)
		handleDeleteError(err, resp)
		return resp
	}

	if !fileExists {
		resp.NotExist = true
		resp.Error = "文件不存在"
		return resp
	}

	// 检查是否为普通文件或目录
	fileInfo, err := os.Stat(absPath)
	if err != nil {
		log.Printf("获取文件信息时出错: %v", err)
		resp.Error = fmt.Sprintf("获取文件信息失败: %v", err)
		handleDeleteError(err, resp)
		return resp
	}

	// 如果是目录且不允许递归删除
	if fileInfo.IsDir() && !req.Recursive {
		log.Printf("路径是目录但未启用递归删除: %s", absPath)
		resp.NotAllow = true
		resp.Error = "路径是目录，需要启用递归删除"
		return resp
	}

	// 如果文件是只读的且需要强制写入
	if req.ForceWrite {
		if err := removeReadOnlyAttribute(absPath); err != nil {
			log.Printf("移除只读属性失败: %v", err)
			// 不返回错误，继续尝试删除
		}
	}

	// 执行删除操作
	var deleteErr error
	var deletedCount int

	if fileInfo.IsDir() {
		// 递归删除目录
		deletedCount, deleteErr = deleteDirectoryRecursive(absPath, req.Secure)
	} else {
		// 删除单个文件
		if req.Secure {
			deleteErr = secureDelete(absPath)
		} else {
			deleteErr = DelFile(absPath)
		}
		if deleteErr == nil {
			deletedCount = 1
		}
	}

	if deleteErr != nil {
		log.Printf("删除文件失败: %v", deleteErr)
		resp.Error = fmt.Sprintf("删除失败: %v", deleteErr)
		handleDeleteError(deleteErr, resp)
		return resp
	}

	// 验证删除结果
	if stillExists, _ := PathExists(absPath); !stillExists {
		resp.Success = true
		resp.DeletedCount = deletedCount
		log.Printf("文件删除成功: %s (删除项目数: %d)", absPath, deletedCount)
	} else {
		resp.Error = "删除操作完成但文件仍然存在"
	}

	return resp
}
