package main

import (
	"debug/macho"
	"fmt"
	"io"
)

// MachOSegment represents a LC_SEGMENT_64 segment from Mach-O file
type MachOSegment struct {
	Name     string // Segment name (e.g., __TEXT, __DATA)
	VAddr    uint64 // Virtual address
	FileSize uint64 // Size in file
	MemSize  uint64 // Size in memory
	Offset   uint64 // Offset in file
	MaxProt  uint32 // Maximum protection flags
	InitProt uint32 // Initial protection flags
	Data     []byte // Segment data
}

// MachOInfo contains parsed Mach-O information needed for shellcode generation
type MachOInfo struct {
	Entry     uint64         // Entry point address
	Segments  []MachOSegment // LC_SEGMENT_64 segments
	IsPIE     bool           // Position Independent Executable
	CPU       macho.Cpu      // Target CPU architecture
	BaseAddr  uint64         // Base address for non-PIE executables
	TotalSize uint64         // Total memory size needed
}

// ParseMachO parses a Mach-O file and extracts information needed for shellcode generation
func ParseMachO(reader io.ReaderAt) (*MachOInfo, error) {
	// Open Mach-O file
	machoFile, err := macho.NewFile(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Mach-O file: %v", err)
	}
	defer machoFile.Close()

	// Validate Mach-O file
	if err := validateMachO(machoFile); err != nil {
		return nil, err
	}

	info := &MachOInfo{
		CPU: machoFile.Cpu,
	}

	// Parse load commands to find LC_SEGMENT_64 segments
	var minAddr, maxAddr uint64 = ^uint64(0), 0

	for _, load := range machoFile.Loads {
		if seg, ok := load.(*macho.Segment); ok {
			// Skip zero-sized segments
			if seg.Memsz == 0 {
				continue
			}

			// Read segment data
			data := make([]byte, seg.Filesz)
			if seg.Filesz > 0 {
				if _, err := seg.ReadAt(data, 0); err != nil {
					return nil, fmt.Errorf("failed to read segment data for %s: %v", seg.Name, err)
				}
			}

			segment := MachOSegment{
				Name:     seg.Name,
				VAddr:    seg.Addr,
				FileSize: seg.Filesz,
				MemSize:  seg.Memsz,
				Offset:   seg.Offset,
				MaxProt:  seg.Maxprot,
				InitProt: seg.Prot,
				Data:     data,
			}

			info.Segments = append(info.Segments, segment)

			// Track address range
			if seg.Addr < minAddr {
				minAddr = seg.Addr
			}
			if seg.Addr+seg.Memsz > maxAddr {
				maxAddr = seg.Addr + seg.Memsz
			}
		}
	}

	if len(info.Segments) == 0 {
		return nil, fmt.Errorf("no LC_SEGMENT_64 segments found")
	}

	// Find entry point from LC_MAIN or LC_UNIXTHREAD
	info.Entry = findEntryPoint(machoFile)

	// Determine if this is a PIE executable
	// In Mach-O, PIE executables typically have MH_PIE flag or start at address 0
	info.IsPIE = (machoFile.Flags&macho.FlagPIE != 0) || (info.Segments[0].VAddr == 0)
	info.BaseAddr = minAddr
	info.TotalSize = maxAddr - minAddr

	return info, nil
}

// validateMachO validates that the Mach-O file is supported
func validateMachO(machoFile *macho.File) error {
	// Check file type
	if machoFile.Type != macho.TypeExec && machoFile.Type != macho.TypeDylib {
		return fmt.Errorf("unsupported Mach-O type: %v (only MH_EXECUTE and MH_DYLIB are supported)", machoFile.Type)
	}

	// Check architecture
	switch machoFile.Cpu {
	case macho.CpuAmd64:
		// x86_64 is supported
	case macho.CpuArm64:
		// ARM64 is supported
	default:
		return fmt.Errorf("unsupported architecture: %v (only x86_64 and ARM64 are supported)", machoFile.Cpu)
	}

	return nil
}

// findEntryPoint finds the entry point from load commands
func findEntryPoint(machoFile *macho.File) uint64 {
	// For Mach-O files, the entry point is typically at the start of the __TEXT segment
	// We'll use a simplified approach since the debug/macho package doesn't expose
	// LC_MAIN or LC_UNIXTHREAD directly

	for _, load := range machoFile.Loads {
		if seg, ok := load.(*macho.Segment); ok {
			if seg.Name == "__TEXT" {
				// Return the virtual address of the __TEXT segment
				// This is a simplified approach - real entry point parsing would be more complex
				return seg.Addr
			}
		}
	}

	// If no __TEXT segment found, return 0
	return 0
}

// GetMemoryLayout calculates the memory layout for the Mach-O
func (info *MachOInfo) GetMemoryLayout() (baseAddr uint64, totalSize uint64) {
	if info.IsPIE {
		// For PIE executables, we'll let the system choose the base address
		return 0, info.TotalSize
	}
	// For non-PIE executables, use the original addresses
	return info.BaseAddr, info.TotalSize
}

// IsStaticLinked checks if the Mach-O is statically linked
func (info *MachOInfo) IsStaticLinked() bool {
	// A simple heuristic: if there are very few segments and no __LINKEDIT, it might be static
	// This is not 100% accurate but good enough for our purposes
	hasLinkedit := false
	for _, segment := range info.Segments {
		if segment.Name == "__LINKEDIT" {
			hasLinkedit = true
			break
		}
	}
	return !hasLinkedit && len(info.Segments) <= 3
}

// GetSegmentProt converts Mach-O segment protection flags to mmap protection flags
func GetSegmentProt(machoFlags uint32) int {
	var prot int
	if machoFlags&0x1 != 0 { // VM_PROT_READ
		prot |= 0x1 // PROT_READ
	}
	if machoFlags&0x2 != 0 { // VM_PROT_WRITE
		prot |= 0x2 // PROT_WRITE
	}
	if machoFlags&0x4 != 0 { // VM_PROT_EXECUTE
		prot |= 0x4 // PROT_EXEC
	}
	return prot
}

// GetTextSegment finds the __TEXT segment (contains executable code)
func (info *MachOInfo) GetTextSegment() *MachOSegment {
	for i := range info.Segments {
		if info.Segments[i].Name == "__TEXT" {
			return &info.Segments[i]
		}
	}
	return nil
}

// GetDataSegment finds the __DATA segment (contains writable data)
func (info *MachOInfo) GetDataSegment() *MachOSegment {
	for i := range info.Segments {
		if info.Segments[i].Name == "__DATA" {
			return &info.Segments[i]
		}
	}
	return nil
}

// HasDynamicLinker checks if the Mach-O uses dynamic linking
func (info *MachOInfo) HasDynamicLinker() bool {
	// Look for __LINKEDIT segment which indicates dynamic linking
	for _, segment := range info.Segments {
		if segment.Name == "__LINKEDIT" {
			return true
		}
	}
	return false
}

// GetArchitecture returns a string representation of the architecture
func (info *MachOInfo) GetArchitecture() string {
	switch info.CPU {
	case macho.CpuAmd64:
		return "x86_64"
	case macho.CpuArm64:
		return "arm64"
	default:
		return fmt.Sprintf("unknown(%d)", info.CPU)
	}
}

// IsExecutable checks if this Mach-O file is executable
func (info *MachOInfo) IsExecutable() bool {
	// Check if there's a __TEXT segment with execute permissions
	textSeg := info.GetTextSegment()
	if textSeg == nil {
		return false
	}
	return textSeg.MaxProt&0x4 != 0 // VM_PROT_EXECUTE
}

// GetLoadAddress returns the preferred load address
func (info *MachOInfo) GetLoadAddress() uint64 {
	if info.IsPIE {
		return 0 // Let the system choose
	}
	return info.BaseAddr
}
