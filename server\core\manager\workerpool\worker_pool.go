package workerpool

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// 错误定义
var (
	ErrQueueFull  = fmt.Errorf("任务队列已满")
	ErrPoolClosed = fmt.<PERSON><PERSON>rf("工作池已关闭")
)

// Task 任务接口
type Task interface {
	Execute() error
	GetID() string
	GetType() string
}

// WorkerPool 工作池结构
type WorkerPool struct {
	// 基础配置
	name       string
	minWorkers int
	maxWorkers int
	queueSize  int

	// 运行时状态
	workers     map[int]*Worker
	workerCount int32
	taskQueue   chan Task
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	mu          sync.RWMutex

	// 统计信息
	stats *PoolStats

	// 配置参数
	workerIdleTimeout  time.Duration
	scaleUpThreshold   float64
	scaleDownThreshold float64
	checkInterval      time.Duration
}

// PoolStats 工作池统计信息 - 使用原子操作优化
type PoolStats struct {
	// 基础统计 - 全部使用原子操作
	TotalTasks     int64 `json:"total_tasks"`
	CompletedTasks int64 `json:"completed_tasks"`
	FailedTasks    int64 `json:"failed_tasks"`
	ActiveTasks    int64 `json:"active_tasks"`
	QueuedTasks    int64 `json:"queued_tasks"`

	// Worker统计 - 全部使用原子操作
	ActiveWorkers int32 `json:"active_workers"`
	IdleWorkers   int32 `json:"idle_workers"`
	TotalWorkers  int32 `json:"total_workers"`

	// 性能统计 - 使用原子操作存储纳秒值
	AvgTaskDurationNs int64 `json:"avg_task_duration_ns"`
	MaxTaskDurationNs int64 `json:"max_task_duration_ns"`
	MinTaskDurationNs int64 `json:"min_task_duration_ns"`

	// 时间统计 - 使用原子操作存储Unix纳秒时间戳
	StartTimeNs    int64 `json:"start_time_ns"`
	LastTaskTimeNs int64 `json:"last_task_time_ns"`

	// 队列统计
	QueueCapacity int     `json:"queue_capacity"`
	QueueUsage    float64 `json:"queue_usage"`

	// 🚨 移除互斥锁，完全使用原子操作
}

// GetAvgTaskDuration 获取平均任务持续时间
func (ps *PoolStats) GetAvgTaskDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&ps.AvgTaskDurationNs))
}

// GetMaxTaskDuration 获取最大任务持续时间
func (ps *PoolStats) GetMaxTaskDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&ps.MaxTaskDurationNs))
}

// GetMinTaskDuration 获取最小任务持续时间
func (ps *PoolStats) GetMinTaskDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&ps.MinTaskDurationNs))
}

// GetStartTime 获取开始时间
func (ps *PoolStats) GetStartTime() time.Time {
	return time.Unix(0, atomic.LoadInt64(&ps.StartTimeNs))
}

// GetLastTaskTime 获取最后任务时间
func (ps *PoolStats) GetLastTaskTime() time.Time {
	ns := atomic.LoadInt64(&ps.LastTaskTimeNs)
	if ns == 0 {
		return time.Time{}
	}
	return time.Unix(0, ns)
}

// ToCompatibleStats 转换为兼容的统计格式（用于API返回）
func (ps *PoolStats) ToCompatibleStats() map[string]interface{} {
	return map[string]interface{}{
		"total_tasks":       ps.TotalTasks,
		"completed_tasks":   ps.CompletedTasks,
		"failed_tasks":      ps.FailedTasks,
		"active_tasks":      ps.ActiveTasks,
		"queued_tasks":      ps.QueuedTasks,
		"active_workers":    ps.ActiveWorkers,
		"idle_workers":      ps.IdleWorkers,
		"total_workers":     ps.TotalWorkers,
		"avg_task_duration": ps.GetAvgTaskDuration(),
		"max_task_duration": ps.GetMaxTaskDuration(),
		"min_task_duration": ps.GetMinTaskDuration(),
		"start_time":        ps.GetStartTime(),
		"last_task_time":    ps.GetLastTaskTime(),
		"queue_capacity":    ps.QueueCapacity,
		"queue_usage":       ps.QueueUsage,
	}
}

// Worker 工作者结构
type Worker struct {
	id         int
	pool       *WorkerPool
	taskChan   chan Task
	quit       chan bool
	isActive   int32
	lastActive time.Time
	taskCount  int64
	mu         sync.RWMutex
}

// WorkerPoolConfig 工作池配置
type WorkerPoolConfig struct {
	Name               string
	MinWorkers         int
	MaxWorkers         int
	QueueSize          int
	WorkerIdleTimeout  time.Duration
	ScaleUpThreshold   float64
	ScaleDownThreshold float64
	CheckInterval      time.Duration
}

// NewWorkerPool 创建新的工作池
func NewWorkerPool(config WorkerPoolConfig) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())

	// 设置默认值
	if config.MinWorkers <= 0 {
		config.MinWorkers = 2
	}
	if config.MaxWorkers <= 0 {
		config.MaxWorkers = runtime.NumCPU() * 2
	}
	if config.QueueSize <= 0 {
		config.QueueSize = 1000
	}
	if config.WorkerIdleTimeout <= 0 {
		config.WorkerIdleTimeout = 30 * time.Second
	}
	if config.ScaleUpThreshold <= 0 {
		config.ScaleUpThreshold = 0.8
	}
	if config.ScaleDownThreshold <= 0 {
		config.ScaleDownThreshold = 0.2
	}
	if config.CheckInterval <= 0 {
		config.CheckInterval = 10 * time.Second
	}

	pool := &WorkerPool{
		name:               config.Name,
		minWorkers:         config.MinWorkers,
		maxWorkers:         config.MaxWorkers,
		queueSize:          config.QueueSize,
		workers:            make(map[int]*Worker),
		taskQueue:          make(chan Task, config.QueueSize),
		ctx:                ctx,
		cancel:             cancel,
		workerIdleTimeout:  config.WorkerIdleTimeout,
		scaleUpThreshold:   config.ScaleUpThreshold,
		scaleDownThreshold: config.ScaleDownThreshold,
		checkInterval:      config.CheckInterval,
		stats: &PoolStats{
			QueueCapacity: config.QueueSize,
		},
	}

	// 🚀 使用原子操作初始化开始时间
	atomic.StoreInt64(&pool.stats.StartTimeNs, time.Now().UnixNano())

	// 启动最小数量的worker
	for i := 0; i < config.MinWorkers; i++ {
		pool.addWorker()
	}

	// 启动监控goroutine
	go pool.monitor()

	global.LOG.Info("工作池创建成功",
		zap.String("name", config.Name),
		zap.Int("minWorkers", config.MinWorkers),
		zap.Int("maxWorkers", config.MaxWorkers),
		zap.Int("queueSize", config.QueueSize))

	return pool
}

// Submit 提交任务到工作池
func (p *WorkerPool) Submit(task Task) error {
	select {
	case p.taskQueue <- task:
		atomic.AddInt64(&p.stats.TotalTasks, 1)
		atomic.AddInt64(&p.stats.QueuedTasks, 1)
		// 🚨 移除updateQueueUsage调用，避免锁竞争
		return nil
	case <-p.ctx.Done():
		return p.ctx.Err()
	default:
		// 队列满了，尝试扩容
		if p.canScaleUp() {
			p.addWorker()
			// 再次尝试提交
			select {
			case p.taskQueue <- task:
				atomic.AddInt64(&p.stats.TotalTasks, 1)
				atomic.AddInt64(&p.stats.QueuedTasks, 1)
				// 🚨 移除updateQueueUsage调用，避免锁竞争
				return nil
			default:
				return ErrQueueFull
			}
		}
		return ErrQueueFull
	}
}

// addWorker 添加新的worker
func (p *WorkerPool) addWorker() {
	p.mu.Lock()
	defer p.mu.Unlock()

	if int(atomic.LoadInt32(&p.workerCount)) >= p.maxWorkers {
		return
	}

	workerID := len(p.workers) + 1
	worker := &Worker{
		id:         workerID,
		pool:       p,
		taskChan:   make(chan Task, 1),
		quit:       make(chan bool),
		lastActive: time.Now(),
	}

	p.workers[workerID] = worker
	atomic.AddInt32(&p.workerCount, 1)
	atomic.AddInt32(&p.stats.TotalWorkers, 1)

	p.wg.Add(1)
	go worker.start()

	global.LOG.Debug("添加新worker",
		zap.String("pool", p.name),
		zap.Int("workerID", workerID),
		zap.Int32("totalWorkers", atomic.LoadInt32(&p.workerCount)))
}

// removeWorker 移除worker
func (p *WorkerPool) removeWorker(workerID int) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if worker, exists := p.workers[workerID]; exists {
		close(worker.quit)
		delete(p.workers, workerID)
		atomic.AddInt32(&p.workerCount, -1)
		atomic.AddInt32(&p.stats.TotalWorkers, -1)

		global.LOG.Debug("移除worker",
			zap.String("pool", p.name),
			zap.Int("workerID", workerID),
			zap.Int32("totalWorkers", atomic.LoadInt32(&p.workerCount)))
	}
}

// 🚨 删除updateQueueUsage函数，使用原子操作和实时计算

// canScaleUp 检查是否可以扩容
func (p *WorkerPool) canScaleUp() bool {
	currentWorkers := atomic.LoadInt32(&p.workerCount)
	queueUsage := float64(len(p.taskQueue)) / float64(p.queueSize)

	return int(currentWorkers) < p.maxWorkers && queueUsage > p.scaleUpThreshold
}

// canScaleDown 检查是否可以缩容
func (p *WorkerPool) canScaleDown() bool {
	currentWorkers := atomic.LoadInt32(&p.workerCount)
	queueUsage := float64(len(p.taskQueue)) / float64(p.queueSize)

	return int(currentWorkers) > p.minWorkers && queueUsage < p.scaleDownThreshold
}

// monitor 监控工作池状态
func (p *WorkerPool) monitor() {
	ticker := time.NewTicker(p.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.checkAndScale()
			p.checkWorkerHealth()
		case <-p.ctx.Done():
			return
		}
	}
}

// checkAndScale 检查并执行扩缩容
func (p *WorkerPool) checkAndScale() {
	if p.canScaleUp() {
		p.addWorker()
	} else if p.canScaleDown() {
		p.scaleDown()
	}
}

// scaleDown 缩容
func (p *WorkerPool) scaleDown() {
	p.mu.RLock()
	var oldestWorker *Worker
	var oldestID int
	oldestTime := time.Now()

	for id, worker := range p.workers {
		worker.mu.RLock()
		if atomic.LoadInt32(&worker.isActive) == 0 && worker.lastActive.Before(oldestTime) {
			oldestTime = worker.lastActive
			oldestWorker = worker
			oldestID = id
		}
		worker.mu.RUnlock()
	}
	p.mu.RUnlock()

	if oldestWorker != nil && time.Since(oldestTime) > p.workerIdleTimeout {
		p.removeWorker(oldestID)
	}
}

// checkWorkerHealth 检查worker健康状态
func (p *WorkerPool) checkWorkerHealth() {
	p.mu.RLock()
	defer p.mu.RUnlock()

	var activeWorkers, idleWorkers int32

	for _, worker := range p.workers {
		if atomic.LoadInt32(&worker.isActive) == 1 {
			activeWorkers++
		} else {
			idleWorkers++
		}
	}

	atomic.StoreInt32(&p.stats.ActiveWorkers, activeWorkers)
	atomic.StoreInt32(&p.stats.IdleWorkers, idleWorkers)
}

// GetStats 获取工作池统计信息 - 完全无锁实现
func (p *WorkerPool) GetStats() *PoolStats {
	// 🚀 完全无锁实现：所有数据都通过原子操作获取
	queueLen := len(p.taskQueue)
	queueUsage := float64(queueLen) / float64(p.queueSize)

	// 创建新的统计对象，所有字段都使用原子操作读取
	stats := &PoolStats{
		TotalTasks:        atomic.LoadInt64(&p.stats.TotalTasks),
		CompletedTasks:    atomic.LoadInt64(&p.stats.CompletedTasks),
		FailedTasks:       atomic.LoadInt64(&p.stats.FailedTasks),
		ActiveTasks:       atomic.LoadInt64(&p.stats.ActiveTasks),
		QueuedTasks:       int64(queueLen), // 实时计算
		ActiveWorkers:     atomic.LoadInt32(&p.stats.ActiveWorkers),
		IdleWorkers:       atomic.LoadInt32(&p.stats.IdleWorkers),
		TotalWorkers:      atomic.LoadInt32(&p.stats.TotalWorkers),
		AvgTaskDurationNs: atomic.LoadInt64(&p.stats.AvgTaskDurationNs),
		MaxTaskDurationNs: atomic.LoadInt64(&p.stats.MaxTaskDurationNs),
		MinTaskDurationNs: atomic.LoadInt64(&p.stats.MinTaskDurationNs),
		StartTimeNs:       atomic.LoadInt64(&p.stats.StartTimeNs),
		LastTaskTimeNs:    atomic.LoadInt64(&p.stats.LastTaskTimeNs),
		QueueCapacity:     p.stats.QueueCapacity,
		QueueUsage:        queueUsage, // 实时计算
	}

	return stats
}

// Shutdown 关闭工作池
func (p *WorkerPool) Shutdown() {
	global.LOG.Info("开始关闭工作池", zap.String("name", p.name))

	// 取消上下文
	p.cancel()

	// 关闭任务队列
	close(p.taskQueue)

	// 等待所有worker完成
	p.wg.Wait()

	global.LOG.Info("工作池已关闭", zap.String("name", p.name))
}

// start 启动worker
func (w *Worker) start() {
	defer w.pool.wg.Done()

	global.LOG.Debug("Worker启动",
		zap.String("pool", w.pool.name),
		zap.Int("workerID", w.id))

	for {
		select {
		case task := <-w.pool.taskQueue:
			if task != nil {
				w.executeTask(task)
			}
		case <-w.quit:
			global.LOG.Debug("Worker停止",
				zap.String("pool", w.pool.name),
				zap.Int("workerID", w.id))
			return
		case <-w.pool.ctx.Done():
			return
		}
	}
}

// executeTask 执行任务
func (w *Worker) executeTask(task Task) {
	atomic.StoreInt32(&w.isActive, 1)
	atomic.AddInt64(&w.pool.stats.ActiveTasks, 1)
	atomic.AddInt64(&w.pool.stats.QueuedTasks, -1)

	defer func() {
		atomic.StoreInt32(&w.isActive, 0)
		atomic.AddInt64(&w.pool.stats.ActiveTasks, -1)
		w.mu.Lock()
		w.lastActive = time.Now()
		w.taskCount++
		w.mu.Unlock()

		// 恢复panic
		if r := recover(); r != nil {
			atomic.AddInt64(&w.pool.stats.FailedTasks, 1)
			global.LOG.Error("Worker执行任务时发生panic",
				zap.String("pool", w.pool.name),
				zap.Int("workerID", w.id),
				zap.String("taskID", task.GetID()),
				zap.String("taskType", task.GetType()),
				zap.Any("panic", r))
		}
	}()

	startTime := time.Now()

	// 执行任务
	err := task.Execute()

	duration := time.Since(startTime)

	// 更新统计信息
	w.updateTaskStats(duration, err)

	if err != nil {
		atomic.AddInt64(&w.pool.stats.FailedTasks, 1)
		global.LOG.Error("Worker执行任务失败",
			zap.String("pool", w.pool.name),
			zap.Int("workerID", w.id),
			zap.String("taskID", task.GetID()),
			zap.String("taskType", task.GetType()),
			zap.Error(err),
			zap.Duration("duration", duration))
	} else {
		atomic.AddInt64(&w.pool.stats.CompletedTasks, 1)
		global.LOG.Debug("Worker执行任务成功",
			zap.String("pool", w.pool.name),
			zap.Int("workerID", w.id),
			zap.String("taskID", task.GetID()),
			zap.String("taskType", task.GetType()),
			zap.Duration("duration", duration))
	}
}

// updateTaskStats 更新任务统计信息 - 使用原子操作
func (w *Worker) updateTaskStats(duration time.Duration, err error) {
	// 🚀 完全使用原子操作，无锁实现
	durationNs := int64(duration)
	nowNs := time.Now().UnixNano()

	// 更新最后任务时间
	atomic.StoreInt64(&w.pool.stats.LastTaskTimeNs, nowNs)

	// 原子更新最大持续时间
	for {
		current := atomic.LoadInt64(&w.pool.stats.MaxTaskDurationNs)
		if current >= durationNs {
			break
		}
		if atomic.CompareAndSwapInt64(&w.pool.stats.MaxTaskDurationNs, current, durationNs) {
			break
		}
	}

	// 原子更新最小持续时间
	for {
		current := atomic.LoadInt64(&w.pool.stats.MinTaskDurationNs)
		if current != 0 && current <= durationNs {
			break
		}
		if atomic.CompareAndSwapInt64(&w.pool.stats.MinTaskDurationNs, current, durationNs) {
			break
		}
	}

	// 使用移动平均更新平均持续时间
	for {
		current := atomic.LoadInt64(&w.pool.stats.AvgTaskDurationNs)
		var newAvg int64
		if current == 0 {
			newAvg = durationNs
		} else {
			newAvg = (current*9 + durationNs) / 10
		}
		if atomic.CompareAndSwapInt64(&w.pool.stats.AvgTaskDurationNs, current, newAvg) {
			break
		}
	}
}
