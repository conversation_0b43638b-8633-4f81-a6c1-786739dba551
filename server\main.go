package main

import (
	"log"
	"net/http"
	_ "net/http/pprof" // 导入pprof包
	"server/core"
	"server/core/initializer"
	"time"

	"github.com/gin-gonic/gin"
)

// initPprof 初始化pprof性能分析服务
// 只在非Release模式下启用，用于开发和测试环境的性能分析
func initPprof() {
	// 检查gin模式，只在非Release模式下启用pprof
	if gin.Mode() == gin.ReleaseMode {
		log.Println("🚀 Production mode detected, pprof disabled for security")
		return
	}

	// 启动pprof HTTP服务器
	pprofServer := &http.Server{
		Addr:         ":6060", // pprof服务端口
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
	}

	go func() {
		log.Println("🔍 Starting pprof server on :6060")
		log.Println("📊 Performance analysis endpoints:")
		log.Println("   • CPU Profile:    http://localhost:6060/debug/pprof/profile")
		log.Println("   • Heap Profile:   http://localhost:6060/debug/pprof/heap")
		log.Println("   • Goroutines:     http://localhost:6060/debug/pprof/goroutine")
		log.Println("   • All Profiles:   http://localhost:6060/debug/pprof/")
		log.Println("   • Trace:          http://localhost:6060/debug/pprof/trace")

		if err := pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("❌ pprof server failed to start: %v", err)
		}
	}()

	// 优雅关闭pprof服务器的逻辑可以在这里添加
	// 但为了简单起见，我们让它随主程序一起退出
}

func main() {
	//gin.SetMode(gin.ReleaseMode) //release mode

	// 🔍 初始化性能分析服务（仅在开发/测试环境）
	initPprof()

	// 初始化系统
	initializer.InitSystem()

	// 启动服务器
	core.RunServer()
}
