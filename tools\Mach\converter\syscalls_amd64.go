//go:build darwin && amd64

package main

// macOS system call numbers for x86_64
const (
	SYS_MMAP     = 0x2000000 + 197 // mmap
	SYS_MPROTECT = 0x2000000 + 74  // mprotect
	SYS_MUNMAP   = 0x2000000 + 73  // munmap
	SYS_OPEN     = 0x2000000 + 5   // open
	SYS_WRITE    = 0x2000000 + 4   // write
	SYS_CLOSE    = 0x2000000 + 6   // close
	SYS_EXECVE   = 0x2000000 + 59  // execve
	SYS_UNLINK   = 0x2000000 + 10  // unlink
	SYS_EXIT     = 0x2000000 + 1   // exit
)
