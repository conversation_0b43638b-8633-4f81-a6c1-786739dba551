package task

import "time"

// NetworkTask 网络监控任务
type NetworkTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	ClientID    uint       `json:"client_id"` // 客户端ID
	TaskType    string     `json:"task_type"` // 任务类型：network_stats, network_interfaces, network_connections, close_connection
	Status      string     `json:"status"`    // 状态：pending, running, completed, failed, cancelled
	Error       string     `json:"error"`     // 错误信息
	Result      string     `json:"result"`    // 任务结果（JSON格式）
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`   // 开始时间
	CompletedAt *time.Time `json:"completed_at"` // 完成时间
}
