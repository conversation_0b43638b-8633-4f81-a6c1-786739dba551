<template>
  <div class="tech-quotes">
    <div class="quote-container">
      <!-- 显示已打出的文字 -->
      <span class="typed-text">{{ displayText }}</span>
      <!-- 打字时显示光标 -->
      <span v-if="isTyping" class="cursor">_</span>
    </div>
    <div class="quote-author" v-if="currentQuote.author && typingComplete">
      — {{ currentQuote.author }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 科技感名人名言数据库
const quotes = [
  {
    text: "The future belongs to those who believe in the beauty of their dreams.",
    author: "<PERSON>"
  },
  {
    text: "Innovation distinguishes between a leader and a follower.",
    author: "<PERSON>"
  },
  {
    text: "The only way to do great work is to love what you do.",
    author: "<PERSON>"
  },
  {
    text: "Technology is best when it brings people together.",
    author: "<PERSON>"
  },
  {
    text: "The advance of technology is based on making it fit in so that you don't really even notice it.",
    author: "<PERSON>"
  },
  {
    text: "Any sufficiently advanced technology is indistinguishable from magic.",
    author: "<PERSON>"
  },
  {
    text: "The science of today is the technology of tomorrow.",
    author: "<PERSON> Teller"
  },
  {
    text: "Code is poetry written in logic.",
    author: "Anonymous"
  },
  {
    text: "The best way to predict the future is to invent it.",
    author: "Alan Kay"
  },
  {
    text: "Simplicity is the ultimate sophistication.",
    author: "<PERSON> da Vinci"
  },
  {
    text: "First, solve the problem. Then, write the code.",
    author: "John Johnson"
  },
  {
    text: "Programs must be written for people to read, and only incidentally for machines to execute.",
    author: "Harold Abelson"
  },
  {
    text: "The computer was born to solve problems that did not exist before.",
    author: "Bill Gates"
  },
  {
    text: "Software is a great combination between artistry and engineering.",
    author: "Bill Gates"
  },
  {
    text: "The most disruptive ideas are not flashy ones. They are quiet revolutions.",
    author: "Anonymous"
  },
  {
    text: "Every great developer you know got there by solving problems they were unqualified to solve.",
    author: "Patrick McKenzie"
  },
  {
    text: "The future is already here — it's just not very evenly distributed.",
    author: "William Gibson"
  },
  {
    text: "Artificial intelligence is the new electricity.",
    author: "Andrew Ng"
  },
  {
    text: "Data is the new oil.",
    author: "Clive Humby"
  },
  {
    text: "In the world of software, the most beautiful code is the code that solves real problems.",
    author: "Anonymous"
  }
]

// 响应式数据
const currentQuoteIndex = ref(0)
const typedChars = ref(0)
const showCursor = ref(true)
const isTyping = ref(false)
const typingComplete = ref(false)

// 计算属性
const currentQuote = computed(() => quotes[currentQuoteIndex.value])
const displayText = computed(() => currentQuote.value.text.slice(0, typedChars.value))

// 打字机效果配置
const TYPING_SPEED = 80 // 打字速度（毫秒）
const PAUSE_DURATION = 10000 // 完成后暂停时间
const CURSOR_BLINK_SPEED = 500 // 光标闪烁速度

let typingTimer = null
let nextQuoteTimer = null
let cursorTimer = null

// 开始打字效果
const startTyping = () => {
  typedChars.value = 0
  typingComplete.value = false
  isTyping.value = true
  showCursor.value = true
  
  const typeNextChar = () => {
    if (typedChars.value < currentQuote.value.text.length) {
      typedChars.value++
      typingTimer = setTimeout(typeNextChar, TYPING_SPEED)
    } else {
      // 打字完成
      isTyping.value = false
      typingComplete.value = true

      // 设置下一句的定时器
      nextQuoteTimer = setTimeout(() => {
        nextQuote()
      }, PAUSE_DURATION)
    }
  }
  
  typeNextChar()
}

// 切换到下一句名言
const nextQuote = () => {
  currentQuoteIndex.value = (currentQuoteIndex.value + 1) % quotes.length
  startTyping()
}

// 光标闪烁效果（简化版）
const startCursorBlink = () => {
  // 打字时光标始终显示，不需要额外的闪烁逻辑
  // 完成后的光标通过CSS动画处理
}

// 清理定时器
const clearTimers = () => {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  if (nextQuoteTimer) {
    clearTimeout(nextQuoteTimer)
    nextQuoteTimer = null
  }
  if (cursorTimer) {
    clearInterval(cursorTimer)
    cursorTimer = null
  }
}

// 组件挂载
onMounted(() => {
  // 随机选择一个起始名言
  currentQuoteIndex.value = Math.floor(Math.random() * quotes.length)
  startTyping()
})

// 组件卸载
onUnmounted(() => {
  clearTimers()
})

// 暴露quotes数组，方便外部访问
defineExpose({
  quotes,
  addQuote: (quote) => quotes.push(quote),
  removeQuote: (index) => quotes.splice(index, 1)
})
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&family=Inter:wght@400;500;600&display=swap');

.tech-quotes {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 改为左对齐 */
  text-align: left;
  min-height: 60px;
  width: 100%; /* 占满容器宽度 */
}

.quote-container {
  font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6;
  color: #2c3e50;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px;
  width: 100%; /* 占满父容器 */
  max-width: none; /* 移除最大宽度限制 */
  position: relative;
  min-height: 1.6em;
  text-align: left;
  word-wrap: break-word; /* 确保长单词换行 */
}

.typed-text {
  display: inline;
}

.cursor {
  font-family: 'JetBrains Mono', monospace;
  color: #667eea;
  font-weight: 600;
  display: inline;
}

.cursor-completed {
  font-family: 'JetBrains Mono', monospace;
  color: #667eea;
  font-weight: 600;
  display: inline;
  margin-left: 2px;
}

.cursor-completed.blink {
  animation: blink 1s infinite;
}

.quote-author {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #8892b0;
  margin-top: 8px;
  font-style: italic;
  opacity: 0;
  animation: fadeInUp 0.5s ease-out 0.3s forwards;
}

@keyframes blink {
  0%, 50% { visibility: visible; }
  51%, 100% { visibility: hidden; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 科技感主题变体 */
.tech-quotes.dark {
  .quote-container {
    background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .cursor {
    color: #00f5ff;
  }
  
  .quote-author {
    color: #64ffda;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quote-container {
    font-size: 12px;
    max-width: 300px;
  }
  
  .quote-author {
    font-size: 11px;
  }
}
</style>
