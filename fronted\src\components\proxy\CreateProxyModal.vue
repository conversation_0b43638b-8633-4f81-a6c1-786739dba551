<template>
  <a-modal
    v-model:visible="modalVisible"
    title="创建代理实例"
    :width="modalWidth"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleClose"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto' }"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ xs: { span: 24 }, sm: { span: 8 }, md: { span: 6 }, lg: { span: 6 } }"
      :wrapper-col="{ xs: { span: 24 }, sm: { span: 16 }, md: { span: 18 }, lg: { span: 18 } }"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="代理名称" name="name">
            <a-input v-model:value="form.name" placeholder="请输入代理名称" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="代理类型" name="type">
            <a-select v-model:value="form.type" placeholder="选择代理类型">
              <a-select-option value="forward">正向代理</a-select-option>
              <a-select-option value="reverse">反向代理</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="描述">
        <a-textarea
          v-model:value="form.description"
          :rows="2"
          placeholder="请输入代理描述（可选）"
        />
      </a-form-item>

      <a-row :gutter="[16, 16]">
        <a-col :span="24">
          <!-- 使用新的端口管理组件 -->
          <PortManager
            :proxy-type="form.type"
            label="端口配置"
            v-model="form.portConfig"
          />
        </a-col>
      </a-row>

      <!-- iox代理类型特殊说明 -->
      <template v-if="form.type === 'forward'">
        <a-alert
          message="iox正向代理模式"
          description="Client启动SOCKS5服务器，Server连接Client，用户通过Server端口访问Client所在内网的资源。适用于横向移动、内网探测、权限提升等场景。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>

      <template v-if="form.type === 'reverse'">
        <a-alert
          message="iox反向代理模式"
          description="Server开启双端口服务，Client主动连接，外部用户可以通过Server访问Client内网的特定服务。适用于服务暴露、数据提取、持久化访问等场景。"
          type="warning"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>

      <template v-if="form.type === 'chain'">
        <a-alert
          message="iox代理链模式"
          description="多个Client协同工作自动搭建代理链，穿越多层内网环境，用户连接最终SOCKS5端口即可访问深层内网资源。适用于深层渗透、复杂拓扑访问等场景。"
          type="success"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>

      <!-- 代理链模式：节点管理 -->
      <template v-if="form.type === 'chain'">
        <ProxyChainNodeManager
          v-model="form.chain_nodes"
          :available-proxies="availableProxies"
          @refresh="loadAvailableProxies"
        />
      </template>

      <!-- 非代理链模式：关联客户端 -->
      <template v-else>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="关联客户端" name="client_id">
              <a-select v-model:value="form.client_id" placeholder="选择客户端">
                <a-select-option
                  v-for="client in clientList"
                  :key="client.id"
                  :value="client.id"
                >
                  {{ client.hostname }} ({{ client.client_id }})
                </a-select-option>
              </a-select>

            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="优先级" name="priority">
              <a-input-number
                v-model:value="form.priority"
                :min="1"
                :max="999"
                placeholder="请输入优先级 (1-999)"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999; margin-top: 4px;">
                数值越小优先级越高
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 认证设置 -->
      <a-divider orientation="left">认证设置</a-divider>
      <a-form-item>
        <a-checkbox v-model:checked="form.auth_required">启用认证</a-checkbox>
        <div style="margin-top: 4px; color: #666; font-size: 12px;">
          <template v-if="form.type === 'forward'">
            🔒 正向代理建议启用认证，防止内网访问权限被滥用
          </template>
          <template v-else-if="form.type === 'reverse'">
            🔒 反向代理建议启用认证，保护暴露的内网服务安全
          </template>
          <template v-else>
            🔒 代理链强烈建议启用认证，保护整个深层访问链路
          </template>
        </div>
      </a-form-item>

      <a-row :gutter="[16, 16]" v-if="form.auth_required">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="用户名" name="username">
            <a-input v-model:value="form.username" placeholder="请输入用户名" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="密码" name="password">
            <a-input-password
              v-model:value="form.password"
              placeholder="请输入密码"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 高级设置 -->
      <a-divider orientation="left">高级设置</a-divider>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="最大连接数">
            <a-input-number
              v-model:value="form.max_connections"
              :min="1"
              :max="10000"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="超时时间(秒)">
            <a-input-number
              v-model:value="form.timeout"
              :min="1"
              :max="3600"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="缓冲区大小">
            <a-input-number
              v-model:value="form.buffer_size"
              :min="1024"
              :max="65536"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="日志级别">
            <a-select v-model:value="form.log_level">
              <a-select-option value="debug">debug</a-select-option>
              <a-select-option value="info">info</a-select-option>
              <a-select-option value="warn">warn</a-select-option>
              <a-select-option value="error">error</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item>
            <a-checkbox v-model:checked="form.metrics_collection">启用指标收集</a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 反向代理特有配置 -->
      <template v-if="form.type === 'reverse'">
        <a-divider orientation="left">反向代理特有配置</a-divider>
        <a-alert
          message="反向代理连接管理"
          description="由于Client主动连接Server，需要特别配置连接保活、心跳检测等参数确保连接稳定性。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="连接保活(秒)">
              <a-input-number
                v-model:value="form.keep_alive_interval"
                :min="10"
                :max="300"
                placeholder="60"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 Client向Server发送保活包的间隔时间
              </div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="心跳检测(秒)">
              <a-input-number
                v-model:value="form.heartbeat_interval"
                :min="5"
                :max="120"
                placeholder="30"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 Server检测Client连接状态的间隔时间
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="Client连接超时(秒)">
              <a-input-number
                v-model:value="form.client_connect_timeout"
                :min="5"
                :max="300"
                placeholder="30"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 等待Client连接的最大超时时间
              </div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="重连间隔(秒)">
              <a-input-number
                v-model:value="form.reconnect_interval"
                :min="1"
                :max="60"
                placeholder="5"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 Client断线后自动重连的间隔时间
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 访问控制 -->
      <a-divider orientation="left">访问控制</a-divider>
      <template v-if="form.type === 'forward'">
        <a-alert
          message="正向代理访问控制"
          description="建议限制允许连接的IP范围，防止内网访问权限被滥用。可以设置特定的管理员IP或内网IP段。"
          type="warning"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="允许的IP" name="allowed_ips">
            <a-input
              v-model:value="form.allowed_ips"
              placeholder="多个IP用逗号分隔，留空表示允许所有"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              <template v-if="form.type === 'forward'">
                💡 建议设置：管理员IP、内网IP段 (如: ***********/24)
              </template>
              <template v-else>
                💡 建议设置：可信任的外部IP地址
              </template>
            </div>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="阻止的IP" name="blocked_ips">
            <a-input
              v-model:value="form.blocked_ips"
              placeholder="多个IP用逗号分隔"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              💡 可设置已知的恶意IP或不信任的IP段
            </div>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 速率限制 -->
      <a-divider orientation="left">速率限制</a-divider>
      <template v-if="form.type === 'forward'">
        <a-alert
          message="正向代理速率控制"
          description="合理设置速率限制可以防止内网访问被滥用，保护Client和Server的资源。建议根据实际需求设置适当的限制。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="请求限制(RPS)">
            <a-input-number
              v-model:value="form.rate_limit"
              :min="0"
              placeholder="0表示不限制"
              style="width: 100%"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              <template v-if="form.type === 'forward'">
                💡 建议设置：100-1000 RPS (根据内网访问需求)
              </template>
              <template v-else>
                💡 建议设置：根据服务类型调整限制
              </template>
            </div>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="带宽限制(B/s)">
            <a-input-number
              v-model:value="form.bandwidth_limit"
              :min="0"
              placeholder="0表示不限制"
              style="width: 100%"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              💡 建议设置：1MB/s - 10MB/s (根据网络环境)
            </div>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 连接配置 -->
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="连接超时(秒)">
            <a-input-number
              v-model:value="form.connection_timeout"
              :min="1"
              :max="300"
              placeholder="10"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 重试配置 -->
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="重试次数">
            <a-input-number
              v-model:value="form.retry_count"
              :min="0"
              :max="10"
              placeholder="3"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="重试间隔(秒)">
            <a-input-number
              v-model:value="form.retry_interval"
              :min="1"
              :max="60"
              placeholder="1"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 负载均衡和监控 -->
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="负载均衡策略">
            <a-select v-model:value="form.load_balance_strategy" style="width: 100%">
              <a-select-option value="round_robin">round_robin</a-select-option>
              <a-select-option value="least_connections">least_connections</a-select-option>
              <a-select-option value="weighted_round_robin">weighted_round_robin</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="健康检查间隔(秒)">
            <a-input-number
              v-model:value="form.health_check_interval"
              :min="5"
              :max="300"
              placeholder="30"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>


    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { createProxy } from '@/api/proxy'
import PortManager from './PortManager.vue'
import ProxyChainNodeManager from './ProxyChainNodeManager.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  clientList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const modalVisible = ref(false)
const availableProxies = ref([])  // 可用代理实例列表

// 响应式模态框宽度
const modalWidth = computed(() => {
  if (typeof window !== 'undefined') {
    const width = window.innerWidth
    if (width < 576) return '95%'      // 超小屏幕
    if (width < 768) return '90%'      // 小屏幕
    if (width < 992) return '85%'      // 中等屏幕
    if (width < 1200) return '800px'   // 大屏幕
    return '900px'                     // 超大屏幕
  }
  return '800px'
})

// 表单数据 - 重构版本，匹配后端API
const form = reactive({
  name: '',
  description: '',
  type: 'forward',
  user_port: 0, // 0表示自动分配
  portConfig: { mode: 'auto', allocMode: 'random', port: null }, // 端口配置
  auth_required: false,
  username: '',
  password: '',
  max_connections: 100,
  timeout: 30,
  client_id: undefined,
  listener_id: undefined,
  allowed_ips: '',
  blocked_ips: '',

  // 高级配置参数
  buffer_size: 8192,
  rate_limit: 0,
  bandwidth_limit: 0,
  connection_timeout: 10,
  retry_count: 3,
  retry_interval: 1,
  load_balance_strategy: 'round_robin',
  health_check_interval: 30,
  log_level: 'info',
  metrics_collection: true
})

// 表单验证规则 - 重构版本
const rules = {
  name: [
    { required: true, message: '请输入代理名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择代理类型', trigger: 'change' }
  ],
  client_id: [
    { required: true, message: '请选择关联客户端', trigger: 'change' }
  ],

  username: [
    {
      validator: (rule, value) => {
        if (form.auth_required && !value) {
          return Promise.reject('启用认证时用户名不能为空')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],

  password: [
    {
      validator: (rule, value) => {
        if (form.auth_required && !value) {
          return Promise.reject('启用认证时密码不能为空')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],

  // 高级参数验证规则
  buffer_size: [
    { type: 'number', min: 1024, max: 65536, message: '缓冲区大小必须在1KB-64KB之间', trigger: 'blur' }
  ],
  rate_limit: [
    { type: 'number', min: 0, max: 10000, message: '请求限制必须在0-10000之间', trigger: 'blur' }
  ],
  bandwidth_limit: [
    { type: 'number', min: 0, message: '带宽限制不能为负数', trigger: 'blur' }
  ],
  connection_timeout: [
    { type: 'number', min: 1, max: 300, message: '连接超时必须在1-300秒之间', trigger: 'blur' }
  ],
  retry_count: [
    { type: 'number', min: 0, max: 10, message: '重试次数必须在0-10之间', trigger: 'blur' }
  ],
  retry_interval: [
    { type: 'number', min: 1, max: 60, message: '重试间隔必须在1-60秒之间', trigger: 'blur' }
  ],
  health_check_interval: [
    { type: 'number', min: 5, max: 300, message: '健康检查间隔必须在5-300秒之间', trigger: 'blur' }
  ]
}




// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
  if (val) {
    // 弹窗打开时加载可用代理列表
    loadAvailableProxies()
    // 调试客户端列表
    console.log('🔍 CreateProxyModal打开，接收到的clientList:', props.clientList)
    console.log('🔍 clientList长度:', props.clientList.length)
    console.log('🔍 在线客户端:', props.clientList.filter(c => c.status === 1))
  }
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const handleClose = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    description: '',
    type: 'forward',
    user_port: 0, // 0表示自动分配
    portConfig: { mode: 'auto', allocMode: 'random', port: null }, // 端口配置
    auth_required: false,
    username: '',
    password: '',
    max_connections: 100,
    timeout: 30,
    client_id: undefined,
    listener_id: undefined,
    allowed_ips: '',
    blocked_ips: '',

    // 高级配置参数
    buffer_size: 8192,
    rate_limit: 0,
    bandwidth_limit: 0,
    connection_timeout: 10,
    retry_count: 3,
    retry_interval: 1,
    load_balance_strategy: 'round_robin',
    health_check_interval: 30,
    log_level: 'info',
    metrics_collection: true
  })
}

// 加载可用代理列表
const loadAvailableProxies = async () => {
  try {
    // TODO: 实际应该从API获取真实代理数据
    // 以下是测试示例数据，用于演示代理链功能
    availableProxies.value = [
      {
        id: 'test-proxy-1',
        name: '[测试] 外网代理-1',
        type: 'forward',
        client_name: 'TestClient-*************',
        status: 'running',
        port: 28001,
        description: '测试示例：目标外网系统，最接近Server',
        isTestData: true
      },
      {
        id: 'test-proxy-2',
        name: '[测试] 第一层内网代理-1',
        type: 'reverse',
        client_name: 'TestClient-*************',
        status: 'running',
        port: 28002,
        description: '测试示例：通过外网代理访问到的第一层内网',
        isTestData: true
      },
      {
        id: 'test-proxy-3',
        name: '[测试] 第二层内网代理-1',
        type: 'forward',
        client_name: 'TestClient-**********',
        status: 'running',
        port: 28003,
        description: '测试示例：通过第一层内网访问到的深层内网',
        isTestData: true
      }
    ]
  } catch (error) {
    console.error('加载可用代理列表失败:', error)
    message.error('加载可用代理列表失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    // 准备提交数据，匹配后端API格式
    const submitData = {
      name: form.name,
      description: form.description,
      type: form.type,
      user_port: form.portConfig?.mode === 'manual' ? form.portConfig.port : 0,
      auth_required: form.auth_required,
      alloc_mode: form.portConfig?.mode === 'auto' ? form.portConfig.allocMode || 'random' : 'manual',
      username: form.auth_required ? form.username : '',
      password: form.auth_required ? form.password : '',
      max_connections: form.max_connections,
      timeout: form.timeout,
      client_id: form.client_id,
      listener_id: form.listener_id,
      allowed_ips: form.allowed_ips,
      blocked_ips: form.blocked_ips,

      // 高级配置参数
      buffer_size: form.buffer_size,
      rate_limit: form.rate_limit,
      bandwidth_limit: form.bandwidth_limit,
      connection_timeout: form.connection_timeout,
      retry_count: form.retry_count,
      retry_interval: form.retry_interval,
      load_balance_strategy: form.load_balance_strategy,
      health_check_interval: form.health_check_interval,
      log_level: form.log_level,
      metrics_collection: form.metrics_collection
    }

    const response = await createProxy(submitData)

    if (response.code === 200) {
      message.success('代理创建成功')
      emit('success')
    } else {
      message.error(response.msg || '代理创建失败')
    }
  } catch (error) {
    if (error.errorFields) {
      // 表单验证失败
      return
    }
    console.error('创建代理失败:', error)
    message.error('创建代理失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}
</style>
