<template>
  <a-modal
    v-model:open="visible"
    title="新建文件"
    @ok="handleCreateFile"
    @cancel="handleCancel"
    :confirm-loading="loading"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="文件名" name="fileName">
        <a-input
          v-model:value="formData.fileName"
          placeholder="请输入文件名（包含扩展名）"
          @keyup.enter="handleCreateFile"
        />
      </a-form-item>
      
      <a-form-item label="文件内容" name="content">
        <a-textarea
          v-model:value="formData.content"
          placeholder="请输入文件内容（可为空）"
          :rows="8"
          show-count
        />
      </a-form-item>
      
      <a-form-item>
        <a-space direction="vertical" style="width: 100%">
          <a-checkbox v-model:checked="formData.createDirs">
            自动创建目录（如果路径中的目录不存在）
          </a-checkbox>
          <a-checkbox v-model:checked="formData.forceCreate">
            强制创建（覆盖已存在的文件）
          </a-checkbox>
        </a-space>
      </a-form-item>
      
      <a-form-item label="编码格式">
        <a-select v-model:value="formData.encoding" style="width: 120px">
          <a-select-option value="utf-8">UTF-8</a-select-option>
          <a-select-option value="gbk">GBK</a-select-option>
          <a-select-option value="ascii">ASCII</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { fileApi } from '@/api';
import { buildPath } from './fileUtils.js';

// 接收属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    default: '/'
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'create-complete']);

// 响应式数据
const loading = ref(false);
const formRef = ref(null);
const visible = ref(false);

// 表单数据
const formData = reactive({
  fileName: '',
  content: '',
  encoding: 'utf-8',
  createDirs: true,
  forceCreate: false
});

// 表单验证规则
const rules = {
  fileName: [
    { required: true, message: '请输入文件名', trigger: 'blur' },
    { 
      pattern: /^[^<>:"/\\|?*]+$/, 
      message: '文件名不能包含特殊字符 < > : " / \\ | ? *', 
      trigger: 'blur' 
    },
    { 
      max: 255, 
      message: '文件名长度不能超过255个字符', 
      trigger: 'blur' 
    }
  ]
};

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    resetForm();
  }
});

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

// 重置表单
const resetForm = () => {
  formData.fileName = '';
  formData.content = '';
  formData.encoding = 'utf-8';
  formData.createDirs = true;
  formData.forceCreate = false;
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
  resetForm();
};

// 处理创建文件
const handleCreateFile = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    loading.value = true;
    
    // 构建完整文件路径
    const filePath = buildPath(props.currentPath, formData.fileName);
    
    // 调用API创建文件
    const response = await fileApi.createFile(props.clientId, {
      path: filePath,
      content: formData.content,
      encoding: formData.encoding,
      force_create: formData.forceCreate,
      create_dirs: formData.createDirs,
      file_mode: '0644' // 默认文件权限
    });
    
    // 检查API响应结构
    const result = response.data.data ||response.data || response;
    
    if (result.success) {
      message.success(`文件创建成功: ${formData.fileName}`);
      visible.value = false;
      emit('create-complete');
      resetForm();
    } else {
      // 处理各种错误情况
      if (result.file_exists) {
        message.error('文件已存在，请勾选"强制创建"选项或使用其他文件名');
      } else if (result.not_allow) {
        message.error('权限不足，无法创建文件');
      } else if (result.is_dir) {
        message.error('目标路径是一个目录，无法创建文件');
      } else {
        message.error(result.error || '创建文件失败');
      }
    }
  } catch (error) {
    console.error('创建文件失败:', error);
    if (error.errors) {
      // 表单验证错误
      return;
    }
    message.error('创建文件失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

.ant-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.ant-checkbox {
  margin-bottom: 8px;
}
</style>