//go:build linux && !cgo && !headless
// +build linux,!cgo,!headless

package common

import (
	"fmt"
	"log"
	"os/exec"
	"time"
)

// ScreenshotRequest 截图请求
type ScreenshotRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	MonitorID    int    `json:"monitor_id"`    // 显示器ID (多显示器支持)
	MonitorIndex int    `json:"monitor_index"` // 显示器索引 (前端兼容)
	X            int    `json:"x"`             // 区域截图的X坐标
	Y            int    `json:"y"`             // 区域截图的Y坐标
	Width        int    `json:"width"`         // 区域截图的宽度
	Height       int    `json:"height"`        // 区域截图的高度
	Format       string `json:"format"`        // 图片格式: "png", "jpeg", "bmp"
	Quality      int    `json:"quality"`       // JPEG质量 (1-100)
}

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	TaskID    uint64 `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 是否成功
	Error     string `json:"error"`      // 错误信息
	ImageData []byte `json:"image_data"` // 图片数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 文件大小
	Timestamp int64  `json:"timestamp"`  // 截图时间戳
}

// handleScreenshotRequest 处理截图请求 - 存根版本（无CGO或headless环境）
func (cm *ConnectionManager) handleScreenshotRequest(packet *Packet) {
	// 完全模仿X11版本的switch逻辑
	switch packet.Header.Code {
	case Pic:
		cm.handleScreenshot(packet)
	case StreamStart:
		cm.handleScreenStreamStart(packet)
	case StreamStop:
		cm.handleScreenStreamStop(packet)
	case StreamData:
		cm.handleScreenStream(packet)
	case MonitorList:
		cm.handleMonitorList(packet)
	default:
		log.Printf("未知的截图操作代码: %d", packet.Header.Code)
	}
}

// handleScreenshot 处理单次截图请求（stub版本）
func (cm *ConnectionManager) handleScreenshot(packet *Packet) {
	// 完全模仿X11版本的逻辑
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析截图请求失败: %v", err)
		return
	}

	log.Printf("🖼️ 开始处理截图请求 - 类型: %d, 格式: %s, 质量: %d", req.Type, req.Format, req.Quality)

	// 使用captureScreenStub函数
	imageData, width, height, err := cm.captureScreenStub(&req)
	if err != nil {
		log.Printf("❌ 截图失败: %v", err)
		errorResp := ScreenshotResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "截图失败: " + err.Error(),
		}
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})

	log.Printf("✅ 截图完成 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(imageData))
}

// 简化的流状态管理
var stubStreamState struct {
	isRunning   bool
	stopChannel chan bool
	taskID      uint64
	// 性能统计
	frameCount       uint64
	startTime        time.Time
	totalCaptureTime time.Duration
	totalSendTime    time.Duration
}

// handleScreenStreamStart 处理视频流开始请求（stub版本）
func (cm *ConnectionManager) handleScreenStreamStart(packet *Packet) {
	log.Printf("📹 视频流开始请求（简化版本）")

	// 解析请求
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流启动请求失败: %v", err)
		return
	}

	// 如果已经在运行，先停止
	if stubStreamState.isRunning {
		stubStreamState.stopChannel <- true
		time.Sleep(100 * time.Millisecond)
	}

	// 初始化流状态和统计
	stubStreamState.isRunning = true
	stubStreamState.stopChannel = make(chan bool, 1)
	stubStreamState.taskID = req.TaskID
	stubStreamState.frameCount = 0
	stubStreamState.startTime = time.Now()
	stubStreamState.totalCaptureTime = 0
	stubStreamState.totalSendTime = 0

	// 启动流数据发送goroutine
	go cm.runScreenStreamStub(req.TaskID)

	log.Printf("🎥 屏幕流已启动（简化版本）- TaskID: %d", req.TaskID)

	// 发送开始成功响应
	response := map[string]interface{}{
		"task_id":   req.TaskID,
		"success":   true,
		"stream_id": fmt.Sprintf("stream_%d_%d", req.TaskID, time.Now().Unix()),
		"error":     "",
	}
	cm.sendResp(Screenshot, StreamStart, response)
}

// handleScreenStreamStop 处理视频流停止请求（stub版本）
func (cm *ConnectionManager) handleScreenStreamStop(packet *Packet) {
	log.Printf("⏹️ 视频流停止请求")

	// 解析请求
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流停止请求失败: %v", err)
		return
	}

	// 停止流并输出统计
	if stubStreamState.isRunning {
		stubStreamState.stopChannel <- true
		stubStreamState.isRunning = false

		// 🛑 完全模仿X11版本的最终统计日志
		duration := time.Since(stubStreamState.startTime)
		avgFPS := float64(stubStreamState.frameCount) / duration.Seconds()
		log.Printf("🛑 屏幕流线程退出 - 总帧数: %d, 运行时长: %.2fs, 平均帧率: %.2f fps",
			stubStreamState.frameCount, duration.Seconds(), avgFPS)

		// 📊 详细性能统计
		if stubStreamState.frameCount > 0 {
			log.Printf("📊 性能统计详情:")
			log.Printf("   截图总耗时: %v (平均: %v/帧)",
				stubStreamState.totalCaptureTime,
				stubStreamState.totalCaptureTime/time.Duration(stubStreamState.frameCount))
			log.Printf("   发送总耗时: %v (平均: %v/帧)",
				stubStreamState.totalSendTime,
				stubStreamState.totalSendTime/time.Duration(stubStreamState.frameCount))

			// 计算各步骤占比
			totalProcessTime := stubStreamState.totalCaptureTime + stubStreamState.totalSendTime
			if totalProcessTime > 0 {
				log.Printf("📈 各步骤耗时占比:")
				log.Printf("   截图: %.1f%%", float64(stubStreamState.totalCaptureTime)/float64(totalProcessTime)*100)
				log.Printf("   发送: %.1f%%", float64(stubStreamState.totalSendTime)/float64(totalProcessTime)*100)
			}
		}

		log.Printf("🛑 屏幕流已停止")
	} else {
		log.Printf("⚠️ 屏幕流未运行")
	}

	// 发送停止成功响应
	response := map[string]interface{}{
		"task_id": req.TaskID,
		"success": true,
		"error":   "",
	}
	cm.sendResp(Screenshot, StreamStop, response)
}

// handleScreenStream 处理视频流数据请求（stub版本）
func (cm *ConnectionManager) handleScreenStream(packet *Packet) {
	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流请求失败: %v", err)
		return
	}

	// 模仿X11版本：如果流未运行，发送单张截图
	// 注意：这里应该检查streamState.isRunning，但stub版本简化为总是发送单张截图
	imageData, width, height, err := cm.captureScreenStub(&req)
	if err != nil {
		log.Printf("❌ 屏幕流截图失败: %v", err)
		return
	}

	// 完全模仿X11版本的响应格式
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})
}

// handleMonitorList 处理显示器列表请求（stub版本）
func (cm *ConnectionManager) handleMonitorList(packet *Packet) {
	log.Printf("🖥️ 显示器列表请求")

	// 解析请求
	var taskID uint64 = 0
	if len(packet.PacketData.Data) > 0 {
		var req map[string]interface{}
		if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err == nil {
			if id, ok := req["task_id"].(float64); ok {
				taskID = uint64(id)
			}
		}
	}

	// 简化的显示器信息
	monitors := []map[string]interface{}{
		{
			"id":     0,
			"name":   "主显示器",
			"width":  1920,
			"height": 1080,
			"x":      0,
			"y":      0,
		},
	}

	response := map[string]interface{}{
		"task_id":  taskID,
		"success":  true,
		"monitors": monitors,
		"count":    1,
	}
	cm.sendResp(Screenshot, MonitorList, response)
}

// captureScreenStub 模仿X11版本的captureScreen函数（命令行版本）
func (cm *ConnectionManager) captureScreenStub(req *ScreenshotRequest) ([]byte, int, int, error) {
	log.Printf("🔍 截图方法检测 - 请求类型: %d, 格式: %s, 质量: %d", req.Type, req.Format, req.Quality)

	var output []byte
	var err error

	// 完全模仿X11版本的命令行回退逻辑
	log.Printf("📸 尝试方法1: scrot命令截图")
	cmd := exec.Command("scrot", "-")
	output, err = cmd.Output()
	if err != nil {
		log.Printf("❌ 方法1失败: scrot命令不可用 - %v", err)
		log.Printf("📸 尝试方法2: ImageMagick import命令截图")
		cmd = exec.Command("import", "-window", "root", "png:-")
		output, err = cmd.Output()
		if err != nil {
			log.Printf("❌ 方法2失败: import命令不可用 - %v", err)
			log.Printf("📸 尝试方法3: gnome-screenshot命令截图")
			// gnome-screenshot不支持stdout，所以跳过
			return nil, 0, 0, fmt.Errorf("截图命令执行失败，请安装scrot或imagemagick: %v", err)
		} else {
			log.Printf("✅ 方法2成功: ImageMagick import命令截图")
		}
	} else {
		log.Printf("✅ 方法1成功: scrot命令截图")
	}

	// 返回默认分辨率（实际应该解析图片获取真实尺寸）
	width := 1920
	height := 1080

	log.Printf("✅ 截图完成 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(output))
	return output, width, height, nil
}

// runScreenStreamStub 运行屏幕流（优化版本）
func (cm *ConnectionManager) runScreenStreamStub(taskID uint64) {
	log.Printf("🎬 开始运行屏幕流 - TaskID: %d", taskID)

	ticker := time.NewTicker(66 * time.Millisecond) // 15 FPS
	defer ticker.Stop()

	// 预先检测最佳截图工具
	bestTool := cm.detectBestScreenshotTool()
	log.Printf("🔧 使用截图工具: %s", bestTool)

	// 🕒 性能计时统计（完全模仿X11版本）
	defer func() {
		// 确保在退出时输出最终统计
		if stubStreamState.frameCount > 0 {
			duration := time.Since(stubStreamState.startTime)
			avgFPS := float64(stubStreamState.frameCount) / duration.Seconds()
			log.Printf("🛑 屏幕流线程退出 - 总帧数: %d, 运行时长: %.2fs, 平均帧率: %.2f fps",
				stubStreamState.frameCount, duration.Seconds(), avgFPS)
		}
	}()

	for {
		select {
		case <-stubStreamState.stopChannel:
			log.Printf("🛑 收到停止信号，退出屏幕流")
			return
		case <-ticker.C:
			// 🕒 帧开始时间
			frameStart := time.Now()
			stubStreamState.frameCount++

			// 🕒 截图计时
			captureStart := time.Now()
			imageData, width, height, err := cm.fastCaptureScreen(bestTool)
			captureTime := time.Since(captureStart)
			stubStreamState.totalCaptureTime += captureTime

			if err != nil {
				log.Printf("❌ 流截图失败: %v", err)
				continue
			}

			// 🕒 发送计时
			sendStart := time.Now()
			response := map[string]interface{}{
				"task_id":    taskID,
				"success":    true,
				"stream_id":  fmt.Sprintf("stream_%d", taskID),
				"frame_data": imageData,
				"width":      width,
				"height":     height,
				"format":     "jpeg",
				"size":       len(imageData),
				"frame":      stubStreamState.frameCount,
			}

			cm.sendResp(Screenshot, StreamData, response)
			sendTime := time.Since(sendStart)
			stubStreamState.totalSendTime += sendTime

			// 🕒 总体统计
			totalFrameTime := time.Since(frameStart)

			// 只在前10帧显示详细日志，之后每50帧显示一次
			if stubStreamState.frameCount <= 10 || stubStreamState.frameCount%50 == 0 {
				log.Printf("✅ 帧 #%d 处理完成 - 总耗时: %v (截图: %v, 发送: %v), 大小: %d bytes",
					stubStreamState.frameCount, totalFrameTime, captureTime, sendTime, len(imageData))
			}

			// 每10帧输出一次平均性能统计（完全模仿X11版本）
			if stubStreamState.frameCount%10 == 0 {
				avgCaptureTime := stubStreamState.totalCaptureTime / time.Duration(stubStreamState.frameCount)
				avgSendTime := stubStreamState.totalSendTime / time.Duration(stubStreamState.frameCount)
				currentDuration := time.Since(stubStreamState.startTime)
				currentFPS := float64(stubStreamState.frameCount) / currentDuration.Seconds()

				log.Printf("📊 最近10帧平均性能 - 截图: %v, 发送: %v, 当前FPS: %.2f",
					avgCaptureTime, avgSendTime, currentFPS)
			}
		}
	}
}

// detectBestScreenshotTool 检测最佳截图工具
func (cm *ConnectionManager) detectBestScreenshotTool() string {
	// 测试各种工具的可用性和速度
	tools := []string{"scrot", "import", "xwd"}

	for _, tool := range tools {
		start := time.Now()
		var cmd *exec.Cmd

		switch tool {
		case "scrot":
			cmd = exec.Command("scrot", "--version")
		case "import":
			cmd = exec.Command("import", "-version")
		case "xwd":
			cmd = exec.Command("xwd", "-help")
		}

		err := cmd.Run()
		duration := time.Since(start)

		if err == nil {
			log.Printf("🔍 工具 %s 可用，响应时间: %v", tool, duration)
			return tool
		}
	}

	log.Printf("⚠️ 未找到可用的截图工具，使用默认scrot")
	return "scrot"
}

// fastCaptureScreen 快速截图（优化版本）
func (cm *ConnectionManager) fastCaptureScreen(tool string) ([]byte, int, int, error) {
	var cmd *exec.Cmd

	switch tool {
	case "scrot":
		// 使用scrot原始分辨率，中等质量JPEG
		cmd = exec.Command("scrot", "-q", "60", "-z", "-")
	case "import":
		// 使用ImageMagick原始分辨率，中等质量
		cmd = exec.Command("import", "-window", "root", "-quality", "60", "jpeg:-")
	case "xwd":
		// xwd转换为JPEG（需要额外处理）
		cmd = exec.Command("xwd", "-root", "-out", "/dev/stdout")
	default:
		cmd = exec.Command("scrot", "-q", "60", "-z", "-")
	}

	output, err := cmd.Output()
	if err != nil {
		return nil, 0, 0, err
	}

	// 返回原始分辨率（实际应该解析图片获取真实尺寸）
	width := 1920  // 原始分辨率
	height := 1080 // 原始分辨率

	return output, width, height, nil
}
