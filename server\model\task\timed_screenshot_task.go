package task

import (
	"time"
)

// TimedScreenshotTask 定时截图任务模型
type TimedScreenshotTask struct {
	ID              uint64     `json:"id" gorm:"primarykey"`
	ClientID        uint       `json:"client_id" gorm:"not null;index"`
	Status          string     `json:"status" gorm:"size:20;default:'stopped'"` // active, stopped, paused
	IntervalSeconds int        `json:"interval_seconds" gorm:"not null"`         // 间隔秒数
	MaxCount        int        `json:"max_count" gorm:"default:0"`               // 最大次数，0表示无限制
	CurrentCount    int        `json:"current_count" gorm:"default:0"`           // 当前执行次数
	ScreenshotType  int        `json:"screenshot_type" gorm:"default:0"`         // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	Quality         int        `json:"quality" gorm:"default:80"`                // 图片质量
	Format          string     `json:"format" gorm:"size:10;default:'png'"`      // 图片格式
	MonitorIndex    int        `json:"monitor_index" gorm:"default:0"`           // 显示器索引
	// 区域截图参数
	X               int        `json:"x" gorm:"default:0"`
	Y               int        `json:"y" gorm:"default:0"`
	Width           int        `json:"width" gorm:"default:0"`
	Height          int        `json:"height" gorm:"default:0"`
	// 智能触发相关
	EnableSmartTrigger bool    `json:"enable_smart_trigger" gorm:"default:false"` // 启用智能触发
	// 时间字段
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	LastExecutedAt  *time.Time `json:"last_executed_at"`
	StartedAt       *time.Time `json:"started_at"`
	StoppedAt       *time.Time `json:"stopped_at"`
}

// TableName 指定表名
func (TimedScreenshotTask) TableName() string {
	return "timed_screenshot_tasks"
}

// IsActive 检查任务是否处于活跃状态
func (t *TimedScreenshotTask) IsActive() bool {
	return t.Status == "active"
}

// ShouldStop 检查是否应该停止任务（达到最大次数）
func (t *TimedScreenshotTask) ShouldStop() bool {
	return t.MaxCount > 0 && t.CurrentCount >= t.MaxCount
}

// IncrementCount 增加执行次数
func (t *TimedScreenshotTask) IncrementCount() {
	t.CurrentCount++
	now := time.Now()
	t.LastExecutedAt = &now
}
