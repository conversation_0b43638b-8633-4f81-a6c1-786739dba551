//go:build linux && arm

package main

import (
	"debug/elf"
)

// setDefaultArchitecture sets the default architecture for the current build
func setDefaultArchitecture(generator *ASMGenerator) {
	generator.arch = elf.EM_ARM
}

// isArchitectureSupported checks if the given architecture is supported on this build
func isArchitectureSupported(arch elf.Machine) bool {
	switch arch {
	case elf.EM_ARM:
		return true
	case elf.EM_X86_64:
		// Cross-compilation support - can generate x86_64 code on ARM
		return true
	case elf.EM_386:
		// Cross-compilation support - can generate i386 code on ARM
		return true
	case elf.EM_AARCH64:
		// Cross-compilation support - can generate ARM64 code on ARM
		return true
	default:
		return false
	}
}

// getArchitectureName returns the human-readable name for the architecture
func getArchitectureName(arch elf.Machine) string {
	switch arch {
	case elf.EM_X86_64:
		return "x86_64"
	case elf.EM_AARCH64:
		return "ARM64"
	case elf.EM_386:
		return "i386"
	case elf.EM_ARM:
		return "ARM"
	default:
		return "Unknown"
	}
}
