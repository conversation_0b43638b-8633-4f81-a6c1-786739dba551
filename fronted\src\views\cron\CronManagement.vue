<template>
  <div class="cron-management">
    <div class="page-header">
      <h2>定时任务管理</h2>
      <div class="header-actions">
        <a-button @click="refreshData" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <PlusOutlined />
          </template>
          创建任务
        </a-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filters.clientId"
            placeholder="选择客户端"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="client in clientList"
              :key="client.id"
              :value="client.id"
            >
              {{ client.hostname || client.remoteAddr }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.taskType"
            placeholder="任务类型"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="command">命令执行</a-select-option>
            <a-select-option value="script">脚本执行</a-select-option>
            <a-select-option value="screenshot">截图任务</a-select-option>
            <a-select-option value="file">文件操作</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.status"
            placeholder="任务状态"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="active">运行中</a-select-option>
            <a-select-option value="paused">已暂停</a-select-option>
            <a-select-option value="stopped">已停止</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-input-search
            v-model:value="filters.keyword"
            placeholder="搜索任务名称"
            @search="handleFilterChange"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 任务列表 -->
    <a-table
      :columns="columns"
      :data-source="taskList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 任务类型 -->
      <template #taskType="{ record }">
        <a-space>
          <a-tag :color="getTaskTypeColor(record.task_type)">
            {{ record.task_type_display }}
          </a-tag>
          <a-tag v-if="record.is_managed" color="orange" size="small">
            {{ getSourceTypeDisplay(record.source_type) }}
          </a-tag>
        </a-space>
      </template>

      <!-- 任务状态 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ record.status_display }}
        </a-tag>
      </template>

      <!-- Cron表达式 -->
      <template #cronExpr="{ record }">
        <a-tooltip :title="record.cron_expr">
          <code class="cron-expr">{{ record.cron_expr }}</code>
        </a-tooltip>
      </template>

      <!-- 下次执行时间 -->
      <template #nextRunAt="{ record }">
        <span v-if="record.next_run_at">
          {{ formatTime(record.next_run_at) }}
        </span>
        <span v-else class="text-muted">-</span>
      </template>

      <!-- 操作 -->
      <template #action="{ record }">
        <a-space>
          <!-- 对于由其他系统管理的任务，显示特殊操作 -->
          <template v-if="record.is_managed">
            <a-tooltip v-if="record.source_type === 'screenshot_timer'" title="点击跳转到客户端管理页面操作定时截图">
              <a-button
                size="small"
                @click="goToClientManagement(record.client_id)"
              >
                管理定时截图
              </a-button>
            </a-tooltip>
            <a-tooltip v-else title="此任务由其他系统管理">
              <a-button
                size="small"
                disabled
              >
                由{{ getSourceTypeDisplay(record.source_type) }}管理
              </a-button>
            </a-tooltip>
          </template>

          <!-- 对于手动创建的任务，显示完整操作 -->
          <template v-else>
            <a-button
              v-if="record.status === 'stopped' || record.status === 'paused'"
              type="primary"
              size="small"
              @click="startTask(record.id)"
            >
              启动
            </a-button>
            <a-button
              v-if="record.status === 'active'"
              size="small"
              @click="pauseTask(record.id)"
            >
              暂停
            </a-button>
            <a-button
              v-if="record.status === 'active' || record.status === 'paused'"
              danger
              size="small"
              @click="stopTask(record.id)"
            >
              停止
            </a-button>
            <a-button
              size="small"
              @click="executeTask(record.id)"
            >
              立即执行
            </a-button>
          </template>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="editTask(record)">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-item @click="viewHistory(record)">
                  <HistoryOutlined />
                  执行历史
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="deleteTask(record)" danger>
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
            <a-button size="small">
              更多
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </a-space>
      </template>
    </a-table>

    <!-- 创建/编辑任务模态框 -->
    <CronTaskModal
      v-model:visible="modalVisible"
      :task="currentTask"
      :client-list="clientList"
      @success="handleModalSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, EditOutlined, HistoryOutlined, DeleteOutlined, DownOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import * as cronApi from '@/api/cron'
import * as clientApi from '@/api/client'
import CronTaskModal from './components/CronTaskModal.vue'

// 响应式数据
const router = useRouter()
const loading = ref(false)
const taskList = ref([])
const clientList = ref([])
const modalVisible = ref(false)
const currentTask = ref(null)

// 筛选条件
const filters = reactive({
  clientId: undefined,
  taskType: undefined,
  status: undefined,
  keyword: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80
  },
  {
    title: '任务名称',
    dataIndex: 'name',
    width: 150
  },
  {
    title: '任务类型',
    dataIndex: 'task_type',
    width: 120,
    slots: { customRender: 'taskType' }
  },
  {
    title: '目标客户端',
    dataIndex: 'client_name',
    width: 150
  },
  {
    title: 'Cron表达式',
    dataIndex: 'cron_expr',
    width: 150,
    slots: { customRender: 'cronExpr' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '下次执行',
    dataIndex: 'next_run_at',
    width: 150,
    slots: { customRender: 'nextRunAt' }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 获取任务列表
const fetchTaskList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...filters
    }
    
    const response = await cronApi.getTaskList(params)
    if (response.code === 200) {
      taskList.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    message.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 获取客户端列表
const fetchClientList = async () => {
  try {
    const response = await clientApi.getClientList({ page: 1, page_size: 1000 })
    if (response.code === 200) {
      clientList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取客户端列表失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    fetchClientList(),
    fetchTaskList()
  ])
  message.success('数据刷新成功')
}

// 处理筛选条件变化
const handleFilterChange = () => {
  pagination.current = 1
  fetchTaskList()
}

// 处理表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchTaskList()
}

// 跳转到创建任务页面
const showCreateModal = () => {
  router.push('/cron/create')
}

// 编辑任务
const editTask = (task) => {
  currentTask.value = task
  modalVisible.value = true
}

// 模态框成功回调
const handleModalSuccess = () => {
  modalVisible.value = false
  fetchTaskList()
}

// 启动任务
const startTask = async (id) => {
  try {
    await cronApi.startTask(id)
    message.success('任务启动成功')
    fetchTaskList()
  } catch (error) {
    console.error('启动任务失败:', error)
  }
}

// 暂停任务
const pauseTask = async (id) => {
  try {
    await cronApi.pauseTask(id)
    message.success('任务暂停成功')
    fetchTaskList()
  } catch (error) {
    console.error('暂停任务失败:', error)
  }
}

// 停止任务
const stopTask = async (id) => {
  try {
    await cronApi.stopTask(id)
    message.success('任务停止成功')
    fetchTaskList()
  } catch (error) {
    console.error('停止任务失败:', error)
  }
}

// 立即执行任务
const executeTask = async (id) => {
  try {
    await cronApi.executeTask(id)
    message.success('任务执行成功')
  } catch (error) {
    console.error('执行任务失败:', error)
  }
}

// 删除任务
const deleteTask = (task) => {
  let title = '确认删除任务'
  let content = `确定要删除任务 "${task.name}" 吗？`

  // 如果是定时截图任务，添加特殊提示
  if (task.source_type === 'screenshot_timer') {
    title = '确认删除定时截图任务'
    content = `确定要删除定时截图任务 "${task.name}" 吗？\n\n⚠️ 删除此任务将同时停止客户端的定时截图功能。`
  }

  Modal.confirm({
    title,
    content,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const response = await cronApi.deleteTask(task.id)
        if (response.code === 200) {
          message.success('删除成功')
          fetchTaskList()
        } else {
          message.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除任务失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 查看执行历史
const viewHistory = (task) => {
  router.push(`/cron/history?task_id=${task.id}`)
}

// 获取任务类型颜色
const getTaskTypeColor = (type) => {
  const colors = {
    command: 'blue',
    script: 'green',
    screenshot: 'orange',
    file: 'purple'
  }
  return colors[type] || 'default'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    paused: 'orange',
    stopped: 'red'
  }
  return colors[status] || 'default'
}

// 获取来源类型显示名称
const getSourceTypeDisplay = (sourceType) => {
  const displays = {
    screenshot_timer: '定时截图',
    manual: '手动创建'
  }
  return displays[sourceType] || sourceType
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

// 跳转到客户端管理页面
const goToClientManagement = (clientId) => {
  router.push(`/client/index?id=${clientId}`)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchClientList()
  fetchTaskList()
})
</script>

<style scoped lang="scss">
.cron-management {
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .filter-section {
    margin-bottom: 16px;
  }

  .cron-expr {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
  }

  .text-muted {
    color: #999;
  }
}
</style>
