package tcp

import (
	"bufio"
	"crypto/hmac"
	"crypto/rsa"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand/v2"
	"net"
	"net/http"
	"runtime"
	"server/core/listener/stats"
	"server/core/manager/cache"
	"server/core/manager/clientmgr"
	"server/core/manager/dbpool"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/basic"
	"server/model/request/heartbeat"
	heartbeatResponse "server/model/response/heartbeat"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/minio/sha256-simd"

	"gorm.io/gorm"

	"go.uber.org/zap"
)

// MemoryPool 内存池管理
type MemoryPool struct {
	bufferPool sync.Pool
	packetPool sync.Pool
}

// ListenerStats TCP监听器统计信息
type ListenerStats struct {
	ListenerID        uint      `json:"listener_id"`
	Address           string    `json:"address"`
	IsActive          bool      `json:"is_active"`
	StartTime         time.Time `json:"start_time"`
	TotalConnections  int       `json:"total_connections"`
	ActiveConnections int       `json:"active_connections"`
	DataTransferred   int64     `json:"data_transferred"`
	PacketsProcessed  int64     `json:"packets_processed"`
	ErrorCount        int64     `json:"error_count"`
	LastActivity      time.Time `json:"last_activity"`
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	TotalConnections    int64     `json:"total_connections"`
	ActiveConnections   int64     `json:"active_connections"`
	ConnectionsPerHour  float64   `json:"connections_per_hour"`
	AverageConnTime     float64   `json:"average_conn_time"`
	LastConnectionTime  time.Time `json:"last_connection_time"`
	DisconnectionsToday int64     `json:"disconnections_today"`
}

// TrafficStats 流量统计信息
type TrafficStats struct {
	BytesReceived   int64     `json:"bytes_received"`
	BytesSent       int64     `json:"bytes_sent"`
	PacketsReceived int64     `json:"packets_received"`
	PacketsSent     int64     `json:"packets_sent"`
	AverageSpeed    float64   `json:"average_speed_mbps"`
	PeakSpeed       float64   `json:"peak_speed_mbps"`
	LastResetTime   time.Time `json:"last_reset_time"`
}

// NewMemoryPool 创建内存池
func NewMemoryPool() *MemoryPool {
	return &MemoryPool{
		bufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 4096) // 4KB缓冲区
			},
		},
		packetPool: sync.Pool{
			New: func() interface{} {
				return &tlv.Packet{}
			},
		},
	}
}

// GetBuffer 获取缓冲区
func (mp *MemoryPool) GetBuffer() []byte {
	return mp.bufferPool.Get().([]byte)
}

// PutBuffer 归还缓冲区
func (mp *MemoryPool) PutBuffer(buf []byte) {
	if cap(buf) == 4096 {
		mp.bufferPool.Put(buf[:0])
	}
}

// GetPacket 获取数据包
func (mp *MemoryPool) GetPacket() *tlv.Packet {
	return mp.packetPool.Get().(*tlv.Packet)
}

// PutPacket 归还数据包
func (mp *MemoryPool) PutPacket(packet *tlv.Packet) {
	// 重置数据包
	packet.Header = nil
	packet.PacketData = nil
	mp.packetPool.Put(packet)
}

// ConnectionInfo 连接信息结构体
type ConnectionInfo struct {
	Conn         net.Conn
	LastActivity time.Time
	Handler      *TLVMessageHandler
	Metadata     *tlv.METADATA
	Client       *sys.Client
}

// TCPListener 表示一个tcp类型的监听器
type TCPListener struct {
	ID                uint
	LocalListenAddr   string
	RemoteConnectAddr string
	Status            int
	Listener          net.Listener

	// 优化后的连接管理
	connections     sync.Map // map[string]*ConnectionInfo - 使用sync.Map提高并发性能
	connectionCount int64    // 原子计数器
	maxConnections  int      // 最大连接数限制

	Key             string // 加密密钥
	Salt            string // 加密盐值
	PingDuration    int
	MaxTimeoutCount int

	// 统计信息
	stats struct {
		startTime         time.Time
		totalConnections  int64
		activeConnections int64
		dataTransferred   int64
		packetsProcessed  int64
		errorCount        int64
		lastActivity      time.Time
		bytesReceived     int64
		bytesSent         int64
		packetsReceived   int64
		packetsSent       int64
		mutex             sync.RWMutex
	}
	Mutex        sync.RWMutex // 使用读写锁
	httpServer   *http.Server
	ClientPrefix string            // 客户端文件名前缀
	Clients      map[string]string // 不同架构的客户端文件名，key为架构标识(l64,l32,a64,a32)

	// RSA密钥
	rsaPrivateKey    *rsa.PrivateKey
	rsaPublicKey     *rsa.PublicKey
	heartbeatManager *HeartbeatManager // 心跳管理器

	// 正向连接管理器
	ForwardConnectionManager *ForwardConnectionManager

	// 内存管理
	cleanupTicker *time.Ticker
	shutdownCh    chan struct{}
	memoryPool    *MemoryPool // 内存池
}

// 连接管理方法
func (l *TCPListener) addConnection(remoteAddr string, conn net.Conn) {
	if l.maxConnections > 0 && atomic.LoadInt64(&l.connectionCount) >= int64(l.maxConnections) {
		conn.Close()
		global.LOG.Warn("连接数已达上限", zap.String("addr", remoteAddr), zap.Int("max", l.maxConnections))
		return
	}

	// 🚨 使用工作池进行进程检测 - 可随时删除的代码块开始
	localAddr := conn.LocalAddr().String()

	// 提交进程检测任务到工作池
	workerpool.SubmitProcessDetection(localAddr, remoteAddr,
		workerpool.IsLocalConnection(remoteAddr),
		func(pid, processName string) {
			if pid != "" && processName != "" {
				global.LOG.Info("检测到本地连接进程信息",
					zap.String("remote_addr", remoteAddr),
					zap.String("local_addr", localAddr),
					zap.String("process_id", pid),
					zap.String("process_name", processName),
					zap.String("platform", runtime.GOOS),
					zap.Bool("is_local", true))
			} else if workerpool.IsLocalConnection(remoteAddr) {
				global.LOG.Info("无法获取本地连接进程信息",
					zap.String("remote_addr", remoteAddr),
					zap.String("local_addr", localAddr),
					zap.String("platform", runtime.GOOS),
					zap.Bool("is_local", true))
			} else {
				global.LOG.Info("检测到远程连接",
					zap.String("remote_addr", remoteAddr),
					zap.String("local_addr", localAddr),
					zap.Bool("is_local", false))
			}
		})
	// 🚨 使用工作池进行进程检测 - 可随时删除的代码块结束

	connInfo := &ConnectionInfo{
		Conn:         conn,
		LastActivity: time.Now(),
	}

	l.connections.Store(remoteAddr, connInfo)
	atomic.AddInt64(&l.connectionCount, 1)
	global.LOG.Info("新连接已添加", zap.String("addr", remoteAddr), zap.Int64("total", atomic.LoadInt64(&l.connectionCount)))
}

func (l *TCPListener) removeConnection(remoteAddr string) {
	if connInfo, ok := l.connections.LoadAndDelete(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil {
			// 🚨 使用工作池进行进程检测 - 可随时删除的代码块开始
			localAddr := info.Conn.LocalAddr().String()

			// 提交进程检测任务到工作池
			workerpool.SubmitProcessDetection(localAddr, remoteAddr,
				workerpool.IsLocalConnection(remoteAddr),
				func(pid, processName string) {
					if pid != "" && processName != "" {
						global.LOG.Info("本地连接断开时的进程信息",
							zap.String("remote_addr", remoteAddr),
							zap.String("local_addr", localAddr),
							zap.String("process_id", pid),
							zap.String("process_name", processName),
							zap.String("platform", runtime.GOOS),
							zap.Bool("is_local", true))
					} else if workerpool.IsLocalConnection(remoteAddr) {
						global.LOG.Info("本地连接断开",
							zap.String("remote_addr", remoteAddr),
							zap.String("local_addr", localAddr),
							zap.Bool("is_local", true))
					} else {
						global.LOG.Info("远程连接断开",
							zap.String("remote_addr", remoteAddr),
							zap.String("local_addr", localAddr),
							zap.Bool("is_local", false))
					}
				})
			// 🚨 使用工作池进行进程检测 - 可随时删除的代码块结束

			info.Conn.Close()
			atomic.AddInt64(&l.connectionCount, -1)
			global.LOG.Info("连接已移除", zap.String("addr", remoteAddr), zap.Int64("total", atomic.LoadInt64(&l.connectionCount)))
		}
	}
}

func (l *TCPListener) getConnection(remoteAddr string) (net.Conn, bool) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil {
			info.LastActivity = time.Now()
			return info.Conn, true
		}
	}
	return nil, false
}

func (l *TCPListener) setMetadata(remoteAddr string, metadata *tlv.METADATA) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil {
			info.Metadata = metadata
		}
	}
}

func (l *TCPListener) getMetadata(remoteAddr string) (*tlv.METADATA, bool) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil && info.Metadata != nil {
			return info.Metadata, true
		}
	}
	return nil, false
}

func (l *TCPListener) setHandler(remoteAddr string, handler *TLVMessageHandler) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil {
			info.Handler = handler
		}
	}
}

func (l *TCPListener) getHandler(remoteAddr string) (*TLVMessageHandler, bool) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil && info.Handler != nil {
			return info.Handler, true
		}
	}
	return nil, false
}

func (l *TCPListener) setClient(remoteAddr string, client *sys.Client) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil {
			info.Client = client
		}
	}
}

func (l *TCPListener) getClient(remoteAddr string) (*sys.Client, bool) {
	if connInfo, ok := l.connections.Load(remoteAddr); ok {
		if info := connInfo.(*ConnectionInfo); info != nil && info.Client != nil {
			return info.Client, true
		}
	}
	return nil, false
}

// 启动定期清理机制
func (l *TCPListener) startCleanup() {
	l.cleanupTicker = time.NewTicker(30 * time.Second)

	go func() {
		defer l.cleanupTicker.Stop()
		for {
			select {
			case <-l.cleanupTicker.C:
				l.cleanupInactiveConnections()
			case <-l.shutdownCh:
				return
			}
		}
	}()
}

// 清理非活跃连接
func (l *TCPListener) cleanupInactiveConnections() {
	now := time.Now()
	timeout := 5 * time.Minute

	l.connections.Range(func(key, value interface{}) bool {
		remoteAddr := key.(string)
		connInfo := value.(*ConnectionInfo)

		if now.Sub(connInfo.LastActivity) > timeout {
			global.LOG.Info("清理超时连接",
				zap.String("addr", remoteAddr),
				zap.Duration("inactive", now.Sub(connInfo.LastActivity)))
			l.removeConnection(remoteAddr)
		}
		return true
	})
}

// 停止清理机制
func (l *TCPListener) stopCleanup() {
	if l.cleanupTicker != nil {
		l.cleanupTicker.Stop()
	}
	close(l.shutdownCh)
}

// initRSAKeys 初始化RSA密钥对
func (l *TCPListener) initRSAKeys() error {
	//privateKey, err := utils.GenerateRSAKeyPair(l.Key, l.Salt, 2048)
	//FOR TEST
	privateKey, err := utils.DecodePrivateKeyFromPEM(`******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)

	if err != nil {
		return fmt.Errorf("生成RSA密钥对失败: %v", err)
	}

	l.rsaPrivateKey = privateKey
	l.rsaPublicKey = &privateKey.PublicKey

	global.LOG.Info("RSA密钥对初始化成功", zap.Uint("listenerID", l.ID))
	return nil
}

func (l *TCPListener) handleConnections() {
	for {
		// 接受新连接
		conn, err := l.Listener.Accept()
		if err != nil {
			// 如果监听器已关闭，退出循环
			if opErr, ok := err.(*net.OpError); ok && opErr.Err.Error() == "use of closed network connection" {
				break
			}
			global.LOG.Error(fmt.Sprintf("接受连接失败: %s", err.Error()))
			continue
		}

		// 🚀 使用网络工作池处理新连接，避免无限制创建goroutine
		connCopy := conn // 避免闭包问题
		task := workerpool.NewNetworkTask("tcp_handle_connection", func() error {
			l.handleConnection(connCopy)
			return nil
		})

		if err := workerpool.SubmitNetworkTask(task); err != nil {
			global.LOG.Error("提交TCP连接处理任务失败",
				zap.String("remoteAddr", conn.RemoteAddr().String()),
				zap.Error(err))
			conn.Close() // 如果无法提交任务，关闭连接
		}
	}
}

// handleConnection 处理单个连接
func (l *TCPListener) handleConnection(conn net.Conn) {
	defer conn.Close()

	remoteAddr := conn.RemoteAddr().String()
	startTime := time.Now()
	global.LOG.Debug("新连接建立", zap.String("remoteAddr", remoteAddr))

	// 增加连接统计
	l.incrementConnection()
	defer l.decrementConnection()

	// 在函数结束时记录连接持续时间
	defer func() {
		duration := time.Since(startTime)
		global.LOG.Info("连接处理结束",
			zap.String("remoteAddr", remoteAddr),
			zap.Duration("duration", duration))
	}()

	// 检测是否为HTTP请求
	reader := bufio.NewReader(conn)
	firstLine, err := reader.Peek(10)
	if err == nil && (strings.HasPrefix(string(firstLine), "GET ") || strings.HasPrefix(string(firstLine), "POST ") || strings.HasPrefix(string(firstLine), "DEL ") || strings.HasPrefix(string(firstLine), "PAT ") || strings.HasPrefix(string(firstLine), "HEA ") || strings.HasPrefix(string(firstLine), "TRA ") || strings.HasPrefix(string(firstLine), "PUT ")) {
		// 转发到HTTP处理器
		global.LOG.Info("检测到HTTP请求，转发到HTTP处理器",
			zap.String("addr", remoteAddr),
			zap.String("firstLine", string(firstLine)))
		l.handleHTTPRequest(conn, reader)
		global.LOG.Info("HTTP请求处理完成，连接将关闭", zap.String("addr", remoteAddr))
		return
	}

	// 添加连接到连接列表
	l.addConnection(remoteAddr, conn)
	// 🔒 持续读取数据 - 添加健壮性保护
	for {
		var shouldBreak bool

		// 🔧 添加recover保护，防止单个数据包处理错误导致连接断开
		func() {
			defer func() {
				if r := recover(); r != nil {
					global.LOG.Error("处理数据包时发生panic，已恢复",
						zap.String("remoteAddr", remoteAddr),
						zap.Any("panic", r))
					shouldBreak = true
				}
			}()

			fullBytes, err := l.readPacket(reader)
			if err != nil {
				// 🔧 精细化错误处理：区分致命错误和可恢复错误
				if err == io.EOF || strings.Contains(err.Error(), "EOF") {
					global.LOG.Info("连接正常关闭",
						zap.String("remoteAddr", remoteAddr),
						zap.String("reason", "EOF"))
					shouldBreak = true
					return
				} else if strings.Contains(err.Error(), "错误的HeaderType") {
					// HeaderType错误：记录警告但不断开连接，可能是客户端版本不匹配
					global.LOG.Warn("收到无效的HeaderType，跳过此数据包",
						zap.String("remoteAddr", remoteAddr),
						zap.Error(err))
					return // 跳过此数据包，继续处理下一个
				} else if strings.Contains(err.Error(), "数据包长度异常") || strings.Contains(err.Error(), "数据包过大") {
					// 数据包格式错误：记录警告但不断开连接
					global.LOG.Warn("收到格式异常的数据包，跳过此数据包",
						zap.String("remoteAddr", remoteAddr),
						zap.Error(err))
					return // 跳过此数据包，继续处理下一个
				} else {
					// 其他严重错误：断开连接
					global.LOG.Info("读取PacketBytes失败，连接将断开",
						zap.String("remoteAddr", remoteAddr),
						zap.Error(err),
						zap.String("reason", "read_packet_error"))
					shouldBreak = true
					return
				}
			}

			// 添加接收数据统计
			l.addBytesReceived(int64(len(fullBytes)))
			l.addDataTransferred(len(fullBytes))

			if err = l.processNetwork(remoteAddr, fullBytes); err != nil {
				global.LOG.Info("❌ 分析PacketBytes失败，连接将断开",
					zap.String("remoteAddr", remoteAddr),
					zap.Error(err),
					zap.Int("dataSize", len(fullBytes)),
					zap.String("reason", "process_network_error"))
				l.incrementError()
				shouldBreak = true
				return
			} else {
				global.LOG.Debug("✅ PacketBytes处理成功",
					zap.String("remoteAddr", remoteAddr),
					zap.Int("dataSize", len(fullBytes)))
			}
		}()

		// 如果发生了错误，退出循环
		if shouldBreak {
			break
		}
	}

	// 清理连接
	err = l.CloseConnection(remoteAddr)
	if err != nil {
		global.LOG.Error("关闭连接错误", zap.String("remoteAddr", remoteAddr), zap.Error(err))
		return
	}
}

func (l *TCPListener) readPacket(reader io.Reader) ([]byte, error) {
	// 🔒 健壮性修复：添加defer recover防止panic导致整个服务崩溃
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("readPacket发生panic，已恢复", zap.Any("panic", r))
		}
	}()

	// 🚨 修复阻塞问题：为连接设置读取超时
	if conn, ok := reader.(net.Conn); ok {
		conn.SetReadDeadline(time.Now().Add(30 * time.Second))
		defer conn.SetReadDeadline(time.Time{}) // 清除超时设置
	}

	// 1. 读取8字节前缀
	prefixBytes := make([]byte, tlv.PrefixSize)
	if _, err := io.ReadFull(reader, prefixBytes); err != nil {
		// 🔧 改进EOF错误处理：处理所有类型的EOF错误
		if err == io.EOF || err == io.ErrUnexpectedEOF || strings.Contains(err.Error(), "EOF") {
			return nil, io.EOF
		}
		// 🔧 处理超时错误
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			return nil, fmt.Errorf("读取数据包前缀超时: %w", err)
		}
		return nil, fmt.Errorf("failed to read packet prefix: %w", err)
	}

	// 2. 读取16字节Header
	headerBytes := make([]byte, tlv.HeaderSize)
	if _, err := io.ReadFull(reader, headerBytes); err != nil {
		// 🔧 改进EOF错误处理：处理所有类型的EOF错误
		if err == io.EOF || err == io.ErrUnexpectedEOF || strings.Contains(err.Error(), "EOF") {
			return nil, io.EOF
		}
		// 🔧 处理超时错误
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			return nil, fmt.Errorf("读取数据包头超时: %w", err)
		}
		return nil, fmt.Errorf("failed to read packet header: %w", err)
	}

	// 🔒 健壮性检查：确保headerBytes有足够长度
	if len(headerBytes) < tlv.HeaderSize {
		return nil, fmt.Errorf("header长度不足: 期望%d字节，实际%d字节", tlv.HeaderSize, len(headerBytes))
	}

	// 3. 验证Header类型
	HeaderType := headerBytes[0]
	if HeaderType > 12 {
		return nil, fmt.Errorf("错误的HeaderType: %v", HeaderType)
	}

	// 🔒 健壮性检查：确保可以安全访问length字段
	if len(headerBytes) < 16 {
		return nil, fmt.Errorf("header数据不完整，无法解析length字段: 长度%d < 16", len(headerBytes))
	}

	// 4. 解析包头获取数据长度
	length := binary.BigEndian.Uint32(headerBytes[12:16])

	// 🔒 数据包大小验证
	if length > 1024*1024*1024 {
		return nil, fmt.Errorf("数据包过大: %d bytes，判定为某处读入空指针，丢弃", length)
	}

	// 🔒 最小长度验证
	if length < tlv.HeaderSize {
		return nil, fmt.Errorf("数据包长度异常: %d bytes < 最小长度%d", length, tlv.HeaderSize)
	}

	// 5. 读取完整数据包
	packetBytesWithPrefix := make([]byte, tlv.PrefixSize+length)
	copy(packetBytesWithPrefix[:tlv.PrefixSize], prefixBytes)

	// 🔒 安全复制：确保不会越界
	headerCopyLen := tlv.HeaderSize
	if len(headerBytes) < headerCopyLen {
		headerCopyLen = len(headerBytes)
	}
	copy(packetBytesWithPrefix[tlv.PrefixSize:tlv.PrefixSize+headerCopyLen], headerBytes[:headerCopyLen])

	// 6. 读取剩余数据
	remainingDataLen := int(length) - tlv.HeaderSize
	if remainingDataLen > 0 {
		if _, err := io.ReadFull(reader, packetBytesWithPrefix[tlv.PrefixSize+tlv.HeaderSize:]); err != nil {
			// 🔧 改进EOF错误处理
			if err == io.EOF || err == io.ErrUnexpectedEOF || strings.Contains(err.Error(), "EOF") {
				return nil, io.EOF
			}
			// 🔧 处理超时错误
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				return nil, fmt.Errorf("读取数据包体超时: %w", err)
			}
			return nil, fmt.Errorf("读取数据包失败: %v", err)
		}
	}

	return packetBytesWithPrefix, nil
}

func (l *TCPListener) processNetwork(remoteAddr string, data []byte) error {
	// 检查是否为注册请求（未加密）
	if l.isRegistrationPacket(data) {
		return l.handleRegistrationRequest(remoteAddr, data)
	}

	// 检查是否已建立元数据
	_, hasMetadata := l.getMetadata(remoteAddr)

	if !hasMetadata {
		return fmt.Errorf("连接 %s 尚未完成注册", remoteAddr)
	}

	// 获取消息处理器
	messageHandler, hasHandler := l.getHandler(remoteAddr)

	if !hasHandler {
		return fmt.Errorf("连接 %s 的消息处理器不存在", remoteAddr)
	}

	// 处理加密的TLV数据包
	packet, complete, err := messageHandler.ProcessIncomingPacket(data)
	if err != nil {
		log.Printf("处理数据包失败: %v", err)
		return fmt.Errorf("处理数据包失败: %v", err)
	}

	if !complete {
		return nil // 分片数据包，等待更多分片
	}

	if err = l.ProcessPacket(remoteAddr, packet); err != nil {
		global.LOG.Error("处理客户端数据包失败", zap.String("remoteAddr", remoteAddr), zap.Error(err))
		return fmt.Errorf("处理客户端数据包失败: %v", err)
	}

	return nil
}

func (l *TCPListener) ProcessPacket(remoteAddr string, packet *tlv.Packet) error {

	// 获取元数据
	metadata, hasMetadata := l.getMetadata(remoteAddr)

	// 如果有元数据，确保数据包已解密
	if !hasMetadata {
		global.LOG.Info(fmt.Sprintf("【ProcessPacket】协商的会话密钥: AES[%x...] HMAC[%x...]",
			metadata.EncryptionKey[:8],
			metadata.HmacKey[:8]))
		return fmt.Errorf("不存在 %s ", remoteAddr)
	}

	switch packet.Header.Type {
	case tlv.Heartbeat:
		return l.handleHeartbeatPacket(remoteAddr, packet)
	case tlv.RunCommand:
		return l.handleCommandOutput(remoteAddr, packet)
	case tlv.File:
		return l.handleFilePacket(remoteAddr, packet)
	case tlv.Dir:
		return l.handleDirPacket(remoteAddr, packet)
	case tlv.Process:
		return l.handleProcessPacket(remoteAddr, packet)
	case tlv.Screenshot:
		return l.handleScreenshotPacket(remoteAddr, packet)
	case tlv.Network:
		return l.handleNetworkPacket(remoteAddr, packet)
	case tlv.Proxy:
		return l.handleProxyPacket(remoteAddr, packet)
	default:
		global.LOG.Info("收到未知类型数据包", zap.String("remoteAddr", remoteAddr), zap.Uint8("type", packet.Header.Type))
		return nil
	}
}

// isRegistrationPacket 检查是否为注册数据包
func (l *TCPListener) isRegistrationPacket(data []byte) bool {
	if len(data) < 24 { // 8字节前缀 + 16字节Header
		global.LOG.Debug("数据包长度不足", zap.Int("length", len(data)))
		return false
	}

	// 跳过8字节前缀，检查Header
	headerData := data[8:24]
	if len(headerData) < 16 {
		global.LOG.Debug("Header数据长度不足", zap.Int("headerLength", len(headerData)))
		return false
	}

	// 检查Type和Code字段
	taskType := headerData[0]
	taskCode := headerData[1]

	global.LOG.Debug("检查注册数据包",
		zap.Uint8("taskType", taskType),
		zap.Uint8("taskCode", taskCode),
		zap.Uint8("expectedType", tlv.Registration),
		zap.Uint8("expectedCode", tlv.RegRequest))

	isRegistration := taskType == tlv.Registration && taskCode == tlv.RegRequest
	global.LOG.Debug("注册数据包检查结果", zap.Bool("isRegistration", isRegistration))

	return isRegistration
}

// handleRegistrationRequest 处理注册请求
func (l *TCPListener) handleRegistrationRequest(remoteAddr string, data []byte) error {
	// 解析注册数据包
	var packet tlv.Packet
	if err := packet.DeserializePacket(data); err != nil {
		return fmt.Errorf("解析注册数据包失败: %v", err)
	}

	if packet.Header.Type != tlv.Registration || packet.Header.Code != tlv.RegRequest {
		return fmt.Errorf("无效的注册请求")
	}

	// 解析混合加密数据：RSA加密的AES密钥 + "|" + AES加密的元数据
	encryptedData := string(packet.PacketData.Data)
	parts := strings.Split(encryptedData, "|")
	if len(parts) != 2 {
		return fmt.Errorf("无效的加密数据格式")
	}

	// 使用RSA私钥解密AES密钥
	decryptedAESKey, err := utils.DecryptOAEP(l.rsaPrivateKey, parts[0])
	if err != nil {
		return fmt.Errorf("解密AES密钥失败: %v", err)
	}

	// 解码AES密钥
	aesKey, err := base64.StdEncoding.DecodeString(decryptedAESKey)
	if err != nil {
		return fmt.Errorf("解码AES密钥失败: %v", err)
	}

	// 解码AES加密的元数据
	encryptedMetadata, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return fmt.Errorf("解码加密元数据失败: %v", err)
	}

	// 使用AES解密元数据
	decodedMetadata, err := utils.DecryptAES(encryptedMetadata, aesKey)
	if err != nil {
		return fmt.Errorf("AES解密元数据失败: %v", err)
	}

	// 反序列化客户端元数据
	var clientMetadata tlv.METADATA
	if err := json.Unmarshal(decodedMetadata, &clientMetadata); err != nil {
		log.Printf("反序列化客户端元数据失败: %v", err)
		return err
	}

	// 提取会话密钥
	encryptionKey := clientMetadata.EncryptionKey
	hmacKey := clientMetadata.HmacKey

	if len(encryptionKey) != 32 || len(hmacKey) != 32 {
		return fmt.Errorf("无效的会话密钥长度: AES[%d] HMAC[%d]", len(encryptionKey), len(hmacKey))
	}

	global.LOG.Info(fmt.Sprintf("注册成功！协商的会话密钥: AES[%x...] HMAC[%x...]",
		encryptionKey[:8],
		hmacKey[:8]))

	// 验证HMAC
	verifyMac := hmac.New(sha256.New, hmacKey)
	verifyMac.Write(packet.PacketData.IV[:])
	verifyMac.Write(packet.PacketData.Data)
	if !hmac.Equal(packet.PacketData.Checksum[:], verifyMac.Sum(nil)) {
		return fmt.Errorf("HMAC验证失败")
	}

	shellType := clientMetadata.ShellType
	Username := clientMetadata.Username
	Hostname := clientMetadata.Hostname
	OS := clientMetadata.OS
	Architecture := clientMetadata.Architecture

	global.LOG.Info(fmt.Sprintf("新客户端的MetaData为shellType: %v, Username: %v,Hostname: %v, OS: %v, Architecture: %v", shellType, Username, Hostname, OS, Architecture))

	// 使用客户端发送的完整元数据，并更新密钥
	clientMetadata.EncryptionKey = encryptionKey
	clientMetadata.HmacKey = hmacKey
	Metadata := &clientMetadata

	// 保存元数据
	l.setMetadata(remoteAddr, Metadata)
	// 创建消息处理器
	handler := NewTLVMessageHandler(Metadata)
	l.setHandler(remoteAddr, handler)

	// 发送注册响应
	return l.sendRegistrationResponse(remoteAddr, packet.Header.Label, Metadata)
}

// sendRegistrationResponse 发送注册响应
func (l *TCPListener) sendRegistrationResponse(remoteAddr string, label uint32, Metadata *tlv.METADATA) error {
	conn, exists := l.getConnection(remoteAddr)
	if !exists {
		return fmt.Errorf("连接不存在: %s", remoteAddr)
	}
	// 创建注册响应
	responseHeader := &tlv.Header{
		Type:  tlv.Registration,
		Code:  tlv.RegResponse,
		Label: label,
	}
	responseData := &tlv.PacketData{
		Data: []byte("OK"),
	}
	responsePacket := &tlv.Packet{
		Header:     responseHeader,
		PacketData: responseData,
	}
	// 加密响应
	if err := responsePacket.EncryptPacket(Metadata); err != nil {
		return fmt.Errorf("加密注册响应失败: %v", err)
	}

	// 发送响应
	responseBytes := responsePacket.Serialize()
	if _, err := conn.Write(responseBytes); err != nil {
		return fmt.Errorf("发送注册响应失败: %v", err)
	}

	client := &sys.Client{
		ListenerID:   l.ID,
		ListenerType: "tcp",
		OS:           Metadata.OS,
		SessionType:  Metadata.ShellType,
		RemoteAddr:   l.RemoteConnectAddr,
		ConnectedAt:  time.Now(),
		LastActiveAt: time.Now(),
		Status:       1,
		Remark:       "",
		TimeOutCount: 0,
	}
	l.setClient(remoteAddr, client)

	// 获取消息处理器
	messageHandler, hasHandler := l.getHandler(remoteAddr)

	if !hasHandler {
		return fmt.Errorf("消息处理器不存在: %s", remoteAddr)
	}

	// 使用心跳管理器启动心跳检测
	l.heartbeatManager.StartHeartbeatForClient(remoteAddr, conn, Metadata, messageHandler)

	// 🚀 使用新的注册方法，保存完整的METADATA信息
	clientReg, err := clientmgr.GlobalClientManager.RegisterClientWithMetadata(l.ID, "tcp", remoteAddr, Metadata)
	if err != nil {
		global.LOG.Error("注册客户端失败", zap.Error(err))
	} else {
		global.LOG.Info("TLV客户端注册成功",
			zap.String("remoteAddr", remoteAddr),
			zap.Uint("clientID", clientReg.ID),
			zap.String("OS", Metadata.OS),
			zap.String("hostname", Metadata.Hostname),
			// zap.String("kernelVersion", Metadata.System.KernelVersion)
		)
	}

	return nil
}

// 旧的心跳检测函数已被心跳管理器替代

func (l *TCPListener) handleHeartbeatPacket(remoteAddr string, packet *tlv.Packet) error {
	switch packet.Header.Code {
	case tlv.PONG:
		// 处理客户端对服务器PING的响应（可能包含结构化数据）
		global.LOG.Debug("收到心跳响应(PONG)", zap.String("remoteAddr", remoteAddr))

		// 尝试解析结构化心跳数据
		if len(packet.PacketData.Data) > 0 {
			l.parseClientHeartbeatData(remoteAddr, packet.PacketData.Data)
		}

		// 通知心跳管理器更新客户端活动时间
		l.heartbeatManager.UpdateClientActivity(remoteAddr)

		client, exists := l.getClient(remoteAddr)
		if !exists {
			global.LOG.Error("此Conn下的Client不存在", zap.String("remoteAddr", remoteAddr))
			return nil
		}
		// 重置内存中的超时计数
		client.TimeOutCount = 0
		// 更新最后活动时间
		client.LastActiveAt = time.Now()
		// 更新客户端信息
		l.setClient(remoteAddr, client)

		// 🚀 使用数据库连接池异步更新客户端心跳状态
		dbpool.ExecuteDBOperationAsyncAndWait("client_heartbeat_update", func(db *gorm.DB) error {
			return db.Model(&sys.Client{}).
				Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).
				Updates(map[string]interface{}{
					"last_active_at": time.Now(),
					"status":         1, // 确保状态为在线
				}).Error
		})

	case tlv.PING:
		// 处理客户端主动发送的PING包（可能包含结构化数据）
		global.LOG.Debug("收到客户端心跳请求(PING)", zap.String("remoteAddr", remoteAddr))

		// 尝试解析结构化心跳数据
		if len(packet.PacketData.Data) > 0 {
			l.parseClientHeartbeatData(remoteAddr, packet.PacketData.Data)
		}

		// 更新客户端活动时间
		l.heartbeatManager.UpdateClientActivity(remoteAddr)

		// 获取消息处理器并发送结构化PONG响应
		messageHandler, hasHandler := l.getHandler(remoteAddr)
		conn, hasConn := l.getConnection(remoteAddr)

		if !hasHandler || !hasConn {
			global.LOG.Error("无法发送PONG响应，连接或处理器不存在", zap.String("remoteAddr", remoteAddr))
			return nil
		}

		// 创建结构化PONG响应
		if err := l.sendStructuredPongResponse(remoteAddr, messageHandler, conn, packet.Header.Label); err != nil {
			global.LOG.Error("发送结构化PONG响应失败", zap.String("remoteAddr", remoteAddr), zap.Error(err))
			return nil
		}

		global.LOG.Debug("已发送结构化PONG响应", zap.String("remoteAddr", remoteAddr))

		// 🚀 使用数据库连接池异步更新客户端心跳状态
		dbpool.ExecuteDBOperationAsyncAndWait("client_heartbeat_timeout_update", func(db *gorm.DB) error {
			return db.Model(&sys.Client{}).
				Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).
				Updates(map[string]interface{}{
					"last_active_at": time.Now(),
					"status":         1, // 确保状态为在线
				}).Error
		})
	}
	return nil
}

// handleCommandOutput 处理命令输出
func (l *TCPListener) handleCommandOutput(remoteAddr string, packet *tlv.Packet) error {
	if packet.Header.Code == tlv.GetTerminalList {
		// 处理获取终端列表的响应
		if packet.PacketData == nil || len(packet.PacketData.Data) == 0 {
			global.LOG.Error("终端列表响应数据为空", zap.String("remoteAddr", remoteAddr))
			return nil
		}

		// 获取客户端信息
		var client sys.Client
		err := dbpool.ExecuteDBOperationAsyncAndWait("client_lookup_for_terminal_list", func(db *gorm.DB) error {
			return db.Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).First(&client).Error
		})
		if err != nil {
			global.LOG.Error("查找客户端失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return nil
		}

		// 解析终端列表响应
		terminalListJSON := string(packet.PacketData.Data)
		global.LOG.Info("收到终端列表响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Uint("clientID", client.ID),
			zap.String("terminalList", terminalListJSON))

		// 解析JSON数据
		var terminalData map[string]interface{}
		if err := json.Unmarshal(packet.PacketData.Data, &terminalData); err != nil {
			global.LOG.Error("解析终端列表JSON失败", zap.Error(err))
			return nil
		}

		// 查找对应的终端任务
		var terminalTask task.TerminalTask
		if err := global.DB.Where("client_id = ? AND task_type = ? AND status = ?",
			client.ID, task.TerminalTaskTypeGetList, task.TaskStatusPending).
			Order("created_at DESC").First(&terminalTask).Error; err == nil {

			// 使用StoreResponse存储获取终端列表响应，前端可以通过WaitForResponse获取
			getTerminalListResponse := map[string]interface{}{
				"action":    "get_terminal_list_response",
				"success":   true,
				"terminals": terminalData["terminals"], // 直接使用解析后的终端列表
				"error":     "",
			}

			// 使用任务ID作为taskID来存储响应
			cache.ResponseMgr.StoreResponse(terminalTask.ID, "get_terminal_list", getTerminalListResponse, "")

			// 更新任务状态
			utils.UpdateTerminalTaskStatus(terminalTask.ID, task.TaskStatusCompleted, "")
		}

		return nil
	} else if packet.Header.Code == tlv.ExecCronCommandOutput {
		// 处理定时任务命令执行结果
		return l.handleCronCommandOutput(remoteAddr, packet)
	} else if packet.Header.Code == tlv.ExecCronScriptOutput {
		// 处理定时任务脚本执行结果（复用命令处理逻辑，因为响应格式相同）
		return l.handleCronScriptOutput(remoteAddr, packet)
	} else if packet.Header.Code == tlv.ExecOutput {
		// 确保数据已经被解密
		if packet.PacketData == nil || len(packet.PacketData.Data) == 0 {
			global.LOG.Error("命令输出数据为空", zap.String("remoteAddr", remoteAddr))
			return nil
		}

		// 尝试解析为CommandResponse结构体
		var cmdResponse tlv.CommandResponse
		if err := cmdResponse.Unmarshal(packet.PacketData.Data); err != nil {
			// 如果解析失败，按旧格式处理（向后兼容）
			output := string(packet.PacketData.Data)
			global.LOG.Info("收到命令输出(旧格式)", zap.String("remoteAddr", remoteAddr), zap.String("output", output))

			// 🚀 查找客户端记录
			var client sys.Client
			err := dbpool.ExecuteDBOperationAsyncAndWait("client_lookup_for_status", func(db *gorm.DB) error {
				return db.Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).First(&client).Error
			})
			if err == nil {
				// 更新客户端状态
				if err = clientmgr.GlobalClientManager.UpdateClientStatus(client.ID, 1); err != nil {
					global.LOG.Error("更新客户端状态失败", zap.Error(err))
				}
				cache.ResponseMgr.StoreCommandOutput(client.ID, output, "command_output")
			}
		} else {
			// 新格式：CommandResponse结构体
			global.LOG.Info("收到命令输出(新格式)",
				zap.String("remoteAddr", remoteAddr),
				zap.Uint32("terminalID", cmdResponse.TerminalID),
				zap.String("output", cmdResponse.Output),
				zap.Bool("success", cmdResponse.Success))

			// 🚀 查找客户端记录
			var client sys.Client
			err := dbpool.ExecuteDBOperationAsyncAndWait("client_lookup_for_status", func(db *gorm.DB) error {
				return db.Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).First(&client).Error
			})
			if err == nil {
				// 更新客户端状态
				if err = clientmgr.GlobalClientManager.UpdateClientStatus(client.ID, 1); err != nil {
					global.LOG.Error("更新客户端状态失败", zap.Error(err))
				}

				// 存储命令输出，包含终端ID信息
				outputType := fmt.Sprintf("command_output_terminal_%d", cmdResponse.TerminalID)
				cache.ResponseMgr.StoreCommandOutput(client.ID, cmdResponse.Output, outputType)

				// 检查是否是创建终端的响应
				if strings.Contains(cmdResponse.Output, "创建成功") && cmdResponse.TerminalID >= 1000 {
					// 查找对应的终端任务
					var terminalTask task.TerminalTask
					if err := global.DB.Where("client_id = ? AND task_type = ? AND status = ?",
						client.ID, task.TerminalTaskTypeCreate, task.TaskStatusPending).
						Order("created_at DESC").First(&terminalTask).Error; err == nil {

						// 使用StoreResponse存储创建终端响应，前端可以通过WaitForResponse获取
						createTerminalResponse := map[string]interface{}{
							"action":      "create_terminal_response",
							"success":     cmdResponse.Success,
							"terminal_id": cmdResponse.TerminalID,
							"error":       cmdResponse.Error,
						}

						// 使用任务ID作为taskID来存储响应
						cache.ResponseMgr.StoreResponse(terminalTask.ID, "create_terminal", createTerminalResponse, cmdResponse.Error)

						// 更新任务状态
						utils.UpdateTerminalTaskStatus(terminalTask.ID, task.TaskStatusCompleted, "")
					}
				}

				// 检查是否是关闭终端的响应
				if strings.Contains(cmdResponse.Output, "关闭成功") && cmdResponse.TerminalID >= 1000 {
					// 使用StoreResponse存储关闭终端响应，前端可以通过WaitForResponse获取
					closeTerminalResponse := map[string]interface{}{
						"action":      "close_terminal_response",
						"success":     cmdResponse.Success,
						"terminal_id": cmdResponse.TerminalID,
						"error":       cmdResponse.Error,
					}
					// 使用客户端ID+终端ID作为taskID来存储响应，避免与创建终端冲突
					taskID := uint64(client.ID*10000 + uint(cmdResponse.TerminalID))
					cache.ResponseMgr.StoreResponse(taskID, "close_terminal", closeTerminalResponse, cmdResponse.Error)
				}
			}
		}
	}
	return nil
}

// SendCommand 向指定连接发送命令（兼容旧接口，默认使用主终端）
func (l *TCPListener) SendCommand(remoteAddr string, command string) error {
	return l.SendCommandToTerminal(remoteAddr, command, 0) // 默认主终端ID=0
}

// SendCommandToTerminal 向指定连接的指定终端发送命令
func (l *TCPListener) SendCommandToTerminal(remoteAddr string, command string, terminalID uint32) error {
	// 创建命令请求结构体
	cmdRequest := &tlv.CommandRequest{
		TerminalID: terminalID,
		Command:    command,
		Type:       0, // 预留字段，暂时为0
	}

	// 序列化命令请求
	cmdData, err := cmdRequest.Marshal()
	if err != nil {
		return fmt.Errorf("序列化命令请求失败: %v", err)
	}

	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.RunCommand,
			Code: tlv.ExecInput,
		},
		PacketData: &tlv.PacketData{
			Data: cmdData,
		},
	}

	if err := l.SendPacket(remoteAddr, packet); err != nil {
		return err
	}

	return nil
}

// SendCreateTerminalCommand 发送创建终端的TLV命令
func (l *TCPListener) SendCreateTerminalCommand(remoteAddr string, taskID uint64) error {
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.RunCommand,
			Code: tlv.CreateTerminal,
		},
		PacketData: &tlv.PacketData{
			Data: []byte{}, // 创建终端命令不需要额外数据
		},
	}

	if err := l.SendPacket(remoteAddr, packet); err != nil {
		return err
	}

	return nil
}

// SendCloseTerminalCommand 发送关闭终端的TLV命令
func (l *TCPListener) SendCloseTerminalCommand(remoteAddr string, terminalID uint32) error {
	// 将terminalID编码到数据中
	data := make([]byte, 4)
	binary.LittleEndian.PutUint32(data, terminalID)

	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.RunCommand,
			Code: tlv.CloseTerminal,
		},
		PacketData: &tlv.PacketData{
			Data: data, // 包含要关闭的终端ID
		},
	}

	if err := l.SendPacket(remoteAddr, packet); err != nil {
		return err
	}

	return nil
}

func (l *TCPListener) SendResize(remoteAddr string, cols, rows uint16, os string) error {
	l.Mutex.Lock()
	defer l.Mutex.Unlock()
	// 检查连接是否存在
	var Code uint8
	if os == "windows" {
		Code = tlv.WinResize
	} else {
		Code = tlv.UnixResize
	}
	buf := make([]byte, 4)
	binary.BigEndian.PutUint16(buf[0:2], cols)
	binary.BigEndian.PutUint16(buf[2:4], rows)
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.TermResize,
			Code: Code,
		},
		PacketData: &tlv.PacketData{
			Data: buf,
		},
	}
	if err := l.SendPacket(remoteAddr, packet); err != nil {
		return err
	}

	return nil
}

// SendPacket 向指定连接发送TLV数据包
func (l *TCPListener) SendPacket(remoteAddr string, packet *tlv.Packet) error {

	// 检查连接是否存在
	conn, exists := l.getConnection(remoteAddr)
	if !exists {
		return fmt.Errorf("连接 %s 不存在", remoteAddr)
	}

	// 检查元数据和消息处理器是否存在
	_, hasMetadata := l.getMetadata(remoteAddr)
	msgHandler, hasHandler := l.getHandler(remoteAddr)
	if !hasMetadata || !hasHandler {
		return fmt.Errorf("连接 %s 尚未完成注册", remoteAddr)
	}
	Type := packet.Header.Type
	Code := packet.Header.Code
	data := packet.PacketData.Data
	// 创建TLV分片包
	packets, err := msgHandler.CreateFragPacket(data, Type, Code)
	if err != nil {
		return fmt.Errorf("创建命令包失败: %v", err)
	}

	// 发送所有分片包（已经在CreateFragPacket中加密了）
	var totalBytesSent int64
	for _, packet = range packets {
		packetBytes := packet.Serialize()
		n, err := conn.Write(packetBytes)
		if err != nil {
			l.incrementError()
			return fmt.Errorf("发送命令包失败: %v", err)
		}
		totalBytesSent += int64(n)
	}

	// 添加发送统计
	l.addBytesSent(totalBytesSent)

	return nil
}

// CloseConnection 关闭指定的连接
func (l *TCPListener) CloseConnection(remoteAddr string) error {
	// 检查连接是否存在
	conn, exists := l.getConnection(remoteAddr)
	if !exists {
		return fmt.Errorf("连接 %s 不存在", remoteAddr)
	}

	// 关闭连接
	err := conn.Close()
	if err != nil {
		return fmt.Errorf("关闭连接失败: %s", err.Error())
	}

	// 从连接映射中删除
	l.removeConnection(remoteAddr)

	// 更新客户端状态为离线
	global.LOG.Debug("客户端连接已关闭", zap.String("remoteAddr", remoteAddr))

	// 异步更新数据库中的客户端状态，避免阻塞主线程
	go func() {
		// 使用重试机制更新数据库
		var client sys.Client
		var dbErr error
		for retries := 0; retries < 5; retries++ {
			// 🚀 使用数据库连接池查找和更新客户端记录
			dbErr = dbpool.ExecuteDBOperationAsyncAndWait("client_disconnect_update", func(db *gorm.DB) error {
				// 查找客户端记录 - 使用静默查询避免日志输出
				if err := db.Session(&gorm.Session{Logger: db.Logger.LogMode(1)}).Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).First(&client).Error; err != nil {
					return err
				}

				// 更新客户端状态
				return db.Model(&client).Updates(map[string]interface{}{
					"status":         0,
					"last_active_at": time.Now(),
				}).Error
			})

			if dbErr != nil {
				// 如果找不到记录，不需要继续尝试
				if dbErr == gorm.ErrRecordNotFound {
					global.LOG.Debug("客户端记录未找到，可能是临时连接", zap.String("remoteAddr", remoteAddr))
					break
				}
				// 其他错误，等待后重试
				global.LOG.Debug("数据库操作失败，重试中", zap.String("remoteAddr", remoteAddr), zap.Error(dbErr), zap.Int("retry", retries))
				time.Sleep(time.Duration(100*(retries+1)) * time.Millisecond)
				continue
			}

			if dbErr == nil {
				// 更新成功
				break
			}

			// 如果是数据库锁定错误，等待更长时间后重试
			if strings.Contains(dbErr.Error(), "database is locked") {
				time.Sleep(time.Duration(500*(retries+1)) * time.Millisecond)
			} else {
				// 其他错误，等待后重试
				time.Sleep(time.Duration(100*(retries+1)) * time.Millisecond)
			}
		}

		if dbErr != nil && dbErr != gorm.ErrRecordNotFound {
			global.LOG.Error("更新客户端状态失败", zap.String("remoteAddr", remoteAddr), zap.Error(dbErr))
		}
	}()

	return nil
}

// handleHTTPRequest 处理HTTP请求
func (l *TCPListener) handleHTTPRequest(conn net.Conn, reader *bufio.Reader) {
	defer conn.Close()

	// 创建HTTP处理器
	handler := l.httpHandler()
	// 创建一个假的ResponseWriter和Request
	hijacker := newHijackResponseWriter(conn)
	// 使用bufio.Reader创建一个新的请求
	req, err := http.ReadRequest(reader)
	if err != nil {
		global.LOG.Error("解析HTTP请求失败", zap.Error(err))
		return
	}
	// 处理请求
	handler.ServeHTTP(hijacker, req)
	// 确保缓冲区中的所有数据都被写入
	hijacker.bufWriter.Flush()
	global.LOG.Info("HTTP请求处理完成", zap.String("remoteAddr", conn.RemoteAddr().String()))
}

// addJitterToInterval 为心跳间隔添加随机抖动
func (l *TCPListener) addJitterToInterval(baseInterval time.Duration) time.Duration {
	// 添加±20%的随机抖动
	jitterPercent := 0.2
	jitter := time.Duration(float64(baseInterval) * jitterPercent * (rand.Float64()*2 - 1))
	result := baseInterval + jitter

	// 确保结果不小于5秒
	if result < 5*time.Second {
		result = 5 * time.Second
	}

	return result
}

// removeClient 清理客户端相关数据
func (l *TCPListener) removeClient(remoteAddr string) {
	// 停止心跳检测
	l.heartbeatManager.StopHeartbeatForClient(remoteAddr)

	// 删除连接和所有相关数据
	l.removeConnection(remoteAddr)

	global.LOG.Info("客户端数据已清理", zap.String("remoteAddr", remoteAddr))
}

// parseClientHeartbeatData 解析客户端心跳数据
func (l *TCPListener) parseClientHeartbeatData(remoteAddr string, data []byte) {
	// 尝试解析为结构化心跳请求
	var heartbeatReq heartbeat.HeartbeatRequest
	if err := json.Unmarshal(data, &heartbeatReq); err != nil {
		// 如果解析失败，可能是旧版本的简单字符串心跳
		global.LOG.Debug("解析心跳数据失败，可能是简单字符串心跳",
			zap.String("remoteAddr", remoteAddr),
			zap.String("data", string(data)))
		return
	}

	// 记录客户端系统状态
	global.LOG.Debug("收到客户端系统状态",
		zap.String("remoteAddr", remoteAddr),
		zap.Float64("cpuUsage", heartbeatReq.SystemInfo.CPUUsage),
		zap.Float64("memoryUsage", heartbeatReq.SystemInfo.MemoryUsage),
		zap.String("localIP", heartbeatReq.NetworkInfo.LocalIP))

	// 🚨 使用工作池异步存储心跳数据到数据库
	workerpool.SubmitHeartbeatUpdate(remoteAddr, func() error {
		l.storeClientHeartbeatData(remoteAddr, &heartbeatReq)
		return nil
	})
}

// storeClientHeartbeatData 存储客户端心跳数据到数据库
func (l *TCPListener) storeClientHeartbeatData(remoteAddr string, heartbeatReq *heartbeat.HeartbeatRequest) {
	// 🚀 根据远程地址查找客户端
	var client sys.Client
	if err := dbpool.ExecuteDBOperationAsyncAndWait("client_heartbeat_lookup", func(db *gorm.DB) error {
		return db.Where("remote_addr = ? AND status = 1", remoteAddr).First(&client).Error
	}); err != nil {
		global.LOG.Debug("未找到对应的客户端记录",
			zap.String("remoteAddr", remoteAddr),
			zap.Error(err))
		return
	}

	// 更新客户端的心跳数据
	updates := map[string]interface{}{
		"last_active_at": time.Now(),
		"cpu_usage":      heartbeatReq.SystemInfo.CPUUsage,
		"memory_usage":   heartbeatReq.SystemInfo.MemoryUsage,
		"disk_usage":     heartbeatReq.SystemInfo.DiskUsage,
		"uptime":         heartbeatReq.SystemInfo.Uptime,
		"load_avg":       heartbeatReq.SystemInfo.LoadAvg,
		"latency":        heartbeatReq.NetworkInfo.Latency,
		"packet_loss":    heartbeatReq.NetworkInfo.PacketLoss,
		"bandwidth":      heartbeatReq.NetworkInfo.Bandwidth,
	}

	// 如果有本地IP信息，也更新
	if heartbeatReq.NetworkInfo.LocalIP != "" {
		updates["local_ip"] = heartbeatReq.NetworkInfo.LocalIP
	}

	// 如果有公网IP信息，也更新
	if heartbeatReq.NetworkInfo.PublicIP != "" {
		updates["public_ip"] = heartbeatReq.NetworkInfo.PublicIP
	}

	// 🚀 使用数据库连接池异步更新客户端心跳数据
	if err := dbpool.ExecuteDBOperationAsyncAndWait("client_heartbeat_data_update", func(db *gorm.DB) error {
		return db.Model(&client).Updates(updates).Error
	}); err != nil {
		global.LOG.Error("更新客户端心跳数据失败",
			zap.Uint("clientID", client.ID),
			zap.String("remoteAddr", remoteAddr),
			zap.Error(err))
	} else {
		global.LOG.Debug("成功更新客户端心跳数据",
			zap.Uint("clientID", client.ID),
			zap.String("remoteAddr", remoteAddr))
	}
}

// sendStructuredPongResponse 发送结构化PONG响应
func (l *TCPListener) sendStructuredPongResponse(remoteAddr string, messageHandler *TLVMessageHandler, conn net.Conn, sequenceNum uint32) error {
	// 创建结构化心跳响应
	heartbeatResp := l.createHeartbeatResponse(uint64(sequenceNum))

	// 序列化响应数据
	responseData, err := json.Marshal(heartbeatResp)
	if err != nil {
		return fmt.Errorf("序列化心跳响应失败: %v", err)
	}

	// 创建TLV包
	pongPacket, err := messageHandler.CreateAdvancedHeartbeatPacket(responseData, tlv.PONG)
	if err != nil {
		return fmt.Errorf("创建PONG包失败: %v", err)
	}

	// 发送响应
	pongBytes := pongPacket.Serialize()
	if _, err := conn.Write(pongBytes); err != nil {
		return fmt.Errorf("发送PONG响应失败: %v", err)
	}

	return nil
}

// createHeartbeatResponse 创建心跳响应
func (l *TCPListener) createHeartbeatResponse(sequenceNum uint64) *heartbeatResponse.HeartbeatResponse {
	return &heartbeatResponse.HeartbeatResponse{
		ServerID:    global.CONFIG.Server.ID,
		Timestamp:   time.Now(),
		SequenceNum: sequenceNum,
		ServerInfo:  l.getServerStatus(),
		ClientInfo:  l.getClientManagement(),
		Config:      l.getHeartbeatConfig(),
		Type:        tlv.PONG,
		Jitter:      l.generateJitter(),
	}
}

// getServerStatus 获取服务器状态
func (l *TCPListener) getServerStatus() basic.ServerStatus {
	return basic.ServerStatus{
		Status:    1,                            // 1=正常
		Timestamp: time.Now().Unix(),            // 服务器时间戳
		Version:   global.CONFIG.Server.Version, // 从配置获取版本
	}
}

// getClientManagement 获取客户端管理信息
func (l *TCPListener) getClientManagement() basic.ClientManagement {
	return basic.ClientManagement{
		ShouldReconnect: false,
		NewServerAddr:   "",
		ConfigUpdate:    false,
		Commands:        []string{},
	}
}

// 统计方法

// initStats 初始化统计信息
func (l *TCPListener) initStats() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.startTime = time.Now()
	l.stats.lastActivity = time.Now()
}

// incrementConnection 增加连接计数
func (l *TCPListener) incrementConnection() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.totalConnections++
	l.stats.activeConnections++
	l.stats.lastActivity = time.Now()
}

// decrementConnection 减少连接计数
func (l *TCPListener) decrementConnection() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	if l.stats.activeConnections > 0 {
		l.stats.activeConnections--
	}
	l.stats.lastActivity = time.Now()
}

// addDataTransferred 添加传输数据量（基于数据长度）
func (l *TCPListener) addDataTransferred(dataLength int) {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.dataTransferred += int64(dataLength)
	l.stats.lastActivity = time.Now()
}

// addBytesReceived 添加接收字节数
func (l *TCPListener) addBytesReceived(bytes int64) {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.bytesReceived += bytes
	l.stats.packetsReceived++
	l.stats.packetsProcessed++
	l.stats.lastActivity = time.Now()
}

// addBytesSent 添加发送字节数
func (l *TCPListener) addBytesSent(bytes int64) {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.bytesSent += bytes
	l.stats.packetsSent++
	l.stats.lastActivity = time.Now()
}

// incrementError 增加错误计数
func (l *TCPListener) incrementError() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.errorCount++
	l.stats.lastActivity = time.Now()
}

// getStats 获取统计信息
func (l *TCPListener) getStats() *stats.BasicListenerStats {
	l.stats.mutex.RLock()
	defer l.stats.mutex.RUnlock()

	return &stats.BasicListenerStats{
		ListenerID:        l.ID,
		ListenerType:      "tcp",
		Address:           l.LocalListenAddr,
		Active:            l.Status == 1,
		StartTime:         l.stats.startTime,
		TotalConnections:  l.stats.totalConnections,
		ActiveConnections: l.stats.activeConnections,
		DataTransferred:   l.stats.dataTransferred,
		PacketsProcessed:  l.stats.packetsProcessed,
		ErrorCount:        l.stats.errorCount,
		LastActivity:      l.stats.lastActivity,
	}
}

// getHeartbeatConfig 获取心跳配置
func (l *TCPListener) getHeartbeatConfig() basic.HeartbeatConfig {
	return basic.HeartbeatConfig{
		Interval:    l.PingDuration,
		Timeout:     10,
		MaxRetries:  l.MaxTimeoutCount,
		JitterRange: 5000,
	}
}

// generateJitter 生成随机抖动值
func (l *TCPListener) generateJitter() int {
	return rand.IntN(5000)
}

// SendGetTerminalListCommand 发送获取终端列表的TLV命令
func (l *TCPListener) SendGetTerminalListCommand(remoteAddr string, taskID uint64) error {
	// 创建TLV包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.RunCommand,
			Code: tlv.GetTerminalList,
		},
		PacketData: &tlv.PacketData{
			Data: []byte{}, // 获取终端列表命令不需要额外数据
		},
	}

	// 发送命令到客户端
	if err := l.SendPacket(remoteAddr, packet); err != nil {
		return fmt.Errorf("发送获取终端列表命令失败: %v", err)
	}

	global.LOG.Info("发送获取终端列表命令成功",
		zap.String("remoteAddr", remoteAddr),
		zap.Uint64("taskID", taskID))

	return nil
}

// handleCronCommandOutput 处理定时任务命令执行结果
func (l *TCPListener) handleCronCommandOutput(remoteAddr string, packet *tlv.Packet) error {
	if packet.PacketData == nil || len(packet.PacketData.Data) == 0 {
		global.LOG.Error("定时任务命令输出数据为空", zap.String("remoteAddr", remoteAddr))
		return nil
	}

	// 解析命令执行结果
	var cronResponse struct {
		TaskID   uint64 `json:"task_id"`
		Success  bool   `json:"success"`
		Output   string `json:"output"`
		Error    string `json:"error"`
		ExitCode int    `json:"exit_code"`
		Duration int64  `json:"duration"`
	}

	if err := json.Unmarshal(packet.PacketData.Data, &cronResponse); err != nil {
		global.LOG.Error("解析定时任务命令结果失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
		return nil
	}

	global.LOG.Info("收到定时任务命令执行结果",
		zap.String("remoteAddr", remoteAddr),
		zap.Uint64("taskID", cronResponse.TaskID),
		zap.Bool("success", cronResponse.Success),
		zap.String("output", cronResponse.Output),
		zap.String("error", cronResponse.Error),
		zap.Int("exitCode", cronResponse.ExitCode),
		zap.Int64("duration", cronResponse.Duration))

	// 查找客户端记录
	var client sys.Client
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_lookup_for_cron_result", func(db *gorm.DB) error {
		return db.Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).First(&client).Error
	})
	if err != nil {
		global.LOG.Error("查找客户端失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
		return nil
	}

	// 更新客户端状态
	if err = clientmgr.GlobalClientManager.UpdateClientStatus(client.ID, 1); err != nil {
		global.LOG.Error("更新客户端状态失败", zap.Error(err))
	}

	// 将结果存储到缓存中，供定时任务管理器使用
	resultData := map[string]interface{}{
		"task_id":   cronResponse.TaskID,
		"success":   cronResponse.Success,
		"output":    cronResponse.Output,
		"error":     cronResponse.Error,
		"exit_code": cronResponse.ExitCode,
		"duration":  cronResponse.Duration,
	}

	// 存储到响应缓存
	cache.ResponseMgr.StoreResponse(cronResponse.TaskID, "cron_command_result", resultData, cronResponse.Error)

	if cronResponse.Success {
		global.LOG.Info("定时任务命令执行成功",
			zap.Uint64("taskID", cronResponse.TaskID),
			zap.Int64("duration", cronResponse.Duration))
	} else {
		global.LOG.Error("定时任务命令执行失败",
			zap.Uint64("taskID", cronResponse.TaskID),
			zap.String("error", cronResponse.Error))
	}

	return nil
}

// handleCronScriptOutput 处理定时任务脚本执行结果
func (l *TCPListener) handleCronScriptOutput(remoteAddr string, packet *tlv.Packet) error {
	if packet.PacketData == nil || len(packet.PacketData.Data) == 0 {
		global.LOG.Error("定时任务脚本输出数据为空", zap.String("remoteAddr", remoteAddr))
		return nil
	}

	// 解析脚本执行结果
	var cronResponse struct {
		TaskID   uint64 `json:"task_id"`
		Success  bool   `json:"success"`
		Output   string `json:"output"`
		Error    string `json:"error"`
		ExitCode int    `json:"exit_code"`
		Duration int64  `json:"duration"`
	}

	if err := json.Unmarshal(packet.PacketData.Data, &cronResponse); err != nil {
		global.LOG.Error("解析定时任务脚本结果失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
		return nil
	}

	global.LOG.Info("收到定时任务脚本执行结果",
		zap.String("remoteAddr", remoteAddr),
		zap.Uint64("taskID", cronResponse.TaskID),
		zap.Bool("success", cronResponse.Success),
		zap.String("output", cronResponse.Output),
		zap.String("error", cronResponse.Error),
		zap.Int("exitCode", cronResponse.ExitCode),
		zap.Int64("duration", cronResponse.Duration))

	// 查找客户端记录
	var client sys.Client
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_lookup_for_cron_script_result", func(db *gorm.DB) error {
		return db.Where("listener_id = ? AND remote_addr = ?", l.ID, remoteAddr).First(&client).Error
	})
	if err != nil {
		global.LOG.Error("查找客户端失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
		return nil
	}

	// 更新客户端状态
	if err = clientmgr.GlobalClientManager.UpdateClientStatus(client.ID, 1); err != nil {
		global.LOG.Error("更新客户端状态失败", zap.Error(err))
	}

	// 将结果存储到缓存中，供定时任务管理器使用
	resultData := map[string]interface{}{
		"task_id":   cronResponse.TaskID,
		"success":   cronResponse.Success,
		"output":    cronResponse.Output,
		"error":     cronResponse.Error,
		"exit_code": cronResponse.ExitCode,
		"duration":  cronResponse.Duration,
	}

	// 存储到响应缓存
	cache.ResponseMgr.StoreResponse(cronResponse.TaskID, "cron_script_result", resultData, cronResponse.Error)

	if cronResponse.Success {
		global.LOG.Info("定时任务脚本执行成功",
			zap.Uint64("taskID", cronResponse.TaskID),
			zap.Int64("duration", cronResponse.Duration))
	} else {
		global.LOG.Error("定时任务脚本执行失败",
			zap.Uint64("taskID", cronResponse.TaskID),
			zap.String("error", cronResponse.Error))
	}

	return nil
}
