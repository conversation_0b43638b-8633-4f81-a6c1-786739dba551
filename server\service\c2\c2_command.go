package c2

import (
	"errors"
	"server/core/manager/dbpool"
	"server/factory"
	"server/model/sys"
	"server/model/task"
	"time"

	"gorm.io/gorm"
)

type CommandService struct{}

// SendCommand 向客户端发送命令（主终端，ID=0）
func (c *CommandService) SendCommand(id uint, command string) (err error) {
	return c.SendCommandToTerminal(id, command, 0) // 默认发送到主终端
}

// SendCommandToTerminal 向客户端的指定终端发送命令
func (c *CommandService) SendCommandToTerminal(id uint, command string, terminalID uint32) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("command_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return errors.New("客户端不在线")
	}

	// 根据监听器类型发送命令
	return factory.SendCommandToTerminalFactory(client, command, terminalID)
}

// SendCreateTerminalCommand 发送创建终端的TLV命令
func (c *CommandService) SendCreateTerminalCommand(id uint) (taskID uint64, err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("command_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return 0, errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return 0, errors.New("客户端不在线")
	}

	// 创建终端任务记录
	terminalTask := &task.TerminalTask{
		ClientID:  id,
		TaskType:  task.TerminalTaskTypeCreate,
		Status:    task.TaskStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 保存任务到数据库
	if err = dbpool.ExecuteDBOperationAsyncAndWait("terminal_task_create", func(db *gorm.DB) error {
		return db.Create(terminalTask).Error
	}); err != nil {
		return 0, err
	}

	// 根据监听器类型发送创建终端命令
	err = factory.SendCreateTerminalCommandFactory(client, terminalTask.ID)
	if err != nil {
		return 0, err
	}

	return terminalTask.ID, nil
}

// SendCloseTerminalCommand 发送关闭终端的TLV命令
func (c *CommandService) SendCloseTerminalCommand(id uint, terminalID uint32) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("command_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return err
	}

	if client.Status != 1 {
		return errors.New("客户端不在线")
	}

	// 根据监听器类型发送关闭终端命令
	return factory.SendCloseTerminalCommandFactory(client, terminalID)
}

// SendResize 向客户端发出resize命令
func (c *CommandService) SendResize(id uint, cols, rows uint16) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("resize_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return errors.New("客户端不在线")
	}

	return factory.SendResizeFactory(client, cols, rows)
}

// GetTerminalList 发送获取终端列表的TLV命令
func (c *CommandService) GetTerminalList(id uint) (taskID uint64, err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("command_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return 0, errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return 0, errors.New("客户端不在线")
	}

	// 创建终端任务记录
	terminalTask := &task.TerminalTask{
		ClientID:  id,
		TaskType:  task.TerminalTaskTypeGetList,
		Status:    task.TaskStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 保存任务到数据库
	if err = dbpool.ExecuteDBOperationAsyncAndWait("terminal_task_create", func(db *gorm.DB) error {
		return db.Create(terminalTask).Error
	}); err != nil {
		return 0, err
	}

	// 根据监听器类型发送获取终端列表命令
	err = factory.GetTerminalListFactory(client, terminalTask.ID)
	if err != nil {
		return 0, err
	}

	return terminalTask.ID, nil
}
