package task

import (
	"time"
)

// CommandTask 命令执行任务
type CommandTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	ClientID    uint       `json:"client_id"`  // 客户端ID
	TaskType    string     `json:"task_type"`  // 任务类型：execute_command, get_command_history
	Command     string     `json:"command"`    // 执行的命令
	WorkDir     string     `json:"work_dir"`   // 工作目录
	Timeout     int        `json:"timeout"`    // 超时时间（秒）
	Status      string     `json:"status"`     // 状态：pending, running, completed, failed, cancelled
	Output      string     `json:"output"`     // 命令输出
	Error       string     `json:"error"`      // 错误信息
	ExitCode    int        `json:"exit_code"`  // 退出码
	Result      string     `json:"result"`     // 任务结果（JSON格式）
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`   // 开始时间
	CompletedAt *time.Time `json:"completed_at"` // 完成时间
}

func (CommandTask) TableName() string {
	return "command_tasks"
}
