package proc

import (
	"server/model/basic"
)

// ProcessListResponse 进程列表响应
type ProcessListResponse struct {
	TaskID    uint64                    `json:"task_id"`   // 任务ID
	Success   bool                    `json:"success"`   // 操作是否成功
	Processes []basic.ProcessFullInfo `json:"processes"` // 进程列表
	Error     string                  `json:"error"`     // 错误信息
	Count     int                     `json:"count"`     // 进程总数
}

// ProcessKillResponse 终止进程响应
type ProcessKillResponse struct {
	TaskID  uint64   `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	PID     int32  `json:"pid"`     // 进程ID
	Error   string `json:"error"`   // 错误信息
}

// ProcessStartResponse 启动进程响应
type ProcessStartResponse struct {
	TaskID     uint64   `json:"task_id"`    // 任务ID
	Success    bool   `json:"success"`    // 操作是否成功
	PID        int32  `json:"pid"`        // 新进程ID
	Error      string `json:"error"`      // 错误信息
	ExitCode   int    `json:"exit_code"`  // 退出码（仅当wait_for_exit为true时有效）
	Output     string `json:"output"`     // 进程输出（仅当wait_for_exit为true时有效）
	Executable string `json:"executable"` // 实际执行的文件路径
}

// ProcessDetailsResponse 进程详情响应
type ProcessDetailsResponse struct {
	TaskID      uint64                      `json:"task_id"`     // 任务ID
	Success     bool                      `json:"success"`     // 操作是否成功
	Process     *basic.ProcessFullInfo    `json:"process"`     // 进程基本信息
	Modules     []basic.ProcessModule     `json:"modules"`     // 加载的模块
	Connections []basic.ProcessConnection `json:"connections"` // 网络连接
	OpenFiles   []basic.ProcessOpenFile   `json:"open_files"`  // 打开的文件
	Error       string                    `json:"error"`       // 错误信息
}

// ProcessSuspendResponse 挂起进程响应
type ProcessSuspendResponse struct {
	TaskID  uint64   `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	PID     int32  `json:"pid"`     // 进程ID
	Error   string `json:"error"`   // 错误信息
}

// ProcessResumeResponse 恢复进程响应
type ProcessResumeResponse struct {
	TaskID  uint64   `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	PID     int32  `json:"pid"`     // 进程ID
	Error   string `json:"error"`   // 错误信息
}
