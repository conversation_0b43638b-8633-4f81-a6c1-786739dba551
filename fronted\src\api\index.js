/**
 * API接口统一出口
 */

// 导出认证相关接口
import * as authApi from './auth'
// 导出仪表盘相关接口
import * as dashboardApi from './dashboard'
// 导出监听器相关接口
import * as listenerApi from './listener'
// 导出客户端相关接口
import * as clientApi from './client'
// 导出文件管理相关接口
import * as fileApi from './file'
// 导出目录管理相关接口
import * as dirApi from './dir'
// 导出设置相关接口
import * as settingsApi from './settings'

// 统一导出
export {
  authApi,
  dashboardApi,
  listenerApi,
  clientApi,
  fileApi,
  dirApi,
  settingsApi
}