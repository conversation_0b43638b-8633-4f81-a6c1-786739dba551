//go:build darwin
// +build darwin

package common

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/creack/pty"
)

// CommandRequest 多终端命令请求结构体
type CommandRequest struct {
	TerminalID uint32 `json:"terminal_id"` // 终端ID: 0=主终端, 1000+=备用终端
	Command    string `json:"command"`     // 要执行的命令
	Type       uint8  `json:"type"`        // 命令类型（预留字段）
}

// CommandResponse 多终端命令响应结构体
type CommandResponse struct {
	TerminalID uint32 `json:"terminal_id"` // 对应的终端ID
	Output     string `json:"output"`      // 命令输出
	Success    bool   `json:"success"`     // 是否成功执行
	Error      string `json:"error"`       // 错误信息（如果有）
}

func (cm *ConnectionManager) readPTYOutput(ctx context.Context) {
	type readResult struct {
		data []byte
		err  error
	}
	readChan := make(chan readResult)

	go func() {
		for {
			buf := make([]byte, 8192)
			n, err := cm.ptmx.Read(buf)
			if err != nil {
				// 如果读取出错（例如PTY关闭），发送错误并退出goroutine
				readChan <- readResult{err: err}
				return
			}
			if n > 0 {
				// 成功读取，发送数据
				readChan <- readResult{data: buf[:n]}
			}
		}
	}()

	for {
		select {
		case <-ctx.Done():
			return
		case res := <-readChan: // 如果PTY有数据或错误
			if res.err != nil {
				if res.err != io.EOF {
					log.Printf("PTY读取错误: %v", res.err)
				}
				// PTY已经关闭，我们不需要再做任何事，
				// cmd.Wait() goroutine 会捕获到进程退出并调用cancel()，
				// 这里直接返回即可。
				return
			}
			// 成功读取数据，发送到ptyOutputChan
			cm.ptyOutputChan <- res.data
		}
	}
}

func (cm *ConnectionManager) handlePTYInput(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case input := <-cm.ptyInputChan:
			_, err := cm.ptmx.Write(input)
			if err != nil {
				log.Printf("写入PTY失败: %v", err)
				return
			}
		}
	}
}

func (cm *ConnectionManager) startPTY(ctx context.Context) error {
	osName := strings.ToLower(runtime.GOOS)
	var shell string
	switch osName {
	case "windows":
		shell = "cmd.exe"
	case "linux":
		shell = "/bin/bash"
	case "darwin":
		shell = "/bin/zsh"
	default:
		shell = "/bin/sh"
	}

	cm.cmdMutex.Lock()
	defer cm.cmdMutex.Unlock()

	cmd := exec.CommandContext(ctx, shell)
	if osName != "windows" {
		cmd.Env = append(os.Environ(), "TERM=xterm-256color", "LANG=en_US.UTF-8")
	}

	// 启动 PTY
	ptmx, err := pty.StartWithSize(cmd, cm.terminalSize.size)
	if err != nil {
		return fmt.Errorf("启动PTY失败: %v", err)
	}

	cm.cmd = cmd
	cm.ptmx = ptmx

	return nil
}

func (cm *ConnectionManager) sendPTYOutput(ctx context.Context) {
	cm.sendPTYOutputWithTerminalID(ctx, 0) // 主终端ID=0
}

func (cm *ConnectionManager) sendPTYOutputWithTerminalID(ctx context.Context, terminalID uint32) {
	// 使用缓冲区减少小包发送
	buf := bytes.NewBuffer(nil)
	flushTimer := time.NewTicker(1 * time.Millisecond) // 1ms，更快响应
	defer flushTimer.Stop()

	flush := func() {
		if buf.Len() == 0 {
			return
		}
		// 创建CommandResponse结构体 - 主终端ID为0
		cmdResponse := CommandResponse{
			TerminalID: terminalID,
			Output:     buf.String(),
			Success:    true,
			Error:      "",
		}

		// 序列化CommandResponse
		responseData, err := cm.serializer.Serialize(cmdResponse)
		if err != nil {
			log.Printf("序列化CommandResponse失败: %v", err)
			return
		}

		packets, err := cm.CreatePackets(responseData, RunCommand, ExecOutput)
		if err != nil {
			log.Printf("创建输出包失败: %v", err)
			return
		}

		for _, packet := range packets {
			packetBytes := packet.Serialize()
			if _, err = cm.conn.Write(packetBytes); err != nil {
				log.Printf("发送输出失败: %v", err)
				return
			}
		}

		log.Printf("⏱️ 发送主终端输出，数据大小: %d", buf.Len())
		buf.Reset()
	}

	for {
		select {
		case <-ctx.Done():
			flush()
			return

		case <-flushTimer.C:
			flush()

		case output, ok := <-cm.ptyOutputChan:
			if !ok {
				flush()
				return
			}

			buf.Write(output)

			// 🚀 优化：更激进的立即刷新策略
			// 1. 降低缓冲阈值到512字节
			// 2. 检测更多触发字符
			shouldFlush := buf.Len() > 512 ||
				bytes.Contains(output, []byte{'\n'}) ||
				bytes.Contains(output, []byte{'\r'}) ||
				bytes.Contains(output, []byte{'\x1b'}) // ANSI转义序列

			if shouldFlush {
				flush()
			}
		}
	}
}

func (cm *ConnectionManager) handleTermResize(packet *Packet) {
	if len(packet.PacketData.Data) < 4 {
		log.Printf("无效的终端大小数据")
		return
	}

	cols := binary.BigEndian.Uint16(packet.PacketData.Data[:2])
	rows := binary.BigEndian.Uint16(packet.PacketData.Data[2:4])

	size := &pty.Winsize{
		Cols: cols,
		Rows: rows,
	}

	if err := pty.Setsize(cm.ptmx, size); err != nil {
		log.Printf("调整终端大小失败: %v", err)
	} else {
		cm.terminalSize.size = size
		log.Printf("终端大小调整为: %dx%d", cols, rows)
	}
}

// handleMainTerminalResize 处理主终端的RESIZE命令
func (cm *ConnectionManager) handleMainTerminalResize(command string) {
	// 解析RESIZE命令: RESIZE:cols:rows
	parts := strings.Split(command, ":")
	if len(parts) != 3 {
		log.Printf("无效的RESIZE命令格式: %s", command)
		return
	}

	cols, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Printf("无效的列数: %s", parts[1])
		return
	}

	rows, err := strconv.ParseInt(parts[2], 10, 32)
	if err != nil {
		log.Printf("无效的行数: %s", parts[2])
		return
	}

	// 调整主终端大小
	if cm.ptmx != nil {
		err := pty.Setsize(cm.ptmx, &pty.Winsize{Cols: uint16(cols), Rows: uint16(rows)})
		if err != nil {
			log.Printf("主终端Resize失败: %v", err)
		} else {
			log.Printf("✅ 主终端大小已调整为: %dx%d", cols, rows)
		}
	}
}

// handleCreateTerminalCommand 处理创建终端的命令
func (cm *ConnectionManager) handleCreateTerminalCommand() {
	log.Println("收到创建终端命令")

	// 使用多终端管理器创建新终端
	terminalID, err := cm.multiTerminalManager.CreateBackupTerminal(cm.ctx)
	if err != nil {
		log.Printf("创建终端失败: %v", err)
		// 发送错误响应
		errorResponse := CommandResponse{
			TerminalID: 0,
			Output:     "",
			Success:    false,
			Error:      fmt.Sprintf("创建终端失败: %v", err),
		}
		cm.sendCommandResponse(errorResponse)
		return
	}

	log.Printf("✅ 成功创建备用终端，ID: %d", terminalID)

	// 发送成功响应
	successResponse := CommandResponse{
		TerminalID: terminalID,
		Output:     fmt.Sprintf("终端 %d 创建成功", terminalID),
		Success:    true,
		Error:      "",
	}

	cm.sendCommandResponse(successResponse)
}

// handleGetTerminalListCommand 处理获取终端列表的命令
func (cm *ConnectionManager) handleGetTerminalListCommand() {
	log.Println("收到获取终端列表命令")

	// 使用多终端管理器获取终端列表
	terminalIDs := cm.multiTerminalManager.GetTerminalList()

	log.Printf("当前活跃终端列表: %v", terminalIDs)

	// 构建终端列表响应
	var terminals []map[string]interface{}
	for _, terminalID := range terminalIDs {
		terminalType := "main"
		if terminalID != 0 {
			terminalType = "backup"
		}

		terminals = append(terminals, map[string]interface{}{
			"id":     terminalID,
			"type":   terminalType,
			"active": true,
		})
	}

	// 构建终端列表响应数据
	terminalListData := map[string]interface{}{
		"terminals": terminals,
	}

	log.Printf("✅ 成功获取终端列表，终端数量: %d", len(terminals))

	// 发送终端列表响应 - 直接传递Go对象，让sendResp进行序列化
	cm.sendResp(RunCommand, GetTerminalList, terminalListData)
}

// handleCloseTerminalCommand 处理关闭终端的命令
func (cm *ConnectionManager) handleCloseTerminalCommand(data []byte) {
	if len(data) < 4 {
		log.Printf("关闭终端命令数据长度不足: %d", len(data))
		return
	}
	// 解析终端ID
	terminalID := binary.LittleEndian.Uint32(data[:4])
	log.Printf("收到关闭终端命令，终端ID: %d", terminalID)

	// 使用多终端管理器关闭终端
	err := cm.multiTerminalManager.CloseTerminal(terminalID)
	if err != nil {
		log.Printf("关闭终端失败: %v", err)
		// 发送错误响应
		errorResponse := CommandResponse{
			TerminalID: terminalID,
			Output:     "",
			Success:    false,
			Error:      fmt.Sprintf("关闭终端失败: %v", err),
		}
		cm.sendCommandResponse(errorResponse)
		return
	}

	log.Printf("✅ 成功关闭备用终端，ID: %d", terminalID)

	// 发送成功响应
	successResponse := CommandResponse{
		TerminalID: terminalID,
		Output:     fmt.Sprintf("终端 %d 关闭成功", terminalID),
		Success:    true,
		Error:      "",
	}

	cm.sendCommandResponse(successResponse)
}

// sendCommandResponse 发送命令响应
func (cm *ConnectionManager) sendCommandResponse(response CommandResponse) {
	// 使用sendResp方法发送响应
	cm.sendResp(RunCommand, ExecOutput, response)
	log.Printf("✅ 命令响应已发送: TerminalID=%d, Success=%t", response.TerminalID, response.Success)
}
