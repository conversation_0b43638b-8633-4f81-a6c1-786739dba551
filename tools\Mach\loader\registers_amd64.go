//go:build darwin && amd64

package main

import (
	"unsafe"
)

// x86_64 register structure for macOS
type PtraceRegsAmd64Darwin struct {
	Rax    uint64
	Rbx    uint64
	Rcx    uint64
	Rdx    uint64
	Rdi    uint64
	Rsi    uint64
	Rbp    uint64
	Rsp    uint64
	R8     uint64
	R9     uint64
	R10    uint64
	R11    uint64
	R12    uint64
	R13    uint64
	R14    uint64
	R15    uint64
	Rip    uint64
	Rflags uint64
	Cs     uint64
	Fs     uint64
	Gs     uint64
}

// Register access functions for x86_64 architecture on macOS

func getRIP(regs interface{}) uint64 {
	// Convert to x86_64 specific structure for macOS
	amd64Regs := (*PtraceRegsAmd64Darwin)(unsafe.Pointer(&regs))
	return amd64Regs.Rip
}

func setRIP(regs interface{}, addr uint64) {
	// Convert to x86_64 specific structure for macOS
	amd64Regs := (*PtraceRegsAmd64Darwin)(unsafe.Pointer(&regs))
	amd64Regs.Rip = addr
}

func getRSP(regs interface{}) uint64 {
	// Convert to x86_64 specific structure for macOS
	amd64Regs := (*PtraceRegsAmd64Darwin)(unsafe.Pointer(&regs))
	return amd64Regs.Rsp
}

func setRSP(regs interface{}, addr uint64) {
	// Convert to x86_64 specific structure for macOS
	amd64Regs := (*PtraceRegsAmd64Darwin)(unsafe.Pointer(&regs))
	amd64Regs.Rsp = addr
}
