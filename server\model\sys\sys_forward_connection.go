package sys

import (
	"server/global"
	"time"
)

// ForwardConnection 正向连接配置模型
type ForwardConnection struct {
	global.DBModel
	ListenerID    uint      `json:"listenerId" gorm:"column:listener_id;comment:关联的监听器ID"`
	ClientAddr    string    `json:"clientAddr" gorm:"column:client_addr;comment:客户端地址"`
	Status        int       `json:"status" gorm:"column:status;default:1;comment:状态 1启用 0禁用"`
	MaxRetries    int       `json:"maxRetries" gorm:"column:max_retries;default:5;comment:最大重连次数"`
	RetryInterval int       `json:"retryInterval" gorm:"column:retry_interval;default:5;comment:重连间隔(秒)"`
	LastConnectAt time.Time `json:"lastConnectAt" gorm:"column:last_connect_at;comment:最后连接时间"`
	RetryCount    int       `json:"retryCount" gorm:"column:retry_count;default:0;comment:当前重连次数"`
	Remark        string    `json:"remark" gorm:"column:remark;comment:备注"`
}

// TableName 设置表名
func (ForwardConnection) TableName() string {
	return "sys_forward_connection"
}
