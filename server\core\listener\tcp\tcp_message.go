package tcp

import (
	"fmt"
	"server/model/tlv"
	"sync"
	"sync/atomic"
)

// TLVMessageHandler TLV消息处理器
type TLVMessageHandler struct {
	mu             sync.RWMutex
	fragmentBuffer map[uint32]*tlv.FragmentBuffer // 按Label分组的分片缓冲区
	metadata       *tlv.METADATA
	labelCounter   uint64
}

// NewTLVMessageHandler 创建新的TLV消息处理器
func NewTLVMessageHandler(metadata *tlv.METADATA) *TLVMessageHandler {
	return &TLVMessageHandler{
		fragmentBuffer: make(map[uint32]*tlv.FragmentBuffer),
		metadata:       metadata,
		labelCounter:   1,
	}
}

// GenerateLabel 生成唯一的Label
func (h *TLVMessageHandler) GenerateLabel() uint32 {
	return uint32(atomic.AddUint64(&h.labelCounter, 1))
}

func (h *TLVMessageHandler) CreatePacket(output []byte, Type, Code uint8) *tlv.Packet {

	label := h.GenerateLabel()
	data := output

	header := &tlv.Header{
		Type:      Type,
		Code:      Code,
		Label:     label,
		FragIndex: 0,
		Flags:     tlv.Nop,
	}
	packetData := &tlv.PacketData{
		Data: data,
	}
	packet := &tlv.Packet{
		Header:     header,
		PacketData: packetData,
	}
	return packet
}

// CreateHeartbeatPacket 创建心跳包
func (h *TLVMessageHandler) CreateHeartbeatPacket(isPong bool) (*tlv.Packet, error) {
	var Code uint8
	if isPong {
		Code = tlv.PONG
	} else {
		Code = tlv.PING
	}
	packet := h.CreatePacket([]byte("heartbeat"), tlv.Heartbeat, Code)
	// 加密数据包
	if err := packet.EncryptPacket(h.metadata); err != nil {
		return nil, fmt.Errorf("加密心跳包失败: %v", err)
	}
	return packet, nil
}

// CreateAdvancedHeartbeatPacket 创建高级心跳包（使用新的结构体）
func (h *TLVMessageHandler) CreateAdvancedHeartbeatPacket(heartbeatData []byte, heartbeatType uint8) (*tlv.Packet, error) {
	packet := h.CreatePacket(heartbeatData, tlv.Heartbeat, heartbeatType)
	// 加密数据包
	if err := packet.EncryptPacket(h.metadata); err != nil {
		return nil, fmt.Errorf("加密心跳包失败: %v", err)
	}
	return packet, nil
}

// CreateFragPacket 创建分片包
func (h *TLVMessageHandler) CreateFragPacket(data []byte, Type uint8, Code uint8) ([]*tlv.Packet, error) {
	label := h.GenerateLabel()

	if len(data) <= 40000 {
		packet := h.CreatePacket(data, Type, Code)
		if err := packet.EncryptPacket(h.metadata); err != nil {
			return nil, err
		}
		return []*tlv.Packet{packet}, nil
	}
	// 创建分片数据包
	packets := tlv.CreateFragmentedPackets(
		Type,
		Code,
		label,
		data,
		40000,
	)
	// 对每个分片数据包进行加密
	for _, packet := range packets {
		if err := packet.EncryptPacket(h.metadata); err != nil {
			return nil, fmt.Errorf("加密命令包失败: %v", err)
		}
	}
	return packets, nil
}

// ProcessIncomingPacket 处理接收到的数据包
func (h *TLVMessageHandler) ProcessIncomingPacket(packetBytes []byte) (*tlv.Packet, bool, error) {
	// 解析数据包
	packet := &tlv.Packet{}

	if err := packet.DeserializePacket(packetBytes); err != nil {
		return nil, false, fmt.Errorf("解析数据包失败: %v", err)
	}

	// 解密数据包
	if err := packet.DecryptPacket(h.metadata); err != nil {
		return nil, false, fmt.Errorf("解密数据包失败: %v", err)
	}

	// 处理分片
	if packet.Header.Flags == tlv.MoreFrag || packet.Header.Flags == tlv.NoMoreFrag {
		return h.handleFragmentedPacket(packet)
	}

	// 非分片数据包直接返回
	return packet, true, nil
}

// handleFragmentedPacket 处理分片数据包
func (h *TLVMessageHandler) handleFragmentedPacket(packet *tlv.Packet) (*tlv.Packet, bool, error) {
	h.mu.Lock()
	defer h.mu.Unlock()

	label := packet.Header.Label

	// 获取或创建分片缓冲区
	buffer, exists := h.fragmentBuffer[label]
	if !exists {
		buffer = tlv.NewFragmentBuffer()
		h.fragmentBuffer[label] = buffer
	}

	// 添加分片
	data, complete := buffer.AddFragment(packet.Header, packet.PacketData.Data)
	if complete {
		// 清理缓冲区
		delete(h.fragmentBuffer, label)
		packet.PacketData.Data = data
		packet.Header.Flags = tlv.NoMoreFrag
		packet.Header.FragIndex = 0

		return packet, true, nil
	}

	return nil, false, nil
}
