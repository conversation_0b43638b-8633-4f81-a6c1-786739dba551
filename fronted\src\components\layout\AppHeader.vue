<template>
  <a-layout-header class="header">
    <div class="header-left">
      <a-button
        type="text"
        :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
        @click="toggleCollapsed"
        class="trigger"
      />
      <span class="header-title">
        {{
          selectedKeys[0] === '1' ? '仪表盘' :
          selectedKeys[0] === '2' ? '监听器管理' :
          selectedKeys[0] === '3' ? '客户端管理' :
          selectedKeys[0] === 'proxy-index' ? '代理管理' :
          selectedKeys[0] === 'proxy-chain' ? '代理链管理' :
          selectedKeys[0] === 'proxy-monitor' ? '代理监控' :
          selectedKeys[0] === '4' ? '下载/上传管理' :
          selectedKeys[0] === '5' ? '截图库' :
          selectedKeys[0] === '6' ? '用户管理' :
          selectedKeys[0] === '7' ? '日志监控' :
          selectedKeys[0] === '8' ? '性能监控' :
          selectedKeys[0] === '9' ? '系统设置' : '仪表盘'
        }}
      </span>
    </div>
    <div class="header-right">
      <!-- 通知中心按钮 -->
      <a-badge :count="notificationStats.unreadCount" :offset="[-2, 2]">
        <a-button
          type="text"
          :icon="h(BellOutlined)"
          @click="showNotificationCenter"
          class="notification-btn"
          title="通知中心"
        />
      </a-badge>

      <a-dropdown>
        <a class="user-dropdown" @click.prevent>
          <a-avatar :size="32">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</a-avatar>
          <span class="username">{{ userInfo.username }}</span>
        </a>
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item key="profile">
              <UserOutlined />
              <span>个人信息</span>
            </a-menu-item>
            <a-menu-item key="settings">
              <SettingOutlined />
              <span>设置</span>
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="logout">
              <LogoutOutlined />
              <span>退出登录</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    
    <!-- 个人信息弹窗 -->
    <UserProfileForm
      v-model:open="profileVisible"
      :userInfo="userInfo"
      @success="handleProfileSuccess"
    />
    
    <!-- 设置弹窗 -->
    <a-modal
      v-model:open="settingsVisible"
      title="系统设置"
      :width="700"
      :footer="null"
    >
      <SettingsContent :active="settingsVisible" />
    </a-modal>

    <!-- 通知中心弹窗 -->
    <a-drawer
      v-model:open="notificationCenterVisible"
      title="通知中心"
      placement="right"
      :width="400"
      :closable="true"
    >
      <NotificationCenter
        ref="notificationCenterRef"
        @notification-click="handleNotificationClick"
      />
    </a-drawer>
  </a-layout-header>
</template>

<script setup>
import { h, ref, reactive, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined
} from '@ant-design/icons-vue';
import UserProfileForm from '@/components/user/UserProfileForm.vue';
import SettingsContent from '@/components/settings/SettingsContent.vue';
import NotificationCenter from '@/components/notification/NotificationCenter.vue';
import notificationManager from '@/utils/notificationManager';

// 路由
const router = useRouter();

// 接收父组件传递的属性
const props = defineProps({
  collapsed: {
    type: Boolean,
    required: true
  },
  selectedKeys: {
    type: Array,
    required: true
  },
  userInfo: {
    type: Object,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['update:collapsed', 'update:userInfo']);

// 状态变量
const profileVisible = ref(false);
const settingsVisible = ref(false);
const notificationCenterVisible = ref(false);
const notificationCenterRef = ref(null);

// 通知统计
const notificationStats = reactive({
  unreadCount: 0,
  totalCount: 0
});

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  emit('update:collapsed', !props.collapsed);
};

// 处理下拉菜单点击
const handleMenuClick = ({ key }) => {
  if (key === 'profile') {
    profileVisible.value = true;
  } else if (key === 'settings') {
    settingsVisible.value = true;
  } else if (key === 'logout') {
    handleLogout();
  }
};

// 处理个人信息更新成功
const handleProfileSuccess = (updatedUserInfo) => {
  // 通知父组件更新用户信息
  emit('update:userInfo', updatedUserInfo);
  message.success('个人信息更新成功');
};

// 显示通知中心
const showNotificationCenter = () => {
  notificationCenterVisible.value = true;
};

// 处理通知点击
const handleNotificationClick = (notification) => {
  console.log('Header: 通知被点击:', notification);
  // 关闭通知中心
  notificationCenterVisible.value = false;

  // 🚨 修复：不要触发弹窗显示，只处理页面跳转
  // 根据通知类型跳转到相应页面
  if (notification.type && notification.type.startsWith('client_')) {
    router.push('/client');
  } else if (notification.type && notification.type.startsWith('listener_')) {
    router.push('/listener');
  }

  // 🚨 关键修复：阻止事件继续传播到其他组件
  // 不要调用任何弹窗显示逻辑
};

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('userInfo');
  router.push('/login');
  message.success('已退出登录');
};

// 组件挂载时注册通知中心组件
onMounted(() => {
  // 注册通知中心组件到通知管理器
  if (notificationCenterRef.value) {
    notificationManager.registerCenter(notificationCenterRef.value);
  }

  // 监听通知统计更新
  notificationManager.on('statsUpdate', (stats) => {
    notificationStats.unreadCount = stats.unreadCount;
    notificationStats.totalCount = stats.totalCount;
  });
});

// 组件卸载时清理
onUnmounted(() => {
  notificationManager.off('statsUpdate');
});
</script>

<style scoped lang="scss">
.header {
  background: #fff;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  margin-left: 12px;
}

.trigger {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
  
  &:hover {
    color: $primary-color;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-btn {
  font-size: 18px;
  color: #595959;
  transition: color 0.3s;

  &:hover {
    color: $primary-color;
  }
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 12px;

  .username {
    margin-left: 8px;
    font-size: 14px;
  }
}
</style>