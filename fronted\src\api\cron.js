import { get, post, put, del } from '@/utils/request'

// 创建定时任务
export function createTask(data) {
  return post('/cron/tasks', data)
}

// 获取任务列表
export function getTaskList(params) {
  return get('/cron/tasks', params)
}

// 获取任务详情
export function getTask(id) {
  return get(`/cron/tasks/${id}`)
}

// 更新任务
export function updateTask(id, data) {
  return put(`/cron/tasks/${id}`, data)
}

// 删除任务
export function deleteTask(id) {
  return del(`/cron/tasks/${id}`)
}

// 启动任务
export function startTask(id) {
  return post(`/cron/tasks/${id}/start`)
}

// 停止任务
export function stopTask(id) {
  return post(`/cron/tasks/${id}/stop`)
}

// 暂停任务
export function pauseTask(id) {
  return post(`/cron/tasks/${id}/pause`)
}

// 立即执行任务
export function executeTask(id) {
  return post(`/cron/tasks/${id}/execute`)
}

// 验证Cron表达式
export function validateCronExpr(data) {
  return post('/cron/validate', data)
}

// 获取执行记录列表
export function getExecutionList(params) {
  return get('/cron/executions', params)
}

// 获取执行记录详情
export function getExecution(id) {
  return get(`/cron/executions/${id}`)
}

// 获取任务模板列表
export function getTemplateList() {
  return get('/cron/templates')
}

// 创建任务模板
export function createTemplate(data) {
  return post('/cron/templates', data)
}

// 更新任务模板
export function updateTemplate(id, data) {
  return put(`/cron/templates/${id}`, data)
}

// 删除任务模板
export function deleteTemplate(id) {
  return del(`/cron/templates/${id}`)
}

// 获取任务模板详情
export function getTemplate(id) {
  return get(`/cron/templates/${id}`)
}

// 获取任务统计
export function getTaskStats() {
  return get('/cron/stats/tasks')
}

// 获取执行统计
export function getExecutionStats() {
  return get('/cron/stats/executions')
}
