package proxy

import (
	"server/model/basic"
	"server/model/sys"
)

// ProxyInstanceResponse 代理实例响应
type ProxyInstanceResponse struct {
	basic.Proxy
	ClientInfo *sys.Client `json:"client_info,omitempty"`
}

// ProxyStartResponse 启动代理响应
type ProxyStartResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	ProxyID string `json:"proxy_id"` // 数据库ID
	Port    uint16 `json:"port"`     // Client开启的实际端口，如果是Server启动Socks5，此项不用设置
	Error   string `json:"error"`
}

// ProxyStopResponse 停止代理响应
type ProxyStopResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	ProxyID string `json:"proxy_id"`
	Error   string `json:"error"`
}

// ProxyDeleteResponse 停止代理响应
type ProxyDeleteResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	ProxyID string `json:"proxy_id"`
	Error   string `json:"error"`
}

// ProxyStatusResponse 代理状态响应
type ProxyStatusResponse struct {
	TaskID    uint64      `json:"task_id"`
	Success   bool        `json:"success"`
	ProxyID   string      `json:"proxy_id"`
	ProxyInfo basic.Proxy `json:"proxy_info"`
	Error     string      `json:"error"`
}

// ProxyListResponse 代理列表响应
type ProxyListResponse struct {
	TaskID  uint64        `json:"task_id"`
	Success bool          `json:"success"`
	Proxies []basic.Proxy `json:"proxies"`
	Count   int           `json:"count"`
	Error   string        `json:"error"`
}

// CheckPortResponse 测试端口响应,一般由Client发出，Server直接测不需要这个结构体
type CheckPortResponse struct {
	TaskID           uint64   `json:"task_id"`
	UnavailablePorts []uint16 `json:"unavailable_ports"`
	AvailablePorts   []uint16 `json:"available_ports"`
	Type             string   `json:"type"` // forward, reverse, chain
	Error            string   `json:"error"`
}
