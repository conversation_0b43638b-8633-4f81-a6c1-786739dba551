package sys

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type DashboardRoute struct{}

func (r *DashboardRoute) InitDashboardRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	DashboardRouter := Router.Group("/dashboard").Use(middleware.OperationRecord())
	{
		DashboardRouter.GET("/info", dashboardApi.GetSystemInfo)
		DashboardRouter.GET("/stats", dashboardApi.GetDashboardStats)
		// 🚀 新增：Dashboard SSE实时数据流
		DashboardRouter.GET("/sse", dashboardApi.DashboardSSE)
		// 🌐 新增：网络拓扑图数据
		DashboardRouter.GET("/topology", dashboardApi.GetNetworkTopology)
	}
	return DashboardRouter
}
