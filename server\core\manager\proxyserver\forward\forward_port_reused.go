package forward

import (
	"fmt"
	"server/global"
	"server/model/basic"

	"go.uber.org/zap"
)

// ===== 端口复用功能 =====

// CanReusePort 检查端口是否可以复用
func (fm *ForwardManager) CanReusePort(port uint16) bool {
	fm.portLock.RLock()
	defer fm.portLock.RUnlock()

	// 如果端口没有被使用，可以复用
	servers, exists := fm.portReuse[port]
	if !exists || len(servers) == 0 {
		return true
	}

	// 检查现有服务器是否都是正向代理（可以复用）
	for _, server := range servers {
		if server.config.Type != "forward" { // forward表示正向代理
			return false
		}
	}

	return true
}

// AddPortReuse 添加端口复用
func (fm *ForwardManager) AddPortReuse(port uint16, server *ForwardProxyServer) {
	fm.portLock.Lock()
	defer fm.portLock.Unlock()

	if fm.portReuse[port] == nil {
		fm.portReuse[port] = make([]*ForwardProxyServer, 0)
	}
	fm.portReuse[port] = append(fm.portReuse[port], server)
}

// RemovePortReuse 移除端口复用
func (fm *ForwardManager) RemovePortReuse(port uint16, server *ForwardProxyServer) {
	fm.portLock.Lock()
	defer fm.portLock.Unlock()

	servers, exists := fm.portReuse[port]
	if !exists {
		return
	}

	// 移除指定的服务器
	for i, s := range servers {
		if s == server {
			fm.portReuse[port] = append(servers[:i], servers[i+1:]...)
			break
		}
	}

	// 如果没有服务器了，删除端口记录
	if len(fm.portReuse[port]) == 0 {
		delete(fm.portReuse, port)
	}
}

// GetPortReuseInfo 获取端口复用信息
func (fm *ForwardManager) GetPortReuseInfo(port uint16) []string {
	fm.portLock.RLock()
	defer fm.portLock.RUnlock()

	servers, exists := fm.portReuse[port]
	if !exists {
		return nil
	}

	info := make([]string, 0, len(servers))
	for _, server := range servers {
		info = append(info, fmt.Sprintf("Client:%s:%d -> Target:%s:%d",
			server.clientAddr, server.clientPort,
			server.config.Name, server.config.ID))
	}

	return info
}

// StartServerWithPortReuse 启动支持端口复用的正向代理服务
func (fm *ForwardManager) StartServerWithPortReuse(proxy *basic.Proxy, allowReuse bool) error {
	if !allowReuse && !fm.CanReusePort(proxy.UserPort) {
		return fmt.Errorf("端口 %d 已被占用且不支持复用", proxy.UserPort)
	}

	// 使用现有的StartServer方法
	if err := fm.StartServer(proxy); err != nil {
		return err
	}

	// 添加到端口复用管理
	fm.lock.Lock()
	server, exists := fm.servers[proxy]
	fm.lock.Unlock()

	if exists {
		fm.AddPortReuse(proxy.UserPort, server)
		global.LOG.Info("正向代理已添加到端口复用",
			zap.Uint16("port", proxy.UserPort),
			zap.String("proxyID", proxy.ProxyID))
	}

	return nil
}
