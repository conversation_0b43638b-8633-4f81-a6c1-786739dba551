package task

import "time"

// ProcessTask 进程管理任务
type ProcessTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	ClientID    uint       `json:"client_id"`  // 客户端ID
	TaskType    string     `json:"task_type"`  // 任务类型：list_proc, kill_proc, start_proc, details_proc, suspend_proc, resume_proc
	TargetPID   int32      `json:"target_pid"` // 目标进程ID（用于kill, details, suspend, resume操作）
	Status      string     `json:"status"`     // 状态：pending, running, completed, failed, cancelled
	Error       string     `json:"error"`      // 错误信息
	Result      string     `json:"result"`     // 任务结果（JSON格式）
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`   // 开始时间
	CompletedAt *time.Time `json:"completed_at"` // 完成时间
}
