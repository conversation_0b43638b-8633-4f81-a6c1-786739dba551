import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { dirApi, clientApi } from '@/api';

/**
 * 文件管理器状态管理
 * 包含：当前路径、文件列表、加载状态、客户端信息等全局状态
 */
export function useFileManagerStore() {
  // 基础状态
  const loading = ref(false);
  const fileList = ref([]);
  const clientInfo = ref(null);
  const showHidden = ref(false);
  
  // 分页状态
  const currentPage = ref(1);
  const pageSize = ref(25);
  const totalCount = ref(0);
  const totalPages = ref(0);
  const hasMore = ref(false);
  
  // 排序状态
  const sortBy = ref('name');
  const sortOrder = ref('asc');
  
  // 磁盘相关状态
  const currentDisk = ref(null);
  
  // 右键菜单状态
  const contextMenuVisible = ref(false);
  const contextMenuPosition = ref({ x: 0, y: 0 });
  const contextMenuTarget = ref(null);
  
  // 弹窗状态
  const uploadModalVisible = ref(false);
  const createDirModalVisible = ref(false);
  const createFileModalVisible = ref(false);
  const renameModalVisible = ref(false);
  const transferTasksVisible = ref(false);

  // 文件操作状态
  const renameFile = ref(null);
  
  // 组件引用
  const fileTransferModalRef = ref(null);
  const toolbarRef = ref(null);
  
  /**
   * 获取目录列表
   * @param {string} path - 目录路径
   * @param {string|number} clientId - 客户端ID
   * @param {string} currentPath - 当前路径（用于更新）
   * @param {number} page - 页码（可选，默认使用当前页）
   */
  const getFileList = async (path, clientId, currentPath, page = null) => {
    try {
      loading.value = true;
      
      // 如果指定了页码，则更新当前页
      if (page !== null) {
        currentPage.value = page;
      }
      
      const res = await dirApi.listDir(clientId, { 
        path,
        show_hidden: showHidden.value,
        page: currentPage.value,
        page_size: pageSize.value,
        sort_by: sortBy.value,
        sort_order: sortOrder.value,
        recursive: false,
        max_depth: 1
      });
      if (res.code === 200) {
        if (res.data.data.error === ""){
          // 后端返回的数据结构是嵌套的，文件列表在res.data.data字段中
          const data = res.data.data;
          
          // 更新分页信息
          totalCount.value = data.total_count || 0;
          totalPages.value = data.total_pages || 0;
          hasMore.value = data.has_more || false;
          
          const rawFileList = data?.file_info_responses || [];
          console.log(rawFileList);
          // 映射后端字段到前端期望的字段
          let mappedFileList = rawFileList.map(file => {
            return {
              ...file,
              type: file.is_dir ? 'directory' : 'file',
              modifiedTime: file.mod_time,
              permissions: file.permissions || '-',
              owner: file.owner || '-',
              group: file.group || '-'
            };
          });

          // 过滤掉特殊目录项
          mappedFileList = mappedFileList.filter(file => {
            // 过滤掉名为 '.' 的当前目录项
            if (file.name === '.') {
              return false;
            }
            // 过滤掉根目录符号（/ 和 \）
            if (file.name === '/' || file.name === '\\') {
              return false;
            }
            // 过滤掉名为 '..' 的上级目录项
            if (file.name === '..') {
              return false;
            }
            return true;
          });

          // 去重处理：根据文件名去重，避免重复显示相同文件/目录
          const uniqueFileMap = new Map();
          mappedFileList.forEach(file => {
            const key = file.name; // 使用文件名作为唯一标识，因为在同一目录下文件名应该是唯一的
            if (!uniqueFileMap.has(key)) {
              uniqueFileMap.set(key, file);
            }
          });
          mappedFileList = Array.from(uniqueFileMap.values());

          fileList.value = mappedFileList;
          // 更新当前路径（通过回调函数）
          if (currentPath && typeof currentPath.value !== 'undefined') {
            currentPath.value = path;
          }
        }else{
          message.error(res.data.data.error || res.data.error || '获取文件列表失败')
        }

      } else {
        message.error(res.data.data.error || res.data.error || '获取文件列表失败');
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 获取客户端信息
   * @param {string|number} clientId - 客户端ID
   */
  const fetchClientInfo = async (clientId) => {
    try {
      const response = await clientApi.getClient(clientId);
      if (response && response.data) {
        clientInfo.value = response.data;
        console.log('获取到客户端信息:', clientInfo.value);
      }
    } catch (error) {
      console.error('获取客户端信息失败:', error);
      clientInfo.value = null;
    }
  };
  
  /**
   * 处理显示隐藏文件切换
   * @param {boolean} checked - 是否显示隐藏文件
   * @param {Function} refreshCallback - 刷新回调函数
   */
  const handleShowHiddenChange = (checked, refreshCallback) => {
    showHidden.value = checked;
    if (refreshCallback) {
      refreshCallback();
    }
  };
  
  /**
   * 切换隐藏文件显示
   */
  const toggleHidden = () => {
    showHidden.value = !showHidden.value;
  };
  
  /**
   * 设置页面大小
   * @param {number} size - 页面大小
   */
  const setPageSize = (size) => {
    pageSize.value = size;
    currentPage.value = 1; // 重置到第一页
  };
  
  /**
   * 设置排序方式
   * @param {string} field - 排序字段
   * @param {string} order - 排序顺序
   */
  const setSorting = (field, order) => {
    sortBy.value = field;
    sortOrder.value = order;
    currentPage.value = 1; // 重置到第一页
  };
  
  /**
   * 跳转到指定页
   * @param {number} page - 页码
   */
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };
  
  /**
   * 下一页
   */
  const nextPage = () => {
    if (hasMore.value) {
      currentPage.value++;
    }
  };
  
  /**
   * 上一页
   */
  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--;
    }
  };
  
  /**
   * 显示右键菜单
   * @param {Event} event - 鼠标事件
   * @param {Object} record - 文件记录
   */
  const showContextMenu = (event, record) => {
    contextMenuVisible.value = true;
    contextMenuPosition.value = {
      x: event.clientX,
      y: event.clientY
    };
    contextMenuTarget.value = record;
    
    // 点击其他地方关闭菜单
    const closeMenu = () => {
      contextMenuVisible.value = false;
      document.removeEventListener('click', closeMenu);
    };
    setTimeout(() => {
      document.addEventListener('click', closeMenu);
    }, 0);
  };
  
  /**
   * 开始重命名
   * @param {Object} file - 文件对象
   */
  const startRename = (file) => {
    renameFile.value = file;
    renameModalVisible.value = true;
  };
  

  
  /**
   * 显示传输任务管理器
   */
  const showTransferTasks = () => {
    if (fileTransferModalRef.value) {
      fileTransferModalRef.value.show();
      // 刷新传输任务列表
      fileTransferModalRef.value.getTransferTasks();
    }
  };
  
  /**
   * 处理各种弹窗完成事件
   */
  const handleUploadComplete = (refreshCallback) => {
    uploadModalVisible.value = false;
    if (refreshCallback) {
      refreshCallback();
    }
  };
  

  
  const handleCreateComplete = (refreshCallback) => {
    createDirModalVisible.value = false;
    createFileModalVisible.value = false;
    if (refreshCallback) {
      refreshCallback();
    }
  };
  
  const handleRenameComplete = (refreshCallback) => {
    renameModalVisible.value = false;
    if (refreshCallback) {
      refreshCallback();
    }
  };
  
  /**
   * 重置所有状态
   */
  const resetState = () => {
    loading.value = false;
    fileList.value = [];
    clientInfo.value = null;
    showHidden.value = false;
    currentDisk.value = null;
    contextMenuVisible.value = false;
    contextMenuPosition.value = { x: 0, y: 0 };
    contextMenuTarget.value = null;
    uploadModalVisible.value = false;
    editModalVisible.value = false;
    createDirModalVisible.value = false;
    createFileModalVisible.value = false;
    renameModalVisible.value = false;
    transferTasksVisible.value = false;
    currentEditFile.value = null;
    renameFile.value = null;
  };
  
  return {
    // 状态
    loading,
    fileList,
    clientInfo,
    showHidden,
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasMore,
    sortBy,
    sortOrder,
    currentDisk,
    contextMenuVisible,
    contextMenuPosition,
    contextMenuTarget,
    uploadModalVisible,
    createDirModalVisible,
    createFileModalVisible,
    renameModalVisible,
    transferTasksVisible,
    renameFile,
    fileTransferModalRef,
    toolbarRef,
    
    // 方法
    getFileList,
    fetchClientInfo,
    handleShowHiddenChange,
    toggleHidden,
    setPageSize,
    setSorting,
    goToPage,
    nextPage,
    prevPage,
    showContextMenu,
    startRename,
    showTransferTasks,
    handleUploadComplete,
    handleCreateComplete,
    handleRenameComplete,
    resetState
  };
}