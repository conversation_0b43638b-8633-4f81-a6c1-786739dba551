//go:build windows
// +build windows

package common

import (
	"fmt"
	"log"
	"os"
	"strings"
	"syscall"

	"github.com/shirou/gopsutil/v3/disk"
	"golang.org/x/sys/windows"
)

// DiskListResponse 磁盘列表响应
type DiskListResponse struct {
	TaskID    uint64     `json:"task_id"`    // 任务ID
	Success   bool       `json:"success"`    // 操作是否成功
	DiskInfos []DiskInfo `json:"disk_infos"` // 磁盘信息列表
	Error     string     `json:"error"`      // 错误信息
}

// DiskListRequest 磁盘列表请求
type DiskListRequest struct {
	TaskID         uint64 `json:"task_id"`         // 任务ID
	IncludeDetails bool   `json:"include_details"` // 是否包含详细信息
}

// DiskInfo 磁盘信息
type DiskInfo struct {
	MountPoint   string  `json:"mount_point"`   // 挂载点/驱动器盘符
	FileSystem   string  `json:"file_system"`   // 文件系统类型
	Device       string  `json:"device"`        // 设备名称
	TotalSize    int64   `json:"total_size"`    // 总大小（字节）
	UsedSize     int64   `json:"used_size"`     // 已使用大小（字节）
	AvailSize    int64   `json:"avail_size"`    // 可用大小（字节）
	UsagePercent float64 `json:"usage_percent"` // 使用百分比
	IsReadOnly   bool    `json:"is_read_only"`  // 是否只读
	Label        string  `json:"label"`         // 磁盘标签
	Icon         string  `json:"icon"`          // 图标类型
}

// handleDiskList 处理磁盘列表请求
func (cm *ConnectionManager) handleDiskList(packet *Packet) {
	log.Printf("[磁盘处理] 收到磁盘列表请求")
	var req DiskListRequest
	respErr := &DiskListResponse{
		TaskID:  0, // 初始化为0，反序列化后更新
		Error:   "",
		Success: false,
	}

	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("[磁盘处理] 磁盘列表请求反序列化失败: %v", err)
		respErr.TaskID = req.TaskID // 反序列化后设置TaskID
		respErr.Error = fmt.Sprintf("磁盘列表请求反序列化失败: %v", err)
		cm.sendResp(Dir, DiskList, respErr)
		return
	}

	log.Printf("[磁盘处理] 请求参数解析成功，包含详细信息: %v", req.IncludeDetails)

	// 获取磁盘列表
	resp := getWindowsDiskList(&req)
	log.Printf("[磁盘处理] 磁盘列表获取完成，成功: %v, 磁盘数量: %d", resp.Success, len(resp.DiskInfos))
	if !resp.Success {
		log.Printf("[磁盘处理] 磁盘列表获取失败: %s", resp.Error)
	}
	cm.sendResp(Dir, DiskList, resp)
	log.Printf("[磁盘处理] 磁盘列表响应已发送")
}

// getWindowsDiskList 获取Windows磁盘列表
func getWindowsDiskList(req *DiskListRequest) *DiskListResponse {
	log.Printf("[磁盘列表] 开始获取Windows磁盘列表，包含详细信息: %v", req.IncludeDetails)
	resp := &DiskListResponse{
		TaskID:    req.TaskID,
		Success:   true,
		DiskInfos: []DiskInfo{},
		Error:     "",
	}

	// 获取所有驱动器
	drives, err := getWindowsDrives()
	if err != nil {
		log.Printf("[磁盘列表] 获取驱动器列表失败: %v", err)
		resp.Success = false
		resp.Error = fmt.Sprintf("获取驱动器列表失败: %v", err)
		return resp
	}
	log.Printf("[磁盘列表] 找到 %d 个驱动器: %v", len(drives), drives)

	for _, drive := range drives {
		log.Printf("[磁盘列表] 处理驱动器: %s", drive)
		diskInfo := DiskInfo{
			MountPoint: drive,
			Device:     drive,
		}

		if req.IncludeDetails {
			// 获取磁盘使用情况
			if usage, err := getWindowsDiskUsage(drive); err == nil {
				diskInfo.TotalSize = usage.Total
				diskInfo.UsedSize = usage.Used
				diskInfo.AvailSize = usage.Free
				if usage.Total > 0 {
					diskInfo.UsagePercent = float64(usage.Used) / float64(usage.Total) * 100
				}
				log.Printf("[磁盘列表] 驱动器 %s 使用情况 - 总计: %d MB, 已用: %d MB, 可用: %d MB, 使用率: %.2f%%",
					drive, usage.Total/(1024*1024), usage.Used/(1024*1024), usage.Free/(1024*1024), diskInfo.UsagePercent)
			} else {
				log.Printf("[磁盘列表] 获取驱动器 %s 使用情况失败: %v", drive, err)
			}
			// 获取文件系统类型
			if fsType, err := getWindowsFileSystemType(drive); err == nil {
				diskInfo.FileSystem = fsType
				log.Printf("[磁盘列表] 驱动器 %s 文件系统: %s", drive, fsType)
			}

			// 获取磁盘标签
			if label, err := getWindowsDiskLabel(drive); err == nil {
				diskInfo.Label = label
				log.Printf("[磁盘列表] 驱动器 %s 标签: %s", drive, label)
			} else {
				log.Printf("[磁盘列表] 获取驱动器 %s 标签失败: %v", drive, err)
			}

			// 检查是否只读
			if IsReadOnly, err := isWindowsDriveReadOnly(drive); err == nil {
				diskInfo.IsReadOnly = IsReadOnly
				log.Printf("[磁盘列表] 驱动器 %s 只读状态: %v", drive, diskInfo.IsReadOnly)
			} else {
				log.Printf("[磁盘列表] 驱动器 %s 获取只读失败: %v", drive, err)
			}

		}

		// 设置图标
		if Icon, err := getWindowsDiskIcon(drive); err == nil {
			diskInfo.Icon = Icon
			log.Printf("[磁盘列表] 驱动器 %s 图标: %s", drive, diskInfo.Icon)
		} else {
			log.Printf("[磁盘列表] 驱动器 %s 图标失败: %s", drive, err)
		}

		resp.DiskInfos = append(resp.DiskInfos, diskInfo)
		log.Printf("[磁盘列表] 驱动器 %s 信息收集完成", drive)
	}

	return resp
}

// WindowsDiskUsage Windows磁盘使用情况
type WindowsDiskUsage struct {
	Total int64
	Used  int64
	Free  int64
}

// getWindowsDrives 获取Windows驱动器列表 (修复版)
func getWindowsDrives() ([]string, error) {
	// 创建一个缓冲区来接收驱动器字符串
	buffer := make([]uint16, 256)

	// 调用API
	length, err := windows.GetLogicalDriveStrings(uint32(len(buffer)), &buffer[0])
	if err != nil {
		return nil, fmt.Errorf("调用 GetLogicalDriveStrings 失败: %w", err)
	}
	if length == 0 {
		return nil, fmt.Errorf("未找到任何驱动器")
	}

	// 手动遍历缓冲区，正确解析由多个 null-terminated 字符串组成的列表
	var drives []string
	start := 0
	// 遍历返回的有效长度部分
	for i := 0; i < int(length); i++ {
		// 检查是否到达一个字符串的末尾 (即空字符)
		if buffer[i] == 0 {
			// 如果 start < i，说明我们找到了一个非空的字符串
			if i > start {
				// 提取这个字符串的切片并转换为 Go 字符串
				driveStr := windows.UTF16ToString(buffer[start:i])
				// 将 "C:\" 转换为 "C:/" 以保持格式一致
				drives = append(drives, strings.Replace(driveStr, `\`, `/`, -1))
			}
			// 更新下一个字符串的起始位置
			start = i + 1

			// 如果 start 已经越界或者指向的是最后的那个空字符，就可以退出了
			// 这是为了处理最后的那个 \x00\x00
			if start >= int(length) || buffer[start] == 0 {
				break
			}
		}
	}

	if len(drives) == 0 {
		// 理论上如果 length > 0，这里不会执行，但作为安全检查保留
		return nil, fmt.Errorf("未找到任何驱动器")
	}

	return drives, nil
}

// getWindowsDiskUsage 获取Windows磁盘使用情况
func getWindowsDiskUsage(drive string) (*WindowsDiskUsage, error) {
	log.Printf("[磁盘使用] 开始获取驱动器 %s 的使用情况", drive)
	// 使用gopsutil库获取磁盘使用情况，模仿服务器端InitDisk函数的实现
	usage, err := disk.Usage(drive)
	if err != nil {
		log.Printf("[磁盘使用] 获取驱动器 %s 使用情况失败: %v", drive, err)
		return nil, fmt.Errorf("获取磁盘使用情况失败: %v", err)
	}

	log.Printf("[磁盘使用] 驱动器 %s gopsutil返回数据 - Total: %d, Used: %d, Free: %d, UsedPercent: %.2f",
		drive, usage.Total, usage.Used, usage.Free, usage.UsedPercent)

	result := &WindowsDiskUsage{
		Total: int64(usage.Total),
		Used:  int64(usage.Used),
		Free:  int64(usage.Free),
	}

	log.Printf("[磁盘使用] 驱动器 %s 最终结果 - Total: %d, Used: %d, Free: %d",
		drive, result.Total, result.Used, result.Free)

	return result, nil
}

func getWindowsFileSystemType(drive string) (string, error) {
	// 为API调用准备参数
	// 确保路径以空字符结尾
	drivePtr, err := windows.UTF16PtrFromString(drive)
	if err != nil {
		return "", err
	}

	// 创建用于接收文件系统名称的缓冲区
	fsNameBuffer := make([]uint16, windows.MAX_PATH+1)

	// 调用GetVolumeInformationW API
	err = windows.GetVolumeInformation(
		drivePtr,
		nil, // 不获取卷标
		0,
		nil, // 不获取序列号
		nil, // 不获取最大组件长度
		nil, // 不获取文件系统标志
		&fsNameBuffer[0],
		uint32(len(fsNameBuffer)),
	)

	if err != nil {
		// 将系统错误转换为更具体的错误信息
		if err == syscall.ERROR_FILE_NOT_FOUND {
			return "", fmt.Errorf("驱动器 %s 未找到", drive)
		}
		return "", fmt.Errorf("获取文件系统类型失败: %w", err)
	}

	// 将返回的UTF-16字符串转换为Go字符串
	fsName := windows.UTF16ToString(fsNameBuffer)
	return fsName, nil
}

// getWindowsDiskLabel 获取Windows磁盘标签
// drive 参数示例: "C:/"
func getWindowsDiskLabel(drive string) (string, error) {
	// 为API调用准备参数
	// 确保路径以空字符结尾
	drivePtr, err := windows.UTF16PtrFromString(drive)
	if err != nil {
		return "", err
	}

	// 创建用于接收卷标的缓冲区
	labelBuffer := make([]uint16, windows.MAX_PATH+1)

	// 调用GetVolumeInformationW API. [1, 2]
	err = windows.GetVolumeInformation(
		drivePtr,
		&labelBuffer[0],
		uint32(len(labelBuffer)),
		nil, // 不获取序列号
		nil, // 不获取最大组件长度
		nil, // 不获取文件系统标志
		nil, // 不获取文件系统名称
		0,
	)

	if err != nil {
		if err == syscall.ERROR_FILE_NOT_FOUND {
			return "", fmt.Errorf("驱动器 %s 未找到", drive)
		}
		return "", fmt.Errorf("获取磁盘标签失败: %w", err)
	}

	// 将返回的UTF-16字符串转换为Go字符串
	label := windows.UTF16ToString(labelBuffer)

	// 如果标签为空，则返回默认格式的标签
	if label == "" {
		driveLetter := strings.TrimSuffix(drive, `:/`)
		driveLetter = strings.TrimSuffix(driveLetter, `:\`)
		return fmt.Sprintf("本地磁盘 (%s:)", driveLetter), nil
	}

	return label, nil
}

// isWindowsDriveReadOnly 检查Windows驱动器是否只读
func isWindowsDriveReadOnly(drive string) (bool, error) {
	// 确保路径以空字符结尾
	drivePtr, err := windows.UTF16PtrFromString(drive)
	if err != nil {
		return false, err
	}

	var fsFlags uint32

	// 调用 GetVolumeInformationW，我们只关心文件系统标志
	err = windows.GetVolumeInformation(
		drivePtr,
		nil,
		0,
		nil,
		nil,
		&fsFlags, // 接收文件系统标志
		nil,
		0,
	)

	if err != nil {
		return false, fmt.Errorf("获取卷信息失败: %w", err)
	}

	// 检查返回的标志中是否包含只读标志. [2]
	// windows.FILE_READ_ONLY_VOLUME 是一个常量，表示卷是只读的
	isReadOnly := (fsFlags & windows.FILE_READ_ONLY_VOLUME) != 0

	return isReadOnly, nil
}

// getWindowsDiskIcon 获取Windows磁盘图标类型 (优化版)
// drive 参数示例: "C:/"
func getWindowsDiskIcon(drive string) (string, error) {
	// 1. 标准化输入并提取盘符
	if len(drive) < 2 {
		return "unknown", fmt.Errorf("无效的驱动器格式: %s", drive)
	}
	driveLetter := strings.ToUpper(drive[0:1])
	driveRootPath := driveLetter + `:\`

	// 2. 优先检查是否为系统驱动器 (最可靠的方法)
	sysRoot := os.Getenv("SystemRoot")
	if sysRoot != "" && strings.HasPrefix(strings.ToUpper(sysRoot), driveLetter) {
		return "system", nil
	}

	// 3. 调用 GetDriveTypeW API 获取驱动器类型. [1]
	// API 需要一个以 null 结尾的 UTF-16 字符串指针
	rootPtr, err := windows.UTF16PtrFromString(driveRootPath)
	if err != nil {
		return "unknown", fmt.Errorf("转换路径失败: %w", err)
	}
	driveType := windows.GetDriveType(rootPtr)

	// 4. 根据返回的类型映射到图标名称
	switch driveType {
	case windows.DRIVE_FIXED:
		return "disk", nil // 固定硬盘 (HDD/SSD)
	case windows.DRIVE_REMOVABLE:
		// 检查是否为传统的软盘驱动器
		if driveLetter == "A" || driveLetter == "B" {
			return "floppy", nil
		}
		return "removable", nil // 其他可移动设备 (USB驱动器等)
	case windows.DRIVE_CDROM:
		return "cdrom", nil // CD/DVD/蓝光驱动器
	case windows.DRIVE_REMOTE:
		return "network", nil // 网络驱动器
	case windows.DRIVE_RAMDISK:
		return "ramdisk", nil // 内存盘
	case windows.DRIVE_NO_ROOT_DIR:
		return "unknown", fmt.Errorf("驱动器 %s 不存在或未就绪", drive)
	case windows.DRIVE_UNKNOWN:
		fallthrough
	default:
		return "unknown", nil
	}
}
