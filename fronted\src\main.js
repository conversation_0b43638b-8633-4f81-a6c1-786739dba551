import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import { message } from 'ant-design-vue';

// 导入全局样式
import './assets/styles/global.scss';

// 导入服务器配置工具
import { fetchServerConfig } from './utils/serverConfig';

// 创建应用实例
const app = createApp(App)

// 初始化应用
async function initApp() {
  try {
    // 获取服务器配置
    const serverConfig = await fetchServerConfig();
    
    // 注册全局属性
    app.config.globalProperties.$baseUrl = serverConfig.apiBaseUrl;
    app.config.globalProperties.$baseApi = serverConfig.apiBaseUrl;
    app.config.globalProperties.$wsBaseUrl = serverConfig.wsBaseUrl;
    app.config.globalProperties.$serverConfig = serverConfig;
    
    // 使用插件
    app.use(router);
    app.use(Antd);
    
    // 等待路由准备就绪
    await router.isReady();
    console.log('应用已准备就绪');
    
    // 挂载应用
    app.mount('#app');
  } catch (error) {
    console.error('应用初始化失败:', error);
    message.error('应用初始化失败，请刷新页面重试');
  }
}

// 启动应用
initApp();