<template>
  <div class="code-editor">
    <!-- 左侧文件树 -->
    <div class="sidebar" :style="{ width: sidebarWidth + 'px' }">
      <div class="sidebar-header">
        <span class="sidebar-title">文件资源管理器</span>
        <div class="sidebar-actions">
          <a-button type="text" size="small" @click="refreshFileTree">
            <template #icon><ReloadOutlined /></template>
          </a-button>
          <a-button type="text" size="small" @click="showCreateFileDialog" title="新建文件">
            <template #icon><FileAddOutlined /></template>
          </a-button>
          <a-button type="text" size="small" @click="showCreateFolderDialog" title="新建文件夹">
            <template #icon><FolderAddOutlined /></template>
          </a-button>
        </div>
      </div>
      <div class="file-tree">
        <FileTree
          :key="updateCounter"
          :client-id="clientId"
          :current-path="currentPath"
          :file-list="fileList"
          :depth="0"
          :creating-item="creatingItem"
          @file-select="handleFileSelect"
          @folder-expand="handleFolderExpand"
          @context-menu="handleContextMenu"
          @create-item="handleCreateItem"
          @rename-item="handleRenameItem"
          @cancel-create="cancelCreate"
          @remove-edit-item="removeEditItem"
        />
      </div>
    </div>

    <!-- 分割线 -->
    <div 
      class="resize-handle"
      @mousedown="startResize"
    ></div>

    <!-- 右侧主编辑区域 -->
    <div class="main-editor">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <a-button type="text" size="small" @click="saveCurrentFile" :disabled="!activeTab">
            <template #icon><SaveOutlined /></template>
            保存
          </a-button>
          <a-button type="text" size="small" @click="saveAllFiles">
            <template #icon><SaveOutlined /></template>
            全部保存
          </a-button>
          <a-divider type="vertical" />
          <a-button type="text" size="small" @click="showSearchDialog">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button type="text" size="small" @click="showReplaceDialog">
            <template #icon><SwapOutlined /></template>
            替换
          </a-button>
          <a-button type="text" size="small" @click="showGoToLineDialog">
            <template #icon><NumberOutlined /></template>
            跳转行
          </a-button>
          <a-divider type="vertical" />
          <a-button type="text" size="small" @click="refreshFileTree">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
        <div class="toolbar-right">
          <span class="font-size-label">字体大小:</span>
          <a-input-number 
            v-model:value="fontSize" 
            :min="10" 
            :max="24" 
            size="small" 
            style="width: 70px; margin-left: 8px;"
            @change="handleFontSizeChange"
          />
        </div>
      </div>

      <!-- 标签页 -->
      <div class="tab-bar">
        <div class="tabs-container">
          <transition-group name="tab" tag="div" class="tabs-wrapper">
            <div
              v-for="tab in openTabs"
              :key="tab.id"
              :class="['tab', { active: tab.id === activeTabId }]"
              @click="switchTab(tab.id)"
              @contextmenu.prevent="showTabContextMenu($event, tab)"
            >
              <span class="tab-icon">
                <component :is="getFileIcon(tab.name)" />
              </span>
              <span class="tab-name">{{ tab.name }}</span>
              <span
                class="tab-close"
                @click.stop="closeTab(tab.id)"
              >
                <CloseOutlined />
              </span>
              <span v-if="tab.modified" class="tab-modified"></span>
            </div>
          </transition-group>
        </div>
        <div class="tab-actions">
          <a-dropdown :trigger="['click']">
            <a-button type="text" size="small">
              <template #icon><MoreOutlined /></template>
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="closeAllTabs">关闭所有标签页</a-menu-item>
                <a-menu-item @click="closeOtherTabs">关闭其他标签页</a-menu-item>
                <a-menu-item @click="closeTabsToRight">关闭右侧标签页</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>

      <!-- 编辑器内容 -->
      <div class="editor-content">
        <div v-if="activeTab" class="editor-wrapper">
          <MonacoEditor
            ref="monacoEditorRef"
            v-model="activeTab.content"
            :language="getLanguageFromExtension(activeTab.name)"
            height="100%"
            theme="custom-dark"
            :options="editorOptions"
            @change="handleContentChange"
            @cursor-position-change="handleCursorPositionChange"
            class="monaco-editor"
          />
        </div>
        <div v-else class="welcome-screen">
          <div class="welcome-content">
            <div class="welcome-header">
              <h2>开始编程</h2>
              <p>选择一个操作来开始</p>
            </div>

            <div class="welcome-actions">
              <div class="action-section">
                <h3>创建</h3>
                <div class="action-buttons">
                  <button class="action-btn" @click="startCreateFile">
                    <FileAddOutlined />
                    <span>新建文件</span>
                    <small>创建一个新的文件</small>
                  </button>
                  <button class="action-btn" @click="startCreateFolder">
                    <FolderAddOutlined />
                    <span>新建文件夹</span>
                    <small>创建一个新的文件夹</small>
                  </button>
                </div>
              </div>

              <div class="action-section">
                <h3>最近文件</h3>
                <div class="recent-files-list" v-if="recentFiles.length > 0">
                  <div
                    v-for="file in recentFiles.slice(0, 5)"
                    :key="file.path"
                    class="recent-file-item"
                    @click="openRecentFile(file)"
                  >
                    <div class="file-icon">
                      <component :is="getFileIcon(file.name)" />
                    </div>
                    <div class="file-info">
                      <div class="file-name">{{ file.name }}</div>
                      <div class="file-path">{{ file.path }}</div>
                      <div class="file-time">{{ formatTime(file.lastOpened) }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="no-recent-files">
                  <p>暂无最近文件</p>
                  <small>从左侧文件树中打开文件后，这里会显示最近打开的文件</small>
                </div>
              </div>
            </div>

            <div class="welcome-tips">
              <p><strong>提示：</strong> 您也可以直接从左侧文件树中点击文件来打开它们</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部状态栏 -->
      <StatusBar
        :current-file="activeTab"
        :cursor-position="cursorPosition"
        :encoding="encoding"
        :line-ending="lineEnding"
        :language="activeTab ? getLanguageFromExtension(activeTab.name) : ''"
        :tab-size="tabSize"
        @encoding-change="handleEncodingChange"
        @line-ending-change="handleLineEndingChange"
        @tab-size-change="handleTabSizeChange"
      />
    </div>
  </div>




</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, reactive, triggerRef } from 'vue';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  FileAddOutlined,
  FolderAddOutlined,
  CloseOutlined,
  MoreOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  SearchOutlined,
  SwapOutlined,
  NumberOutlined
} from '@ant-design/icons-vue';
import MonacoEditor from '@/components/common/MonacoEditor.vue';
import FileTree from './FileTree.vue';
import StatusBar from './StatusBar.vue';
import { fileApi, dirApi } from '@/api';
import { buildPath } from '../fileUtils.js';

// Props
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  initialPath: {
    type: String,
    default: '/'
  },
  initialFile: {
    type: Object,
    default: null
  },
  initialFilePath: {
    type: String,
    default: ''
  },
  rootPath: {
    type: String,
    default: '/'
  }
});

// 响应式数据
const sidebarWidth = ref(250);
const currentPath = ref(props.initialPath);
const fileList = ref([]);
const openTabs = ref([]);
const activeTabId = ref(null);
const cursorPosition = ref({ line: 1, column: 1 });
const encoding = ref('UTF-8');
const lineEnding = ref('LF');
const tabSize = ref(4);
const isResizing = ref(false);
const fontSize = ref(14);



// Monaco Editor引用
const monacoEditorRef = ref(null);

// 强制更新机制
const updateCounter = ref(0);
const forceUpdate = (reason = '') => {
  console.log('强制更新触发:', reason);
  updateCounter.value++;
  triggerRef(fileList);
  nextTick();
};

// 内联创建状态
const creatingItem = ref(null); // { type: 'file'|'folder', parentPath: string, name: string }

// 最近文件状态
const recentFiles = ref([]);

// 计算属性
const activeTab = computed(() => {
  return openTabs.value.find(tab => tab.id === activeTabId.value);
});

const editorHeight = computed(() => {
  return 'calc(100% - 70px)'; // 减去标签页和状态栏高度
});

const editorOptions = computed(() => ({
  automaticLayout: true,
  fontSize: fontSize.value,
  fontFamily: 'JetBrains Mono, Monaco, Menlo, Ubuntu Mono, monospace',
  lineHeight: 1.6,
  minimap: { enabled: false },
  scrollBeyondLastLine: false,
  wordWrap: 'on',
  lineNumbers: 'on',
  glyphMargin: false,
  folding: true,
  tabSize: tabSize.value,
  insertSpaces: true,
  detectIndentation: true,
  theme: 'custom-dark',
  colorDecorators: true,
  renderLineHighlight: 'all',
  renderWhitespace: 'selection',
  cursorBlinking: 'blink',
  cursorSmoothCaretAnimation: true
}));

// 方法
const refreshFileTree = async () => {
  // 重新初始化文件树
  await initializeFileTree();
};

const handleFileSelect = async (file) => {
  console.log('文件选择:', file);

  // 只处理文件，目录由handleFolderExpand处理
  if (file.type === 'directory') {
    console.log('这是一个目录，应该由handleFolderExpand处理');
    return;
  }

  // 使用文件的完整路径
  const filePath = file.path;

  // 检查文件是否已经打开
  const existingTab = openTabs.value.find(tab => tab.path === filePath);
  if (existingTab) {
    activeTabId.value = existingTab.id;
    console.log('文件已打开，切换到现有标签页');
    return;
  }

  // 加载文件内容
  try {
    console.log(`正在读取文件: ${filePath}`);
    const res = await fileApi.readFileContent(props.clientId, {
      path: filePath
    });

    console.log('文件API响应:', res);

    if (res.code === 200) {
      // 检查多种可能的数据结构
      let content = '';
      const data = res.data;

      if (typeof data === 'string') {
        content = data;
      } else if (data && typeof data.content === 'string') {
        content = data.content;
      } else if (data && data.data && typeof data.data.content === 'string') {
        content = data.data.content;
      } else if (data && data.data && typeof data.data === 'string') {
        content = data.data;
      } else {
        console.warn('未知的文件内容格式:', data);
        content = '';
      }

      console.log(`文件 ${file.name} 内容长度: ${content.length}`);

      const newTab = {
        id: Date.now().toString(),
        name: file.name,
        path: filePath,
        content: content,
        modified: false,
        originalContent: content
      };
      openTabs.value.push(newTab);
      activeTabId.value = newTab.id;

      // 添加到最近文件
      addToRecentFiles(file);

      console.log('新标签页创建成功，不触发forceUpdate');
      // 注意：这里不调用forceUpdate()，避免影响文件夹展开状态
    } else {
      console.error('读取文件失败:', res);
      message.error('读取文件内容失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('打开文件失败:', error);
    message.error('打开文件失败: ' + (error.message || '网络错误'));
  }
};

const handleFolderExpand = async (folder) => {
  console.log('文件夹展开:', folder);

  // 使用nextTick确保DOM更新
  await nextTick();

  // 切换展开状态
  const wasExpanded = folder.expanded;

  if (wasExpanded) {
    // 收起文件夹 - 使用Vue的响应式更新
    folder.expanded = false;

    // 强制触发响应式更新
    forceUpdate(`文件夹收起: ${folder.name}`);

    console.log(`文件夹 ${folder.name} 已收起`);
    return;
  }

  // 展开文件夹 - 使用Vue的响应式更新
  folder.expanded = true;

  // 强制触发响应式更新
  forceUpdate(`文件夹展开: ${folder.name}`);

  console.log(`文件夹 ${folder.name} 正在展开`);

  // 如果已经有子内容，直接返回
  if (folder.children && folder.children.length > 0) {
    console.log(`文件夹 ${folder.name} 已有 ${folder.children.length} 个子项，直接显示`);
    // 强制触发响应式更新
    await nextTick();
    return;
  }

  // 展开文件夹，加载子目录内容
  try {
    console.log(`正在加载文件夹内容: ${folder.path}`);
    const res = await dirApi.listDir(props.clientId, {
      path: folder.path,
      show_hidden: false,
      page: 1,
      page_size: 1000, // 获取所有文件
      sort_by: 'name',
      sort_order: 'asc'
    });

    console.log('目录API响应:', res);
    console.log('目录API响应数据结构:', JSON.stringify(res.data, null, 2));

    if (res.code === 200) {
      const data = res.data;

      // 检查多种可能的数据结构
      let fileInfos = [];
      let errorMsg = "";

      // 检查错误信息
      if (data.error) {
        errorMsg = data.error;
      } else if (data.data && data.data.error) {
        errorMsg = data.data.error;
      }

      // 如果有错误，显示错误信息
      if (errorMsg && errorMsg !== "") {
        message.error('获取目录内容失败: ' + errorMsg);
        folder.expanded = false;
        return;
      }

      // 解析文件列表
      if (data.file_info_responses) {
        fileInfos = data.file_info_responses;
      } else if (data.data && data.data.file_info_responses) {
        fileInfos = data.data.file_info_responses;
      } else if (Array.isArray(data.data)) {
        fileInfos = data.data;
      } else if (Array.isArray(data)) {
        fileInfos = data;
      }

      console.log('解析出的文件信息:', fileInfos);

      // 转换为文件树节点 - 使用reactive确保深层响应式
      const children = fileInfos.map(file => reactive({
        name: file.name,
        path: buildPath(folder.path, file.name),
        type: file.is_dir ? 'directory' : 'file',
        size: file.size || 0,
        mod_time: file.mod_time,
        is_dir: file.is_dir,
        permissions: file.permissions,
        owner: file.owner,
        group: file.group,
        expanded: false,
        children: null
      }));

      // 使用响应式赋值并强制更新
      folder.children = children;

      // 强制触发响应式更新
      forceUpdate(`文件夹加载子项: ${folder.name}`);

      console.log(`文件夹 ${folder.name} 加载了 ${children.length} 个子项:`, children.map(c => c.name));
      console.log(`文件夹 ${folder.name} 展开状态: ${folder.expanded}`);
    } else {
      message.error('获取目录内容失败: ' + (res.message || '未知错误'));
      folder.expanded = false; // 加载失败时收起
    }
  } catch (error) {
    console.error('展开文件夹失败:', error);
    message.error('展开文件夹失败: ' + (error.message || '网络错误'));
    folder.expanded = false; // 加载失败时收起
  }
};

const handleContextMenu = (event, file) => {
  // 处理右键菜单
};

const switchTab = (tabId) => {
  activeTabId.value = tabId;
};

const closeTab = async (tabId) => {
  const tabIndex = openTabs.value.findIndex(tab => tab.id === tabId);
  if (tabIndex === -1) return;

  const tab = openTabs.value[tabIndex];
  
  // 检查是否有未保存的更改
  if (tab.modified) {
    // 这里可以添加保存确认对话框
  }

  openTabs.value.splice(tabIndex, 1);
  
  // 如果关闭的是当前活动标签页，切换到其他标签页
  if (activeTabId.value === tabId) {
    if (openTabs.value.length > 0) {
      const newActiveIndex = Math.min(tabIndex, openTabs.value.length - 1);
      activeTabId.value = openTabs.value[newActiveIndex].id;
    } else {
      activeTabId.value = null;
    }
  }
};

const closeAllTabs = () => {
  openTabs.value = [];
  activeTabId.value = null;
};

const closeOtherTabs = () => {
  if (activeTab.value) {
    openTabs.value = [activeTab.value];
  }
};

const closeTabsToRight = () => {
  const activeIndex = openTabs.value.findIndex(tab => tab.id === activeTabId.value);
  if (activeIndex !== -1) {
    openTabs.value = openTabs.value.slice(0, activeIndex + 1);
  }
};

const handleContentChange = (content) => {
  if (activeTab.value) {
    activeTab.value.content = content;
    activeTab.value.modified = content !== activeTab.value.originalContent;
  }
};

const handleCursorPositionChange = (position) => {
  cursorPosition.value = position;
};



const handleCreateFile = async () => {
  if (!newFileName.value.trim()) {
    message.error('请输入文件名');
    return;
  }

  try {
    const filePath = buildPath(currentPath.value, newFileName.value);
    const res = await fileApi.createFile(props.clientId, {
      path: filePath,
      content: '',
      encoding: 'utf-8',
      force_create: true,
      create_dirs: true
    });

    if (res.code === 200) {
      message.success('文件创建成功');
      createFileVisible.value = false;
      await refreshFileTree();

      // 自动打开新创建的文件
      const newFile = {
        name: newFileName.value,
        path: filePath,
        type: 'file'
      };
      await handleFileSelect(newFile);
    } else {
      message.error(res.message || '创建文件失败');
    }
  } catch (error) {
    console.error('创建文件失败:', error);
    message.error('创建文件失败: ' + (error.message || '网络错误'));
  }
};

const handleCreateFolder = async () => {
  if (!newFolderName.value.trim()) {
    message.error('请输入文件夹名');
    return;
  }

  try {
    const folderPath = buildPath(currentPath.value, newFolderName.value);
    const res = await dirApi.createDir(props.clientId, {
      path: folderPath
    });

    if (res.code === 200) {
      message.success('文件夹创建成功');
      createFolderVisible.value = false;
      await refreshFileTree();
    } else {
      message.error(res.message || '创建文件夹失败');
    }
  } catch (error) {
    console.error('创建文件夹失败:', error);
    message.error('创建文件夹失败: ' + (error.message || '网络错误'));
  }
};

const handleCreateItem = async (data) => {
  try {
    if (data.type === 'file') {
      const filePath = buildPath(data.path, data.name);
      const res = await fileApi.createFile(props.clientId, {
        path: filePath,
        content: '',
        encoding: 'utf-8',
        force_create: true,
        create_dirs: true
      });

      if (res.code === 200) {
        message.success('文件创建成功');
        await refreshFileTree();

        // 自动打开新创建的文件
        const newFile = {
          name: data.name,
          path: filePath,
          type: 'file'
        };
        await handleFileSelect(newFile);
      } else {
        message.error(res.message || '创建文件失败');
      }
    } else if (data.type === 'directory') {
      const folderPath = buildPath(data.path, data.name);
      const res = await dirApi.createDir(props.clientId, {
        path: folderPath
      });

      if (res.code === 200) {
        message.success('文件夹创建成功');
        await refreshFileTree();
      } else {
        message.error(res.message || '创建文件夹失败');
      }
    }
  } catch (error) {
    console.error('创建失败:', error);
    message.error('创建失败: ' + (error.message || '网络错误'));
  }
};

const handleRenameItem = async (data) => {
  try {
    const oldPath = buildPath(data.path, data.oldName);
    const newPath = buildPath(data.path, data.newName);

    if (data.type === 'file') {
      const res = await fileApi.moveFile(props.clientId, {
        sourcePath: oldPath,
        targetPath: newPath
      });

      if (res.code === 200) {
        message.success('文件重命名成功');
        await refreshFileTree();
      } else {
        message.error(res.message || '文件重命名失败');
      }
    } else if (data.type === 'directory') {
      const res = await dirApi.moveDir(props.clientId, {
        sourcePath: oldPath,
        targetPath: newPath
      });

      if (res.code === 200) {
        message.success('文件夹重命名成功');
        await refreshFileTree();
      } else {
        message.error(res.message || '文件夹重命名失败');
      }
    }
  } catch (error) {
    console.error('重命名失败:', error);
    message.error('重命名失败: ' + (error.message || '网络错误'));
  }
};

// 初始化文件树（从磁盘列表开始）
const initializeFileTree = async () => {
  try {
    // 获取磁盘列表作为根目录
    const res = await dirApi.listDisks(props.clientId, { includeDetails: true });
    console.log('磁盘API响应:', res);

    if (res.code === 200) {
      // 检查多种可能的数据结构
      let diskInfos = [];
      const data = res.data;

      if (data?.disk_infos) {
        diskInfos = data.disk_infos;
      } else if (data?.data?.disk_infos) {
        diskInfos = data.data.disk_infos;
      } else if (Array.isArray(data)) {
        diskInfos = data;
      } else if (Array.isArray(data?.data)) {
        diskInfos = data.data;
      }

      console.log('解析的磁盘数据:', diskInfos);

      if (!diskInfos || diskInfos.length === 0) {
        console.warn('磁盘列表为空，回退到根目录模式');
        // 回退到根目录模式
        currentPath.value = props.rootPath || '/';
        await loadDirectoryContent(currentPath.value);
        return;
      }

      // 将磁盘转换为文件树节点 - 使用reactive确保深层响应式
      fileList.value = diskInfos.map(disk => reactive({
        name: disk.label || disk.mount_point || disk.device || 'Unknown Drive',
        path: disk.mount_point || disk.path || '/',
        type: 'directory',
        size: disk.total_size || disk.size || 0,
        mod_time: new Date().toISOString(),
        is_dir: true,
        expanded: false,
        children: null,
        isDisk: true, // 标记为磁盘节点
        diskInfo: disk // 保存完整磁盘信息
      }));

      // 设置当前路径为第一个磁盘
      if (diskInfos.length > 0) {
        currentPath.value = diskInfos[0].mount_point || diskInfos[0].path || '/';
      }
    } else {
      console.error('获取磁盘列表失败:', res);
      // 回退到根目录模式
      currentPath.value = props.rootPath || '/';
      await loadDirectoryContent(currentPath.value);
    }
  } catch (error) {
    console.error('初始化文件树失败:', error);
    // 回退到根目录模式
    currentPath.value = props.rootPath || '/';
    await loadDirectoryContent(currentPath.value);
  }
};





// 加载目录内容（回退模式）
const loadDirectoryContent = async (path) => {
  try {
    const res = await dirApi.listDir(props.clientId, {
      path: path,
      show_hidden: false,
      page: 1,
      page_size: 1000,
      sort_by: 'name',
      sort_order: 'asc'
    });

    if (res.code === 200) {
      const data = res.data;
      console.log('回退模式目录API响应数据:', JSON.stringify(data, null, 2));

      // 检查多种可能的数据结构
      let fileInfos = [];
      let errorMsg = "";

      // 检查错误信息
      if (data.error) {
        errorMsg = data.error;
      } else if (data.data && data.data.error) {
        errorMsg = data.data.error;
      }

      // 如果有错误，显示错误信息
      if (errorMsg && errorMsg !== "") {
        message.error('获取目录内容失败: ' + errorMsg);
        return;
      }

      // 解析文件列表
      if (data.file_info_responses) {
        fileInfos = data.file_info_responses;
      } else if (data.data && data.data.file_info_responses) {
        fileInfos = data.data.file_info_responses;
      } else if (Array.isArray(data.data)) {
        fileInfos = data.data;
      } else if (Array.isArray(data)) {
        fileInfos = data;
      }

      console.log('回退模式解析出的文件信息:', fileInfos);

      // 转换为文件树节点 - 使用reactive确保深层响应式
      fileList.value = fileInfos.map(file => reactive({
        name: file.name,
        path: buildPath(path, file.name),
        type: file.is_dir ? 'directory' : 'file',
        size: file.size || 0,
        mod_time: file.mod_time,
        is_dir: file.is_dir,
        permissions: file.permissions,
        owner: file.owner,
        group: file.group,
        expanded: false,
        children: null
      }));

      console.log(`加载目录 ${path}，共 ${fileList.value.length} 个项目`);
    } else {
      message.error('获取目录内容失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('加载目录内容失败:', error);
    message.error('加载目录内容失败: ' + (error.message || '网络错误'));
  }
};

// 内联创建文件/文件夹
const showCreateFileDialog = () => {
  // 确定创建位置 - 如果有展开的文件夹，在其中创建；否则在根目录创建
  let targetPath = currentPath.value;

  // 查找第一个展开的文件夹作为创建位置
  const findExpandedFolder = (items) => {
    for (const item of items) {
      if (item.type === 'directory' && item.expanded && item.children) {
        // 递归查找更深层的展开文件夹
        const deeper = findExpandedFolder(item.children);
        return deeper || item.path;
      }
    }
    return null;
  };

  const expandedPath = findExpandedFolder(fileList.value);
  if (expandedPath) {
    targetPath = expandedPath;
  }

  console.log('创建文件位置:', targetPath);

  // 在文件列表中添加一个新的编辑项
  const newFileItem = reactive({
    id: `new-file-${Date.now()}`,
    name: '',
    type: 'file',
    isEditing: true,
    isNew: true,
    path: targetPath,
    expanded: false,
    children: null
  });

  // 将新项添加到正确的位置
  if (expandedPath) {
    // 找到对应的文件夹并添加到其children中
    const addToFolder = (items) => {
      for (const item of items) {
        if (item.type === 'directory' && item.path === expandedPath && item.children) {
          item.children.push(newFileItem);
          return true;
        }
        if (item.children && addToFolder(item.children)) {
          return true;
        }
      }
      return false;
    };

    if (!addToFolder(fileList.value)) {
      // 如果没找到，添加到根目录
      fileList.value.push(newFileItem);
    }
  } else {
    // 添加到根目录
    fileList.value.push(newFileItem);
  }

  forceUpdate('添加新文件项');
};

const showCreateFolderDialog = () => {
  // 确定创建位置 - 如果有展开的文件夹，在其中创建；否则在根目录创建
  let targetPath = currentPath.value;

  // 查找第一个展开的文件夹作为创建位置
  const findExpandedFolder = (items) => {
    for (const item of items) {
      if (item.type === 'directory' && item.expanded && item.children) {
        // 递归查找更深层的展开文件夹
        const deeper = findExpandedFolder(item.children);
        return deeper || item.path;
      }
    }
    return null;
  };

  const expandedPath = findExpandedFolder(fileList.value);
  if (expandedPath) {
    targetPath = expandedPath;
  }

  console.log('创建文件夹位置:', targetPath);

  // 在文件列表中添加一个新的编辑项
  const newFolderItem = reactive({
    id: `new-folder-${Date.now()}`,
    name: '',
    type: 'directory',
    isEditing: true,
    isNew: true,
    path: targetPath,
    expanded: false,
    children: null
  });

  // 将新项添加到正确的位置
  if (expandedPath) {
    // 找到对应的文件夹并添加到其children中
    const addToFolder = (items) => {
      for (const item of items) {
        if (item.type === 'directory' && item.path === expandedPath && item.children) {
          item.children.push(newFolderItem);
          return true;
        }
        if (item.children && addToFolder(item.children)) {
          return true;
        }
      }
      return false;
    };

    if (!addToFolder(fileList.value)) {
      // 如果没找到，添加到根目录
      fileList.value.push(newFolderItem);
    }
  } else {
    // 添加到根目录
    fileList.value.push(newFolderItem);
  }

  forceUpdate('添加新文件夹项');
};

// 欢迎屏幕的创建方法
const startCreateFile = () => {
  showCreateFileDialog();
};

const startCreateFolder = () => {
  showCreateFolderDialog();
};

const cancelCreate = () => {
  // 移除正在创建的项目
  fileList.value = fileList.value.filter(item => !item.isNew);
  forceUpdate('取消创建');
};

// 移除编辑项
const removeEditItem = (itemId) => {
  console.log('移除编辑项:', itemId);

  // 递归移除函数
  const removeFromList = (list) => {
    for (let i = list.length - 1; i >= 0; i--) {
      const item = list[i];
      if (item.id === itemId) {
        console.log('找到并移除项:', item.name);
        list.splice(i, 1);
        return true;
      }
      if (item.children && removeFromList(item.children)) {
        return true;
      }
    }
    return false;
  };

  if (removeFromList(fileList.value)) {
    forceUpdate('移除编辑项');
  } else {
    console.warn('未找到要移除的编辑项:', itemId);
  }
};

// 最近文件管理
const loadRecentFiles = () => {
  try {
    const stored = localStorage.getItem(`recentFiles_${props.clientId}`);
    if (stored) {
      recentFiles.value = JSON.parse(stored);
    }
  } catch (error) {
    console.error('加载最近文件失败:', error);
    recentFiles.value = [];
  }
};

const saveRecentFiles = () => {
  try {
    localStorage.setItem(`recentFiles_${props.clientId}`, JSON.stringify(recentFiles.value));
  } catch (error) {
    console.error('保存最近文件失败:', error);
  }
};

const addToRecentFiles = (file) => {
  const fileInfo = {
    name: file.name,
    path: file.path,
    lastOpened: new Date().toISOString()
  };

  // 移除已存在的相同文件
  recentFiles.value = recentFiles.value.filter(f => f.path !== file.path);

  // 添加到开头
  recentFiles.value.unshift(fileInfo);

  // 限制最多保存10个最近文件
  if (recentFiles.value.length > 10) {
    recentFiles.value = recentFiles.value.slice(0, 10);
  }

  saveRecentFiles();
};

const openRecentFile = async (fileInfo) => {
  try {
    // 检查文件是否已经打开
    const existingTab = openTabs.value.find(tab => tab.path === fileInfo.path);
    if (existingTab) {
      activeTabId.value = existingTab.id;
      return;
    }

    // 加载文件内容
    const res = await fileApi.readFileContent(props.clientId, {
      path: fileInfo.path
    });

    if (res.code === 200) {
      let content = '';
      const data = res.data;

      if (typeof data === 'string') {
        content = data;
      } else if (data && typeof data.content === 'string') {
        content = data.content;
      } else if (data && data.data && typeof data.data.content === 'string') {
        content = data.data.content;
      } else if (data && data.data && typeof data.data === 'string') {
        content = data.data;
      }

      const newTab = {
        id: Date.now().toString(),
        name: fileInfo.name,
        path: fileInfo.path,
        content: content,
        modified: false,
        originalContent: content
      };
      openTabs.value.push(newTab);
      activeTabId.value = newTab.id;

      // 更新最近文件列表
      addToRecentFiles(fileInfo);
    } else {
      message.error('读取文件内容失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('打开最近文件失败:', error);
    message.error('打开文件失败: ' + (error.message || '网络错误'));
  }
};

const formatTime = (timeString) => {
  const time = new Date(timeString);
  const now = new Date();
  const diff = now - time;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else if (diff < 604800000) { // 1周内
    return `${Math.floor(diff / 86400000)}天前`;
  } else {
    return time.toLocaleDateString();
  }
};

const openFile = () => {
  // 打开文件对话框逻辑
};

// 工具栏方法
const saveCurrentFile = async () => {
  if (!activeTab.value) return;

  try {
    const res = await fileApi.writeFileContent(props.clientId, {
      path: activeTab.value.path,
      content: activeTab.value.content,
      createDir: true,
      backup: false
    });

    if (res.code === 200) {
      // 标记文件为未修改状态
      activeTab.value.modified = false;
      message.success('文件保存成功');
    } else {
      message.error(res.message || '保存文件失败');
    }
  } catch (error) {
    console.error('保存文件失败:', error);
    message.error('保存文件失败: ' + (error.message || '网络错误'));
  }
};

const saveAllFiles = async () => {
  const modifiedTabs = openTabs.value.filter(tab => tab.modified);
  if (modifiedTabs.length === 0) {
    message.info('没有需要保存的文件');
    return;
  }

  let successCount = 0;
  let errorCount = 0;

  for (const tab of modifiedTabs) {
    try {
      const res = await fileApi.writeFileContent(props.clientId, {
        path: tab.path,
        content: tab.content,
        createDir: true,
        backup: false
      });

      if (res.code === 200) {
        tab.modified = false;
        successCount++;
      } else {
        errorCount++;
        console.error(`保存文件 ${tab.name} 失败:`, res.message);
      }
    } catch (error) {
      errorCount++;
      console.error(`保存文件 ${tab.name} 失败:`, error);
    }
  }

  if (errorCount === 0) {
    message.success(`成功保存 ${successCount} 个文件`);
  } else {
    message.warning(`保存完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
  }
};

const showSearchDialog = () => {
  if (!monacoEditorRef.value) {
    message.error('编辑器未准备就绪');
    return;
  }

  try {
    // 直接调用Monaco Editor的搜索功能
    const editor = monacoEditorRef.value.getEditor();
    if (editor) {
      editor.getAction('actions.find').run();
    }
  } catch (error) {
    console.error('打开搜索失败:', error);
    message.error('打开搜索失败');
  }
};

const showReplaceDialog = () => {
  if (!monacoEditorRef.value) {
    message.error('编辑器未准备就绪');
    return;
  }

  try {
    // 直接调用Monaco Editor的替换功能
    const editor = monacoEditorRef.value.getEditor();
    if (editor) {
      editor.getAction('editor.action.startFindReplaceAction').run();
    }
  } catch (error) {
    console.error('打开替换失败:', error);
    message.error('打开替换失败');
  }
};

const showGoToLineDialog = () => {
  if (!monacoEditorRef.value) {
    message.error('编辑器未准备就绪');
    return;
  }

  try {
    // 直接调用Monaco Editor的跳转行功能
    const editor = monacoEditorRef.value.getEditor();
    if (editor) {
      editor.getAction('editor.action.quickCommand').run();
    }
  } catch (error) {
    console.error('打开跳转行失败:', error);
    // 尝试备用方法
    try {
      const editor = monacoEditorRef.value.getEditor();
      if (editor) {
        editor.trigger('keyboard', 'editor.action.gotoLine', null);
      }
    } catch (fallbackError) {
      console.error('备用跳转行方法也失败:', fallbackError);
      message.error('跳转行功能暂不可用');
    }
  }
};



const handleFontSizeChange = (value) => {
  fontSize.value = value;
};

const getFileIcon = (filename) => {
  // 根据文件扩展名返回对应图标
  const ext = filename.split('.').pop()?.toLowerCase();
  // 这里可以返回不同的图标组件
  return 'FileOutlined';
};

const getLanguageFromExtension = (filename) => {
  const ext = filename.split('.').pop()?.toLowerCase();
  const languageMap = {
    // JavaScript/TypeScript
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'vue': 'vue',

    // Web技术
    'html': 'html',
    'htm': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'less': 'less',

    // 数据格式
    'json': 'json',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'toml': 'toml',
    'ini': 'ini',
    'cfg': 'ini',
    'conf': 'ini',

    // 编程语言
    'py': 'python',
    'java': 'java',
    'c': 'c',
    'cpp': 'cpp',
    'cc': 'cpp',
    'cxx': 'cpp',
    'h': 'c',
    'hpp': 'cpp',
    'cs': 'csharp',
    'php': 'php',
    'rb': 'ruby',
    'go': 'go',
    'rs': 'rust',
    'swift': 'swift',
    'kt': 'kotlin',
    'scala': 'scala',
    'clj': 'clojure',
    'hs': 'haskell',
    'ml': 'ocaml',
    'fs': 'fsharp',
    'pas': 'pascal',
    'pl': 'perl',
    'lua': 'lua',
    'r': 'r',
    'jl': 'julia',
    'dart': 'dart',

    // 脚本语言
    'sh': 'shell',
    'bash': 'shell',
    'zsh': 'shell',
    'fish': 'shell',
    'bat': 'bat',
    'cmd': 'bat',
    'ps1': 'powershell',
    'psm1': 'powershell',

    // 标记语言
    'md': 'markdown',
    'markdown': 'markdown',
    'rst': 'restructuredtext',
    'tex': 'latex',

    // 数据库
    'sql': 'sql',

    // 其他
    'dockerfile': 'dockerfile',
    'makefile': 'makefile',
    'txt': 'plaintext',
    'log': 'plaintext',
    'csv': 'csv',
    'tsv': 'csv',

    // 无扩展名的特殊文件
    'readme': 'markdown',
    'license': 'plaintext',
    'changelog': 'markdown',
    'authors': 'plaintext',
    'contributors': 'plaintext'
  };

  // 处理无扩展名的文件
  if (!ext) {
    const name = filename.toLowerCase();
    if (languageMap[name]) {
      return languageMap[name];
    }
  }

  return languageMap[ext] || 'plaintext';
};

const handleEncodingChange = (newEncoding) => {
  encoding.value = newEncoding;
};

const handleLineEndingChange = (newLineEnding) => {
  lineEnding.value = newLineEnding;
};

const handleTabSizeChange = (newTabSize) => {
  tabSize.value = newTabSize;
};

// 拖拽调整侧边栏宽度
const startResize = (event) => {
  isResizing.value = true;
  document.addEventListener('mousemove', handleResize, { passive: true });
  document.addEventListener('mouseup', stopResize, { passive: true });
  event.preventDefault();
};

const handleResize = (event) => {
  if (!isResizing.value) return;

  // 获取编辑器容器的位置
  const editorContainer = event.target.closest('.code-editor');
  if (!editorContainer) return;

  const containerRect = editorContainer.getBoundingClientRect();
  const newWidth = event.clientX - containerRect.left;

  // 限制侧边栏宽度在合理范围内
  const minWidth = 200;
  const maxWidth = Math.min(500, containerRect.width * 0.6); // 最大不超过容器宽度的60%

  if (newWidth >= minWidth && newWidth <= maxWidth) {
    sidebarWidth.value = newWidth;
  }
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
};

// 生命周期
onMounted(async () => {
  // 初始化文件树
  await initializeFileTree();

  // 加载最近文件
  loadRecentFiles();

  // 如果有初始文件，打开文件
  if (props.initialFile && props.initialFilePath) {
    await handleFileSelect(props.initialFile);
  }
});

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
});
</script>

<style scoped>
.code-editor {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  background: #2d2d30;
  color: #e8e8e8;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 6px;
  overflow: hidden;
  min-height: 75vh;
}

.sidebar {
  background: #383838;
  border-right: 1px solid #4a4a4a;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  max-width: 500px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #4a4a4a;
  background: #3c3c3c;
}

.sidebar-title {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #e8e8e8;
  letter-spacing: 0.5px;
}

.sidebar-actions {
  display: flex;
  gap: 4px;
}

.file-tree {
  flex: 1;
  overflow-y: auto;
  padding: 4px 0;
}

.resize-handle {
  width: 4px;
  background: transparent;
  cursor: col-resize;
  position: relative;
}

.resize-handle:hover {
  background: #007acc;
}

.main-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #2d2d30;
  min-width: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #3c3c3c;
  border-bottom: 1px solid #4a4a4a;
  padding: 8px 12px;
  min-height: 40px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  color: #e8e8e8;
  font-size: 12px;
}

.font-size-label {
  color: #e8e8e8;
  font-size: 12px;
}

.toolbar .ant-btn {
  color: #e8e8e8;
  border: none;
  background: transparent;
}

.toolbar .ant-btn:hover {
  background: #4a4a4a;
  color: #ffffff;
}

.toolbar .ant-btn:disabled {
  color: #808080;
}

.toolbar .ant-divider {
  border-color: #4a4a4a;
  margin: 0 8px;
}

.toolbar :deep(.ant-input-number) {
  background: #4a4a4a;
  border-color: #4a4a4a;
  color: #e8e8e8;
}

.toolbar :deep(.ant-input-number:hover) {
  border-color: #007acc;
}

.toolbar :deep(.ant-input-number:focus) {
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.toolbar :deep(.ant-input-number .ant-input-number-input) {
  background: transparent;
  color: #e8e8e8;
}

.toolbar :deep(.ant-input-number-handler) {
  background: #4a4a4a;
  border-color: #4a4a4a;
  color: #e8e8e8;
}

.toolbar :deep(.ant-input-number-handler:hover) {
  background: #5a5a5a;
  color: #ffffff;
}

.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #3c3c3c;
  border-bottom: 1px solid #4a4a4a;
  min-height: 35px;
}

.tabs-container {
  display: flex;
  flex: 1;
  overflow-x: auto;
}

.tabs-wrapper {
  display: flex;
  flex: 1;
}

/* 标签页动画 */
.tab-enter-active,
.tab-leave-active {
  transition: all 0.3s ease;
}

.tab-enter-from {
  opacity: 0;
  transform: translateX(-20px) scale(0.9);
}

.tab-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.tab-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.tab-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.9);
}

.tab-move {
  transition: transform 0.3s ease;
}

.tab {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #3c3c3c;
  border-right: 1px solid #4a4a4a;
  cursor: pointer;
  position: relative;
  min-width: 120px;
  max-width: 200px;
  transition: background-color 0.2s;
}

.tab:hover {
  background: #4a4a4a;
}

.tab.active {
  background: #2d2d30;
  border-bottom: 2px solid #007acc;
}

.tab-icon {
  margin-right: 6px;
  font-size: 14px;
}

.tab-name {
  flex: 1;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-close {
  margin-left: 6px;
  padding: 2px;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tab:hover .tab-close {
  opacity: 1;
}

.tab-close:hover {
  background: #5a5a5a;
}

.tab-modified {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #007acc;
}

.tab-actions {
  padding: 0 8px;
}

.editor-content {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.editor-wrapper {
  flex: 1;
  background: #2d2d30;
  display: flex;
  flex-direction: column;
}

.monaco-editor {
  background: #2d2d30 !important;
  flex: 1;
  height: 100% !important;
  width: 100% !important;
}

:deep(.monaco-editor-container) {
  height: 100% !important;
  width: 100% !important;
}

:deep(.monaco-editor .monaco-editor-background) {
  background: #2d2d30 !important;
}

:deep(.monaco-editor .margin) {
  background: #2d2d30 !important;
}

.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #2d2d30;
  color: #e8e8e8;
  padding: 40px;
}

.welcome-content {
  max-width: 600px;
  width: 100%;
}

.welcome-header {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-header h2 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 300;
  margin: 0 0 8px 0;
}

.welcome-header p {
  color: #cccccc;
  font-size: 16px;
  margin: 0;
}

.welcome-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.action-section h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  background: #3c3c3c;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  color: #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.action-btn:hover {
  background: #404040;
  border-color: #007acc;
  transform: translateY(-1px);
}

.action-btn .anticon {
  font-size: 20px;
  color: #007acc;
  margin-bottom: 8px;
}

.action-btn span {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.action-btn small {
  font-size: 12px;
  color: #cccccc;
  opacity: 0.8;
}

.welcome-tips {
  text-align: center;
  padding: 20px;
  background: rgba(0, 122, 204, 0.1);
  border: 1px solid rgba(0, 122, 204, 0.3);
  border-radius: 6px;
}

.welcome-tips p {
  margin: 0;
  color: #cccccc;
  font-size: 14px;
}

.welcome-tips strong {
  color: #007acc;
}

/* 最近文件列表样式 */
.recent-files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.recent-file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #3c3c3c;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recent-file-item:hover {
  background: #404040;
  border-color: #007acc;
  transform: translateY(-1px);
}

.recent-file-item .file-icon {
  margin-right: 12px;
  font-size: 16px;
  color: #007acc;
}

.recent-file-item .file-info {
  flex: 1;
  min-width: 0;
}

.recent-file-item .file-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-file-item .file-path {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-file-item .file-time {
  font-size: 11px;
  color: #999999;
}

.no-recent-files {
  text-align: center;
  padding: 40px 20px;
  color: #cccccc;
}

.no-recent-files p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.no-recent-files small {
  font-size: 12px;
  color: #999999;
  line-height: 1.4;
}

.quick-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #2d2d30;
}

::-webkit-scrollbar-thumb {
  background: #5a5a5a;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6a6a6a;
}

/* 文件树工具栏样式 */
.file-tree-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  font-size: 12px;
}

.toolbar-title {
  color: #cccccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.toolbar-actions {
  display: flex;
  gap: 4px;
}

.toolbar-actions .ant-btn {
  color: #cccccc;
  border: none;
  background: transparent;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-actions .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* 文件树高亮动画 */
@keyframes highlight {
  0%, 100% {
    box-shadow: none;
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 122, 204, 0.5);
    background: rgba(0, 122, 204, 0.1);
  }
}
</style>