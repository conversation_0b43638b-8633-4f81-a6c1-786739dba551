package basic

import "time"

// ProcessFullInfo 进程信息
type ProcessFullInfo struct {
	PID           int32     `json:"pid"`           // 进程ID
	PPID          int32     `json:"ppid"`          // 父进程ID
	Name          string    `json:"name"`          // 进程名
	Executable    string    `json:"executable"`    // 可执行文件路径
	CommandLine   string    `json:"command_line"`  // 命令行参数
	Username      string    `json:"username"`      // 运行用户
	Status        string    `json:"status"`        // 进程状态
	CPUPercent    float64   `json:"cpu_percent"`   // CPU使用率
	MemoryMB      uint64    `json:"memory_mb"`     // 内存使用量(MB)
	MemoryPercent float32   `json:"memoryPercent"` // 内存使用百分比
	Priority      int32     `json:"priority"`      // 进程优先级
	CreateTime    time.Time `json:"create_time"`   // 创建时间
	RunTime       string    `json:"runTime"`       // 运行时间
	ThreadCount   uint32    `json:"thread_count"`  // 线程数
	System        bool      `json:"system"`        // 是否为系统进程
}

// ProcessModule 进程模块信息
type ProcessModule struct {
	Name    string `json:"name"`    // 模块名
	Path    string `json:"path"`    // 模块路径
	Size    uint64 `json:"size"`    // 模块大小
	Version string `json:"version"` // 版本信息
}

// ProcessConnection 进程网络连接
type ProcessConnection struct {
	Protocol   string `json:"protocol"`    // 协议类型 (TCP/UDP)
	LocalAddr  string `json:"local_addr"`  // 本地地址
	LocalPort  uint16 `json:"local_port"`  // 本地端口
	RemoteAddr string `json:"remote_addr"` // 远程地址
	RemotePort uint16 `json:"remote_port"` // 远程端口
	Status     string `json:"status"`      // 连接状态
}

// ProcessOpenFile 进程打开的文件
type ProcessOpenFile struct {
	Path string `json:"path"` // 文件路径
	Mode string `json:"mode"` // 打开模式
}
