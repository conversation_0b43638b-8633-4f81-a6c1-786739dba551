package tcp

import (
	"fmt"
	"server/core/manager/cache"
	"server/core/manager/dbpool"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/basic"
	screenshot2 "server/model/response/screenshot"
	"server/model/sys"
	"server/model/tlv"
	"server/utils"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// handleScreenshotPacket 处理截图数据包
func (l *TCPListener) handleScreenshotPacket(remoteAddr string, packet *tlv.Packet) error {
	global.LOG.Info("收到屏幕监控数据包", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))

	// 根据Code字段判断截图操作类型
	switch packet.Header.Code {
	case tlv.Pic:
		// 处理单次截图响应
		return l.handleScreenshotOperationResponse(remoteAddr, packet, "take_screenshot")
	case tlv.StreamStart:
		// 处理屏幕流开始响应
		return l.handleScreenshotOperationResponse(remoteAddr, packet, "start_screen_stream")
	case tlv.StreamStop:
		// 处理屏幕流停止响应
		return l.handleScreenshotOperationResponse(remoteAddr, packet, "stop_screen_stream")
	case tlv.StreamData:
		// 处理屏幕流数据响应
		return l.handleScreenshotOperationResponse(remoteAddr, packet, "screen_stream_data")
	case tlv.MonitorList:
		// 处理显示器列表响应
		return l.handleScreenshotOperationResponse(remoteAddr, packet, "get_monitor_list")
	default:
		global.LOG.Warn("未知的截图操作类型", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))
		return nil
	}
}

// handleScreenshotOperationResponse 处理截图操作响应
func (l *TCPListener) handleScreenshotOperationResponse(remoteAddr string, packet *tlv.Packet, operation string) error {
	switch operation {
	case "take_screenshot":
		// 反序列化截图响应
		var response screenshot2.ScreenshotResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化截图响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.Success {
			// 🚀 优化：使用处理池进行CPU密集型的图像保存操作
			err := workerpool.SubmitImageProcessingTask("screenshot_save", func() error {
				// 保存截图到文件系统
				filename, saveErr := l.saveScreenshot(remoteAddr, &response)
				if saveErr != nil {
					global.LOG.Error("保存截图失败", zap.Error(saveErr))
					cache.ResponseMgr.StoreResponse(response.TaskID, "take_screenshot", response, saveErr.Error())
					return saveErr
				} else {
					// 更新响应中的文件名
					response.ImageData = nil // 清空图片数据以节省内存
					cache.ResponseMgr.StoreResponse(response.TaskID, "take_screenshot", basic.ScreenshotInfo{
						Filename:  filename,
						FilePath:  fmt.Sprintf("%s/screenshots/%s/%s", global.CONFIG.Server.UploadDir, sanitizeRemoteAddr(remoteAddr), filename),
						Width:     response.Width,
						Height:    response.Height,
						Format:    response.Format,
						Size:      response.Size,
						Timestamp: response.Timestamp,
					}, "")
					return nil
				}
			})

			if err != nil {
				global.LOG.Warn("提交截图保存任务失败，回退到直接处理", zap.Error(err))
				// 回退到直接处理
				filename, saveErr := l.saveScreenshot(remoteAddr, &response)
				if saveErr != nil {
					global.LOG.Error("保存截图失败", zap.Error(saveErr))
					cache.ResponseMgr.StoreResponse(response.TaskID, "take_screenshot", response, saveErr.Error())
				} else {
					response.ImageData = nil
					cache.ResponseMgr.StoreResponse(response.TaskID, "take_screenshot", basic.ScreenshotInfo{
						Filename:  filename,
						FilePath:  fmt.Sprintf("%s/screenshots/%s/%s", global.CONFIG.Server.UploadDir, sanitizeRemoteAddr(remoteAddr), filename),
						Width:     response.Width,
						Height:    response.Height,
						Format:    response.Format,
						Size:      response.Size,
						Timestamp: response.Timestamp,
					}, "")
				}
			}
		} else {
			cache.ResponseMgr.StoreResponse(response.TaskID, "take_screenshot", response, response.Error)
		}

		if response.Error != "" {
			global.LOG.Warn("客户端截图操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("截图响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Int("width", response.Width),
				zap.Int("height", response.Height))
		}

	case "start_screen_stream":
		// 处理屏幕流开始响应
		var response screenshot2.ScreenStreamStartResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化屏幕流开始响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("屏幕流开始响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("streamID", response.StreamID),
			zap.String("error", response.Error))

		// 存储响应到缓存
		cache.ResponseMgr.StoreResponse(response.TaskID, "start_screen_stream", response, response.Error)

	case "stop_screen_stream":
		// 处理屏幕流停止响应
		var response screenshot2.ScreenStreamStopResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化屏幕流停止响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("屏幕流停止响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("error", response.Error))

		// 存储响应到缓存
		cache.ResponseMgr.StoreResponse(response.TaskID, "stop_screen_stream", response, response.Error)

	case "screen_stream_data":
		// 处理屏幕流数据响应
		var response screenshot2.ScreenStreamDataResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化屏幕流数据响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 获取客户端ID
		clientID, err := l.getClientIDByAddr(remoteAddr)
		if err != nil {
			global.LOG.Error("获取客户端ID失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 保存流数据帧到响应管理器，使用客户端ID作为键
		if response.Success && len(response.FrameData) > 0 {
			// 存储到特定的流数据键
			streamKey := fmt.Sprintf("stream_data_%d", clientID)
			cache.ResponseMgr.StoreStreamData(streamKey, "screen_stream_data", map[string]interface{}{
				"success":    true,
				"stream_id":  response.StreamID,
				"width":      response.Width,
				"height":     response.Height,
				"format":     response.Format,
				"size":       response.Size,
				"timestamp":  response.Timestamp,
				"frame_data": response.FrameData,
			}, "")

			// 同时存储到任务ID键（保持兼容性）
			cache.ResponseMgr.StoreResponse(response.TaskID, "screen_stream_data", map[string]interface{}{
				"success":    true,
				"stream_id":  response.StreamID,
				"frame_data": response.FrameData,
				"width":      response.Width,
				"height":     response.Height,
				"format":     response.Format,
				"timestamp":  response.Timestamp,
			}, "")
		} else {
			streamKey := fmt.Sprintf("stream_data_%d", clientID)
			cache.ResponseMgr.StoreStreamData(streamKey, "screen_stream_data", response, response.Error)
			cache.ResponseMgr.StoreResponse(response.TaskID, "screen_stream_data", response, response.Error)
		}

		global.LOG.Debug("屏幕流数据响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.String("streamID", response.StreamID),
			zap.Int("frameSize", len(response.FrameData)))

	case "get_monitor_list":
		// 处理显示器列表响应
		var response screenshot2.MonitorListResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化显示器列表响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("显示器列表响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Int("count", response.Count),
			zap.String("error", response.Error))

		// 存储响应到缓存
		cache.ResponseMgr.StoreResponse(response.TaskID, "get_monitor_list", response, response.Error)

	default:
		global.LOG.Warn("未知的截图操作类型", zap.String("remoteAddr", remoteAddr), zap.String("operation", operation))
	}

	return nil
}

// saveScreenshot 保存截图到文件系统
func (l *TCPListener) saveScreenshot(remoteAddr string, response *screenshot2.ScreenshotResponse) (string, error) {
	// 生成文件名
	timestamp := time.Unix(response.Timestamp, 0)
	filename := fmt.Sprintf("screenshot_%s_%d_%s.%s",
		remoteAddr,
		response.TaskID,
		timestamp.Format("20060102_150405"),
		response.Format)

	// 清理文件名中的特殊字符
	filename = utils.SanitizeFilename(filename, "_")

	// 构建完整路径
	// 清理remoteAddr中的特殊字符，避免Windows路径问题
	sanitizedRemoteAddr := sanitizeRemoteAddr(remoteAddr)
	screenshotDir := fmt.Sprintf("%s/screenshots/%s", global.CONFIG.Server.UploadDir, sanitizedRemoteAddr)
	if err := utils.CreateDir(screenshotDir); err != nil {
		return "", fmt.Errorf("创建截图目录失败: %v", err)
	}

	filePath := fmt.Sprintf("%s/%s", screenshotDir, filename)

	// 保存文件
	if err := utils.WriteFile(filePath, response.ImageData); err != nil {
		return "", fmt.Errorf("写入截图文件失败: %v", err)
	}

	global.LOG.Info("截图保存成功",
		zap.String("remoteAddr", remoteAddr),
		zap.String("filePath", filePath),
		zap.Int64("size", response.Size))

	return filename, nil
}

// getClientIDByAddr 根据远程地址获取客户端ID
func (l *TCPListener) getClientIDByAddr(remoteAddr string) (uint, error) {
	var client sys.Client
	// 🚀 使用数据库连接池查询客户端
	if err := dbpool.ExecuteDBOperationAsyncAndWait("client_by_addr_query", func(db *gorm.DB) error {
		return db.Where("remote_addr = ? AND status = ?", remoteAddr, 1).First(&client).Error
	}); err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, fmt.Errorf("客户端不在线或不存在: %s", remoteAddr)
		}
		return 0, fmt.Errorf("查询客户端失败: %v", err)
	}
	return client.ID, nil
}

// sanitizeRemoteAddr 清理远程地址中的特殊字符，将冒号替换为下划线
func sanitizeRemoteAddr(remoteAddr string) string {
	// 将冒号替换为下划线，避免Windows路径问题
	return strings.ReplaceAll(remoteAddr, ":", "_")
}
