<template>
  <div class="cron-templates">
    <div class="page-header">
      <h2>任务模板</h2>
      <a-button type="primary" @click="showCreateModal">
        <template #icon>
          <PlusOutlined />
        </template>
        创建模板
      </a-button>
    </div>

    <a-row :gutter="16">
      <a-col
        v-for="template in templateList"
        :key="template.id"
        :span="8"
        style="margin-bottom: 16px"
      >
        <a-card
          :title="template.name"
          size="small"
        >
          <template #actions>
            <a-tooltip title="使用模板">
              <PlayCircleOutlined @click="handleAction('use', template)" />
            </a-tooltip>
            <a-tooltip title="编辑">
              <EditOutlined @click="handleAction('edit', template)" />
            </a-tooltip>
            <a-tooltip title="删除">
              <DeleteOutlined @click="handleAction('delete', template)" />
            </a-tooltip>
          </template>
          <template #extra>
            <a-tag :color="getCategoryColor(template.category)">
              {{ template.category_display }}
            </a-tag>
          </template>

          <div class="template-content">
            <p class="description">{{ template.description }}</p>
            
            <div class="template-info">
              <div class="info-item">
                <span class="label">任务类型:</span>
                <a-tag size="small" :color="getTaskTypeColor(template.task_type)">
                  {{ template.task_type_display }}
                </a-tag>
              </div>
              
              <div class="info-item">
                <span class="label">Cron表达式:</span>
                <code class="cron-expr">{{ template.cron_expr }}</code>
              </div>
              
              <div class="info-item">
                <span class="label">超时时间:</span>
                <span>{{ template.timeout }}秒</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 空状态 -->
    <a-empty v-if="templateList.length === 0 && !loading" description="暂无模板">
      <a-button type="primary" @click="showCreateModal">创建第一个模板</a-button>
    </a-empty>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <!-- 创建模板模态框 -->
    <a-modal
      v-model:visible="createModalVisible"
      title="创建任务模板"
      width="800px"
      :confirm-loading="createLoading"
      @ok="handleCreateTemplate"
      @cancel="resetCreateForm"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="模板名称" name="name">
          <a-input v-model:value="createForm.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="模板描述" name="description">
          <a-textarea
            v-model:value="createForm.description"
            placeholder="请输入模板描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="分类" name="category">
          <a-select v-model:value="createForm.category" placeholder="请选择分类">
            <a-select-option value="system">系统管理</a-select-option>
            <a-select-option value="monitor">监控类</a-select-option>
            <a-select-option value="maintain">维护类</a-select-option>
            <a-select-option value="backup">备份类</a-select-option>
            <a-select-option value="security">安全类</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="任务类型" name="task_type">
          <a-select v-model:value="createForm.task_type" placeholder="请选择任务类型">
            <a-select-option value="command">命令执行</a-select-option>
            <a-select-option value="screenshot">截图任务</a-select-option>
            <a-select-option value="script">脚本执行</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="Cron表达式" name="cron_expr">
          <CronBuilder v-model="createForm.cron_expr" />
        </a-form-item>

        <a-form-item label="执行命令" name="command">
          <a-textarea
            v-model:value="createForm.command"
            placeholder="请输入要执行的命令"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="超时时间(秒)" name="timeout">
          <a-input-number
            v-model:value="createForm.timeout"
            :min="1"
            :max="3600"
            placeholder="默认300秒"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="重试次数" name="retry_count">
          <a-input-number
            v-model:value="createForm.retry_count"
            :min="0"
            :max="10"
            placeholder="默认0次"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑模板模态框 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑任务模板"
      width="800px"
      :confirm-loading="editLoading"
      @ok="handleEditTemplate"
      @cancel="resetEditForm"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="createRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="模板名称" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="模板描述" name="description">
          <a-textarea
            v-model:value="editForm.description"
            placeholder="请输入模板描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="分类" name="category">
          <a-select v-model:value="editForm.category" placeholder="请选择分类">
            <a-select-option value="system">系统管理</a-select-option>
            <a-select-option value="monitor">监控类</a-select-option>
            <a-select-option value="maintain">维护类</a-select-option>
            <a-select-option value="backup">备份类</a-select-option>
            <a-select-option value="security">安全类</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="任务类型" name="task_type">
          <a-select v-model:value="editForm.task_type" placeholder="请选择任务类型">
            <a-select-option value="command">命令执行</a-select-option>
            <a-select-option value="screenshot">截图任务</a-select-option>
            <a-select-option value="script">脚本执行</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="Cron表达式" name="cron_expr">
          <CronBuilder v-model="editForm.cron_expr" />
        </a-form-item>

        <a-form-item label="执行命令" name="command">
          <a-textarea
            v-model:value="editForm.command"
            placeholder="请输入要执行的命令"
            :rows="4"
            class="command-textarea"
          />
        </a-form-item>

        <a-form-item label="超时时间(秒)" name="timeout">
          <a-input-number
            v-model:value="editForm.timeout"
            :min="1"
            :max="3600"
            placeholder="默认300秒"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="重试次数" name="retry_count">
          <a-input-number
            v-model:value="editForm.retry_count"
            :min="0"
            :max="10"
            placeholder="默认0次"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, PlayCircleOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import * as cronApi from '@/api/cron'
import CronBuilder from './components/CronBuilder.vue'

const router = useRouter()
const loading = ref(false)
const templateList = ref([])

// 创建模板相关
const createModalVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref()
const createForm = ref({
  name: '',
  description: '',
  category: '',
  task_type: '',
  cron_expr: '',
  command: '',
  timeout: 300,
  retry_count: 0
})

// 编辑模板相关
const editModalVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref()
const editForm = ref({
  id: null,
  name: '',
  description: '',
  category: '',
  task_type: '',
  cron_expr: '',
  command: '',
  timeout: 300,
  retry_count: 0
})

// 创建表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模板名称长度在1到100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  cron_expr: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' }
  ],
  command: [
    { required: true, message: '请输入执行命令', trigger: 'blur' }
  ],
  timeout: [
    { type: 'number', min: 1, max: 3600, message: '超时时间必须在1-3600秒之间', trigger: 'blur' }
  ],
  retry_count: [
    { type: 'number', min: 0, max: 10, message: '重试次数必须在0-10次之间', trigger: 'blur' }
  ]
}

// 获取模板列表
const fetchTemplateList = async () => {
  loading.value = true
  try {
    const response = await cronApi.getTemplateList()
    if (response.code === 200) {
      templateList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 显示创建模板模态框
const showCreateModal = () => {
  createModalVisible.value = true
}

// 重置创建表单
const resetCreateForm = () => {
  createModalVisible.value = false
  createForm.value = {
    name: '',
    description: '',
    category: '',
    task_type: '',
    cron_expr: '',
    command: '',
    timeout: 300,
    retry_count: 0
  }
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

// 处理创建模板
const handleCreateTemplate = async () => {
  try {
    // 表单验证
    await createFormRef.value.validate()

    createLoading.value = true

    // 调用API创建模板
    const response = await cronApi.createTemplate(createForm.value)
    if (response.code === 200) {
      message.success('创建模板成功')
      resetCreateForm()
      // 刷新模板列表
      await fetchTemplateList()
    } else {
      message.error(response.msg || '创建模板失败')
    }
  } catch (error) {
    console.error('创建模板失败:', error)
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    message.error('创建模板失败: ' + (error.message || '未知错误'))
  } finally {
    createLoading.value = false
  }
}

// 处理模板操作
const handleAction = (action, template) => {
  switch (action) {
    case 'use':
      useTemplate(template)
      break
    case 'edit':
      editTemplate(template)
      break
    case 'delete':
      deleteTemplate(template)
      break
  }
}

// 使用模板
const useTemplate = (template) => {
  // 跳转到创建任务页面，并传递模板数据
  router.push({
    path: '/cron/create',
    query: {
      template_id: template.id
    }
  })
}

// 编辑模板
const editTemplate = (template) => {
  // 填充编辑表单
  editForm.value = {
    id: template.id,
    name: template.name,
    description: template.description,
    category: template.category,
    task_type: template.task_type,
    cron_expr: template.cron_expr,
    command: template.command,
    timeout: template.timeout,
    retry_count: template.retry_count
  }
  editModalVisible.value = true
}

// 处理编辑模板
const handleEditTemplate = async () => {
  try {
    await editFormRef.value.validate()
    editLoading.value = true

    const { id, ...data } = editForm.value
    await cronApi.updateTemplate(id, data)

    message.success('模板更新成功')
    resetEditForm()
    fetchTemplateList()
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单填写')
      return
    }
    console.error('更新模板失败:', error)
    message.error('更新模板失败')
  } finally {
    editLoading.value = false
  }
}

// 重置编辑表单
const resetEditForm = () => {
  editModalVisible.value = false
  editForm.value = {
    id: null,
    name: '',
    description: '',
    category: '',
    task_type: '',
    cron_expr: '',
    command: '',
    timeout: 300,
    retry_count: 0
  }
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 删除模板
const deleteTemplate = (template) => {
  if (template.is_built_in) {
    message.warning('内置模板不能删除')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板 "${template.name}" 吗？`,
    onOk: async () => {
      try {
        await cronApi.deleteTemplate(template.id)
        message.success('模板删除成功')
        fetchTemplateList()
      } catch (error) {
        console.error('删除模板失败:', error)
        message.error('删除模板失败')
      }
    }
  })
}

// 获取分类颜色
const getCategoryColor = (category) => {
  const colors = {
    system: 'blue',
    monitor: 'green',
    maintain: 'orange',
    backup: 'purple',
    security: 'red',
    custom: 'default'
  }
  return colors[category] || 'default'
}

// 获取任务类型颜色
const getTaskTypeColor = (type) => {
  const colors = {
    command: 'blue',
    script: 'green',
    screenshot: 'orange',
    file: 'purple'
  }
  return colors[type] || 'default'
}

onMounted(() => {
  fetchTemplateList()
})
</script>

<style scoped lang="scss">
.cron-templates {
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
    }
  }

  .template-content {
    .description {
      color: #666;
      margin-bottom: 16px;
      min-height: 40px;
    }

    .template-info {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .label {
          font-size: 12px;
          color: #999;
          margin-right: 8px;
          min-width: 70px;
        }

        .cron-expr {
          font-family: 'Courier New', monospace;
          background: #f5f5f5;
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 11px;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>
