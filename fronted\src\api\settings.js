import { get, put, post } from '@/utils/request'

/**
 * 获取常规配置
 * @returns {Promise}
 */
export function getGeneralConfig() {
  return get('/config/general')
}

/**
 * 更新常规配置
 * @param {Object} data - 常规配置数据
 * @param {Object} data.server - 服务器配置
 * @param {Object} data.jwt - JWT配置
 * @param {Object} data.captcha - 验证码配置
 * @returns {Promise}
 */
export function updateGeneralConfig(data) {
  return put('/config/general', data)
}

/**
 * 获取通知配置
 * @returns {Promise}
 */
export function getNotificationConfig() {
  return get('/config/notification')
}

/**
 * 更新通知配置
 * @param {Object} data - 通知配置数据
 * @param {boolean} data.clientStatus - 客户端状态通知开关
 * @param {number} data.displayDuration - 通知显示时长
 * @param {boolean} data.soundEnabled - 音效开关
 * @param {number} data.maxNotifications - 最大通知数量
 * @returns {Promise}
 */
export function updateNotificationConfig(data) {
  return put('/config/notification', data)
}

/**
 * 重置配置到默认值
 * @returns {Promise}
 */
export function resetConfig() {
  return post('/config/reset')
}