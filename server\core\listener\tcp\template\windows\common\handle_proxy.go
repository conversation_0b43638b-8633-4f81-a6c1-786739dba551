//go:build windows
// +build windows

package common

import (
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"strconv"
	"sync"
	"time"

	"github.com/xtaci/smux"
)

// 基于iox-master的协议定义
const (
	CTL_HANDSHAKE = iota
	CTL_CONNECT_ME
	CTL_CLEANUP

	MAX_CONNECTION   = 0x800
	CLIENT_HANDSHAKE = 0xC0
	SERVER_HANDSHAKE = 0xE0
)

// 协议结构
type Protocol struct {
	CMD byte
	N   byte
}

var PROTO_END = []byte{0xEE, 0xFF}

// 协议编解码函数
func marshal(p Protocol) []byte {
	buf := make([]byte, 4)
	buf[0] = p.CMD
	buf[1] = p.N
	buf[2], buf[3] = PROTO_END[0], PROTO_END[1]
	return buf
}

func unmarshal(b []byte) Protocol {
	return Protocol{
		CMD: b[0],
		N:   b[1],
	}
}

func bytesEq(a, b []byte) bool {
	for i := 0; i < len(a); i++ {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func readUntilEnd(conn net.Conn) ([]byte, error) {
	buf := make([]byte, 1)
	output := make([]byte, 0, 4)

	for {
		n, err := conn.Read(buf)
		if err != nil {
			return nil, err
		}

		if n != 1 || len(output) > 4 {
			return nil, errors.New("transmission error")
		}

		output = append(output, buf[0])

		if len(output) == 4 && bytesEq(PROTO_END, output[len(output)-2:]) {
			break
		}
	}

	return output[:2], nil
}

// ProxyRequest 代理请求结构
type ProxyRequest struct {
	TaskID      uint64 `json:"task_id"`
	ProxyID     string `json:"proxy_id"`
	Type        string `json:"type"`         // forward, reverse
	Name        string `json:"name"`
	UserPort    uint16 `json:"user_port"`
	ClientPort  uint16 `json:"client_port"`  // 服务器分配的ClientPort
	AllocMode   string `json:"alloc_mode"`   // auto, manual
	ManualAlloc bool   `json:"manual_alloc"`
}

// ProxyResponse 代理响应结构
type ProxyResponse struct {
	TaskID  uint64 `json:"task_id"`
	ProxyID string `json:"proxy_id"`
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
	Port    uint16 `json:"port,omitempty"`
}

// CheckPortRequest 端口检查请求
type CheckPortRequest struct {
	TaskID uint64 `json:"task_id"`
	Port   uint16 `json:"port"`
}

// CheckPortResponse 端口检查响应
type CheckPortResponse struct {
	TaskID    uint64 `json:"task_id"`
	Port      uint16 `json:"port"`
	Available bool   `json:"available"`
	Error     string `json:"error,omitempty"`
}

// ProxyManager 代理管理器
type ProxyManager struct {
	proxies map[string]*ProxyInstance
	mutex   sync.RWMutex
}

// ProxyInstance 代理实例
type ProxyInstance struct {
	ProxyID     string
	Type        string
	Name        string
	Port        uint16 // UserPort
	ClientPort  uint16 // 服务器分配的ClientPort
	listener    net.Listener
	connections map[string]net.Conn
	connMutex   sync.RWMutex
	stopChan    chan struct{}
	running     bool
}

var globalProxyManager = &ProxyManager{
	proxies: make(map[string]*ProxyInstance),
}

// handleProxyRequest 处理代理请求
func (cm *ConnectionManager) handleProxyRequest(packet *Packet) {
	switch packet.Header.Code {
	case CheckPort:
		cm.handleCheckPortRequest(packet)
	case ProxyStart:
		cm.handleProxyStartRequest(packet)
	case ProxyStop:
		cm.handleProxyStopRequest(packet)
	case ProxyDelete:
		cm.handleProxyDeleteRequest(packet)
	default:
		log.Printf("未知的代理操作类型: %d", packet.Header.Code)
	}
}

// handleCheckPortRequest 处理端口检查请求
func (cm *ConnectionManager) handleCheckPortRequest(packet *Packet) {
	var req CheckPortRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析端口检查请求失败: %v", err)
		return
	}

	log.Printf("收到端口检查请求: Port=%d, TaskID=%d", req.Port, req.TaskID)

	// 检查端口可用性
	available := isPortAvailable(req.Port)
	
	response := CheckPortResponse{
		TaskID:    req.TaskID,
		Port:      req.Port,
		Available: available,
	}

	if !available {
		response.Error = fmt.Sprintf("端口 %d 不可用", req.Port)
	}
	cm.sendResp(Proxy, CheckPort, response)
}

// handleProxyStartRequest 处理代理启动请求
func (cm *ConnectionManager) handleProxyStartRequest(packet *Packet) {
	var req ProxyRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析代理启动请求失败: %v", err)
		return
	}

	log.Printf("🚀 [CLIENT] 收到代理启动请求: ProxyID=%s, Type=%s, Port=%d", req.ProxyID, req.Type, req.UserPort)
	log.Printf("📋 [CLIENT] 代理详细信息: TaskID=%d, AllocMode=%s, ManualAlloc=%t",
		req.TaskID, req.AllocMode, req.ManualAlloc)

	response := ProxyResponse{
		TaskID:  req.TaskID,
		ProxyID: req.ProxyID,
		Success: false,
	}

	// 检查代理是否已存在
	log.Printf("🔍 [CLIENT] 检查代理是否已存在: %s", req.ProxyID)
	if globalProxyManager.getProxy(req.ProxyID) != nil {
		log.Printf("⚠️ [CLIENT] 代理已存在: %s", req.ProxyID)
		response.Error = "代理已存在"
		cm.sendResp(Proxy, ProxyStart, response)
		return
	}
	log.Printf("✅ [CLIENT] 代理不存在，可以创建")

	var port uint16
	var err error

	switch req.Type {
	case "forward":
		log.Printf("🔄 [CLIENT] 开始启动正向代理，调用startForwardProxy")
		port, err = cm.startForwardProxy(&req)
	case "reverse":
		log.Printf("🔄 [CLIENT] 开始启动反向代理，调用startReverseProxy")
		port, err = cm.startReverseProxy(&req)
	default:
		err = fmt.Errorf("不支持的代理类型: %s", req.Type)
		log.Printf("🔴 [CLIENT] 不支持的代理类型: %s", req.Type)
	}

	if err != nil {
		response.Error = err.Error()
		log.Printf("🔴 [CLIENT] 启动代理失败: %v", err)
	} else {
		response.Success = true
		response.Port = port
		log.Printf("✅ [CLIENT] 代理启动成功: ProxyID=%s, Port=%d", req.ProxyID, port)
	}

	cm.sendResp(Proxy, ProxyStart, response)
}

// handleProxyStopRequest 处理代理停止请求
func (cm *ConnectionManager) handleProxyStopRequest(packet *Packet) {
	var req ProxyRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析代理停止请求失败: %v", err)
		return
	}

	log.Printf("收到代理停止请求: ProxyID=%s", req.ProxyID)

	response := ProxyResponse{
		TaskID:  req.TaskID,
		ProxyID: req.ProxyID,
		Success: false,
	}

	if err := globalProxyManager.stopProxy(req.ProxyID); err != nil {
		response.Error = err.Error()
		log.Printf("停止代理失败: %v", err)
	} else {
		response.Success = true
		log.Printf("代理停止成功: ProxyID=%s", req.ProxyID)
	}

	cm.sendResp(Proxy, ProxyStop, response)
}

// handleProxyDeleteRequest 处理代理删除请求
func (cm *ConnectionManager) handleProxyDeleteRequest(packet *Packet) {
	var req ProxyRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析代理删除请求失败: %v", err)
		return
	}

	log.Printf("收到代理删除请求: ProxyID=%s", req.ProxyID)

	response := ProxyResponse{
		TaskID:  req.TaskID,
		ProxyID: req.ProxyID,
		Success: false,
	}

	if err := globalProxyManager.deleteProxy(req.ProxyID); err != nil {
		response.Error = err.Error()
		log.Printf("删除代理失败: %v", err)
	} else {
		response.Success = true
		log.Printf("代理删除成功: ProxyID=%s", req.ProxyID)
	}
	cm.sendResp(Proxy, ProxyDelete, response)
}

// isPortAvailable 检查端口是否可用
func isPortAvailable(port uint16) bool {
	listener, err := net.Listen("tcp", ":"+strconv.Itoa(int(port)))
	if err != nil {
		return false
	}
	listener.Close()
	return true
}

// ProxyManager 方法实现

// getProxy 获取代理实例
func (pm *ProxyManager) getProxy(proxyID string) *ProxyInstance {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return pm.proxies[proxyID]
}

// addProxy 添加代理实例
func (pm *ProxyManager) addProxy(proxy *ProxyInstance) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.proxies[proxy.ProxyID] = proxy
}

// removeProxy 移除代理实例
func (pm *ProxyManager) removeProxy(proxyID string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	delete(pm.proxies, proxyID)
}

// stopProxy 停止代理
func (pm *ProxyManager) stopProxy(proxyID string) error {
	proxy := pm.getProxy(proxyID)
	if proxy == nil {
		return fmt.Errorf("代理不存在: %s", proxyID)
	}

	if !proxy.running {
		return fmt.Errorf("代理未运行: %s", proxyID)
	}

	// 关闭停止信号
	close(proxy.stopChan)

	// 关闭监听器
	if proxy.listener != nil {
		proxy.listener.Close()
	}

	// 关闭所有连接
	proxy.connMutex.Lock()
	for _, conn := range proxy.connections {
		conn.Close()
	}
	proxy.connections = make(map[string]net.Conn)
	proxy.connMutex.Unlock()

	proxy.running = false
	log.Printf("代理已停止: %s", proxyID)
	return nil
}

// deleteProxy 删除代理
func (pm *ProxyManager) deleteProxy(proxyID string) error {
	if err := pm.stopProxy(proxyID); err != nil {
		// 如果停止失败，仍然尝试删除
		log.Printf("停止代理时出错: %v", err)
	}
	pm.removeProxy(proxyID)
	log.Printf("代理已删除: %s", proxyID)
	return nil
}

// startForwardProxy 启动正向代理
func (cm *ConnectionManager) startForwardProxy(req *ProxyRequest) (uint16, error) {
	log.Printf("🚀 [CLIENT-FORWARD] 开始启动正向代理")
	log.Printf("📋 [CLIENT-FORWARD] 请求参数: ProxyID=%s, UserPort=%d, ManualAlloc=%t, AllocMode=%s",
		req.ProxyID, req.UserPort, req.ManualAlloc, req.AllocMode)

	// 正向代理：客户端启动SOCKS5服务，用户连接客户端，流量转发到目标服务器

	var port uint16
	if req.ManualAlloc && req.UserPort > 0 {
		port = req.UserPort
		log.Printf("📌 [CLIENT-FORWARD] 使用手动指定端口: %d", port)
	} else {
		// 自动分配端口
		log.Printf("🎯 [CLIENT-FORWARD] 自动分配端口")
		port = findAvailablePort()
		log.Printf("✅ [CLIENT-FORWARD] 自动分配端口成功: %d", port)
	}

	log.Printf("🔍 [CLIENT-FORWARD] 检查端口可用性: %d", port)
	if !isPortAvailable(port) {
		log.Printf("🔴 [CLIENT-FORWARD] 端口不可用: %d", port)
		return 0, fmt.Errorf("端口 %d 不可用", port)
	}
	log.Printf("✅ [CLIENT-FORWARD] 端口可用性检查通过: %d", port)

	listenAddr := ":" + strconv.Itoa(int(port))
	log.Printf("🌐 [CLIENT-FORWARD] 启动TCP监听器: %s", listenAddr)
	listener, err := net.Listen("tcp", listenAddr)
	if err != nil {
		log.Printf("🔴 [CLIENT-FORWARD] 启动监听失败: %v", err)
		return 0, fmt.Errorf("启动监听失败: %v", err)
	}
	log.Printf("✅ [CLIENT-FORWARD] TCP监听器启动成功: %s", listenAddr)

	log.Printf("🔧 [CLIENT-FORWARD] 创建代理实例")
	proxy := &ProxyInstance{
		ProxyID:     req.ProxyID,
		Type:        req.Type,
		Name:        req.Name,
		Port:        port,
		listener:    listener,
		connections: make(map[string]net.Conn),
		stopChan:    make(chan struct{}),
		running:     true,
	}

	log.Printf("💾 [CLIENT-FORWARD] 保存代理实例到管理器")
	globalProxyManager.addProxy(proxy)

	// 启动SOCKS5服务
	log.Printf("🔄 [CLIENT-FORWARD] 启动SOCKS5服务器协程")
	go cm.runForwardProxyServer(proxy)

	log.Printf("✅ [CLIENT-FORWARD] 正向代理启动完成，返回端口: %d", port)
	return port, nil
}

// startReverseProxy 启动反向代理
func (cm *ConnectionManager) startReverseProxy(req *ProxyRequest) (uint16, error) {
	// 反向代理：客户端连接到服务器的SOCKS5服务
	// 这里我们需要连接到服务器端口，但首先需要从服务器获取端口信息

	proxy := &ProxyInstance{
		ProxyID:     req.ProxyID,
		Type:        req.Type,
		Name:        req.Name,
		Port:        req.UserPort, // 用户端口
		ClientPort:  req.ClientPort, // 服务器分配的ClientPort
		connections: make(map[string]net.Conn),
		stopChan:    make(chan struct{}),
		running:     true,
	}

	globalProxyManager.addProxy(proxy)

	// 启动反向代理连接
	go cm.runReverseProxyClient(proxy)

	return req.UserPort, nil
}

// findAvailablePort 查找可用端口
func findAvailablePort() uint16 {
	port, err := findAvailablePortInRange(10000, 20000)
	if err != nil {
		log.Printf("🔴 [CLIENT] 查找可用端口失败: %v", err)
		return 0
	}
	return port
}

// findAvailablePortInRange 在指定范围内查找可用端口
func findAvailablePortInRange(start, end uint16) (uint16, error) {
	log.Printf("🔍 [CLIENT] 在端口范围 %d-%d 中查找可用端口", start, end)

	// 随机起始点，避免多个客户端同时从同一端口开始
	offset := uint16(rand.Intn(int(end - start + 1)))

	for i := uint16(0); i <= end-start; i++ {
		port := start + (offset+i)%(end-start+1)
		if isPortAvailable(port) {
			log.Printf("✅ [CLIENT] 找到可用端口: %d", port)
			return port, nil
		}
	}

	log.Printf("🔴 [CLIENT] 在范围 %d-%d 中未找到可用端口", start, end)
	return 0, fmt.Errorf("在范围 %d-%d 中没有可用端口", start, end)
}

// runForwardProxyServer 运行正向代理SOCKS5服务器
func (cm *ConnectionManager) runForwardProxyServer(proxy *ProxyInstance) {
	defer func() {
		if proxy.listener != nil {
			proxy.listener.Close()
		}
		proxy.running = false
		log.Printf("正向代理服务器已停止: %s", proxy.ProxyID)
	}()

	log.Printf("正向代理SOCKS5服务器启动: %s, 端口: %d", proxy.ProxyID, proxy.Port)

	for {
		// 检查停止信号
		select {
		case <-proxy.stopChan:
			return
		default:
		}

		conn, err := proxy.listener.Accept()
		if err != nil {
			// 检查是否是因为停止信号导致的错误
			select {
			case <-proxy.stopChan:
				return
			default:
			}
			continue
		}

		// 处理SOCKS5连接
		go cm.handleSocks5Connection(proxy, conn)
	}
}

// runReverseProxyClient 运行反向代理客户端
func (cm *ConnectionManager) runReverseProxyClient(proxy *ProxyInstance) {
	defer func() {
		proxy.running = false
		log.Printf("🛑 反向代理客户端已停止: %s", proxy.ProxyID)
	}()

	log.Printf("🔄 反向代理客户端启动: %s, 准备连接服务器ClientPort", proxy.ProxyID)

	// 获取服务器地址
	serverAddr := cm.getServerAddress()
	if serverAddr == "" {
		log.Printf("❌ 无法获取服务器地址")
		return
	}

	// 反向代理连接重试逻辑
	maxRetries := 5
	retryDelay := time.Second * 2

	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		select {
		case <-proxy.stopChan:
			return
		default:
			if cm.connectToServerClientPort(proxy, serverAddr, retryCount) {
				return // 连接成功并处理完毕
			}

			// 连接失败，等待重试
			if retryCount < maxRetries-1 {
				log.Printf("⏳ 反向代理连接失败，%v后重试 (%d/%d)", retryDelay, retryCount+1, maxRetries)
				time.Sleep(retryDelay)
				retryDelay *= 2 // 指数退避
			}
		}
	}

	log.Printf("❌ 反向代理客户端连接失败，已达到最大重试次数: %s", proxy.ProxyID)
}

// getServerAddress 获取服务器地址
func (cm *ConnectionManager) getServerAddress() string {
	if cm.conn == nil {
		return ""
	}

	// 从现有连接获取服务器地址
	if addr, ok := cm.conn.RemoteAddr().(*net.TCPAddr); ok {
		return addr.IP.String()
	}

	return ""
}

// connectToServerClientPort 连接到服务器的ClientPort (基于iox-master的smux实现)
func (cm *ConnectionManager) connectToServerClientPort(proxy *ProxyInstance, serverAddr string, retryCount int) bool {
	// 构建ClientPort地址 (使用服务器分配的ClientPort)
	clientPortAddr := fmt.Sprintf("%s:%d", serverAddr, proxy.ClientPort)

	log.Printf("🔗 尝试连接服务器ClientPort: %s (重试: %d)", clientPortAddr, retryCount)

	// 建立TCP连接
	conn, err := net.DialTimeout("tcp", clientPortAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接服务器ClientPort失败: %v", err)
		return false
	}

	log.Printf("✅ 成功建立TCP连接到服务器ClientPort: %s", clientPortAddr)

	// 建立smux客户端session
	session, err := smux.Client(conn, &smux.Config{
		Version:           2,
		KeepAliveInterval: 30 * time.Second,
		KeepAliveTimeout:  60 * time.Second,
		MaxFrameSize:      32768,
		MaxReceiveBuffer:  4194304,
		MaxStreamBuffer:   65536,
	})
	if err != nil {
		conn.Close()
		log.Printf("❌ 建立smux session失败: %v", err)
		return false
	}

	// 建立控制流
	ctlStream, err := session.OpenStream()
	if err != nil {
		session.Close()
		log.Printf("❌ 建立控制流失败: %v", err)
		return false
	}

	// 进行握手
	err = cm.clientHandshake(ctlStream)
	if err != nil {
		ctlStream.Close()
		session.Close()
		log.Printf("❌ 握手失败: %v", err)
		return false
	}

	log.Printf("✅ 握手成功，开始处理反向代理")

	// 处理控制流和数据流
	cm.handleSmuxSession(proxy, session, ctlStream)

	return false // 连接断开后返回false，触发重连
}

// handleControlConnection 处理控制连接
func (cm *ConnectionManager) handleControlConnection(proxy *ProxyInstance, controlConn net.Conn, serverAddr string) {
	defer controlConn.Close()
	defer func() {
		log.Printf("🔚 控制连接处理结束: %s", proxy.ProxyID)
	}()

	log.Printf("📡 开始处理控制连接: %s", proxy.ProxyID)

	for {
		// 检查停止信号
		select {
		case <-proxy.stopChan:
			return
		default:
		}

		// 读取控制消息
		controlConn.SetReadDeadline(time.Now().Add(60 * time.Second))

		// 先读取第一个字节判断消息类型
		firstByte := make([]byte, 1)
		_, err := io.ReadFull(controlConn, firstByte)
		if err != nil {
			if err != io.EOF {
				log.Printf("❌ 读取控制消息失败: %v", err)
			}
			return
		}

		cmd := firstByte[0]

		// 处理心跳消息
		if cmd == 0xFF {
			// 心跳消息，继续循环
			continue
		}

		// 读取剩余的4字节连接ID
		idBuf := make([]byte, 4)
		_, err = io.ReadFull(controlConn, idBuf)
		if err != nil {
			log.Printf("❌ 读取连接ID失败: %v", err)
			return
		}

		connID := uint32(idBuf[0])<<24 | uint32(idBuf[1])<<16 | uint32(idBuf[2])<<8 | uint32(idBuf[3])

		switch cmd {
		case 0x01: // CMD_CONNECT_REQUEST
			log.Printf("📥 收到连接请求, connID: %d", connID)
			go cm.handleConnectRequest(proxy, serverAddr, connID)

		default:
			log.Printf("⚠️ 未知控制命令: 0x%02x", cmd)
		}
	}
}

// handleConnectRequest 处理连接请求
func (cm *ConnectionManager) handleConnectRequest(proxy *ProxyInstance, serverAddr string, connID uint32) {
	// 建立数据连接到服务器
	clientPortAddr := fmt.Sprintf("%s:%d", serverAddr, proxy.ClientPort)
	dataConn, err := net.DialTimeout("tcp", clientPortAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 建立数据连接失败: %v", err)
		return
	}
	defer dataConn.Close()

	// 发送连接ID (大端序)
	idBuf := make([]byte, 4)
	idBuf[0] = byte(connID >> 24)
	idBuf[1] = byte(connID >> 16)
	idBuf[2] = byte(connID >> 8)
	idBuf[3] = byte(connID)

	_, err = dataConn.Write(idBuf)
	if err != nil {
		log.Printf("❌ 发送连接ID失败: %v", err)
		return
	}

	log.Printf("📤 已发送连接ID: %d", connID)

	// 接收目标地址
	target, err := cm.receiveTarget(dataConn)
	if err != nil {
		log.Printf("❌ 接收目标地址失败: %v", err)
		return
	}

	log.Printf("🎯 收到目标地址: %s", target)

	// 连接到目标
	targetConn, err := net.DialTimeout("tcp", target, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接目标失败 %s: %v", target, err)
		return
	}
	defer targetConn.Close()

	log.Printf("✅ 成功连接到目标: %s", target)

	// 双向转发
	cm.forwardData(dataConn, targetConn)

	log.Printf("✅ 双向转发完成, connID: %d", connID)
}

// receiveTarget 接收目标地址
func (cm *ConnectionManager) receiveTarget(conn net.Conn) (string, error) {
	// 设置读超时
	conn.SetReadDeadline(time.Now().Add(15 * time.Second))

	// 读取长度 (2字节，大端序)
	lengthBuf := make([]byte, 2)
	_, err := io.ReadFull(conn, lengthBuf)
	if err != nil {
		return "", err
	}

	length := uint16(lengthBuf[0])<<8 | uint16(lengthBuf[1])
	if length == 0 || length > 256 {
		return "", fmt.Errorf("无效的目标地址长度: %d", length)
	}

	// 读取目标地址
	targetBuf := make([]byte, length)
	_, err = io.ReadFull(conn, targetBuf)
	if err != nil {
		return "", err
	}

	return string(targetBuf), nil
}

// forwardData 双向数据转发
func (cm *ConnectionManager) forwardData(dataConn, targetConn net.Conn) {
	done := make(chan struct{}, 2)

	// Data -> Target
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(targetConn, dataConn)
		if err != nil && err != io.EOF {
			log.Printf("❌ 数据->目标转发失败: %v", err)
		} else {
			log.Printf("📤 数据->目标转发完成: %d bytes", written)
		}
	}()

	// Target -> Data
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(dataConn, targetConn)
		if err != nil && err != io.EOF {
			log.Printf("❌ 目标->数据转发失败: %v", err)
		} else {
			log.Printf("📥 目标->数据转发完成: %d bytes", written)
		}
	}()

	// 等待任一方向结束
	<-done
}

// handleSocks5Connection 处理SOCKS5连接
func (cm *ConnectionManager) handleSocks5Connection(proxy *ProxyInstance, clientConn net.Conn) {
	defer clientConn.Close()

	connID := fmt.Sprintf("%s_%d", clientConn.RemoteAddr().String(), time.Now().UnixNano())

	// 添加到连接管理
	proxy.connMutex.Lock()
	proxy.connections[connID] = clientConn
	proxy.connMutex.Unlock()

	defer func() {
		proxy.connMutex.Lock()
		delete(proxy.connections, connID)
		proxy.connMutex.Unlock()
	}()

	log.Printf("新的SOCKS5连接: %s -> %s", clientConn.RemoteAddr(), proxy.ProxyID)

	// SOCKS5握手
	if err := cm.socks5Handshake(clientConn); err != nil {
		log.Printf("SOCKS5握手失败: %v", err)
		return
	}

	// 处理SOCKS5请求
	targetAddr, err := cm.socks5Request(clientConn)
	if err != nil {
		log.Printf("SOCKS5请求处理失败: %v", err)
		return
	}

	// 连接目标服务器
	targetConn, err := net.Dial("tcp", targetAddr)
	if err != nil {
		log.Printf("连接目标服务器失败: %s, %v", targetAddr, err)
		// 发送连接失败响应
		cm.socks5ConnectResponse(clientConn, false)
		return
	}
	defer targetConn.Close()

	// 发送连接成功响应
	if err := cm.socks5ConnectResponse(clientConn, true); err != nil {
		log.Printf("发送SOCKS5响应失败: %v", err)
		return
	}

	log.Printf("SOCKS5连接建立成功: %s -> %s", clientConn.RemoteAddr(), targetAddr)

	// 开始数据转发
	cm.forwardData(clientConn, targetConn)
}

// SOCKS5协议实现

// socks5Handshake 处理SOCKS5握手
func (cm *ConnectionManager) socks5Handshake(conn net.Conn) error {
	// 读取客户端握手请求
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil {
		return fmt.Errorf("读取握手请求失败: %v", err)
	}

	if n < 3 || buf[0] != 0x05 {
		return fmt.Errorf("无效的SOCKS5握手请求")
	}

	// 简单实现：不需要认证
	response := []byte{0x05, 0x00} // VER=5, METHOD=0(无认证)
	_, err = conn.Write(response)
	if err != nil {
		return fmt.Errorf("发送握手响应失败: %v", err)
	}

	return nil
}

// socks5Request 处理SOCKS5连接请求
func (cm *ConnectionManager) socks5Request(conn net.Conn) (string, error) {
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil {
		return "", fmt.Errorf("读取连接请求失败: %v", err)
	}

	if n < 7 || buf[0] != 0x05 || buf[1] != 0x01 {
		return "", fmt.Errorf("无效的SOCKS5连接请求")
	}

	var targetAddr string

	// 解析地址类型
	switch buf[3] {
	case 0x01: // IPv4
		if n < 10 {
			return "", fmt.Errorf("IPv4地址数据不完整")
		}
		ip := net.IPv4(buf[4], buf[5], buf[6], buf[7])
		port := (uint16(buf[8]) << 8) | uint16(buf[9])
		targetAddr = fmt.Sprintf("%s:%d", ip.String(), port)

	case 0x03: // 域名
		if n < 5 {
			return "", fmt.Errorf("域名地址数据不完整")
		}
		domainLen := int(buf[4])
		if n < 5+domainLen+2 {
			return "", fmt.Errorf("域名地址数据不完整")
		}
		domain := string(buf[5 : 5+domainLen])
		port := (uint16(buf[5+domainLen]) << 8) | uint16(buf[5+domainLen+1])
		targetAddr = fmt.Sprintf("%s:%d", domain, port)

	case 0x04: // IPv6
		return "", fmt.Errorf("暂不支持IPv6")

	default:
		return "", fmt.Errorf("不支持的地址类型: %d", buf[3])
	}

	return targetAddr, nil
}

// socks5ConnectResponse 发送SOCKS5连接响应
func (cm *ConnectionManager) socks5ConnectResponse(conn net.Conn, success bool) error {
	var response []byte

	if success {
		// 成功响应：VER=5, REP=0, RSV=0, ATYP=1, BND.ADDR=0.0.0.0, BND.PORT=0
		response = []byte{0x05, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}
	} else {
		// 失败响应：VER=5, REP=1(一般错误), RSV=0, ATYP=1, BND.ADDR=0.0.0.0, BND.PORT=0
		response = []byte{0x05, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}
	}

	_, err := conn.Write(response)
	return err
}

// clientHandshake 客户端握手
func (cm *ConnectionManager) clientHandshake(ctlStream *smux.Stream) error {
	// 发送握手请求
	_, err := ctlStream.Write(marshal(Protocol{
		CMD: CTL_HANDSHAKE,
		N:   CLIENT_HANDSHAKE,
	}))
	if err != nil {
		return err
	}

	// 读取握手响应
	pb, err := readUntilEnd(ctlStream)
	if err != nil {
		return errors.New("握手响应读取失败")
	}

	p := unmarshal(pb)
	if !(p.CMD == CTL_HANDSHAKE && p.N == SERVER_HANDSHAKE) {
		return errors.New("握手响应无效")
	}

	return nil
}

// handleSmuxSession 处理smux session
func (cm *ConnectionManager) handleSmuxSession(proxy *ProxyInstance, session *smux.Session, ctlStream *smux.Stream) {
	defer session.Close()
	defer ctlStream.Close()

	log.Printf("📡 开始处理smux session: %s", proxy.ProxyID)

	// 处理控制流消息
	go func() {
		for {
			select {
			case <-proxy.stopChan:
				return
			default:
			}

			pb, err := readUntilEnd(ctlStream)
			if err != nil {
				log.Printf("❌ 控制流读取失败: %v", err)
				return
			}

			p := unmarshal(pb)
			switch p.CMD {
			case CTL_CONNECT_ME:
				// 收到连接请求，建立数据流
				for i := byte(0); i < p.N; i++ {
					go cm.handleDataStream(session)
				}
			case CTL_CLEANUP:
				log.Printf("📥 收到清理信号")
				return
			}
		}
	}()

	// 保持session活跃
	<-proxy.stopChan
}

// handleDataStream 处理数据流
func (cm *ConnectionManager) handleDataStream(session *smux.Session) {
	stream, err := session.OpenStream()
	if err != nil {
		log.Printf("❌ 建立数据流失败: %v", err)
		return
	}
	defer stream.Close()

	// 在数据流上处理SOCKS5连接
	cm.handleSocks5OnStream(stream)
}

// handleSocks5OnStream 在流上处理SOCKS5连接
func (cm *ConnectionManager) handleSocks5OnStream(stream net.Conn) {
	// SOCKS5握手
	if !cm.performSocks5Handshake(stream) {
		return
	}

	// 处理连接请求
	targetAddr, err := cm.handleSocks5ConnectRequest(stream)
	if err != nil {
		log.Printf("❌ SOCKS5连接请求失败: %v", err)
		return
	}

	// 连接到目标地址
	targetConn, err := net.DialTimeout("tcp", targetAddr, 10*time.Second)
	if err != nil {
		log.Printf("❌ 连接目标地址失败: %s, %v", targetAddr, err)
		// 发送连接失败响应
		stream.Write([]byte{0x05, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00})
		return
	}
	defer targetConn.Close()

	// 发送连接成功响应
	stream.Write([]byte{0x05, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00})

	log.Printf("✅ 成功连接到目标: %s", targetAddr)

	// 双向转发数据
	cm.forwardData(stream, targetConn)
}

// performSocks5Handshake 执行SOCKS5握手
func (cm *ConnectionManager) performSocks5Handshake(conn net.Conn) bool {
	// 读取客户端握手请求
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil || n < 3 {
		log.Printf("❌ 读取SOCKS5握手请求失败: %v", err)
		return false
	}

	// 检查SOCKS版本
	if buf[0] != 0x05 {
		log.Printf("❌ 不支持的SOCKS版本: %d", buf[0])
		return false
	}

	// 发送握手响应 (无认证)
	_, err = conn.Write([]byte{0x05, 0x00})
	if err != nil {
		log.Printf("❌ 发送SOCKS5握手响应失败: %v", err)
		return false
	}

	return true
}

// handleSocks5ConnectRequest 处理SOCKS5连接请求
func (cm *ConnectionManager) handleSocks5ConnectRequest(conn net.Conn) (string, error) {
	// 读取连接请求
	buf := make([]byte, 256)
	n, err := conn.Read(buf)
	if err != nil || n < 7 {
		return "", fmt.Errorf("读取连接请求失败: %v", err)
	}

	// 检查请求格式
	if buf[0] != 0x05 || buf[1] != 0x01 || buf[2] != 0x00 {
		return "", fmt.Errorf("无效的连接请求")
	}

	var targetAddr string
	var port uint16

	// 解析地址类型
	switch buf[3] {
	case 0x01: // IPv4
		if n < 10 {
			return "", fmt.Errorf("IPv4地址数据不完整")
		}
		targetAddr = fmt.Sprintf("%d.%d.%d.%d", buf[4], buf[5], buf[6], buf[7])
		port = uint16(buf[8])<<8 | uint16(buf[9])
	case 0x03: // 域名
		if n < 5 {
			return "", fmt.Errorf("域名数据不完整")
		}
		domainLen := int(buf[4])
		if n < 5+domainLen+2 {
			return "", fmt.Errorf("域名数据不完整")
		}
		targetAddr = string(buf[5 : 5+domainLen])
		port = uint16(buf[5+domainLen])<<8 | uint16(buf[5+domainLen+1])
	case 0x04: // IPv6
		return "", fmt.Errorf("暂不支持IPv6")
	default:
		return "", fmt.Errorf("不支持的地址类型: %d", buf[3])
	}

	return fmt.Sprintf("%s:%d", targetAddr, port), nil
}
