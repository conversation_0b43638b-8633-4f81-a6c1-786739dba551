package request

// ListenerSearch 监听器搜索请求结构体
type ListenerSearch struct {
	Type              string `json:"type" form:"type"`
	Status            int    `json:"status" form:"status"`
	Remark            string `json:"remark" form:"remark"`
	LocalListenAddr   string `json:"localListenAddr" form:"localListenAddr"`
	RemoteConnectAddr string `json:"remoteConnectAddr" form:"remoteConnectAddr"`
	PageInfo
}

// ListenerStatusChange 监听器状态变更请求结构体
type ListenerStatusChange struct {
	ID     uint `json:"id" form:"id"`
	Status int  `json:"status" form:"status"`
}

// ForwardConnectionRequest 正向连接请求结构体
type ForwardConnectionRequest struct {
	ListenerID uint   `json:"listenerId" form:"listenerId" binding:"required"`
	ClientAddr string `json:"clientAddr" form:"clientAddr" binding:"required"`
}

// ForwardConnectionConfigRequest 正向连接配置请求结构体
type ForwardConnectionConfigRequest struct {
	ListenerID    uint   `json:"listenerId" form:"listenerId" binding:"required"`
	ClientAddr    string `json:"clientAddr" form:"clientAddr" binding:"required"`
	MaxRetries    int    `json:"maxRetries" form:"maxRetries" binding:"min=1,max=20"`
	RetryInterval int    `json:"retryInterval" form:"retryInterval" binding:"min=1,max=300"`
	Remark        string `json:"remark" form:"remark"`
}

// ForwardConnectionListRequest 正向连接列表请求结构体
type ForwardConnectionListRequest struct {
	ListenerID uint `json:"listenerId" form:"listenerId" binding:"required"`
	PageInfo
}
