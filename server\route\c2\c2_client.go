package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type ClientRoute struct{}

// InitClientRoute 初始化客户端路由
func (r *ClientRoute) InitClientRoute(Router *gin.RouterGroup) {
	clientRouter := Router.Group("client").Use(middleware.OperationRecord())

	{
		// 获取客户端列表
		clientRouter.POST("/list", clientApi.GetClientList)
		// 获取单个客户端
		clientRouter.GET(":id", clientApi.GetClient)
		// 更新客户端备注
		clientRouter.PUT("", clientApi.UpdateClientRemark)
		// 删除客户端
		clientRouter.DELETE(":id", clientApi.DeleteClient)
		// 发送命令
		clientRouter.POST("/command", clientApi.SendCommand)
		// 发送多终端命令
		clientRouter.POST("/command/terminal", clientApi.SendCommandToTerminal)
		// 断开连接
		clientRouter.POST("/disconnect", clientApi.DisconnectClient)
		// 清除所有离线客户端
		clientRouter.DELETE("/offline", clientApi.ClearOfflineClients)

		// 心跳数据流 (SSE)
		clientRouter.GET("/:id/heartbeat-stream", clientApi.GetClientHeartbeatStream)
		// 客户端管理指令
		clientRouter.POST("/:id/reconnect", clientApi.SendClientReconnect)
		clientRouter.POST("/:id/config-update", clientApi.SendClientConfigUpdate)

		// 多终端管理
		clientRouter.POST("/:id/terminal/create", clientApi.CreateBackupTerminal)
		clientRouter.POST("/:id/terminal/close", clientApi.CloseBackupTerminal)
		clientRouter.GET("/:id/terminal/list", clientApi.GetTerminalList)
		clientRouter.POST("/:id/execute-command", clientApi.ExecuteClientCommand)
	}
}
