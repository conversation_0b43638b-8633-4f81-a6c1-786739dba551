package reverse

import (
	"errors"
	"io"
	"net"
	"server/global"
	"sync/atomic"
	"time"

	"github.com/xtaci/smux"
	"go.uber.org/zap"
)

// serverHandshake 服务器端握手
func (s *ReverseProxyServer) serverHandshake() (*smux.Session, *smux.Stream, error) {
	var conn net.Conn
	var session *smux.Session
	var ctlStream *smux.Stream
	var err error

	for {
		select {
		case <-s.ctx.Done():
			return nil, nil, errors.New("服务已停止")
		default:
		}

		conn, err = s.clientListener.Accept()
		if err != nil {
			continue
		}

		session, err = smux.Server(conn, &smux.Config{
			Version:           2,
			KeepAliveInterval: 30 * time.Second,
			KeepAliveTimeout:  60 * time.Second,
			MaxFrameSize:      32768,
			MaxReceiveBuffer:  4194304,
			MaxStreamBuffer:   65536,
		})
		if err != nil {
			conn.Close()
			continue
		}

		ctlStream, err = session.AcceptStream()
		if err != nil {
			session.Close()
			continue
		}

		pb, err := readUntilEnd(ctlStream)
		if err != nil {
			ctlStream.Close()
			session.Close()
			continue
		}

		p := unmarshal(pb)
		if p.CMD == CTL_HANDSHAKE && p.N == CLIENT_HANDSHAKE {
			ctlStream.Write(marshal(Protocol{
				CMD: CTL_HANDSHAKE,
				N:   SERVER_HANDSHAKE,
			}))
			break
		}

		ctlStream.Close()
		session.Close()
	}

	return session, ctlStream, nil
}

// handleClientConnection 处理客户端连接
func (s *ReverseProxyServer) handleClientConnection() {
	session, ctlStream, err := s.serverHandshake()
	if err != nil {
		if global.LOG != nil {
			global.LOG.Error("客户端握手失败", zap.Error(err))
		}
		return
	}

	s.mu.Lock()
	s.session = session
	s.ctlStream = ctlStream
	s.mu.Unlock()

	if global.LOG != nil {
		global.LOG.Info("✅ [REVERSE-SERVER] 客户端握手成功",
			zap.String("remoteAddr", session.RemoteAddr().String()))
	}

	// 处理控制流读取
	go func() {
		defer func() {
			s.mu.Lock()
			s.session = nil
			s.ctlStream = nil
			s.mu.Unlock()
		}()

		for {
			select {
			case <-s.ctx.Done():
				return
			default:
			}

			pb, err := readUntilEnd(ctlStream)
			if err != nil {
				if global.LOG != nil {
					global.LOG.Warn("控制连接已断开", zap.Error(err))
				}
				return
			}

			p := unmarshal(pb)
			switch p.CMD {
			case CTL_CLEANUP:
				if global.LOG != nil {
					global.LOG.Info("收到客户端清理信号")
				}
				return
			}
		}
	}()

	// 处理数据流
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
		}

		remoteStream, err := session.AcceptStream()
		if err != nil {
			if global.LOG != nil {
				global.LOG.Warn("接受数据流失败", zap.Error(err))
			}
			continue
		}

		// 从缓冲区获取用户连接
		select {
		case userConn := <-s.userConnBuffer:
			go s.forwardConnections(userConn, remoteStream)
		case <-time.After(30 * time.Second):
			remoteStream.Close()
			if global.LOG != nil {
				global.LOG.Warn("等待用户连接超时")
			}
		case <-s.ctx.Done():
			remoteStream.Close()
			return
		}
	}
}

// handleUserConnections 处理用户连接
func (s *ReverseProxyServer) handleUserConnections() {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
		}

		userConn, err := s.userListener.Accept()
		if err != nil {
			continue
		}

		atomic.AddInt64(&s.stats.totalConnections, 1)
		atomic.AddInt64(&s.stats.activeConnections, 1)

		// 将用户连接放入缓冲区
		select {
		case s.userConnBuffer <- userConn:
			// 通知客户端建立连接
			s.mu.RLock()
			ctlStream := s.ctlStream
			s.mu.RUnlock()

			if ctlStream != nil {
				_, err = ctlStream.Write(marshal(Protocol{
					CMD: CTL_CONNECT_ME,
					N:   1,
				}))
				if err != nil {
					if global.LOG != nil {
						global.LOG.Warn("发送连接请求失败", zap.Error(err))
					}
					userConn.Close()
					atomic.AddInt64(&s.stats.activeConnections, -1)
				}
			} else {
				userConn.Close()
				atomic.AddInt64(&s.stats.activeConnections, -1)
				if global.LOG != nil {
					global.LOG.Warn("控制连接不可用")
				}
			}
		case <-time.After(5 * time.Second):
			userConn.Close()
			atomic.AddInt64(&s.stats.activeConnections, -1)
			if global.LOG != nil {
				global.LOG.Warn("用户连接缓冲区已满")
			}
		case <-s.ctx.Done():
			userConn.Close()
			return
		}
	}
}

// forwardConnections 转发用户连接和客户端流
func (s *ReverseProxyServer) forwardConnections(userConn net.Conn, remoteStream net.Conn) {
	defer func() {
		userConn.Close()
		remoteStream.Close()
		atomic.AddInt64(&s.stats.activeConnections, -1)
	}()

	if global.LOG != nil {
		global.LOG.Info("🔄 [REVERSE-SERVER] 开始转发连接",
			zap.String("userAddr", userConn.RemoteAddr().String()))
	}

	// 双向转发
	done := make(chan struct{}, 2)

	// 用户 -> 客户端
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(remoteStream, userConn)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("用户->客户端转发结束", zap.Error(err))
		}
		atomic.AddInt64(&s.stats.bytesTransferred, written)
	}()

	// 客户端 -> 用户
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(userConn, remoteStream)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("客户端->用户转发结束", zap.Error(err))
		}
		atomic.AddInt64(&s.stats.bytesTransferred, written)
	}()

	// 等待任一方向完成
	<-done

	if global.LOG != nil {
		global.LOG.Info("🔚 [REVERSE-SERVER] 连接转发结束",
			zap.String("userAddr", userConn.RemoteAddr().String()))
	}
}
