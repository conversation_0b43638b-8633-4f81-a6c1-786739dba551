/**
 * SSE (Server-Sent Events) 工具类
 * 用于处理服务器推送的实时数据
 */

class SSEManager {
  constructor() {
    this.connections = new Map() // 存储所有SSE连接
  }

  /**
   * 创建SSE连接
   * @param {string} url SSE接口地址
   * @param {Object} options 配置选项
   * @returns {EventSource} SSE连接实例
   */
  connect(url, options = {}) {
    const {
      onOpen = null,
      onMessage = null,
      onError = null,
      onClose = null,
      events = {}, // 自定义事件处理器
      reconnect = true,
      reconnectInterval = 3000,
      maxReconnectAttempts = 5,
      allowMultiple = false // 新增：是否允许多个连接
    } = options

    // 如果不允许多个连接且已存在连接，检查状态
    if (!allowMultiple && this.connections.has(url)) {
      const existingConnection = this.connections.get(url)
      if (existingConnection.eventSource && existingConnection.eventSource.readyState === EventSource.OPEN) {
        console.log(`⚠️ SSE连接已存在且正常，跳过重复连接: ${url}`)
        return existingConnection.eventSource
      }
      console.log(`🔄 关闭已存在的SSE连接: ${url}`)
      this.disconnect(url)
    }

    // 如果允许多个连接，为URL添加唯一标识
    let connectionKey = url
    if (allowMultiple) {
      connectionKey = `${url}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      console.log(`🔗 创建多连接SSE，连接键: ${connectionKey}`)
    }

    let reconnectAttempts = 0
    let reconnectTimer = null

    const createConnection = () => {
      console.log(`🔗 创建SSE连接: ${url}`)

      const eventSource = new EventSource(url)

      // 连接打开
      eventSource.onopen = (event) => {
        console.log(`✅ SSE连接已建立: ${url}`)
        reconnectAttempts = 0 // 重置重连次数
        if (onOpen) onOpen(event)
      }

      // 接收消息
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (onMessage) onMessage(data, event)
        } catch (error) {
          console.error('解析SSE消息失败:', error, event.data)
        }
      }

      // 处理自定义事件
      Object.keys(events).forEach(eventType => {
        eventSource.addEventListener(eventType, (event) => {
          try {
            const data = JSON.parse(event.data)
            events[eventType](data, event)
          } catch (error) {
            console.error(`解析SSE事件 [${eventType}] 失败:`, error, event.data)
          }
        })
      })

      // 连接错误
      eventSource.onerror = (event) => {
        console.error(`❌ SSE连接错误: ${url}`, event)

        if (eventSource.readyState === EventSource.CLOSED) {
          console.log(`🔌 SSE连接已关闭: ${url}`)
          this.connections.delete(connectionKey)

          if (onClose) onClose(event)

          // 自动重连 - 使用指数退避策略
          if (reconnect && reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++
            // 指数退避：基础间隔 * 2^(重连次数-1)，最大不超过30秒
            const backoffInterval = Math.min(reconnectInterval * Math.pow(2, reconnectAttempts - 1), 30000)
            console.log(`🔄 尝试重连SSE (${reconnectAttempts}/${maxReconnectAttempts}) 延迟${backoffInterval}ms: ${url}`)

            reconnectTimer = setTimeout(() => {
              createConnection()
            }, backoffInterval)
          } else if (reconnectAttempts >= maxReconnectAttempts) {
            console.error(`💥 SSE重连失败，已达到最大重试次数: ${url}`)
          }
        } else if (eventSource.readyState === EventSource.CONNECTING) {
          console.log(`🔄 SSE正在重连: ${url}`)
        }

        if (onError) onError(event)
      }

      // 存储连接信息
      this.connections.set(connectionKey, {
        eventSource,
        reconnectTimer,
        options,
        originalUrl: url,
        connectionKey
      })

      return eventSource
    }

    return createConnection()
  }

  /**
   * 断开SSE连接
   * @param {string} url SSE接口地址
   */
  disconnect(url) {
    const connection = this.connections.get(url)
    if (connection) {
      console.log(`🔌 断开SSE连接: ${url}`)
      
      // 清除重连定时器
      if (connection.reconnectTimer) {
        clearTimeout(connection.reconnectTimer)
      }
      
      // 关闭连接
      if (connection.eventSource) {
        connection.eventSource.close()
      }
      
      this.connections.delete(url)
    }
  }

  /**
   * 断开所有SSE连接
   */
  disconnectAll() {
    console.log('🔌 断开所有SSE连接')
    for (const url of this.connections.keys()) {
      this.disconnect(url)
    }
  }

  /**
   * 获取连接状态
   * @param {string} url SSE接口地址
   * @returns {number} 连接状态
   */
  getConnectionState(url) {
    const connection = this.connections.get(url)
    if (connection && connection.eventSource) {
      return connection.eventSource.readyState
    }
    return EventSource.CLOSED
  }

  /**
   * 检查连接是否活跃
   * @param {string} url SSE接口地址
   * @returns {boolean} 是否活跃
   */
  isConnected(url) {
    return this.getConnectionState(url) === EventSource.OPEN
  }

  /**
   * 获取所有连接信息
   * @returns {Array} 连接信息列表
   */
  getAllConnections() {
    const connections = []
    for (const [url, connection] of this.connections.entries()) {
      connections.push({
        url,
        state: connection.eventSource ? connection.eventSource.readyState : EventSource.CLOSED,
        stateText: this.getStateText(connection.eventSource ? connection.eventSource.readyState : EventSource.CLOSED)
      })
    }
    return connections
  }

  /**
   * 获取连接状态文本
   * @param {number} state 连接状态
   * @returns {string} 状态文本
   */
  getStateText(state) {
    switch (state) {
      case EventSource.CONNECTING:
        return 'CONNECTING'
      case EventSource.OPEN:
        return 'OPEN'
      case EventSource.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }
}

// 创建全局SSE管理器实例
const sseManager = new SSEManager()

// 页面卸载时自动断开所有连接
window.addEventListener('beforeunload', () => {
  sseManager.disconnectAll()
})

export default sseManager
