import { get, post, del } from '@/utils/request'

/**
 * 客户端截图
 * @param {Number} clientId 客户端ID
 * @param {Object} data 截图参数 {type, format, quality, x, y, width, height}
 * @returns {Promise}
 */
export function takeScreenshot(clientId, data) {
  return post(`/screenshot/take/${clientId}`, data)
}

/**
 * 获取客户端截图列表
 * @param {Number} clientId 客户端ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getScreenshotList(clientId, params = {}) {
  return post(`/screenshot/list/${clientId}`, params)
}

/**
 * 删除截图
 * @param {Number} clientId 客户端ID
 * @param {String} filename 文件名
 * @returns {Promise}
 */
export function deleteScreenshot(clientId, filename) {
  return del(`/screenshot/delete/${clientId}/${encodeURIComponent(filename)}`)
}

/**
 * 开始屏幕流
 * @param {Number} clientId 客户端ID
 * @param {Object} data 流参数 {quality, fps}
 * @returns {Promise}
 */
export function startScreenStream(clientId, data = {}) {
  return post(`/screenshot/stream/start/${clientId}`, data)
}

/**
 * 停止屏幕流
 * @param {Number} clientId 客户端ID
 * @returns {Promise}
 */
export function stopScreenStream(clientId) {
  return post(`/screenshot/stream/stop/${clientId}`, {})
}

/**
 * 获取显示器列表
 * @param {Number} clientId 客户端ID
 * @returns {Promise}
 */
export function getMonitorList(clientId) {
  return get(`/screenshot/monitors/${clientId}`)
}

/**
 * 获取屏幕流数据
 * @param {Number} clientId 客户端ID
 * @returns {Promise}
 */
export function getStreamData(clientId) {
  return get(`/screenshot/stream/data/${clientId}`)
}

/**
 * 获取所有截图列表（不限制客户端）
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getAllScreenshots(params = {}) {
  return post('/screenshot/all', params)
}

/**
 * 根据截图ID删除截图
 * @param {Number} screenshotId 截图ID
 * @returns {Promise}
 */
export function deleteScreenshotById(screenshotId) {
  return del(`/screenshot/deleteById/${screenshotId}`)
}

/**
 * 启动定时截图
 * @param {Number} clientId 客户端ID
 * @param {Object} data 定时截图参数
 * @returns {Promise}
 */
export function startTimedScreenshot(clientId, data) {
  return post(`/screenshot/timer/start/${clientId}`, data)
}

/**
 * 停止定时截图
 * @param {Number} clientId 客户端ID
 * @returns {Promise}
 */
export function stopTimedScreenshot(clientId) {
  return post(`/screenshot/timer/stop/${clientId}`, {})
}

/**
 * 获取定时截图状态
 * @param {Number} clientId 客户端ID
 * @returns {Promise}
 */
export function getTimedScreenshotStatus(clientId) {
  return get(`/screenshot/timer/status/${clientId}`)
}

// 导出API对象
export const screenshotApi = {
  takeScreenshot,
  getScreenshotList,
  deleteScreenshot,
  startScreenStream,
  stopScreenStream,
  getMonitorList,
  getAllScreenshots,
  deleteScreenshotById,
  getStreamData,
  startTimedScreenshot,
  stopTimedScreenshot,
  getTimedScreenshotStatus
}

export default screenshotApi