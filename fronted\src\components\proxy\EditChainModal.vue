<template>
  <a-modal
    v-model:visible="modalVisible"
    title="编辑代理链"
    width="600px"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleClose"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="代理链名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入代理链名称" />
      </a-form-item>

      <a-form-item label="描述">
        <a-textarea
          v-model:value="form.description"
          :rows="2"
          placeholder="请输入代理链描述（可选）"
        />
      </a-form-item>

      <a-form-item label="路由策略" name="strategy">
        <a-radio-group v-model:value="form.strategy">
          <a-radio value="sequential">顺序连接</a-radio>
          <a-radio value="random">随机选择</a-radio>
          <a-radio value="round_robin">轮询</a-radio>
          <a-radio value="least_connections">最少连接</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="超时时间(秒)">
            <a-input-number
              v-model:value="form.timeout"
              :min="1"
              :max="3600"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="重试次数">
            <a-input-number
              v-model:value="form.retry_count"
              :min="0"
              :max="10"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="高级选项">
        <a-space direction="vertical">
          <a-checkbox v-model:checked="form.health_check">启用健康检查</a-checkbox>
          <a-checkbox v-model:checked="form.load_balancing">启用负载均衡</a-checkbox>
          <a-checkbox v-model:checked="form.failover">启用故障转移</a-checkbox>
          <a-checkbox v-model:checked="form.enable_metrics">启用指标收集</a-checkbox>
        </a-space>
      </a-form-item>

      <div v-if="form.health_check">
        <a-divider orientation="left">健康检查配置</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="检查间隔(秒)">
              <a-input-number
                v-model:value="form.health_check_interval"
                :min="5"
                :max="300"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="失败阈值">
              <a-input-number
                v-model:value="form.health_check_threshold"
                :min="1"
                :max="10"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
// import { updateProxyChain } from '@/api/proxy' // 暂未实现

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  chainData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const modalVisible = ref(false)

// 表单数据
const form = reactive({
  id: null,
  name: '',
  description: '',
  strategy: 'sequential',
  timeout: 30,
  retry_count: 3,
  health_check: true,
  load_balancing: false,
  failover: true,
  enable_metrics: true,
  health_check_interval: 30,
  health_check_threshold: 3
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入代理链名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  strategy: [
    { required: true, message: '请选择路由策略', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
  if (val && props.chainData) {
    // 初始化表单数据
    Object.assign(form, {
      id: props.chainData.id,
      name: props.chainData.name,
      description: props.chainData.description || '',
      strategy: props.chainData.strategy || 'sequential',
      timeout: props.chainData.timeout || 30,
      retry_count: props.chainData.retry_count || 3,
      health_check: props.chainData.health_check !== false,
      load_balancing: props.chainData.load_balancing || false,
      failover: props.chainData.failover !== false,
      enable_metrics: props.chainData.enable_metrics !== false,
      health_check_interval: props.chainData.health_check_interval || 30,
      health_check_threshold: props.chainData.health_check_threshold || 3
    })
  }
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const handleClose = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    id: null,
    name: '',
    description: '',
    strategy: 'sequential',
    timeout: 30,
    retry_count: 3,
    health_check: true,
    load_balancing: false,
    failover: true,
    enable_metrics: true,
    health_check_interval: 30,
    health_check_threshold: 3
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    // 暂未实现代理链功能
    message.info('代理链功能暂未实现')

  } catch (error) {
    if (error.errorFields) {
      return
    }
    console.error('更新代理链失败:', error)
    message.error('表单验证失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}
</style>
