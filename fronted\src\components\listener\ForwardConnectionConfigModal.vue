<template>
  <a-modal
    title="正向连接配置管理"
    :open="visible"
    :footer="null"
    @cancel="handleCancel"
    width="900px"
    :destroyOnClose="true"
  >
    <div class="forward-config-container">
      <!-- 操作栏 -->
      <div class="action-bar">
        <a-button type="primary" @click="showAddModal">
          <template #icon><PlusOutlined /></template>
          添加配置
        </a-button>
        <a-button @click="loadConfigs" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>

      <!-- 配置列表 -->
      <a-table
        :columns="columns"
        :data-source="configList"
        :loading="loading"
        :pagination="false"
        rowKey="id"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <template v-if="column.dataIndex === 'lastConnectAt'">
            {{ record.lastConnectAt ? formatTime(record.lastConnectAt) : '从未连接' }}
          </template>
          
          <template v-if="column.dataIndex === 'retryInfo'">
            <div>
              <div>当前重连: {{ record.retryCount }}/{{ record.maxRetries }}</div>
              <div v-if="record.retryCount >= record.maxRetries" style="color: red;">
                已达最大重连次数
              </div>
            </div>
          </template>
          
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个配置吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" danger size="small">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 添加/编辑配置弹窗 -->
    <a-modal
      :title="isEdit ? '编辑正向连接配置' : '添加正向连接配置'"
      :open="formModalVisible"
      :confirm-loading="formLoading"
      @cancel="handleFormCancel"
      @ok="handleFormSubmit"
      width="500px"
    >
      <a-form
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        ref="formRef"
      >
        <a-form-item label="客户端地址" name="clientAddr">
          <a-input 
            v-model:value="formData.clientAddr" 
            placeholder="例如: 192.168.1.100:4444"
            :disabled="isEdit"
          />
        </a-form-item>
        
        <a-form-item label="最大重连次数" name="maxRetries">
          <a-input-number 
            v-model:value="formData.maxRetries" 
            :min="1" 
            :max="20"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="重连间隔(秒)" name="retryInterval">
          <a-input-number 
            v-model:value="formData.retryInterval" 
            :min="1" 
            :max="300"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea 
            v-model:value="formData.remark" 
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { listenerApi } from '@/api';
import { formatTime } from '@/utils/format';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  listenerId: {
    type: Number,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:visible']);

// 响应式数据
const loading = ref(false);
const configList = ref([]);
const formModalVisible = ref(false);
const formLoading = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 表单数据
const formData = reactive({
  clientAddr: '',
  maxRetries: 5,
  retryInterval: 5,
  remark: ''
});

// 表格列定义
const columns = [
  {
    title: '客户端地址',
    dataIndex: 'clientAddr',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
  },
  {
    title: '重连信息',
    dataIndex: 'retryInfo',
    width: 120,
  },
  {
    title: '最后连接时间',
    dataIndex: 'lastConnectAt',
    width: 150,
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
  },
];

// 表单验证规则
const formRules = {
  clientAddr: [
    { required: true, message: '请输入客户端地址', trigger: 'blur' },
    { pattern: /^.+:\d+$/, message: '请输入正确的地址格式，如: 192.168.1.100:4444', trigger: 'blur' }
  ],
  maxRetries: [
    { required: true, message: '请输入最大重连次数', trigger: 'blur' }
  ],
  retryInterval: [
    { required: true, message: '请输入重连间隔', trigger: 'blur' }
  ]
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.listenerId) {
    loadConfigs();
  }
});

// 加载配置列表
const loadConfigs = async () => {
  if (!props.listenerId) return;
  
  loading.value = true;
  try {
    const response = await listenerApi.getForwardConnectionConfigs(props.listenerId);
    if (response.code === 200) {
      configList.value = response.data || [];
    } else {
      message.error(response.msg || '获取配置列表失败');
    }
  } catch (error) {
    console.error('获取配置列表失败:', error);
    message.error('获取配置列表失败');
  } finally {
    loading.value = false;
  }
};

// 显示添加弹窗
const showAddModal = () => {
  isEdit.value = false;
  Object.assign(formData, {
    clientAddr: '',
    maxRetries: 5,
    retryInterval: 5,
    remark: ''
  });
  formModalVisible.value = true;
};

// 显示编辑弹窗
const showEditModal = (record) => {
  isEdit.value = true;
  Object.assign(formData, {
    id: record.id,
    clientAddr: record.clientAddr,
    maxRetries: record.maxRetries,
    retryInterval: record.retryInterval,
    remark: record.remark
  });
  formModalVisible.value = true;
};

// 处理表单提交
const handleFormSubmit = async () => {
  try {
    await formRef.value.validate();
    formLoading.value = true;
    
    const data = {
      listenerId: props.listenerId,
      clientAddr: formData.clientAddr,
      maxRetries: formData.maxRetries,
      retryInterval: formData.retryInterval,
      remark: formData.remark
    };
    
    const response = await listenerApi.addForwardConnectionConfig(data);
    if (response.code === 200) {
      message.success(isEdit.value ? '更新配置成功' : '添加配置成功');
      formModalVisible.value = false;
      loadConfigs();
    } else {
      message.error(response.msg || '操作失败');
    }
  } catch (error) {
    console.error('表单提交失败:', error);
    if (error.errorFields) {
      // 表单验证失败
      return;
    }
    message.error('操作失败');
  } finally {
    formLoading.value = false;
  }
};

// 处理删除
const handleDelete = async (record) => {
  try {
    const response = await listenerApi.removeForwardConnectionConfig(props.listenerId, record.clientAddr);
    if (response.code === 200) {
      message.success('删除配置成功');
      loadConfigs();
    } else {
      message.error(response.msg || '删除配置失败');
    }
  } catch (error) {
    console.error('删除配置失败:', error);
    message.error('删除配置失败');
  }
};

// 处理弹窗关闭
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理表单弹窗关闭
const handleFormCancel = () => {
  formModalVisible.value = false;
};
</script>

<style scoped>
.forward-config-container {
  padding: 16px 0;
}

.action-bar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}
</style>
