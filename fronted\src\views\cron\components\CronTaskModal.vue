<template>
  <a-modal
    :visible="visible"
    :title="isEdit ? '编辑任务' : '创建任务'"
    width="800px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      layout="vertical"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="任务名称" name="name">
            <a-input v-model:value="form.name" placeholder="请输入任务名称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="目标客户端" name="client_id">
            <a-select
              v-model:value="form.client_id"
              placeholder="选择目标客户端"
              show-search
              :filter-option="filterClient"
            >
              <a-select-option
                v-for="client in clientList"
                :key="client.id"
                :value="client.id"
              >
                {{ client.hostname || client.remoteAddr }} ({{ client.os }})
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="任务类型" name="task_type">
            <a-select
              v-model:value="form.task_type"
              placeholder="选择任务类型"
              @change="handleTaskTypeChange"
            >
              <a-select-option value="command">命令执行</a-select-option>
              <a-select-option value="screenshot">截图任务</a-select-option>
              <a-select-option value="script">脚本执行</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="任务描述" name="description">
            <a-textarea
              v-model:value="form.description"
              placeholder="请输入任务描述（可选）"
              :rows="3"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Cron表达式" name="cron_expr">
            <CronBuilder
              v-model="form.cron_expr"
              @validate="handleCronValidation"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 脚本类型选择 -->
      <a-form-item
        v-if="form.task_type === 'script'"
        label="脚本类型"
        name="script_type"
      >
        <a-select
          v-model:value="form.script_type"
          placeholder="选择脚本类型"
          @change="handleScriptTypeChange"
        >
          <a-select-option
            v-for="scriptType in availableScriptTypes"
            :key="scriptType.value"
            :value="scriptType.value"
          >
            {{ scriptType.label }}
          </a-select-option>
        </a-select>
        <div class="script-type-help" style="margin-top: 8px;">
          <a-alert
            :message="getScriptTypeDescription(form.script_type)"
            type="info"
            show-icon
          />
        </div>
      </a-form-item>

      <!-- 命令/脚本内容 -->
      <a-form-item
        v-if="form.task_type === 'command' || form.task_type === 'script'"
        :label="form.task_type === 'command' ? '执行命令' : '脚本内容'"
        name="command"
      >
        <a-textarea
          v-model:value="form.command"
          :placeholder="getCommandPlaceholder()"
          :rows="6"
          :auto-size="{ minRows: 6, maxRows: 12 }"
          class="command-textarea"
        />
        <div v-if="form.task_type === 'script'" class="script-help">
          <a-alert
            message="脚本执行说明"
            :description="getScriptExecutionDescription()"
            type="info"
            show-icon
            style="margin-top: 8px"
          />
        </div>
      </a-form-item>

      <!-- 截图任务参数 -->
      <div v-if="form.task_type === 'screenshot'">
        <a-form-item label="截图参数">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="截图类型">
                <a-select v-model:value="screenshotParams.type">
                  <a-select-option :value="0">全屏截图</a-select-option>
                  <a-select-option :value="1">活动窗口</a-select-option>
                  <a-select-option :value="2">指定区域</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="图片质量">
                <a-slider
                  v-model:value="screenshotParams.quality"
                  :min="1"
                  :max="100"
                  :marks="{ 1: '1%', 50: '50%', 100: '100%' }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="图片格式">
                <a-select v-model:value="screenshotParams.format">
                  <a-select-option value="png">PNG</a-select-option>
                  <a-select-option value="jpg">JPG</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
      </div>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="超时时间(秒)" name="timeout">
            <a-input-number
              v-model:value="form.timeout"
              :min="1"
              :max="3600"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="重试次数" name="retry_count">
            <a-input-number
              v-model:value="form.retry_count"
              :min="0"
              :max="10"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import * as cronApi from '@/api/cron'
import CronBuilder from './CronBuilder.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  },
  clientList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  name: '',
  description: '',
  client_id: undefined,
  task_type: 'command',
  script_type: 'sh', // 默认脚本类型
  cron_expr: '',
  command: '',
  timeout: 300,
  retry_count: 0,
  params: {}
})

// 截图参数
const screenshotParams = reactive({
  type: 0,
  quality: 80,
  format: 'png',
  monitor_index: 0
})

// 脚本类型定义
const scriptTypes = {
  windows: [
    { value: 'bat', label: 'Batch (.bat)', description: 'Windows批处理脚本' },
    { value: 'ps1', label: 'PowerShell (.ps1)', description: 'PowerShell脚本' },
    { value: 'cmd', label: 'Command (.cmd)', description: 'Windows命令脚本' },
    { value: 'py', label: 'Python (.py)', description: 'Python脚本' }
  ],
  linux: [
    { value: 'sh', label: 'Shell (.sh)', description: 'Shell脚本' },
    { value: 'py', label: 'Python (.py)', description: 'Python脚本' },
    { value: 'pl', label: 'Perl (.pl)', description: 'Perl脚本' }
  ],
  darwin: [
    { value: 'sh', label: 'Shell (.sh)', description: 'Shell脚本' },
    { value: 'py', label: 'Python (.py)', description: 'Python脚本' },
    { value: 'pl', label: 'Perl (.pl)', description: 'Perl脚本' }
  ]
}

// 计算当前客户端可用的脚本类型
const availableScriptTypes = computed(() => {
  if (!form.client_id) return []

  const client = props.clientList.find(c => c.id === form.client_id)
  if (!client) return []

  const os = client.os.toLowerCase()
  if (os.includes('windows')) {
    return scriptTypes.windows
  } else if (os.includes('linux')) {
    return scriptTypes.linux
  } else if (os.includes('darwin') || os.includes('macos')) {
    return scriptTypes.darwin
  }

  // 默认返回Linux脚本类型
  return scriptTypes.linux
})

// Cron表达式验证结果
const cronValidation = reactive({
  valid: null,
  error: '',
  description: '',
  next_runs: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  client_id: [
    { required: true, message: '请选择目标客户端', trigger: 'change' }
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  cron_expr: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' }
  ],
  command: [
    { required: true, message: '请输入命令或脚本内容', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.task)

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    client_id: undefined,
    task_type: 'command',
    cron_expr: '',
    command: '',
    timeout: 300,
    retry_count: 0,
    params: {}
  })

  Object.assign(screenshotParams, {
    type: 0,
    quality: 80,
    format: 'png',
    monitor_index: 0
  })

  Object.assign(cronValidation, {
    valid: null,
    error: '',
    description: '',
    next_runs: []
  })

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 监听任务数据变化
watch(() => props.task, (newTask) => {
  if (newTask) {
    // 编辑模式，填充表单数据
    Object.assign(form, {
      name: newTask.name,
      description: newTask.description,
      client_id: newTask.client_id,
      task_type: newTask.task_type,
      cron_expr: newTask.cron_expr,
      command: newTask.command,
      timeout: newTask.timeout,
      retry_count: newTask.retry_count
    })

    // 如果是截图任务，解析参数
    if (newTask.task_type === 'screenshot' && newTask.params) {
      try {
        const params = JSON.parse(newTask.params)
        Object.assign(screenshotParams, params)
      } catch (error) {
        console.error('解析截图参数失败:', error)
      }
    } else if (newTask.task_type === 'script' && newTask.params) {
      // 如果是脚本任务，解析脚本类型参数
      try {
        const params = JSON.parse(newTask.params)
        if (params.script_type) {
          form.script_type = params.script_type
        }
      } catch (error) {
        console.error('解析脚本参数失败:', error)
        // 设置默认脚本类型
        form.script_type = 'sh'
      }
    }
  } else {
    // 创建模式，重置表单
    resetForm()
  }
}, { immediate: true })

// 处理任务类型变化
const handleTaskTypeChange = (type) => {
  form.command = ''
  if (type === 'screenshot') {
    form.params = { ...screenshotParams }
  } else if (type === 'script') {
    // 设置默认脚本类型
    const defaultScriptType = availableScriptTypes.value[0]?.value || 'sh'
    form.script_type = defaultScriptType
    form.params = { script_type: defaultScriptType }
  } else {
    form.params = {}
  }
}

// 处理脚本类型变化
const handleScriptTypeChange = (scriptType) => {
  form.params = { script_type: scriptType }
}

// 获取脚本类型描述
const getScriptTypeDescription = (scriptType) => {
  const allTypes = [...scriptTypes.windows, ...scriptTypes.linux, ...scriptTypes.darwin]
  const type = allTypes.find(t => t.value === scriptType)
  return type ? type.description : '请选择脚本类型'
}

// 获取命令输入框占位符
const getCommandPlaceholder = () => {
  if (form.task_type === 'command') {
    return '请输入要执行的命令'
  } else if (form.task_type === 'script') {
    const scriptType = form.script_type
    switch (scriptType) {
      case 'bat':
      case 'cmd':
        return '@echo off\necho Hello World\npause'
      case 'ps1':
        return 'Write-Host "Hello World"\nGet-Date'
      case 'sh':
        return '#!/bin/bash\necho "Hello World"\ndate'
      case 'py':
        return 'import datetime\nprint("Hello World")\nprint(datetime.datetime.now())'
      case 'pl':
        return '#!/usr/bin/perl\nprint "Hello World\\n";\nprint scalar localtime, "\\n";'
      default:
        return '请输入脚本内容'
    }
  }
  return ''
}

// 获取脚本执行说明
const getScriptExecutionDescription = () => {
  const scriptType = form.script_type
  switch (scriptType) {
    case 'bat':
    case 'cmd':
      return '脚本将在Windows客户端使用cmd执行，支持批处理命令和变量。'
    case 'ps1':
      return '脚本将在Windows客户端使用PowerShell执行，支持PowerShell命令和模块。'
    case 'sh':
      return '脚本将在客户端使用bash执行，支持Shell命令和语法。'
    case 'py':
      return '脚本将在客户端使用Python解释器执行，支持Python语法和库。'
    case 'pl':
      return '脚本将在客户端使用Perl解释器执行，支持Perl语法和模块。'
    default:
      return '脚本将在客户端执行，请确保脚本语法正确。'
  }
}

// 处理Cron表达式验证结果
const handleCronValidation = (result) => {
  Object.assign(cronValidation, result)
}

// 客户端筛选
const filterClient = (input, option) => {
  const client = props.clientList.find(c => c.id === option.value)
  if (!client) return false
  
  const searchText = input.toLowerCase()
  return (
    (client.hostname && client.hostname.toLowerCase().includes(searchText)) ||
    (client.remoteAddr && client.remoteAddr.toLowerCase().includes(searchText)) ||
    (client.os && client.os.toLowerCase().includes(searchText))
  )
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true

    // 准备提交数据
    const submitData = { ...form }

    // 如果是截图任务，设置参数
    if (form.task_type === 'screenshot') {
      submitData.params = { ...screenshotParams }
    } else if (form.task_type === 'script') {
      // 如果是脚本任务，设置脚本类型参数
      submitData.params = { script_type: form.script_type }
    }

    // 调用API
    if (isEdit.value) {
      await cronApi.updateTask(props.task.id, submitData)
      message.success('任务更新成功')
    } else {
      await cronApi.createTask(submitData)
      message.success('任务创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    message.error(isEdit.value ? '更新任务失败' : '创建任务失败')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.error-text {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.success-text {
  color: #52c41a;
  font-size: 12px;
  margin-top: 4px;
}

.command-textarea {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  line-height: 1.5;

  :deep(.ant-input) {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
  }
}
</style>
