//go:build linux && arm64

package main

import (
"syscall"
"unsafe"
)

// ARM64 register structure for Linux
type PtraceRegsArm64 struct {
Regs   [31]uint64
Sp     uint64
Pc     uint64
Pstate uint64
}

// Register access functions for ARM64 architecture on Linux

func getRIP(regs *syscall.PtraceRegs) uint64 {
// Convert to ARM64 specific structure
arm64Regs := (*PtraceRegsArm64)(unsafe.Pointer(regs))
return arm64Regs.Pc
}

func setRIP(regs *syscall.PtraceRegs, addr uint64) {
// Convert to ARM64 specific structure
arm64Regs := (*PtraceRegsArm64)(unsafe.Pointer(regs))
arm64Regs.Pc = addr
}

func getRSP(regs *syscall.PtraceRegs) uint64 {
// Convert to ARM64 specific structure
arm64Regs := (*PtraceRegsArm64)(unsafe.Pointer(regs))
return arm64Regs.Sp
}

func setRSP(regs *syscall.PtraceRegs, addr uint64) {
// Convert to ARM64 specific structure
arm64Regs := (*PtraceRegsArm64)(unsafe.Pointer(regs))
arm64Regs.Sp = addr
}
