/**
 * 下载相关API
 * 提供文件下载的接口封装
 */

import { downloadManager } from '@/utils/downloadManager'
import { getApiBaseUrl } from '@/utils/serverConfig'

/**
 * 下载服务器文件
 * @param {string} filePath - 服务器文件路径
 * @param {string} fileName - 文件名（可选，如果不提供则从路径中提取）
 * @param {Object} options - 下载选项
 * @param {Function} options.onProgress - 进度回调函数
 * @param {Function} options.onComplete - 完成回调函数
 * @param {Function} options.onError - 错误回调函数
 * @returns {string} 下载任务ID
 */
export function downloadServerFile(filePath, fileName = null, options = {}) {
  // 如果没有提供文件名，从路径中提取
  if (!fileName) {
    fileName = filePath.split('/').pop() || 'download'
  }

  // 默认回调函数
  const defaultOptions = {
    onProgress: (progress) => {
      console.log(`下载进度: ${progress.progress.toFixed(2)}%`)
    },
    onComplete: () => {
      console.log(`文件 ${fileName} 下载完成`)
    },
    onError: (error) => {
      console.error(`文件 ${fileName} 下载失败:`, error)
    }
  }

  // 合并选项
  const finalOptions = { ...defaultOptions, ...options }

  // 开始下载
  return downloadManager.startDownload(
    filePath,
    fileName,
    finalOptions.onProgress,
    finalOptions.onComplete,
    finalOptions.onError
  )
}

/**
 * 批量下载服务器文件
 * @param {Array} files - 文件列表，每个元素包含 {filePath, fileName?}
 * @param {Object} options - 下载选项
 * @returns {Array} 下载任务ID列表
 */
export function downloadMultipleFiles(files, options = {}) {
  const downloadIds = []
  
  files.forEach(file => {
    const downloadId = downloadServerFile(
      file.filePath,
      file.fileName,
      {
        ...options,
        onProgress: (progress) => {
          options.onProgress?.(file.filePath, progress)
        },
        onComplete: () => {
          options.onComplete?.(file.filePath)
        },
        onError: (error) => {
          options.onError?.(file.filePath, error)
        }
      }
    )
    downloadIds.push(downloadId)
  })
  
  return downloadIds
}

/**
 * 获取文件信息（用于预检查文件是否存在、大小等）
 * @param {string} filePath - 服务器文件路径
 * @returns {Promise<Object>} 文件信息
 */
export async function getFileInfo(filePath) {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${baseUrl}/download/server-file?path=${encodeURIComponent(filePath)}`, {
      method: 'HEAD',
      headers: {
        'X-Token': token
      }
    })

    if (!response.ok) {
      // 处理401未授权错误
      if (response.status === 401) {
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1500)
        throw new Error('未授权，请重新登录')
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const contentLength = response.headers.get('content-length')
    const lastModified = response.headers.get('last-modified')
    const contentType = response.headers.get('content-type')
    const acceptRanges = response.headers.get('accept-ranges')

    return {
      size: contentLength ? parseInt(contentLength) : 0,
      lastModified: lastModified ? new Date(lastModified) : null,
      contentType: contentType || 'application/octet-stream',
      supportsRanges: acceptRanges === 'bytes',
      exists: true
    }
  } catch (error) {
    return {
      exists: false,
      error: error.message
    }
  }
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 服务器文件路径
 * @returns {Promise<boolean>} 文件是否存在
 */
export async function checkFileExists(filePath) {
  const fileInfo = await getFileInfo(filePath)
  return fileInfo.exists
}

/**
 * 获取下载URL（用于直接链接下载）
 * @param {string} filePath - 服务器文件路径
 * @returns {string} 下载URL
 */
export function getDownloadUrl(filePath) {
  const baseUrl = getApiBaseUrl()
  const token = localStorage.getItem('token')
  return `${baseUrl}/download/server-file?path=${encodeURIComponent(filePath)}&token=${encodeURIComponent(token)}`
}

/**
 * 暂停下载
 * @param {string} downloadId - 下载任务ID
 */
export function pauseDownload(downloadId) {
  downloadManager.pauseDownload(downloadId)
}

/**
 * 恢复下载
 * @param {string} downloadId - 下载任务ID
 */
export function resumeDownload(downloadId) {
  downloadManager.resumeDownload(downloadId)
}

/**
 * 取消下载
 * @param {string} downloadId - 下载任务ID
 */
export function cancelDownload(downloadId) {
  downloadManager.cancelDownload(downloadId)
}

/**
 * 获取下载任务信息
 * @param {string} downloadId - 下载任务ID
 * @returns {Object|null} 下载任务信息
 */
export function getDownloadInfo(downloadId) {
  return downloadManager.getDownloadInfo(downloadId)
}

/**
 * 获取所有下载任务
 * @returns {Array} 下载任务列表
 */
export function getAllDownloads() {
  return downloadManager.getAllDownloads()
}

/**
 * 获取上传文件列表
 * @param {string} path - 子路径，可选
 * @returns {Promise<Array>} 上传文件列表
 */
export async function getUploadedFiles(path = '') {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    let url = `${baseUrl}/file/uploaded-files`
    if (path) {
      url += `?path=${encodeURIComponent(path)}`
    }
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-Token': token,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      // 处理401未授权错误
      if (response.status === 401) {
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1500)
        throw new Error('未授权，请重新登录')
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result.data || []
  } catch (error) {
    console.error('获取上传文件列表失败:', error)
    throw error
  }
}

/**
 * 获取下载文件列表
 * @param {string} path - 子路径，可选
 * @returns {Promise<Array>} 下载文件列表
 */
export async function getDownloadFiles(path = '') {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    let url = `${baseUrl}/file/download-files`
    if (path) {
      url += `?path=${encodeURIComponent(path)}`
    }
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-Token': token,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      // 处理401未授权错误
      if (response.status === 401) {
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1500)
        throw new Error('未授权，请重新登录')
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result.data || []
  } catch (error) {
    console.error('获取下载文件列表失败:', error)
    throw error
  }
}

/**
 * 删除服务器文件
 * @param {string} filePath - 服务器文件路径
 * @returns {Promise}
 */
export async function deleteServerFile(filePath) {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${baseUrl}/file/server-file`, {
      method: 'DELETE',
      headers: {
        'X-Token': token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        file_path: filePath
      })
    })

    if (!response.ok) {
      // 处理401未授权错误
      if (response.status === 401) {
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1000)
        throw new Error('登录已过期，请重新登录')
      }
      
      const errorData = await response.json()
      throw new Error(errorData.message || '删除文件失败')
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('删除服务器文件失败:', error)
    throw error
  }
}

/**
 * 批量删除服务器文件
 * @param {Array<string>} filePaths - 服务器文件路径数组
 * @returns {Promise}
 */
export async function deleteServerFiles(filePaths) {
  try {
    const baseUrl = getApiBaseUrl()
    const token = localStorage.getItem('token')
    
    const response = await fetch(`${baseUrl}/file/server-files`, {
      method: 'DELETE',
      headers: {
        'X-Token': token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        file_paths: filePaths
      })
    })

    if (!response.ok) {
      // 处理401未授权错误
      if (response.status === 401) {
        localStorage.removeItem('token')
        setTimeout(() => {
          location.reload()
        }, 1000)
        throw new Error('登录已过期，请重新登录')
      }
      
      const errorData = await response.json()
      throw new Error(errorData.message || '批量删除文件失败')
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('批量删除服务器文件失败:', error)
    throw error
  }
}

export default {
  downloadServerFile,
  downloadMultipleFiles,
  getFileInfo,
  checkFileExists,
  getDownloadUrl,
  pauseDownload,
  resumeDownload,
  cancelDownload,
  getDownloadInfo,
  getAllDownloads,
  getUploadedFiles,
  getDownloadFiles,
  deleteServerFile,
  deleteServerFiles
}