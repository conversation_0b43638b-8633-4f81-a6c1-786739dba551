package c2

import (
	"errors"
	"server/core/manager/dbpool"
	"server/factory"
	"server/model/request/network"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"
	"time"

	"gorm.io/gorm"
)

type NetworkService struct{}

func NewNetworkService() *NetworkService {
	return &NetworkService{}
}

// GetNetworkStats 获取网络统计信息
func (n *NetworkService) GetNetworkStats(clientID uint, req network.NetworkStatsRequest) (uint64, error) {
	networkTask := &task.NetworkTask{
		ClientID: clientID,
		TaskType: "network_stats",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("network_stats_task_create", func(db *gorm.DB) error {
		return db.Create(networkTask).Error
	}); err != nil {
		return 0, err
	}

	go n.executeNetworkTask(networkTask, req)
	n.updateTaskStatus(networkTask.ID, "running", "")
	return networkTask.ID, nil
}

// GetNetworkInterfaces 获取网络接口信息
func (n *NetworkService) GetNetworkInterfaces(clientID uint, req network.NetworkInterfacesRequest) (uint64, error) {
	networkTask := &task.NetworkTask{
		ClientID: clientID,
		TaskType: "network_interfaces",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("network_interfaces_task_create", func(db *gorm.DB) error {
		return db.Create(networkTask).Error
	}); err != nil {
		return 0, err
	}

	go n.executeNetworkTask(networkTask, req)
	n.updateTaskStatus(networkTask.ID, "running", "")
	return networkTask.ID, nil
}

// GetNetworkConnections 获取网络连接信息
func (n *NetworkService) GetNetworkConnections(clientID uint, req network.NetworkConnectionsRequest) (uint64, error) {
	networkTask := &task.NetworkTask{
		ClientID: clientID,
		TaskType: "network_connections",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("network_connections_task_create", func(db *gorm.DB) error {
		return db.Create(networkTask).Error
	}); err != nil {
		return 0, err
	}

	go n.executeNetworkTask(networkTask, req)
	n.updateTaskStatus(networkTask.ID, "running", "")
	return networkTask.ID, nil
}

// CloseConnection 关闭网络连接
func (n *NetworkService) CloseConnection(clientID uint, req network.CloseConnectionRequest) (uint64, error) {
	networkTask := &task.NetworkTask{
		ClientID: clientID,
		TaskType: "close_connection",
		Status:   "pending",
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("network_close_connection_task_create", func(db *gorm.DB) error {
		return db.Create(networkTask).Error
	}); err != nil {
		return 0, err
	}

	go n.executeNetworkTask(networkTask, req)
	n.updateTaskStatus(networkTask.ID, "running", "")
	return networkTask.ID, nil
}

// executeNetworkTask 执行网络任务
func (n *NetworkService) executeNetworkTask(task *task.NetworkTask, req interface{}) {
	client, err := n.getOnlineClient(task.ClientID)
	if err != nil {
		n.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 设置TaskID到请求中
	switch r := req.(type) {
	case network.NetworkStatsRequest:
		r.TaskID = task.ID
		req = r
	case network.NetworkInterfacesRequest:
		r.TaskID = task.ID
		req = r
	case network.NetworkConnectionsRequest:
		r.TaskID = task.ID
		req = r
	case network.CloseConnectionRequest:
		r.TaskID = task.ID
		req = r
	}

	var code uint8
	switch task.TaskType {
	case "network_stats":
		code = tlv.NetStatsCmd
	case "network_interfaces":
		code = tlv.NetInterfacesCmd
	case "network_connections":
		code = tlv.NetConnectionsCmd
	case "close_connection":
		code = tlv.NetCloseConnCmd
	default:
		n.updateTaskStatus(task.ID, "failed", "未知的任务类型")
		return
	}

	// 序列化请求数据
	reqBytes, err := utils.SerializerManager.Serialize(req)
	if err != nil {
		n.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 创建TLV数据包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.Network,
			Code: code,
		},
		PacketData: &tlv.PacketData{
			Data: reqBytes,
		},
	}

	// 发送到客户端
	err = n.sendPacket(client, packet)
	if err != nil {
		n.updateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	n.updateTaskStatus(task.ID, "running", "")
}

// getOnlineClient 获取在线客户端
func (n *NetworkService) getOnlineClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_online_check", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("客户端不在线或不存在")
		}
		return nil, err
	}
	return &client, nil
}

// sendPacket 发送数据包到客户端
func (n *NetworkService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 获取客户端连接并发送数据包
	return factory.SendPacketFactory(*client, packet)
}

// updateTaskStatus 更新任务状态
func (n *NetworkService) updateTaskStatus(taskID uint64, status, message string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if message != "" {
		if status == "failed" {
			updates["error"] = message
		}
	}

	if status == "completed" || status == "failed" || status == "cancelled" {
		updates["completed_at"] = time.Now()
	} else if status == "running" {
		updates["started_at"] = time.Now()
	}

	// 🚀 使用数据库连接池异步更新任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("network_task_status_update", func(db *gorm.DB) error {
		return db.Model(&task.NetworkTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}
