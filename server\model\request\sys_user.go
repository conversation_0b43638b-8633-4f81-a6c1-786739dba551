package request

// UpdateUserInfoReq 更新用户信息请求
type UpdateUserInfoReq struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Password string `json:"password"` // 可选，为空表示不修改密码
}

// UserSearch 用户搜索请求结构体
type UserSearch struct {
	Username string `json:"username" form:"username"` // 用户名
	RoleName string `json:"roleName" form:"roleName"` // 角色名
	Enable   *int   `json:"enable" form:"enable"`     // 启用状态 1启用 2禁用
	PageInfo
}

// CreateUserRequest 创建用户请求结构体
type CreateUserRequest struct {
	Username string `json:"username" binding:"required" validate:"min=3,max=20"` // 用户名
	Password string `json:"password" binding:"required" validate:"min=6,max=50"` // 密码
	RoleName string `json:"roleName" binding:"required"`                         // 角色名
	Enable   int    `json:"enable"`                                              // 启用状态 1启用 2禁用
}

// UpdateUserRequest 更新用户请求结构体
type UpdateUserRequest struct {
	ID       uint   `json:"id" binding:"required"`            // 用户ID
	Username string `json:"username" validate:"min=3,max=20"` // 用户名
	Password string `json:"password" validate:"min=6,max=50"` // 密码（可选）
	RoleName string `json:"roleName"`                         // 角色名
	Enable   int    `json:"enable"`                           // 启用状态 1启用 2禁用
}

// DeleteUserRequest 删除用户请求结构体
type DeleteUserRequest struct {
	ID uint `json:"id" binding:"required"` // 用户ID
}

// ChangeUserStatusRequest 修改用户状态请求结构体
type ChangeUserStatusRequest struct {
	ID     uint `json:"id" binding:"required"`     // 用户ID
	Enable int  `json:"enable" binding:"required"` // 启用状态 1启用 2禁用
}
