<template>
  <div class="file-tree">
    <div
      v-for="item in fileList"
      :key="item.name"
      :class="['tree-item', { 'is-directory': item.type === 'directory' }]"
      @contextmenu.prevent="$emit('context-menu', $event, item)"
    >
      <div
        class="tree-item-content"
        :style="{ paddingLeft: (depth * 24) + 'px' }"
        @click="handleItemClick(item)"
      >
        <!-- 缩进连接线 -->
        <div
          v-if="depth > 0"
          class="indent-guides"
          :style="{ left: '0px', width: (depth * 24) + 'px' }"
        >
          <div
            v-for="i in depth"
            :key="i"
            class="indent-line"
            :style="{ left: ((i - 1) * 24 + 12) + 'px' }"
          ></div>
        </div>
        <!-- 展开/收起图标 - 固定在左侧 -->
        <span
          v-if="item.type === 'directory'"
          class="expand-icon"
        >
          <RightOutlined v-if="!item.expanded" class="expand-arrow" />
          <DownOutlined v-else class="expand-arrow" />
        </span>
        <!-- 占位符，确保文件图标对齐 -->
        <span v-else class="expand-icon-placeholder"></span>

        <!-- 文件/文件夹图标 -->
        <span
          class="file-icon"
          :class="getFileIconClass(item)"
        >
          <component :is="getFileIcon(item)" />
        </span>
        <!-- 内联编辑输入框 -->
        <a-input
          v-if="item.isEditing"
          ref="editInput"
          v-model:value="item.name"
          size="small"
          class="inline-edit-input"
          @keyup.enter="confirmEdit(item)"
          @keyup.esc="cancelEdit(item)"
          :placeholder="item.type === 'directory' ? '文件夹名' : '文件名'"
          auto-focus
        />
        <!-- 正常显示文件名 -->
        <span
          v-else
          class="file-name"
          @click="handleFileNameClick(item)"
        >{{ item.name }}</span>
        <!-- 编辑操作按钮 -->
        <div v-if="item.isEditing" class="edit-actions">
          <a-button
            type="text"
            size="small"
            class="confirm-btn"
            @click.stop="confirmEdit(item)"
            title="确认 (Enter)"
          >
            <template #icon><CheckOutlined /></template>
          </a-button>
          <a-button
            type="text"
            size="small"
            class="cancel-btn"
            @click.stop="cancelEdit(item)"
            title="取消 (Esc)"
          >
            <template #icon><CloseOutlined /></template>
          </a-button>
        </div>
      </div>
      
      <!-- 子文件夹内容 - 添加动画 -->
      <transition name="folder-expand">
        <div
          v-if="item.type === 'directory' && item.expanded && item.children !== null"
          class="tree-children"
        >
          <FileTree
            :client-id="clientId"
            :current-path="buildPath(currentPath, item.name)"
            :file-list="item.children"
            :depth="depth + 1"
            @file-select="$emit('file-select', $event)"
            @folder-expand="$emit('folder-expand', $event)"
            @context-menu="$emit('context-menu', $event)"
            @create-item="$emit('create-item', $event)"
            @rename-item="$emit('rename-item', $event)"
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import {
  RightOutlined,
  DownOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  FileOutlined,
  FileTextOutlined,
  CodeOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileZipOutlined,
  CheckOutlined,
  CloseOutlined,
  DatabaseOutlined,
  SettingOutlined,
  BugOutlined,
  ApiOutlined,
  FileMarkdownOutlined,
  FileImageOutlined,
  Html5Outlined,
  ConsoleSqlOutlined,
  FileProtectOutlined,
  KeyOutlined,
  CloudOutlined,
  ToolOutlined,
  BranchesOutlined
} from '@ant-design/icons-vue';
import { dirApi } from '@/api';
import { buildPath } from '../fileUtils.js';

// Props
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    required: true
  },
  fileList: {
    type: Array,
    default: () => []
  },
  depth: {
    type: Number,
    default: 0
  }
});

// Emits
const emit = defineEmits(['file-select', 'folder-expand', 'context-menu', 'create-item', 'rename-item']);

// 方法
const handleItemClick = (item) => {
  if (item.type === 'directory') {
    // 文件夹：切换展开/收起状态
    handleFolderToggle(item);
  } else {
    // 文件：选择文件
    handleFileSelect(item);
  }
};

const handleFolderToggle = (item) => {
  console.log('FileTree handleFolderToggle:', item.name);

  if (item.type === 'directory') {
    console.log('发送folder-expand事件:', item.name);
    // 发送展开事件给父组件处理
    emit('folder-expand', item);
  }
};

const handleFileNameClick = (item) => {
  console.log('FileTree handleFileNameClick:', item.name, item.type);

  if (item.type === 'directory') {
    console.log('文件夹名称点击，发送folder-expand事件:', item.name);
    // 点击文件夹名称也可以展开/收起
    emit('folder-expand', item);
  } else {
    console.log('文件名称点击，发送file-select事件:', item.name);
    // 发送文件选择事件
    emit('file-select', item);
  }
};



// 编辑相关方法
const confirmEdit = async (item) => {
  const trimmedName = item.name.trim();

  // 验证名称
  if (!trimmedName) {
    message.warning('请输入名称');
    return;
  }

  // 验证文件名格式
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(trimmedName)) {
    message.error('文件名不能包含以下字符: < > : " / \\ | ? *');
    return;
  }

  // 检查是否与现有文件重名
  const siblings = props.fileList.filter(f => f.id !== item.id);
  if (siblings.some(f => f.name === trimmedName)) {
    message.error('已存在同名的文件或文件夹');
    return;
  }

  console.log('确认编辑:', {
    type: item.type,
    name: trimmedName,
    path: item.path,
    isNew: item.isNew
  });

  if (item.isNew) {
    // 创建新文件或文件夹
    emit('create-item', {
      type: item.type,
      name: trimmedName,
      path: item.path
    });
  } else {
    // 重命名现有文件或文件夹
    emit('rename-item', {
      oldName: item.originalName,
      newName: trimmedName,
      type: item.type,
      path: item.path
    });
  }

  // 通知父组件移除编辑状态
  emit('remove-edit-item', item.id);
};

const cancelEdit = (item) => {
  console.log('取消编辑:', item.name, item.isNew);

  if (item.isNew) {
    // 通知父组件移除新创建的项
    console.log('通知父组件移除新创建的项:', item.id);
    emit('remove-edit-item', item.id);

    // 通知父组件取消创建
    emit('cancel-create');
  } else {
    // 恢复原始名称
    item.name = item.originalName;
    item.isEditing = false;
  }
};

const getFileIcon = (item) => {
  if (item.type === 'directory') {
    return item.expanded ? FolderOpenOutlined : FolderOutlined;
  }

  const ext = item.name.split('.').pop()?.toLowerCase();
  const fileName = item.name.toLowerCase();

  // Web开发文件
  if (['html', 'htm'].includes(ext)) return Html5Outlined;
  if (['css', 'scss', 'sass', 'less'].includes(ext)) return FileTextOutlined;
  if (['js', 'mjs', 'cjs'].includes(ext)) return CodeOutlined;
  if (['ts', 'tsx'].includes(ext)) return CodeOutlined;
  if (['jsx'].includes(ext)) return CodeOutlined;
  if (['vue'].includes(ext)) return CodeOutlined;

  // 后端开发文件
  if (['py', 'pyw', 'pyc'].includes(ext)) return CodeOutlined;
  if (['java', 'class', 'jar'].includes(ext)) return CodeOutlined;
  if (['php', 'phtml'].includes(ext)) return CodeOutlined;
  if (['cpp', 'cxx', 'cc', 'c++'].includes(ext)) return CodeOutlined;
  if (['c', 'h', 'hpp'].includes(ext)) return CodeOutlined;
  if (['cs', 'csx'].includes(ext)) return CodeOutlined;
  if (['go', 'mod'].includes(ext)) return CodeOutlined;
  if (['rs', 'toml'].includes(ext)) return CodeOutlined;
  if (['rb', 'rbw'].includes(ext)) return CodeOutlined;
  if (['swift'].includes(ext)) return CodeOutlined;
  if (['kt', 'kts'].includes(ext)) return CodeOutlined;
  if (['scala', 'sc'].includes(ext)) return CodeOutlined;

  // 数据库文件
  if (['sql', 'mysql', 'pgsql', 'sqlite', 'db'].includes(ext)) return ConsoleSqlOutlined;
  if (['mdb', 'accdb'].includes(ext)) return DatabaseOutlined;

  // 配置文件
  if (['json', 'jsonc'].includes(ext)) return SettingOutlined;
  if (['xml', 'xsd', 'xsl'].includes(ext)) return FileTextOutlined;
  if (['yaml', 'yml'].includes(ext)) return SettingOutlined;
  if (['ini', 'cfg', 'conf', 'config'].includes(ext)) return SettingOutlined;
  if (['env', 'environment'].includes(ext)) return SettingOutlined;
  if (['properties'].includes(ext)) return SettingOutlined;

  // 文档文件
  if (['md', 'markdown', 'mdown'].includes(ext)) return FileMarkdownOutlined;
  if (['txt', 'text'].includes(ext)) return FileTextOutlined;
  if (['rtf'].includes(ext)) return FileTextOutlined;
  if (['pdf'].includes(ext)) return FilePdfOutlined;
  if (['doc', 'docx'].includes(ext)) return FileWordOutlined;
  if (['xls', 'xlsx', 'xlsm'].includes(ext)) return FileExcelOutlined;
  if (['ppt', 'pptx', 'pptm'].includes(ext)) return FilePptOutlined;

  // 图片文件
  if (['jpg', 'jpeg', 'jpe'].includes(ext)) return FileImageOutlined;
  if (['png', 'apng'].includes(ext)) return FileImageOutlined;
  if (['gif'].includes(ext)) return FileImageOutlined;
  if (['bmp', 'dib'].includes(ext)) return FileImageOutlined;
  if (['svg', 'svgz'].includes(ext)) return FileImageOutlined;
  if (['webp'].includes(ext)) return FileImageOutlined;
  if (['ico', 'icon'].includes(ext)) return FileImageOutlined;
  if (['tiff', 'tif'].includes(ext)) return FileImageOutlined;

  // 视频文件
  if (['mp4', 'm4v'].includes(ext)) return VideoCameraOutlined;
  if (['avi', 'divx'].includes(ext)) return VideoCameraOutlined;
  if (['mov', 'qt'].includes(ext)) return VideoCameraOutlined;
  if (['wmv', 'asf'].includes(ext)) return VideoCameraOutlined;
  if (['flv', 'f4v'].includes(ext)) return VideoCameraOutlined;
  if (['webm'].includes(ext)) return VideoCameraOutlined;
  if (['mkv', 'mka'].includes(ext)) return VideoCameraOutlined;
  if (['mpg', 'mpeg', 'mpe'].includes(ext)) return VideoCameraOutlined;

  // 音频文件
  if (['mp3'].includes(ext)) return AudioOutlined;
  if (['wav', 'wave'].includes(ext)) return AudioOutlined;
  if (['ogg', 'oga'].includes(ext)) return AudioOutlined;
  if (['aac', 'm4a'].includes(ext)) return AudioOutlined;
  if (['flac'].includes(ext)) return AudioOutlined;
  if (['wma'].includes(ext)) return AudioOutlined;
  if (['opus'].includes(ext)) return AudioOutlined;

  // 压缩文件
  if (['zip', 'zipx'].includes(ext)) return FileZipOutlined;
  if (['rar', 'r00', 'r01'].includes(ext)) return FileZipOutlined;
  if (['7z', '7zip'].includes(ext)) return FileZipOutlined;
  if (['tar', 'tgz', 'tbz', 'tbz2'].includes(ext)) return FileZipOutlined;
  if (['gz', 'gzip'].includes(ext)) return FileZipOutlined;
  if (['bz2', 'bzip2'].includes(ext)) return FileZipOutlined;
  if (['xz', 'lzma'].includes(ext)) return FileZipOutlined;

  // 系统文件
  if (['exe', 'msi', 'app', 'deb', 'rpm'].includes(ext)) return ToolOutlined;
  if (['dll', 'so', 'dylib'].includes(ext)) return ToolOutlined;
  if (['bat', 'cmd', 'ps1'].includes(ext)) return ConsoleSqlOutlined;
  if (['sh', 'bash', 'zsh', 'fish'].includes(ext)) return ConsoleSqlOutlined;

  // 开发工具文件
  if (['dockerfile'].includes(ext) || fileName === 'dockerfile') return CloudOutlined;
  if (['gitignore', 'gitattributes'].includes(ext) || fileName.startsWith('.git')) return BranchesOutlined;
  if (['log', 'logs'].includes(ext)) return BugOutlined;
  if (['lock'].includes(ext)) return KeyOutlined;
  if (['cert', 'crt', 'pem', 'key', 'p12', 'pfx'].includes(ext)) return FileProtectOutlined;

  // API文件
  if (['wsdl', 'wadl', 'raml'].includes(ext)) return ApiOutlined;
  if (['postman_collection', 'har'].includes(ext)) return ApiOutlined;

  return FileOutlined;
};

// 获取文件图标的CSS类名
const getFileIconClass = (item) => {
  if (item.type === 'directory') {
    return 'icon-directory';
  }

  const ext = item.name.split('.').pop()?.toLowerCase();
  const fileName = item.name.toLowerCase();

  // 返回对应的CSS类名
  const extClassMap = {
    // Web开发文件
    'js': 'icon-javascript',
    'mjs': 'icon-javascript',
    'cjs': 'icon-javascript',
    'ts': 'icon-typescript',
    'tsx': 'icon-typescript',
    'jsx': 'icon-react',
    'vue': 'icon-vue',
    'html': 'icon-html',
    'htm': 'icon-html',
    'css': 'icon-css',
    'scss': 'icon-css',
    'sass': 'icon-css',
    'less': 'icon-css',

    // 后端开发文件
    'py': 'icon-python',
    'pyw': 'icon-python',
    'java': 'icon-java',
    'php': 'icon-php',
    'cpp': 'icon-cpp',
    'cxx': 'icon-cpp',
    'c': 'icon-cpp',
    'h': 'icon-cpp',
    'hpp': 'icon-cpp',
    'cs': 'icon-csharp',
    'go': 'icon-go',
    'rs': 'icon-rust',
    'rb': 'icon-ruby',
    'swift': 'icon-swift',
    'kt': 'icon-kotlin',

    // 配置文件
    'json': 'icon-json',
    'xml': 'icon-xml',
    'yaml': 'icon-yaml',
    'yml': 'icon-yaml',
    'sql': 'icon-sql',

    // 文档文件
    'md': 'icon-markdown',
    'markdown': 'icon-markdown',
    'pdf': 'icon-pdf',

    // 媒体文件
    'png': 'icon-image',
    'jpg': 'icon-image',
    'jpeg': 'icon-image',
    'gif': 'icon-image',
    'svg': 'icon-image',
    'webp': 'icon-image',
    'mp4': 'icon-video',
    'avi': 'icon-video',
    'mov': 'icon-video',
    'mp3': 'icon-audio',
    'wav': 'icon-audio',
    'ogg': 'icon-audio',

    // 压缩文件
    'zip': 'icon-archive',
    'rar': 'icon-archive',
    '7z': 'icon-archive',
    'tar': 'icon-archive',

    // 系统文件
    'exe': 'icon-executable',
    'msi': 'icon-executable',
    'bat': 'icon-script',
    'cmd': 'icon-script',
    'sh': 'icon-script'
  };

  return extClassMap[ext] || 'icon-file';
};
</script>

<style scoped>
.file-tree {
  user-select: none;
}

.tree-item {
  cursor: pointer;
  position: relative;
}

.tree-item-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 13px;
  line-height: 22px;
  border-radius: 3px;
  margin: 1px 4px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  cursor: pointer;
  user-select: none;
}

.tree-item-content:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(2px);
}

.tree-item-content:active {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateX(1px);
}

/* 缩进连接线 */
.indent-guides {
  position: absolute;
  top: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.indent-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background: rgba(255, 255, 255, 0.1);
  border-left: 1px dotted rgba(255, 255, 255, 0.2);
}

.tree-item-content:hover {
  background: #37373d;
}

.tree-item.is-directory .tree-item-content:hover {
  background: #094771;
}

.expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  font-size: 10px;
  color: #cccccc;
  flex-shrink: 0; /* 防止收缩 */
}

/* 占位符，确保文件图标对齐 */
.expand-icon-placeholder {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  flex-shrink: 0; /* 防止收缩 */
}

.file-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
  font-size: 14px;
  color: #cccccc;
}

.file-name {
  flex: 1;
  color: #cccccc;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 子文件夹容器 */
.tree-children {
  position: relative;
  overflow: hidden;
}

/* 文件夹展开/收起动画 */
.folder-expand-enter-active,
.folder-expand-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

.folder-expand-enter-from {
  opacity: 0;
  transform: scaleY(0);
  max-height: 0;
}

.folder-expand-enter-to {
  opacity: 1;
  transform: scaleY(1);
  max-height: 1000px; /* 足够大的值 */
}

.folder-expand-leave-from {
  opacity: 1;
  transform: scaleY(1);
  max-height: 1000px;
}

.folder-expand-leave-to {
  opacity: 0;
  transform: scaleY(0);
  max-height: 0;
}

/* 展开箭头旋转动画 */
.expand-arrow {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.expand-icon:hover .expand-arrow {
  transform: scale(1.1);
}

/* 文件类型特定颜色 */
.tree-item:not(.is-directory) .file-icon {
  color: #cccccc;
}

/* JavaScript/TypeScript */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".js"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".ts"]) .file-icon {
  color: #f7df1e;
}

/* Python */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".py"]) .file-icon {
  color: #3776ab;
}

/* Java */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".java"]) .file-icon {
  color: #ed8b00;
}

/* PHP */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".php"]) .file-icon {
  color: #777bb4;
}

/* HTML */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".html"]) .file-icon {
  color: #e34f26;
}

/* CSS */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".css"]) .file-icon {
  color: #1572b6;
}

/* JSON */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".json"]) .file-icon {
  color: #ffd700;
}

/* Markdown */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".md"]) .file-icon {
  color: #083fa1;
}

/* C/C++ */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".c"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".cpp"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".h"]) .file-icon {
  color: #00599c;
}

/* C# */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".cs"]) .file-icon {
  color: #239120;
}

/* Go */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".go"]) .file-icon {
  color: #00add8;
}

/* Rust */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".rs"]) .file-icon {
  color: #dea584;
}

/* Ruby */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".rb"]) .file-icon {
  color: #cc342d;
}

/* Vue */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".vue"]) .file-icon {
  color: #4fc08d;
}

/* React JSX */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".jsx"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".tsx"]) .file-icon {
  color: #61dafb;
}

/* XML */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".xml"]) .file-icon {
  color: #ff6600;
}

/* YAML */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".yaml"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".yml"]) .file-icon {
  color: #cc0000;
}

/* SQL */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".sql"]) .file-icon {
  color: #336791;
}

/* 图片文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".jpg"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".jpeg"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".png"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".gif"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".svg"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".webp"]) .file-icon {
  color: #ff69b4;
}

/* 视频文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".mp4"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".avi"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".mov"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".wmv"]) .file-icon {
  color: #ff4500;
}

/* 音频文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".mp3"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".wav"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".ogg"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".flac"]) .file-icon {
  color: #9932cc;
}

/* 压缩文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".zip"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".rar"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".7z"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".tar"]) .file-icon {
  color: #ffff00;
}

/* 可执行文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".exe"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".msi"]) .file-icon {
  color: #ff6347;
}

/* 脚本文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".bat"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".cmd"]) .file-icon,
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".sh"]) .file-icon {
  color: #90ee90;
}

/* PDF文件 */
.tree-item:not(.is-directory) .tree-item-content:has(.file-name[title$=".pdf"]) .file-icon {
  color: #ff0000;
}

/* 文件夹图标颜色 */
.tree-item.is-directory .file-icon {
  color: #dcb67a;
}

/* 内联编辑样式 */
.inline-edit-input {
  flex: 1;
  margin-left: 6px;
  background: #3c3c3c !important;
  border-color: #007acc !important;
  color: #e8e8e8 !important;
}

.inline-edit-input:focus {
  border-color: #007acc !important;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2) !important;
}

.edit-actions {
  display: flex;
  margin-left: 4px;
  gap: 2px;
}

.confirm-btn {
  color: #4caf50 !important;
  padding: 0 4px !important;
  min-width: 20px !important;
  height: 20px !important;
}

.confirm-btn:hover {
  background: rgba(76, 175, 80, 0.1) !important;
}

.cancel-btn {
  color: #f44336 !important;
  padding: 0 4px !important;
  min-width: 20px !important;
  height: 20px !important;
}

.cancel-btn:hover {
  background: rgba(244, 67, 54, 0.1) !important;
}

/* 基于类名的文件图标颜色 - 更兼容的方式 */
.file-icon.icon-directory {
  color: #dcb67a !important;
}

.file-icon.icon-javascript {
  color: #f7df1e !important;
}

.file-icon.icon-typescript {
  color: #3178c6 !important;
}

.file-icon.icon-react {
  color: #61dafb !important;
}

.file-icon.icon-vue {
  color: #4fc08d !important;
}

.file-icon.icon-html {
  color: #e34f26 !important;
}

.file-icon.icon-css {
  color: #1572b6 !important;
}

.file-icon.icon-python {
  color: #3776ab !important;
}

.file-icon.icon-java {
  color: #ed8b00 !important;
}

.file-icon.icon-php {
  color: #777bb4 !important;
}

.file-icon.icon-cpp {
  color: #00599c !important;
}

.file-icon.icon-csharp {
  color: #239120 !important;
}

.file-icon.icon-go {
  color: #00add8 !important;
}

.file-icon.icon-rust {
  color: #dea584 !important;
}

.file-icon.icon-ruby {
  color: #cc342d !important;
}

.file-icon.icon-json {
  color: #ffd700 !important;
}

.file-icon.icon-xml {
  color: #ff6600 !important;
}

.file-icon.icon-yaml {
  color: #cc0000 !important;
}

.file-icon.icon-sql {
  color: #336791 !important;
}

.file-icon.icon-markdown {
  color: #083fa1 !important;
}

.file-icon.icon-pdf {
  color: #ff0000 !important;
}

.file-icon.icon-image {
  color: #ff69b4 !important;
}

.file-icon.icon-video {
  color: #ff4500 !important;
}

.file-icon.icon-audio {
  color: #9932cc !important;
}

.file-icon.icon-archive {
  color: #ffff00 !important;
}

.file-icon.icon-executable {
  color: #ff6347 !important;
}

.file-icon.icon-script {
  color: #90ee90 !important;
}

.file-icon.icon-file {
  color: #cccccc !important;
}

/* 文件夹展开/收起动画 */
.folder-expand-enter-active,
.folder-expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.folder-expand-enter-from {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.folder-expand-enter-to {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
}

.folder-expand-leave-from {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
}

.folder-expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

/* 展开图标旋转动画 */
.expand-icon {
  transition: transform 0.2s ease;
}

.tree-item.is-directory .expand-icon {
  transform-origin: center;
}
</style>