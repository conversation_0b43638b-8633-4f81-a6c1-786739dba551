package c2

import (
	"server/global"
	"server/model/request"
	"server/model/response"
	"server/model/sys"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HeartbeatConfigApi struct{}

// GetHeartbeatConfig 获取心跳配置
// @Tags HeartbeatConfig
// @Summary 获取心跳配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param client_id query int false "客户端ID，不传或0表示获取全局配置"
// @Success 200 {object} response.Response{data=sys.HeartbeatConfig,msg=string} "获取成功"
// @Router /heartbeat/config [get]
func (h *HeartbeatConfigApi) GetHeartbeatConfig(c *gin.Context) {
	clientIDStr := c.Query("client_id")
	var clientID uint = 0

	if clientIDStr != "" {
		if id, err := strconv.ParseUint(clientIDStr, 10, 32); err == nil {
			clientID = uint(id)
		}
	}

	config, err := heartbeatConfigService.GetHeartbeatConfig(clientID)
	if err != nil {
		global.LOG.Error("获取心跳配置失败!", zap.Error(err))
		response.ErrorWithMessage("获取心跳配置失败", c)
		return
	}

	response.OkWithDetailed(config, "获取成功", c)
}

// UpdateHeartbeatConfig 更新心跳配置
// @Tags HeartbeatConfig
// @Summary 更新心跳配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body sys.HeartbeatConfig true "心跳配置信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /heartbeat/config [put]
func (h *HeartbeatConfigApi) UpdateHeartbeatConfig(c *gin.Context) {
	var config sys.HeartbeatConfig
	err := c.ShouldBindJSON(&config)
	if err != nil {
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	// 验证配置参数
	if config.Interval < 5 || config.Interval > 300 {
		response.ErrorWithMessage("心跳间隔必须在5-300秒之间", c)
		return
	}

	if config.Timeout < 1 || config.Timeout > 60 {
		response.ErrorWithMessage("心跳超时必须在1-60秒之间", c)
		return
	}

	if config.MaxRetries < 1 || config.MaxRetries > 20 {
		response.ErrorWithMessage("最大重试次数必须在1-20次之间", c)
		return
	}

	if config.JitterRange < 0 || config.JitterRange > 30000 {
		response.ErrorWithMessage("抖动范围必须在0-30000毫秒之间", c)
		return
	}

	err = heartbeatConfigService.UpdateHeartbeatConfig(&config)
	if err != nil {
		global.LOG.Error("更新心跳配置失败!", zap.Error(err))
		response.ErrorWithMessage("更新心跳配置失败", c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// CreateHeartbeatConfig 创建心跳配置
// @Tags HeartbeatConfig
// @Summary 创建心跳配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body sys.HeartbeatConfig true "心跳配置信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /heartbeat/config [post]
func (h *HeartbeatConfigApi) CreateHeartbeatConfig(c *gin.Context) {
	var config sys.HeartbeatConfig
	err := c.ShouldBindJSON(&config)
	if err != nil {
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	// 验证配置参数（同上）
	if config.Interval < 5 || config.Interval > 300 {
		response.ErrorWithMessage("心跳间隔必须在5-300秒之间", c)
		return
	}

	if config.Timeout < 1 || config.Timeout > 60 {
		response.ErrorWithMessage("心跳超时必须在1-60秒之间", c)
		return
	}

	if config.MaxRetries < 1 || config.MaxRetries > 20 {
		response.ErrorWithMessage("最大重试次数必须在1-20次之间", c)
		return
	}

	if config.JitterRange < 0 || config.JitterRange > 30000 {
		response.ErrorWithMessage("抖动范围必须在0-30000毫秒之间", c)
		return
	}

	err = heartbeatConfigService.CreateHeartbeatConfig(&config)
	if err != nil {
		global.LOG.Error("创建心跳配置失败!", zap.Error(err))
		response.ErrorWithMessage("创建心跳配置失败", c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// DeleteHeartbeatConfig 删除心跳配置
// @Tags HeartbeatConfig
// @Summary 删除心跳配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "配置ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /heartbeat/config/{id} [delete]
func (h *HeartbeatConfigApi) DeleteHeartbeatConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("无效的配置ID", c)
		return
	}

	err = heartbeatConfigService.DeleteHeartbeatConfig(uint(id))
	if err != nil {
		global.LOG.Error("删除心跳配置失败!", zap.Error(err))
		response.ErrorWithMessage("删除心跳配置失败", c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetHeartbeatConfigList 获取心跳配置列表
// @Tags HeartbeatConfig
// @Summary 获取心跳配置列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /heartbeat/configs [get]
func (h *HeartbeatConfigApi) GetHeartbeatConfigList(c *gin.Context) {
	var pageInfo request.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.ErrorWithMessage(err.Error(), c)
		return
	}

	list, total, err := heartbeatConfigService.GetHeartbeatConfigList(pageInfo)
	if err != nil {
		global.LOG.Error("获取心跳配置列表失败!", zap.Error(err))
		response.ErrorWithMessage("获取心跳配置列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
