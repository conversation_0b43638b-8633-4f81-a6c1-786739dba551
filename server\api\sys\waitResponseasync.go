package sys

import (
	"server/core/manager/workerpool"
	"server/global"
	"server/model/response"
	"time"

	"github.com/gin-gonic/gin"
)

func waitForServiceResponseAsync(ctx *gin.Context, operation string, function func() map[string]interface{}) {
	responseChan := make(chan map[string]interface{}, 1)
	task := workerpool.NewNetworkTask("wait_response_"+operation, func() error {
		result := function()
		responseChan <- result

		return nil
	})
	workerpool.SubmitNetworkTask(task)
	select {
	case result := <-responseChan:
		global.LOG.Info(operation + "成功")
		response.OkWithData(result, ctx)
	case <-time.After(35 * time.Second): // 比工作池超时稍长一点
		response.ErrorWithMessage(operation+"超时", ctx)
	}
}

func waitForServiceResponseAsyncWithReturn(operation string, function func() interface{}) interface{} {
	responseChan := make(chan interface{}, 1)
	task := workerpool.NewNetworkTask("wait_response_"+operation, func() error {
		result := function()
		responseChan <- result
		return nil
	})
	workerpool.SubmitNetworkTask(task)
	select {
	case result := <-responseChan:
		global.LOG.Info(operation + "成功")
		return result
	case <-time.After(35 * time.Second): // 比工作池超时稍长一点
		return nil
	}
}
