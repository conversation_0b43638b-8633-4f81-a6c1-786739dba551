<template>
  <a-modal
    v-model:open="visible"
    title="代理链节点管理"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="chain-nodes-modal">
      <!-- 节点列表 -->
      <div class="nodes-section">
        <div class="section-header">
          <h3>代理链节点</h3>
          <a-button type="primary" @click="showAddNodeModal">
            <template #icon>
              <PlusOutlined />
            </template>
            添加节点
          </a-button>
        </div>
        
        <a-table
          :columns="nodeColumns"
          :data-source="nodes"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'order'">
              <a-tag color="blue">{{ index + 1 }}</a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button size="small" @click="editNode(record)">
                  <template #icon>
                    <EditOutlined />
                  </template>
                </a-button>
                <a-button size="small" @click="moveNodeUp(index)" :disabled="index === 0">
                  <template #icon>
                    <ArrowUpOutlined />
                  </template>
                </a-button>
                <a-button size="small" @click="moveNodeDown(index)" :disabled="index === nodes.length - 1">
                  <template #icon>
                    <ArrowDownOutlined />
                  </template>
                </a-button>
                <a-popconfirm
                  title="确定要删除这个节点吗？"
                  @confirm="removeNode(index)"
                >
                  <a-button size="small" danger>
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
      
      <!-- 操作按钮 -->
      <div class="modal-footer">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSave">保存</a-button>
        </a-space>
      </div>
    </div>
    
    <!-- 添加/编辑节点模态框 -->
    <a-modal
      v-model:open="nodeModalVisible"
      :title="editingNode ? '编辑节点' : '添加节点'"
      width="600px"
      @ok="handleNodeSave"
      @cancel="handleNodeCancel"
    >
      <a-form
        ref="nodeFormRef"
        :model="nodeForm"
        :rules="nodeRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="节点名称" name="name">
              <a-input v-model:value="nodeForm.name" placeholder="请输入节点名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="节点类型" name="type">
              <a-select v-model:value="nodeForm.type" placeholder="请选择节点类型">
                <a-select-option value="forward">正向代理</a-select-option>
                <a-select-option value="reverse">反向代理</a-select-option>
                <a-select-option value="socks5">SOCKS5代理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="主机地址" name="host">
              <a-input v-model:value="nodeForm.host" placeholder="请输入主机地址" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="端口" name="port">
              <a-input-number
                v-model:value="nodeForm.port"
                :min="1"
                :max="65535"
                placeholder="请输入端口"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="nodeForm.description"
            placeholder="请输入节点描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="配置参数" name="config">
          <a-textarea
            v-model:value="nodeForm.config"
            placeholder="请输入JSON格式的配置参数"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  chainId: {
    type: String,
    default: ''
  },
  initialNodes: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:open', 'save'])

// 响应式数据
const visible = ref(false)
const nodes = ref([])
const nodeModalVisible = ref(false)
const editingNode = ref(null)
const nodeFormRef = ref()

// 节点表单
const nodeForm = reactive({
  name: '',
  type: 'forward',
  host: '',
  port: null,
  description: '',
  config: ''
})

// 表单验证规则
const nodeRules = {
  name: [
    { required: true, message: '请输入节点名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择节点类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围为1-65535', trigger: 'blur' }
  ]
}

// 表格列定义
const nodeColumns = [
  {
    title: '顺序',
    key: 'order',
    width: 80,
    align: 'center'
  },
  {
    title: '节点名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    align: 'center'
  },
  {
    title: '地址',
    key: 'address',
    customRender: ({ record }) => `${record.host}:${record.port}`
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center'
  }
]

// 监听 props 变化
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadNodes()
  }
})

watch(() => props.initialNodes, (newVal) => {
  if (newVal && newVal.length > 0) {
    nodes.value = [...newVal]
  }
}, { immediate: true })

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// 方法
const loadNodes = () => {
  if (props.initialNodes && props.initialNodes.length > 0) {
    nodes.value = [...props.initialNodes]
  } else {
    nodes.value = []
  }
}

const showAddNodeModal = () => {
  editingNode.value = null
  resetNodeForm()
  nodeModalVisible.value = true
}

const editNode = (node) => {
  editingNode.value = node
  Object.assign(nodeForm, {
    name: node.name,
    type: node.type,
    host: node.host,
    port: node.port,
    description: node.description || '',
    config: node.config ? JSON.stringify(node.config, null, 2) : ''
  })
  nodeModalVisible.value = true
}

const resetNodeForm = () => {
  Object.assign(nodeForm, {
    name: '',
    type: 'forward',
    host: '',
    port: null,
    description: '',
    config: ''
  })
}

const handleNodeSave = async () => {
  try {
    await nodeFormRef.value.validate()
    
    // 验证配置JSON格式
    let config = {}
    if (nodeForm.config.trim()) {
      try {
        config = JSON.parse(nodeForm.config)
      } catch (e) {
        message.error('配置参数格式错误，请输入有效的JSON格式')
        return
      }
    }
    
    const nodeData = {
      id: editingNode.value ? editingNode.value.id : Date.now().toString(),
      name: nodeForm.name,
      type: nodeForm.type,
      host: nodeForm.host,
      port: nodeForm.port,
      description: nodeForm.description,
      config: config,
      status: editingNode.value ? editingNode.value.status : 'inactive'
    }
    
    if (editingNode.value) {
      // 编辑节点
      const index = nodes.value.findIndex(n => n.id === editingNode.value.id)
      if (index !== -1) {
        nodes.value[index] = nodeData
      }
    } else {
      // 添加节点
      nodes.value.push(nodeData)
    }
    
    nodeModalVisible.value = false
    message.success(editingNode.value ? '节点更新成功' : '节点添加成功')
  } catch (error) {
    console.error('保存节点失败:', error)
  }
}

const handleNodeCancel = () => {
  nodeModalVisible.value = false
  resetNodeForm()
}

const moveNodeUp = (index) => {
  if (index > 0) {
    const temp = nodes.value[index]
    nodes.value[index] = nodes.value[index - 1]
    nodes.value[index - 1] = temp
  }
}

const moveNodeDown = (index) => {
  if (index < nodes.value.length - 1) {
    const temp = nodes.value[index]
    nodes.value[index] = nodes.value[index + 1]
    nodes.value[index + 1] = temp
  }
}

const removeNode = (index) => {
  nodes.value.splice(index, 1)
  message.success('节点删除成功')
}

const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    inactive: 'default',
    error: 'red',
    connecting: 'blue'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    inactive: '未激活',
    error: '错误',
    connecting: '连接中'
  }
  return texts[status] || '未知'
}

const getTypeColor = (type) => {
  const colors = {
    forward: 'blue',
    reverse: 'green',
    socks5: 'orange'
  }
  return colors[type] || 'default'
}

const getTypeText = (type) => {
  const texts = {
    forward: '正向代理',
    reverse: '反向代理',
    socks5: 'SOCKS5'
  }
  return texts[type] || type
}

const handleCancel = () => {
  visible.value = false
}

const handleSave = () => {
  emit('save', nodes.value)
  visible.value = false
  message.success('代理链节点保存成功')
}
</script>

<style scoped>
.chain-nodes-modal {
  padding: 16px 0;
}

.nodes-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>
