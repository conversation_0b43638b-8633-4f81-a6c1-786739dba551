<template>
  <div class="dashboard">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="system-indicator" :class="{ active: dashboardStats?.systemStats?.uptime > 0 }">
          <div class="indicator-dot"></div>
          <span class="indicator-text">{{ systemInfo?.os?.hostname || 'C2 Server' }}</span>
        </div>
        <div class="uptime-badge">
          <span>{{ formatUptime(dashboardStats?.systemStats?.uptime || 0) }}</span>
        </div>
      </div>
      <div class="status-right">
        <div class="quick-stats">
          <div class="stat-item">
            <span class="stat-value">{{ dashboardStats?.clientStats?.online || 0 }}</span>
            <span class="stat-label">在线</span>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <span class="stat-value">{{ dashboardStats?.listenerStats?.running || 0 }}</span>
            <span class="stat-label">监听</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-grid">
      <!-- 左侧：系统概览 -->
      <div class="system-overview">
        <div class="overview-header">
          <div class="header-left">
            <div class="logo-container">
              <img src="@/assets/logo/ez-logo.svg" alt="EZ C2 Logo" class="ez-logo" />
            </div>
            <div class="title-group">
              <h2 class="section-title">EZ C2 控制台</h2>
              <div class="system-info">
                <span class="os-name">{{ getOSDisplayName() }}</span>
                <span class="arch-info">{{ systemInfo?.os?.arch }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 资源使用情况 -->
        <div class="resource-metrics">
          <div class="metric-row">
            <div class="metric-label">CPU</div>
            <div class="metric-visual">
              <div class="metric-bar">
                <div class="metric-fill cpu" :style="{ width: (dashboardStats?.systemStats?.cpuUsage || 0) + '%' }"></div>
              </div>
              <span class="metric-value">{{ (dashboardStats?.systemStats?.cpuUsage || 0).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="metric-row">
            <div class="metric-label">内存</div>
            <div class="metric-visual">
              <div class="metric-bar">
                <div class="metric-fill memory" :style="{ width: (dashboardStats?.systemStats?.memoryUsage || 0) + '%' }"></div>
              </div>
              <span class="metric-value">{{ (dashboardStats?.systemStats?.memoryUsage || 0).toFixed(1) }}%</span>
            </div>
          </div>
          
          <div class="metric-row">
            <div class="metric-label">磁盘</div>
            <div class="metric-visual">
              <div class="metric-bar">
                <div class="metric-fill disk" :style="{ width: (dashboardStats?.systemStats?.diskUsage || 0) + '%' }"></div>
              </div>
              <span class="metric-value">{{ (dashboardStats?.systemStats?.diskUsage || 0).toFixed(1) }}%</span>
            </div>
          </div>
        </div>

        <!-- 网络活动 -->
        <div class="network-activity">
          <div class="network-header">
            <span class="network-title">网络活动</span>
          </div>
          <div class="network-stats">
            <div class="network-item upload">
              <div class="network-icon">↑</div>
              <div class="network-info">
                <span class="network-value">{{ formatNetworkSpeed(dashboardStats?.systemStats?.networkUpload || 0) }}</span>
                <span class="network-label">上传</span>
              </div>
            </div>
            <div class="network-item download">
              <div class="network-icon">↓</div>
              <div class="network-info">
                <span class="network-value">{{ formatNetworkSpeed(dashboardStats?.systemStats?.networkDownload || 0) }}</span>
                <span class="network-label">下载</span>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- 中间：客户端管理 -->
      <div class="client-management">
        <div class="management-header">
          <h2 class="section-title">客户端管理</h2>
          <div class="client-summary">
            <div class="summary-item">
              <span class="summary-number">{{ dashboardStats?.clientStats?.total || 0 }}</span>
              <span class="summary-label">总计</span>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-item online">
              <span class="summary-number">{{ dashboardStats?.clientStats?.online || 0 }}</span>
              <span class="summary-label">在线</span>
            </div>
            <div class="summary-item offline">
              <span class="summary-number">{{ dashboardStats?.clientStats?.offline || 0 }}</span>
              <span class="summary-label">离线</span>
            </div>
          </div>
        </div>

        <!-- 平台分布 -->
        <div class="platform-distribution">
          <div class="platform-header">
            <span class="platform-title">平台分布</span>
          </div>
          <div class="platform-grid">
            <div class="platform-item windows">
              <div class="platform-icon">⊞</div>
              <div class="platform-info">
                <span class="platform-count">{{ dashboardStats?.clientStats?.windows || 0 }}</span>
                <span class="platform-name">Windows</span>
              </div>
            </div>
            <div class="platform-item macos">
              <div class="platform-icon">⌘</div>
              <div class="platform-info">
                <span class="platform-count">{{ dashboardStats?.clientStats?.macOS || 0 }}</span>
                <span class="platform-name">macOS</span>
              </div>
            </div>
            <div class="platform-item linux">
              <div class="platform-icon">🐧</div>
              <div class="platform-info">
                <span class="platform-count">{{ dashboardStats?.clientStats?.linux || 0 }}</span>
                <span class="platform-name">Linux</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近连接 -->
        <div class="recent-connections">
          <div class="connections-header">
            <span class="connections-title">最近连接</span>
          </div>
          <div class="connections-list">
            <div 
              v-for="client in dashboardStats?.recentActivity?.recentClients?.slice(0, 5) || []" 
              :key="client.id" 
              class="connection-item"
            >
              <!-- 🎨 客户端状态指示器 -->
              <div class="connection-status-indicator" :class="{ online: client.status === 1 }">
                <div class="status-core"></div>
                <div class="status-ring"></div>
                <div class="status-pulse" v-if="client.status === 1"></div>
              </div>
              <div class="connection-info">
                <div class="connection-primary">
                  <span class="connection-host">{{ client.hostname }}</span>
                  <span class="connection-user">{{ client.username }}</span>
                </div>
                <div class="connection-secondary">
                  <span class="connection-addr">{{ client.remoteAddr }}</span>
                  <span class="connection-time">{{ formatTime(client.connectedAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：监听器状态 -->
      <div class="listener-status">
        <div class="status-header">
          <h2 class="section-title">监听器状态</h2>
          <div class="listener-summary">
            <div class="summary-item">
              <span class="summary-number">{{ dashboardStats?.listenerStats?.total || 0 }}</span>
              <span class="summary-label">总计</span>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-item running">
              <span class="summary-number">{{ dashboardStats?.listenerStats?.running || 0 }}</span>
              <span class="summary-label">运行</span>
            </div>
            <div class="summary-item stopped">
              <span class="summary-number">{{ dashboardStats?.listenerStats?.stopped || 0 }}</span>
              <span class="summary-label">停止</span>
            </div>
          </div>
        </div>

        <!-- 监听器类型 -->
        <div class="listener-types">
          <div class="types-header">
            <span class="types-title">类型分布</span>
          </div>
          <div class="types-grid">
            <div class="type-item tcp">
              <div class="type-icon">TCP</div>
              <div class="type-info">
                <span class="type-count">{{ dashboardStats?.listenerStats?.tcp || 0 }}</span>
                <span class="type-name">TCP 监听器</span>
              </div>
            </div>
            <div class="type-item pipe">
              <div class="type-icon">PIPE</div>
              <div class="type-info">
                <span class="type-count">{{ dashboardStats?.listenerStats?.pipe || 0 }}</span>
                <span class="type-name">管道监听器</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近监听器 -->
        <div class="recent-listeners">
          <div class="listeners-header">
            <span class="listeners-title">最近创建</span>
          </div>
          <div class="listeners-list">
            <div 
              v-for="listener in dashboardStats?.recentActivity?.recentListeners?.slice(0, 5) || []" 
              :key="listener.id" 
              class="listener-item"
            >
              <!-- 🎨 新设计的状态指示器 -->
              <div class="listener-status-indicator" :class="{ running: listener.status === 1 }">
                <div class="status-core"></div>
                <div class="status-ring"></div>
                <div class="status-pulse" v-if="listener.status === 1"></div>
              </div>
              <div class="listener-info">
                <div class="listener-primary">
                  <span class="listener-name">{{ listener.name }}</span>
                  <span class="listener-type">{{ listener.listenerType.toUpperCase() }}</span>
                </div>
                <div class="listener-secondary">
                  <span class="listener-addr">{{ listener.localListenAddr }}</span>
                  <span class="listener-time">{{ formatTime(listener.createdAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🌐 网络拓扑图区域 -->
    <div class="network-topology-section">
      <NetworkTopology
        :dashboardStats="dashboardStats"
        :topologyData="topologyData"
        :sseConnected="!!sseConnection"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { dashboardApi } from '@/api';
import NetworkTopology from './NetworkTopology.vue';

// 接收父组件传递的属性
const props = defineProps({
  active: {
    type: Boolean,
    default: true
  }
});

// 状态变量
const loading = ref(true);
const systemInfo = ref(null);
const dashboardStats = ref(null);
const topologyData = ref(null);

// 🚀 SSE连接实例
let sseConnection = null;

// 🚀 创建SSE连接
const createSSEConnection = () => {
  console.log('🔗 创建Dashboard SSE连接');

  sseConnection = dashboardApi.createDashboardSSE({
    onSystemInfo: (data) => {
      systemInfo.value = data;
    },
    onDashboardStats: (data) => {
      dashboardStats.value = data;
      loading.value = false;
    },
    onTopologyData: (data) => {
      console.log('🌐 收到拓扑数据:', data);
      topologyData.value = data;
    },
    onOpen: () => {
      console.log('✅ Dashboard SSE连接已建立');
    },
    onError: (error) => {
      console.error('❌ Dashboard SSE连接错误:', error);
    },
    onClose: () => {
      console.log('🔌 Dashboard SSE连接已关闭');
    }
  });
};

// 🚀 断开SSE连接
const disconnectSSE = () => {
  if (sseConnection) {
    console.log('🔌 断开Dashboard SSE连接');
    dashboardApi.disconnectDashboardSSE();
    sseConnection = null;
  }
};

// 🚀 备用方法：获取系统信息（SSE失败时使用）
const getSystemInfo = async () => {
  try {
    const res = await dashboardApi.getSystemInfo();
    systemInfo.value = res.data.server;
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }
};

// 🚀 备用方法：获取dashboard统计信息（SSE失败时使用）
const getDashboardStats = async () => {
  try {
    const res = await dashboardApi.getDashboardStats();
    dashboardStats.value = res.data.stats;
    loading.value = false;
  } catch (error) {
    console.error('获取dashboard统计信息失败:', error);
    loading.value = false;
  }
};

// 获取操作系统显示名称
const getOSDisplayName = () => {
  const osType = systemInfo.value?.os?.goos;
  
  if (!osType) return '未知系统';
  
  switch (osType.toLowerCase()) {
    case 'windows':
      return 'Windows';
    case 'darwin':
      return 'macOS';
    case 'linux':
      return 'Linux';
    default:
      return osType;
  }
};

// 格式化运行时间
const formatUptime = (seconds) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) {
    return `${days}天 ${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) {
    return '刚刚';
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    return `${Math.floor(diff / 86400000)}天前`;
  }
};

// 格式化网络速度
const formatNetworkSpeed = (kbps) => {
  if (kbps >= 1024) {
    return `${(kbps / 1024).toFixed(1)} MB/s`;
  }
  return `${kbps.toFixed(1)} KB/s`;
};



// 🚀 监听active属性变化，使用SSE替代定时器
watch(() => props.active, (newVal) => {
  if (newVal) {
    // 创建SSE连接
    createSSEConnection();
  } else {
    // 断开SSE连接
    disconnectSSE();
  }
}, { immediate: true });

// 🚀 组件卸载时断开SSE连接
onBeforeUnmount(() => {
  disconnectSSE();
});
</script>

<style scoped lang="scss">
.dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #1a1a1a;
  padding: 0;
  margin: 0;
}

// 顶部状态栏
.status-bar {
  height: 60px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  position: sticky;
  top: 0;
  z-index: 100;

  .status-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .system-indicator {
      display: flex;
      align-items: center;
      gap: 8px;

      .indicator-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #d1d5db;
        transition: all 0.3s ease;
      }

      &.active .indicator-dot {
        background: #10b981;
        box-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
      }

      .indicator-text {
        font-weight: 600;
        font-size: 15px;
        color: #374151;
      }
    }

    .uptime-badge {
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 13px;
      font-weight: 500;
    }
  }

  .status-right {
    .quick-stats {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;

        .stat-value {
          font-size: 18px;
          font-weight: 700;
          color: #1f2937;
        }

        .stat-label {
          font-size: 11px;
          color: #6b7280;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .stat-divider {
        width: 1px;
        height: 24px;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }


}

// 主要内容网格
.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

// 通用区域样式
.system-overview,
.client-management,
.listener-status {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  }
}

// 区域标题样式
.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 20px 0;
  letter-spacing: -0.3px;
}

// 系统概览
.system-overview {
  .overview-header {
    margin-bottom: 24px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .logo-container {
        .ez-logo {
          width: 48px;
          height: 48px;
          filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.2));
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.3));
          }
        }
      }

      .title-group {
        .section-title {
          margin-bottom: 4px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .system-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .os-name {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }

          .arch-info {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            background: rgba(59, 130, 246, 0.1);
            padding: 2px 8px;
            border-radius: 6px;
          }
        }
      }
    }
  }

  .resource-metrics {
    margin-bottom: 32px;

    .metric-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .metric-label {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        width: 60px;
      }

      .metric-visual {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .metric-bar {
          flex: 1;
          height: 6px;
          background: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
          overflow: hidden;

          .metric-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.5s ease;

            &.cpu {
              background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            }

            &.memory {
              background: linear-gradient(90deg, #10b981, #047857);
            }

            &.disk {
              background: linear-gradient(90deg, #f59e0b, #d97706);
            }
          }
        }

        .metric-value {
          font-size: 13px;
          font-weight: 600;
          color: #1f2937;
          min-width: 45px;
          text-align: right;
        }
      }
    }
  }

  .network-activity {
    .network-header {
      margin-bottom: 16px;

      .network-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .network-stats {
      display: flex;
      gap: 16px;

      .network-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: rgba(0, 0, 0, 0.02);
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);

        .network-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: 700;
          color: white;
        }

        &.upload .network-icon {
          background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        &.download .network-icon {
          background: linear-gradient(135deg, #22c55e, #16a34a);
        }

        .network-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .network-value {
            font-size: 14px;
            font-weight: 700;
            color: #1f2937;
          }

          .network-label {
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }
  }
}

// 客户端管理
.client-management {
  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .client-summary {
      display: flex;
      align-items: center;
      gap: 12px;

      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;

        .summary-number {
          font-size: 16px;
          font-weight: 700;
          color: #1f2937;
        }

        .summary-label {
          font-size: 11px;
          color: #6b7280;
          text-transform: uppercase;
        }

        &.online .summary-number {
          color: #10b981;
        }

        &.offline .summary-number {
          color: #ef4444;
        }
      }

      .summary-divider {
        width: 1px;
        height: 20px;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .platform-distribution {
    margin-bottom: 32px;

    .platform-header {
      margin-bottom: 16px;

      .platform-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .platform-grid {
      display: flex;
      gap: 12px;

      .platform-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: rgba(0, 0, 0, 0.02);
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);

        .platform-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: 700;
          color: white;
        }

        &.windows .platform-icon {
          background: linear-gradient(135deg, #0078d4, #106ebe);
        }

        &.macos .platform-icon {
          background: linear-gradient(135deg, #007aff, #0051d5);
        }

        &.linux .platform-icon {
          background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .platform-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .platform-count {
            font-size: 14px;
            font-weight: 700;
            color: #1f2937;
          }

          .platform-name {
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }
  }

  .recent-connections {
    .connections-header {
      margin-bottom: 16px;

      .connections-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .connections-list {
      .connection-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        // 🎨 客户端状态指示器（与监听器保持一致）
        .connection-status-indicator {
          position: relative;
          width: 16px;
          height: 16px;
          flex-shrink: 0;

          .status-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #d1d5db;
            transition: all 0.3s ease;
            z-index: 3;
          }

          .status-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 14px;
            height: 14px;
            border: 1px solid rgba(209, 213, 219, 0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 2;
          }

          .status-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: rgba(59, 130, 246, 0.2); // 客户端使用蓝色主题
            animation: pulse-ring 2s infinite;
            z-index: 1;
          }

          // 在线状态样式（蓝色主题）
          &.online {
            .status-core {
              background: linear-gradient(135deg, #3b82f6, #2563eb);
              box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
            }

            .status-ring {
              border-color: rgba(59, 130, 246, 0.4);
              box-shadow: 0 0 4px rgba(59, 130, 246, 0.2);
            }
          }

          // 离线状态样式
          &:not(.online) {
            .status-core {
              background: linear-gradient(135deg, #d1d5db, #9ca3af);
            }

            .status-ring {
              border-color: rgba(156, 163, 175, 0.3);
            }
          }
        }

        .connection-info {
          flex: 1;
          min-width: 0;

          .connection-primary {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .connection-host {
              font-size: 14px;
              font-weight: 600;
              color: #1f2937;
            }

            .connection-user {
              font-size: 12px;
              color: #6b7280;
              background: rgba(0, 0, 0, 0.05);
              padding: 2px 6px;
              border-radius: 4px;
            }
          }

          .connection-secondary {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .connection-addr {
              font-size: 12px;
              color: #6b7280;
              font-family: 'SF Mono', Monaco, monospace;
            }

            .connection-time {
              font-size: 11px;
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

// 监听器状态
.listener-status {
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .listener-summary {
      display: flex;
      align-items: center;
      gap: 12px;

      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;

        .summary-number {
          font-size: 16px;
          font-weight: 700;
          color: #1f2937;
        }

        .summary-label {
          font-size: 11px;
          color: #6b7280;
          text-transform: uppercase;
        }

        &.running .summary-number {
          color: #10b981;
        }

        &.stopped .summary-number {
          color: #ef4444;
        }
      }

      .summary-divider {
        width: 1px;
        height: 20px;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .listener-types {
    margin-bottom: 32px;

    .types-header {
      margin-bottom: 16px;

      .types-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .types-grid {
      display: flex;
      gap: 12px;

      .type-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: rgba(0, 0, 0, 0.02);
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);

        .type-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          font-weight: 700;
          color: white;
        }

        &.tcp .type-icon {
          background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        &.pipe .type-icon {
          background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .type-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .type-count {
            font-size: 14px;
            font-weight: 700;
            color: #1f2937;
          }

          .type-name {
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }
  }

  .recent-listeners {
    .listeners-header {
      margin-bottom: 16px;

      .listeners-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .listeners-list {
      .listener-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        // 🎨 新设计的状态指示器
        .listener-status-indicator {
          position: relative;
          width: 16px;
          height: 16px;
          flex-shrink: 0;

          .status-core {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #d1d5db;
            transition: all 0.3s ease;
            z-index: 3;
          }

          .status-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 14px;
            height: 14px;
            border: 1px solid rgba(209, 213, 219, 0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 2;
          }

          .status-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: rgba(16, 185, 129, 0.2);
            animation: pulse-ring 2s infinite;
            z-index: 1;
          }

          // 运行状态样式
          &.running {
            .status-core {
              background: linear-gradient(135deg, #10b981, #059669);
              box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
            }

            .status-ring {
              border-color: rgba(16, 185, 129, 0.4);
              box-shadow: 0 0 4px rgba(16, 185, 129, 0.2);
            }
          }

          // 停止状态样式
          &:not(.running) {
            .status-core {
              background: linear-gradient(135deg, #d1d5db, #9ca3af);
            }

            .status-ring {
              border-color: rgba(156, 163, 175, 0.3);
            }
          }
        }

        // 脉冲动画
        @keyframes pulse-ring {
          0% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 1;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0.3;
          }
          100% {
            transform: translate(-50%, -50%) scale(1.4);
            opacity: 0;
          }
        }

        .listener-info {
          flex: 1;
          min-width: 0;

          .listener-primary {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .listener-name {
              font-size: 14px;
              font-weight: 600;
              color: #1f2937;
            }

            .listener-type {
              font-size: 12px;
              color: #6b7280;
              background: rgba(0, 0, 0, 0.05);
              padding: 2px 6px;
              border-radius: 4px;
            }
          }

          .listener-secondary {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .listener-addr {
              font-size: 12px;
              color: #6b7280;
              font-family: 'SF Mono', Monaco, monospace;
            }

            .listener-time {
              font-size: 11px;
              color: #9ca3af;
            }
          }
        }
      }
    }
  }
}

// 🌐 网络拓扑图区域
.network-topology-section {
  margin-top: 32px;
  height: calc(100vh - 600px); // 动态高度，减去其他组件的高度
  min-height: 400px; // 最小高度
  max-height: 800px; // 最大高度

  @media (max-width: 1200px) {
    height: calc(100vh - 550px);
    min-height: 350px;
  }

  @media (max-width: 768px) {
    height: calc(100vh - 500px);
    min-height: 300px;
    margin-top: 24px;
  }

  @media (max-height: 800px) {
    height: calc(100vh - 400px);
    min-height: 250px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-grid {
    grid-template-columns: 1fr 1fr;
    
    .listener-status {
      grid-column: 1 / -1;
    }
  }
}

@media (max-width: 768px) {
  .main-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .status-bar {
    padding: 0 16px;
    
    .status-left {
      gap: 12px;
    }
    
    .quick-stats {
      gap: 12px;
    }
  }

  .platform-grid,
  .types-grid {
    flex-direction: column;
  }

  .network-stats {
    flex-direction: column;
  }
}
</style>