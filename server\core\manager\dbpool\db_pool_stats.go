package dbpool

import (
	"context"
	"time"
)

// HealthCheck 数据库健康检查
func (w *DBOperationWrapper) HealthCheck() error {
	return ExecuteWithMonitoring("health_check", func() error {
		sqlDB, err := w.db.DB()
		if err != nil {
			return err
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		return sqlDB.PingContext(ctx)
	})
}

// CheckDBHealth 检查数据库健康状态
func CheckDBHealth() error {
	wrapper := GetDBWrapper()
	return wrapper.HealthCheck()
}

// DBStats 数据库统计信息结构
type DBStats struct {
	ConnectionStats  map[string]interface{} `json:"connection_stats"`
	QueryStats       map[string]interface{} `json:"query_stats"`
	HealthStats      map[string]interface{} `json:"health_stats"`
	PerformanceStats map[string]interface{} `json:"performance_stats"`
}

// GetDBStats 获取完整的数据库统计信息
func GetDBStats() *DBStats {
	stats := GetGlobalDBPoolStatsCompatible()

	return &DBStats{
		ConnectionStats: map[string]interface{}{
			"max_open_conns":         stats["max_open_conns"],
			"max_idle_conns":         stats["max_idle_conns"],
			"open_conns":             stats["open_conns"],
			"in_use_conns":           stats["in_use_conns"],
			"idle_conns":             stats["idle_conns"],
			"connection_utilization": stats["connection_utilization"],
		},
		QueryStats: map[string]interface{}{
			"total_queries":   stats["total_queries"],
			"success_queries": stats["success_queries"],
			"failed_queries":  stats["failed_queries"],
			"slow_queries":    stats["slow_queries"],
			"success_rate":    stats["success_rate"],
			"query_timeouts":  stats["query_timeouts"],
			"retry_attempts":  stats["retry_attempts"],
		},
		HealthStats: map[string]interface{}{
			"health_check_count":    stats["health_check_count"],
			"health_check_failures": stats["health_check_failures"],
			"last_health_check":     stats["last_health_check"],
			"health_status":         stats["health_status"],
		},
		PerformanceStats: map[string]interface{}{
			"avg_query_time": stats["avg_query_time"],
			"max_query_time": stats["max_query_time"],
			"min_query_time": stats["min_query_time"],
			"start_time":     stats["start_time"],
		},
	}
}
