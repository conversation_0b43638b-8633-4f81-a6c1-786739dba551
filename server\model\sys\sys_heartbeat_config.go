package sys

import (
	"errors"
	"server/core/manager/dbpool"
	"server/global"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HeartbeatConfig 心跳配置表
type HeartbeatConfig struct {
	global.DBModel
	ClientID    uint   `json:"client_id" gorm:"index;comment:客户端ID，0表示全局配置"`      // 客户端ID，0表示全局配置
	Interval    int    `json:"interval" gorm:"default:30;comment:心跳间隔（秒）"`        // 心跳间隔（秒）
	Timeout     int    `json:"timeout" gorm:"default:10;comment:心跳超时时间（秒）"`       // 心跳超时时间（秒）
	MaxRetries  int    `json:"max_retries" gorm:"default:5;comment:最大重试次数"`       // 最大重试次数
	JitterRange int    `json:"jitter_range" gorm:"default:5000;comment:抖动范围（毫秒）"` // 抖动范围（毫秒）
	IsActive    *bool  `json:"is_active" gorm:"default:true;comment:是否启用"`        // 是否启用
	Remark      string `json:"remark" gorm:"size:255;comment:备注"`                 // 备注
}

// TableName 设置表名
func (HeartbeatConfig) TableName() string {
	return "sys_heartbeat_configs"
}

// GetGlobalConfig 获取全局心跳配置
func GetGlobalConfig() (*HeartbeatConfig, error) {
	var config HeartbeatConfig
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("global_heartbeat_config_get", func(db *gorm.DB) error {
		return db.Where("client_id = ? AND is_active = ?", 0, true).First(&config).Error
	})

	if err != nil {
		global.LOG.Warn("未找到全局心跳配置，返回默认配置", zap.Error(err))
		// 如果没有找到全局配置，返回默认配置
		return &HeartbeatConfig{
			ClientID:    0,
			Interval:    30,
			Timeout:     10,
			MaxRetries:  5,
			JitterRange: 5000,
			IsActive:    &[]bool{true}[0],
			Remark:      "默认全局配置",
		}, nil
	}
	global.LOG.Info("成功获取全局心跳配置", zap.Uint("clientID", config.ClientID), zap.Int("interval", config.Interval))
	return &config, nil
}

// GetClientConfig 获取客户端特定的心跳配置
func GetClientConfig(clientID uint) (*HeartbeatConfig, error) {
	// 🚀 首先检查客户端是否为pipe监听器类型
	var client Client
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_type_check", func(db *gorm.DB) error {
		return db.Where("id = ?", clientID).First(&client).Error
	})
	if err == nil && client.ListenerType == "pipe" {
		// pipe监听器不需要心跳配置
		return nil, errors.New("pipe监听器客户端不需要心跳配置")
	}

	var config HeartbeatConfig

	// 🚀 尝试获取客户端特定配置
	err = dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_get", func(db *gorm.DB) error {
		return db.Where("client_id = ? AND is_active = ?", clientID, true).First(&config).Error
	})
	if err != nil {
		global.LOG.Info("未找到客户端特定心跳配置，尝试获取全局配置", zap.Uint("clientID", clientID), zap.Error(err))
		// 如果没有客户端特定配置，返回全局配置
		return GetGlobalConfig()
	}

	//global.LOG.Info("成功获取客户端特定心跳配置", zap.Uint("clientID", clientID), zap.Int("interval", config.Interval))
	return &config, nil
}
