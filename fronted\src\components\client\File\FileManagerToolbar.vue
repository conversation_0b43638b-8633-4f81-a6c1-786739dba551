<template>
  <div class="file-manager-toolbar">
    <div class="toolbar-left">
      <!-- 磁盘切换器 -->
      <DiskSelector 
        ref="diskSelectorRef"
        :client-id="clientId"
        :client-info="clientInfo"
        :current-disk="currentDisk"
        @disk-changed="handleDiskChanged"
      />
    </div>
    
    <div class="toolbar-center">
      <!-- 主要操作按钮 -->
      <a-space>
        <a-button 
          type="primary" 
          :disabled="!clientInfo || clientInfo.status !== 1" 
          @click="$emit('upload')"
        >
          <template #icon>
            <UploadOutlined />
          </template>
          上传文件
        </a-button>
        
        <a-button 
          :disabled="!clientInfo || clientInfo.status !== 1" 
          @click="$emit('create-dir')"
        >
          <template #icon>
            <FolderAddOutlined />
          </template>
          新建文件夹
        </a-button>
        
        <a-button 
          :disabled="!clientInfo || clientInfo.status !== 1" 
          @click="$emit('create-file')"
        >
          <template #icon>
            <FileAddOutlined />
          </template>
          新建文件
        </a-button>
        
        <a-button 
          :disabled="!clientInfo || clientInfo.status !== 1" 
          @click="$emit('refresh')"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        
        <a-button 
          :disabled="!clipboardFiles.length || !clientInfo || clientInfo.status !== 1"
          @click="$emit('paste')"
        >
          <template #icon>
            <CopyOutlined />
          </template>
          粘贴 ({{ clipboardFiles.length }})
        </a-button>
        
        <a-button @click="$emit('show-transfer-tasks')">
          传输任务
        </a-button>

        <a-button
          type="default"
          :disabled="!clientInfo || clientInfo.status !== 1"
          @click="$emit('open-code-editor')"
        >
          <template #icon>
            <CodeOutlined />
          </template>
          代码编辑器
        </a-button>
      </a-space>
    </div>
    
    <div class="toolbar-right">
      <a-space>
        <!-- 显示隐藏文件切换开关 -->
        <a-switch 
          :checked="showHidden" 
          :disabled="!clientInfo || clientInfo.status !== 1"
          @change="handleShowHiddenChange"
          size="small"
        >
          <template #checkedChildren>显示隐藏</template>
          <template #unCheckedChildren>隐藏文件</template>
        </a-switch>
        
        <!-- 返回上级按钮 -->
        <a-button 
          :disabled="isRootPath || !clientInfo || clientInfo.status !== 1" 
          @click="$emit('go-back')"
        >
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回上级
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import {
  UploadOutlined,
  FolderAddOutlined,
  FileAddOutlined,
  ReloadOutlined,
  ArrowLeftOutlined,
  CopyOutlined,
  CodeOutlined
} from '@ant-design/icons-vue';
import DiskSelector from './DiskSelector.vue';

// 接收属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  clientInfo: {
    type: Object,
    default: () => null
  },
  currentDisk: {
    type: Object,
    default: () => null
  },
  currentPath: {
    type: String,
    default: '/'
  },
  showHidden: {
    type: Boolean,
    default: false
  },
  clipboardFiles: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits([
  'disk-changed',
  'upload',
  'create-dir',
  'create-file',
  'refresh',
  'paste',
  'show-transfer-tasks',
  'show-hidden-change',
  'go-back'
]);

// 组件引用
const diskSelectorRef = ref(null);

// 计算属性
const isRootPath = computed(() => {
  return props.currentPath === '/' || props.currentPath === '\\' || props.currentPath === '';
});

// 处理磁盘切换事件
const handleDiskChanged = (data) => {
  emit('disk-changed', data);
};

// 处理显示隐藏文件切换
const handleShowHiddenChange = (checked) => {
  emit('show-hidden-change', checked);
};

// 暴露方法给父组件
defineExpose({
  getDiskList: () => {
    return diskSelectorRef.value?.getDiskList();
  }
});
</script>

<style scoped>
.file-manager-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  gap: 16px;
}

.toolbar-left {
  flex: 0 0 auto;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-right {
  flex: 0 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager-toolbar {
    flex-direction: column;
    gap: 8px;
  }
  
  .toolbar-center {
    justify-content: flex-start;
  }
  
  .toolbar-center :deep(.ant-space) {
    flex-wrap: wrap;
  }
}
</style>