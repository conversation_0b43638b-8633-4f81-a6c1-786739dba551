package sys

import (
	"fmt"
	"os"
	"server/config"
	"server/global"
	"server/model/request"
	"server/model/response"
	"time"

	"gopkg.in/yaml.v3"
)

type ConfigService struct{}

// GetGeneralConfig 获取常规配置
func (s *ConfigService) GetGeneralConfig() (response.GeneralConfigResponse, error) {
	config := global.CONFIG
	
	return response.GeneralConfigResponse{
		Server: response.ServerConfigResponse{
			PORT:            config.Server.PORT,
			Version:         config.Server.Version,
			RouterPrefix:    config.Server.RouterPrefix,
			LimitCountIP:    config.Server.LimitCountIP,
			LimitTimeIP:     config.Server.LimitTimeIP,
			ServerStartPort: config.Server.ServerStartPort,
			ServerEndPort:   config.Server.ServerEndPort,
			ClientBinDir:    config.Server.ClientBinDir,
			UploadDir:       config.Server.UploadDir,
			DownloadDir:     config.Server.DownloadDir,
		},
		JWT: response.JWTConfigResponse{
			ExpiresTime: config.JWT.ExpiresTime,
			BufferTime:  config.JWT.BufferTime,
			SigningKey:  config.JWT.SigningKey,
		},
		Captcha: response.CaptchaConfigResponse{
			KeyLong:            config.Captcha.KeyLong,
			ImgWidth:           config.Captcha.ImgWidth,
			ImgHeight:          config.Captcha.ImgHeight,
			OpenCaptcha:        config.Captcha.OpenCaptcha,
			MaxFailedAttempts:  config.Captcha.MaxFailedAttempts,
			OpenCaptchaTimeOut: config.Captcha.OpenCaptchaTimeOut,
		},
		Sqlite: response.SqliteConfigResponse{
			DbName:       config.Sqlite.Dbname,
			Path:         config.Sqlite.Path,
			MaxOpenConns: config.Sqlite.MaxOpenConns,
			MaxIdleConns: config.Sqlite.MaxIdleConns,
		},
		Zap: response.ZapConfigResponse{
			Level:        config.Zap.Level,
			Director:     config.Zap.Director,
			RetentionDay: config.Zap.RetentionDay,
			LogInConsole: config.Zap.LogInConsole,
			ShowLine:     config.Zap.ShowLine,
		},
	}, nil
}

// UpdateGeneralConfig 更新常规配置
func (s *ConfigService) UpdateGeneralConfig(req request.GeneralConfigRequest) error {
	// 验证JWT时间格式
	if err := s.validateJWTTimeFormat(req.JWT.ExpiresTime); err != nil {
		return fmt.Errorf("JWT过期时间格式错误: %v", err)
	}
	if err := s.validateJWTTimeFormat(req.JWT.BufferTime); err != nil {
		return fmt.Errorf("JWT缓冲时间格式错误: %v", err)
	}

	// 验证端口范围
	if req.Server.ServerStartPort >= req.Server.ServerEndPort {
		return fmt.Errorf("代理端口起始范围必须小于结束范围")
	}

	// 验证目录路径
	if err := s.validateDirectoryPaths(req.Server.ClientBinDir, req.Server.UploadDir, req.Server.DownloadDir); err != nil {
		return fmt.Errorf("目录路径验证失败: %v", err)
	}

	// 更新服务器配置
	global.CONFIG.Server.PORT = req.Server.PORT
	global.CONFIG.Server.Version = req.Server.Version
	global.CONFIG.Server.RouterPrefix = req.Server.RouterPrefix
	global.CONFIG.Server.LimitCountIP = req.Server.LimitCountIP
	global.CONFIG.Server.LimitTimeIP = req.Server.LimitTimeIP
	global.CONFIG.Server.ServerStartPort = req.Server.ServerStartPort
	global.CONFIG.Server.ServerEndPort = req.Server.ServerEndPort
	global.CONFIG.Server.ClientBinDir = req.Server.ClientBinDir
	global.CONFIG.Server.UploadDir = req.Server.UploadDir
	global.CONFIG.Server.DownloadDir = req.Server.DownloadDir

	// 更新JWT配置
	global.CONFIG.JWT.ExpiresTime = req.JWT.ExpiresTime
	global.CONFIG.JWT.BufferTime = req.JWT.BufferTime
	global.CONFIG.JWT.SigningKey = req.JWT.SigningKey

	// 更新验证码配置
	global.CONFIG.Captcha.KeyLong = req.Captcha.KeyLong
	global.CONFIG.Captcha.ImgWidth = req.Captcha.ImgWidth
	global.CONFIG.Captcha.ImgHeight = req.Captcha.ImgHeight
	global.CONFIG.Captcha.OpenCaptcha = req.Captcha.OpenCaptcha
	global.CONFIG.Captcha.MaxFailedAttempts = req.Captcha.MaxFailedAttempts
	global.CONFIG.Captcha.OpenCaptchaTimeOut = req.Captcha.OpenCaptchaTimeOut

	// 更新SQLite配置
	global.CONFIG.Sqlite.Dbname = req.Sqlite.DbName
	global.CONFIG.Sqlite.Path = req.Sqlite.Path
	global.CONFIG.Sqlite.MaxOpenConns = req.Sqlite.MaxOpenConns
	global.CONFIG.Sqlite.MaxIdleConns = req.Sqlite.MaxIdleConns

	// 更新日志配置
	global.CONFIG.Zap.Level = req.Zap.Level
	global.CONFIG.Zap.Director = req.Zap.Director
	global.CONFIG.Zap.RetentionDay = req.Zap.RetentionDay
	global.CONFIG.Zap.LogInConsole = req.Zap.LogInConsole
	global.CONFIG.Zap.ShowLine = req.Zap.ShowLine

	// 持久化到配置文件
	return s.saveConfigToFile()
}

// GetNotificationConfig 获取通知配置
func (s *ConfigService) GetNotificationConfig() (response.NotificationConfigResponse, error) {
	// 从Viper获取通知配置，如果不存在则使用默认值
	return response.NotificationConfigResponse{
		ClientStatus:     global.VP.GetBool("notification.clientStatus"),
		DisplayDuration:  global.VP.GetInt("notification.displayDuration"),
		SoundEnabled:     global.VP.GetBool("notification.soundEnabled"),
		MaxNotifications: global.VP.GetInt("notification.maxNotifications"),
	}, nil
}

// UpdateNotificationConfig 更新通知配置
func (s *ConfigService) UpdateNotificationConfig(req request.NotificationConfigRequest) error {
	// 设置默认值
	if req.DisplayDuration == 0 {
		req.DisplayDuration = 5000
	}
	if req.MaxNotifications == 0 {
		req.MaxNotifications = 5
	}

	// 更新Viper配置
	global.VP.Set("notification.clientStatus", req.ClientStatus)
	global.VP.Set("notification.displayDuration", req.DisplayDuration)
	global.VP.Set("notification.soundEnabled", req.SoundEnabled)
	global.VP.Set("notification.maxNotifications", req.MaxNotifications)

	// 持久化到配置文件
	return s.saveConfigToFile()
}

// ResetConfig 重置配置到默认值
func (s *ConfigService) ResetConfig() error {
	// 创建默认配置
	defaultConfig := s.getDefaultConfig()
	
	// 更新全局配置
	global.CONFIG = defaultConfig
	
	// 重置通知配置
	global.VP.Set("notification.clientStatus", false)
	global.VP.Set("notification.displayDuration", 5000)
	global.VP.Set("notification.soundEnabled", true)
	global.VP.Set("notification.maxNotifications", 5)

	// 持久化到配置文件
	return s.saveConfigToFile()
}

// validateJWTTimeFormat 验证JWT时间格式
func (s *ConfigService) validateJWTTimeFormat(timeStr string) error {
	// 支持的时间格式: 1d, 2h, 30m, 60s
	if len(timeStr) < 2 {
		return fmt.Errorf("时间格式错误")
	}
	
	unit := timeStr[len(timeStr)-1:]
	if unit != "d" && unit != "h" && unit != "m" && unit != "s" {
		return fmt.Errorf("时间单位必须是 d(天), h(小时), m(分钟), s(秒)")
	}
	
	// 尝试解析时间
	_, err := time.ParseDuration(timeStr)
	if err != nil {
		// 如果直接解析失败，尝试转换格式
		switch unit {
		case "d":
			numStr := timeStr[:len(timeStr)-1]
			_, err = time.ParseDuration(numStr + "h")
			if err != nil {
				return fmt.Errorf("天数格式错误")
			}
		default:
			return fmt.Errorf("时间格式错误: %v", err)
		}
	}
	
	return nil
}

// validateDirectoryPaths 验证目录路径
func (s *ConfigService) validateDirectoryPaths(clientBinDir, uploadDir, downloadDir string) error {
	dirs := map[string]string{
		"客户端程序目录": clientBinDir,
		"上传目录":    uploadDir,
		"下载目录":    downloadDir,
	}
	
	for name, dir := range dirs {
		if dir == "" {
			return fmt.Errorf("%s不能为空", name)
		}
		
		// 检查目录是否存在，如果不存在则尝试创建
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建%s失败: %v", name, err)
			}
			global.LOG.Info(fmt.Sprintf("创建目录: %s", dir))
		}
	}
	
	return nil
}

// saveConfigToFile 保存配置到文件
func (s *ConfigService) saveConfigToFile() error {
	configPath := "config.yaml"
	
	// 创建配置文件的备份
	backupPath := configPath + ".backup." + time.Now().Format("20060102150405")
	if err := s.copyFile(configPath, backupPath); err != nil {
		global.LOG.Warn("创建配置文件备份失败: " + err.Error())
	}

	// 将当前配置转换为map以便序列化
	configMap := make(map[string]interface{})
	
	// 服务器配置
	configMap["server"] = map[string]interface{}{
		"id":                global.CONFIG.Server.ID,
		"version":           global.CONFIG.Server.Version,
		"db-type":           global.CONFIG.Server.DbType,
		"port":              global.CONFIG.Server.PORT,
		"router-prefix":     global.CONFIG.Server.RouterPrefix,
		"iplimit-count":     global.CONFIG.Server.LimitCountIP,
		"iplimit-time":      global.CONFIG.Server.LimitTimeIP,
		"client-bin-dir":    global.CONFIG.Server.ClientBinDir,
		"upload-dir":        global.CONFIG.Server.UploadDir,
		"download-dir":      global.CONFIG.Server.DownloadDir,
		"server-start-port": global.CONFIG.Server.ServerStartPort,
		"server-end-port":   global.CONFIG.Server.ServerEndPort,
	}

	// JWT配置
	configMap["jwt"] = map[string]interface{}{
		"signing-key":  global.CONFIG.JWT.SigningKey,
		"expires-time": global.CONFIG.JWT.ExpiresTime,
		"buffer-time":  global.CONFIG.JWT.BufferTime,
		"issuer":       global.CONFIG.JWT.Issuer,
	}

	// 验证码配置
	configMap["captcha"] = map[string]interface{}{
		"key-long":              global.CONFIG.Captcha.KeyLong,
		"img-width":             global.CONFIG.Captcha.ImgWidth,
		"img-height":            global.CONFIG.Captcha.ImgHeight,
		"open-captcha":          global.CONFIG.Captcha.OpenCaptcha,
		"max-failed-attempts":   global.CONFIG.Captcha.MaxFailedAttempts,
		"open-captcha-timeout":  global.CONFIG.Captcha.OpenCaptchaTimeOut,
	}

	// 其他配置保持不变
	configMap["admin"] = map[string]interface{}{
		"username": global.CONFIG.Admin.Username,
		"password": global.CONFIG.Admin.Password,
	}

	configMap["sqlite"] = map[string]interface{}{
		"path":           global.CONFIG.Sqlite.Path,
		"db-name":        global.CONFIG.Sqlite.Dbname,
		"username":       global.CONFIG.Sqlite.Username,
		"password":       global.CONFIG.Sqlite.Password,
		"port":           global.CONFIG.Sqlite.Port,
		"config":         global.CONFIG.Sqlite.Config,
		"max-idle-conns": global.CONFIG.Sqlite.MaxIdleConns,
		"max-open-conns": global.CONFIG.Sqlite.MaxOpenConns,
		"log-mode":       global.CONFIG.Sqlite.LogMode,
		"log-zap":        global.CONFIG.Sqlite.LogZap,
		"engine":         global.CONFIG.Sqlite.Engine,
		"prefix":         global.CONFIG.Sqlite.Prefix,
		"singular":       global.CONFIG.Sqlite.Singular,
	}

	configMap["zap"] = map[string]interface{}{
		"level":         global.CONFIG.Zap.Level,
		"prefix":        global.CONFIG.Zap.Prefix,
		"format":        global.CONFIG.Zap.Format,
		"director":      global.CONFIG.Zap.Director,
		"encode-level":  global.CONFIG.Zap.EncodeLevel,
		"stacktrace-key": global.CONFIG.Zap.StacktraceKey,
		"log-in-console": global.CONFIG.Zap.LogInConsole,
		"show-line":     global.CONFIG.Zap.ShowLine,
		"retention-day": global.CONFIG.Zap.RetentionDay,
	}

	configMap["disk-list"] = global.CONFIG.DiskList

	// 添加通知配置
	configMap["notification"] = map[string]interface{}{
		"clientStatus":     global.VP.GetBool("notification.clientStatus"),
		"displayDuration":  global.VP.GetInt("notification.displayDuration"),
		"soundEnabled":     global.VP.GetBool("notification.soundEnabled"),
		"maxNotifications": global.VP.GetInt("notification.maxNotifications"),
	}

	// 序列化为YAML
	yamlData, err := yaml.Marshal(configMap)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 安全写入文件
	tempPath := configPath + ".tmp"
	if err := os.WriteFile(tempPath, yamlData, 0644); err != nil {
		return fmt.Errorf("写入临时配置文件失败: %v", err)
	}

	// 原子性替换配置文件
	if err := os.Rename(tempPath, configPath); err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("替换配置文件失败: %v", err)
	}

	// 重新加载Viper配置
	global.VP.SetConfigFile(configPath)
	if err := global.VP.ReadInConfig(); err != nil {
		global.LOG.Warn("重新加载配置失败: " + err.Error())
	}

	return nil
}

// copyFile 复制文件
func (s *ConfigService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}

// getDefaultConfig 获取默认配置
func (s *ConfigService) getDefaultConfig() config.Config {
	return config.Config{
		Server: config.Server{
			ID:              "RANDOM",
			Version:         "1.0.0",
			DbType:          "sqlite",
			PORT:            8888,
			RouterPrefix:    "/api",
			LimitCountIP:    15000,
			LimitTimeIP:     3600,
			ClientBinDir:    "./clientbin",
			UploadDir:       "./upload",
			DownloadDir:     "./download",
			ServerStartPort: 20000,
			ServerEndPort:   65535,
		},
		JWT: config.JWT{
			SigningKey:  global.CONFIG.JWT.SigningKey, // 保持原有签名密钥
			ExpiresTime: "7d",
			BufferTime:  "1d",
			Issuer:      "ezUser",
		},
		Captcha: config.Captcha{
			KeyLong:            6,
			ImgWidth:           240,
			ImgHeight:          80,
			OpenCaptcha:        true,
			MaxFailedAttempts:  5,
			OpenCaptchaTimeOut: 3600,
		},
		Admin:    global.CONFIG.Admin,    // 保持原有管理员配置
		Sqlite:   global.CONFIG.Sqlite,   // 保持原有数据库配置
		Zap:      global.CONFIG.Zap,      // 保持原有日志配置
		DiskList: global.CONFIG.DiskList, // 保持原有磁盘配置
	}
}