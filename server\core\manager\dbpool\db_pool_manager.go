package dbpool

import (
	"context"
	"database/sql"
	"fmt"
	"server/core/manager/workerpool"
	"server/global"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DBPoolManager 数据库连接池管理器
type DBPoolManager struct {
	db    *gorm.DB
	sqlDB *sql.DB
	stats *DBPoolStats
	
	// 健康检查
	healthCheckInterval time.Duration
	healthCheckTimeout  time.Duration
	stopHealthCheck     chan struct{}
	
	// 监控
	mu sync.RWMutex
}

// DBPoolStats 数据库连接池统计信息
type DBPoolStats struct {
	// 连接池基础统计 - 使用原子操作
	MaxOpenConns    int32 `json:"max_open_conns"`
	MaxIdleConns    int32 `json:"max_idle_conns"`
	OpenConns       int32 `json:"open_conns"`
	InUseConns      int32 `json:"in_use_conns"`
	IdleConns       int32 `json:"idle_conns"`
	
	// 操作统计 - 使用原子操作
	TotalQueries    int64 `json:"total_queries"`
	SuccessQueries  int64 `json:"success_queries"`
	FailedQueries   int64 `json:"failed_queries"`
	SlowQueries     int64 `json:"slow_queries"`
	
	// 性能统计 - 使用原子操作存储纳秒值
	AvgQueryTimeNs  int64 `json:"avg_query_time_ns"`
	MaxQueryTimeNs  int64 `json:"max_query_time_ns"`
	MinQueryTimeNs  int64 `json:"min_query_time_ns"`
	
	// 健康检查统计
	HealthCheckCount    int64 `json:"health_check_count"`
	HealthCheckFailures int64 `json:"health_check_failures"`
	LastHealthCheckNs   int64 `json:"last_health_check_ns"`
	
	// 连接生命周期统计
	ConnectionsCreated int64 `json:"connections_created"`
	ConnectionsClosed  int64 `json:"connections_closed"`
	ConnectionErrors   int64 `json:"connection_errors"`
	
	// 超时和重试统计
	QueryTimeouts int64 `json:"query_timeouts"`
	RetryAttempts int64 `json:"retry_attempts"`
	
	// 时间统计
	StartTimeNs int64 `json:"start_time_ns"`
}

// DBPoolConfig 数据库连接池配置
type DBPoolConfig struct {
	MaxOpenConns        int           `json:"max_open_conns"`
	MaxIdleConns        int           `json:"max_idle_conns"`
	ConnMaxLifetime     time.Duration `json:"conn_max_lifetime"`
	ConnMaxIdleTime     time.Duration `json:"conn_max_idle_time"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`
	QueryTimeout        time.Duration `json:"query_timeout"`
	SlowQueryThreshold  time.Duration `json:"slow_query_threshold"`

	// 🚀 自动扩容配置
	AutoScale           bool    `json:"auto_scale"`            // 是否启用自动扩容
	ScaleUpThreshold    float64 `json:"scale_up_threshold"`    // 扩容阈值（连接利用率）
	ScaleDownThreshold  float64 `json:"scale_down_threshold"`  // 缩容阈值
	ScaleUpStep         int     `json:"scale_up_step"`         // 每次扩容的连接数
	ScaleDownStep       int     `json:"scale_down_step"`       // 每次缩容的连接数
	MinConnections      int     `json:"min_connections"`       // 最小连接数
	MaxConnections      int     `json:"max_connections"`       // 绝对最大连接数
	ScaleCheckInterval  time.Duration `json:"scale_check_interval"` // 扩缩容检查间隔
}

// 全局数据库连接池管理器
var GlobalDBPoolManager *DBPoolManager

// NewDBPoolManager 创建数据库连接池管理器
func NewDBPoolManager(db *gorm.DB, config DBPoolConfig) (*DBPoolManager, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层数据库连接失败: %w", err)
	}
	
	manager := &DBPoolManager{
		db:                  db,
		sqlDB:               sqlDB,
		healthCheckInterval: config.HealthCheckInterval,
		healthCheckTimeout:  config.HealthCheckTimeout,
		stopHealthCheck:     make(chan struct{}),
		stats: &DBPoolStats{
			MaxOpenConns: int32(config.MaxOpenConns),
			MaxIdleConns: int32(config.MaxIdleConns),
		},
	}
	
	// 配置连接池参数
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime)
	
	// 初始化统计信息
	atomic.StoreInt64(&manager.stats.StartTimeNs, time.Now().UnixNano())
	atomic.StoreInt64(&manager.stats.MinQueryTimeNs, int64(time.Hour)) // 初始化为一个大值
	
	global.LOG.Info("数据库连接池管理器创建成功",
		zap.Int("maxOpenConns", config.MaxOpenConns),
		zap.Int("maxIdleConns", config.MaxIdleConns),
		zap.Duration("connMaxLifetime", config.ConnMaxLifetime),
		zap.Duration("healthCheckInterval", config.HealthCheckInterval))
	
	return manager, nil
}

// Start 启动数据库连接池管理器
func (dm *DBPoolManager) Start() {
	// 启动健康检查
	go dm.startHealthCheck()

	// 启动统计更新
	go dm.startStatsUpdater()

	// 🚀 启动自动扩缩容检查
	go dm.startAutoScaler()

	global.LOG.Info("数据库连接池管理器已启动")
}

// Stop 停止数据库连接池管理器
func (dm *DBPoolManager) Stop() {
	close(dm.stopHealthCheck)
	global.LOG.Info("数据库连接池管理器已停止")
}

// GetStats 获取数据库连接池统计信息
func (dm *DBPoolManager) GetStats() *DBPoolStats {
	// 更新实时连接统计
	dm.updateConnectionStats()
	
	// 创建统计信息副本
	stats := &DBPoolStats{
		MaxOpenConns:        atomic.LoadInt32(&dm.stats.MaxOpenConns),
		MaxIdleConns:        atomic.LoadInt32(&dm.stats.MaxIdleConns),
		OpenConns:           atomic.LoadInt32(&dm.stats.OpenConns),
		InUseConns:          atomic.LoadInt32(&dm.stats.InUseConns),
		IdleConns:           atomic.LoadInt32(&dm.stats.IdleConns),
		TotalQueries:        atomic.LoadInt64(&dm.stats.TotalQueries),
		SuccessQueries:      atomic.LoadInt64(&dm.stats.SuccessQueries),
		FailedQueries:       atomic.LoadInt64(&dm.stats.FailedQueries),
		SlowQueries:         atomic.LoadInt64(&dm.stats.SlowQueries),
		AvgQueryTimeNs:      atomic.LoadInt64(&dm.stats.AvgQueryTimeNs),
		MaxQueryTimeNs:      atomic.LoadInt64(&dm.stats.MaxQueryTimeNs),
		MinQueryTimeNs:      atomic.LoadInt64(&dm.stats.MinQueryTimeNs),
		HealthCheckCount:    atomic.LoadInt64(&dm.stats.HealthCheckCount),
		HealthCheckFailures: atomic.LoadInt64(&dm.stats.HealthCheckFailures),
		LastHealthCheckNs:   atomic.LoadInt64(&dm.stats.LastHealthCheckNs),
		ConnectionsCreated:  atomic.LoadInt64(&dm.stats.ConnectionsCreated),
		ConnectionsClosed:   atomic.LoadInt64(&dm.stats.ConnectionsClosed),
		ConnectionErrors:    atomic.LoadInt64(&dm.stats.ConnectionErrors),
		QueryTimeouts:       atomic.LoadInt64(&dm.stats.QueryTimeouts),
		RetryAttempts:       atomic.LoadInt64(&dm.stats.RetryAttempts),
		StartTimeNs:         atomic.LoadInt64(&dm.stats.StartTimeNs),
	}
	
	return stats
}

// updateConnectionStats 更新连接统计信息
func (dm *DBPoolManager) updateConnectionStats() {
	stats := dm.sqlDB.Stats()
	
	atomic.StoreInt32(&dm.stats.OpenConns, int32(stats.OpenConnections))
	atomic.StoreInt32(&dm.stats.InUseConns, int32(stats.InUse))
	atomic.StoreInt32(&dm.stats.IdleConns, int32(stats.Idle))
}

// startHealthCheck 启动健康检查
func (dm *DBPoolManager) startHealthCheck() {
	ticker := time.NewTicker(dm.healthCheckInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			dm.performHealthCheck()
		case <-dm.stopHealthCheck:
			return
		}
	}
}

// performHealthCheck 执行健康检查
func (dm *DBPoolManager) performHealthCheck() {
	// 使用工作池执行健康检查，避免阻塞
	task := workerpool.NewDatabaseTask("db_health_check", func() error {
		ctx, cancel := context.WithTimeout(context.Background(), dm.healthCheckTimeout)
		defer cancel()
		
		start := time.Now()
		err := dm.sqlDB.PingContext(ctx)
		duration := time.Since(start)
		
		atomic.AddInt64(&dm.stats.HealthCheckCount, 1)
		atomic.StoreInt64(&dm.stats.LastHealthCheckNs, time.Now().UnixNano())
		
		if err != nil {
			atomic.AddInt64(&dm.stats.HealthCheckFailures, 1)
			global.LOG.Error("数据库健康检查失败",
				zap.Error(err),
				zap.Duration("duration", duration))
			return err
		}
		
		global.LOG.Debug("数据库健康检查成功",
			zap.Duration("duration", duration))
		
		return nil
	})
	
	if err := workerpool.SubmitDatabaseTask(task); err != nil {
		global.LOG.Error("提交数据库健康检查任务失败", zap.Error(err))
	}
}

// startStatsUpdater 启动统计更新器
func (dm *DBPoolManager) startStatsUpdater() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dm.updateConnectionStats()
		case <-dm.stopHealthCheck:
			return
		}
	}
}

// startAutoScaler 启动自动扩缩容检查器
func (dm *DBPoolManager) startAutoScaler() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dm.checkAndScale()
		case <-dm.stopHealthCheck:
			return
		}
	}
}

// checkAndScale 检查并执行自动扩缩容
func (dm *DBPoolManager) checkAndScale() {
	stats := dm.sqlDB.Stats()

	// 计算连接利用率
	utilization := float64(stats.InUse) / float64(stats.MaxOpenConnections)

	// 获取当前配置
	currentMaxOpen := stats.MaxOpenConnections

	global.LOG.Debug("数据库连接池扩缩容检查",
		zap.Int("inUse", stats.InUse),
		zap.Int("idle", stats.Idle),
		zap.Int("maxOpen", currentMaxOpen),
		zap.Float64("utilization", utilization))

	// 扩容检查
	if utilization > 0.8 && currentMaxOpen < 100 { // 利用率超过80%且未达到最大值
		newMaxOpen := currentMaxOpen + 5 // 每次增加5个连接
		if newMaxOpen > 100 {
			newMaxOpen = 100
		}

		newMaxIdle := int(float64(newMaxOpen) * 0.3) // 空闲连接为最大连接的30%
		if newMaxIdle < 2 {
			newMaxIdle = 2
		}

		dm.sqlDB.SetMaxOpenConns(newMaxOpen)
		dm.sqlDB.SetMaxIdleConns(newMaxIdle)

		// 更新统计信息
		atomic.StoreInt32(&dm.stats.MaxOpenConns, int32(newMaxOpen))
		atomic.StoreInt32(&dm.stats.MaxIdleConns, int32(newMaxIdle))

		global.LOG.Info("数据库连接池自动扩容",
			zap.Int("oldMaxOpen", currentMaxOpen),
			zap.Int("newMaxOpen", newMaxOpen),
			zap.Int("newMaxIdle", newMaxIdle),
			zap.Float64("utilization", utilization))

	} else if utilization < 0.3 && currentMaxOpen > 10 { // 利用率低于30%且大于最小值
		// 缩容检查
		newMaxOpen := currentMaxOpen - 2 // 每次减少2个连接
		if newMaxOpen < 10 {
			newMaxOpen = 10
		}

		newMaxIdle := int(float64(newMaxOpen) * 0.3)
		if newMaxIdle < 2 {
			newMaxIdle = 2
		}

		dm.sqlDB.SetMaxOpenConns(newMaxOpen)
		dm.sqlDB.SetMaxIdleConns(newMaxIdle)

		// 更新统计信息
		atomic.StoreInt32(&dm.stats.MaxOpenConns, int32(newMaxOpen))
		atomic.StoreInt32(&dm.stats.MaxIdleConns, int32(newMaxIdle))

		global.LOG.Info("数据库连接池自动缩容",
			zap.Int("oldMaxOpen", currentMaxOpen),
			zap.Int("newMaxOpen", newMaxOpen),
			zap.Int("newMaxIdle", newMaxIdle),
			zap.Float64("utilization", utilization))
	}
}

// ExecuteWithMonitoring 执行数据库操作并监控性能
func (dm *DBPoolManager) ExecuteWithMonitoring(operation string, fn func() error) error {
	return dm.ExecuteWithRetry(operation, fn, 3, time.Second)
}

// ExecuteWithRetry 执行数据库操作，支持重试和监控
func (dm *DBPoolManager) ExecuteWithRetry(operation string, fn func() error, maxRetries int, retryDelay time.Duration) error {
	start := time.Now()
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			atomic.AddInt64(&dm.stats.RetryAttempts, 1)
			time.Sleep(retryDelay * time.Duration(attempt))
		}

		// 执行操作
		err := fn()
		duration := time.Since(start)

		// 更新统计信息
		atomic.AddInt64(&dm.stats.TotalQueries, 1)
		dm.updateQueryStats(duration, err)

		if err == nil {
			atomic.AddInt64(&dm.stats.SuccessQueries, 1)
			return nil
		}

		lastErr = err

		// 检查是否应该重试
		if !dm.shouldRetry(err) || attempt == maxRetries {
			break
		}

		global.LOG.Warn("数据库操作失败，准备重试",
			zap.String("operation", operation),
			zap.Int("attempt", attempt+1),
			zap.Int("maxRetries", maxRetries),
			zap.Error(err))
	}

	atomic.AddInt64(&dm.stats.FailedQueries, 1)
	global.LOG.Error("数据库操作最终失败",
		zap.String("operation", operation),
		zap.Int("attempts", maxRetries+1),
		zap.Error(lastErr))

	return lastErr
}

// shouldRetry 判断是否应该重试
func (dm *DBPoolManager) shouldRetry(err error) bool {
	if err == nil {
		return false
	}

	// 检查是否是可重试的错误
	errStr := err.Error()
	retryableErrors := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"temporary failure",
		"database is locked",
	}

	for _, retryableErr := range retryableErrors {
		if contains(errStr, retryableErr) {
			return true
		}
	}

	return false
}

// updateQueryStats 更新查询统计信息
func (dm *DBPoolManager) updateQueryStats(duration time.Duration, err error) {
	durationNs := int64(duration)

	// 更新平均查询时间（移动平均）
	for {
		current := atomic.LoadInt64(&dm.stats.AvgQueryTimeNs)
		var newAvg int64
		if current == 0 {
			newAvg = durationNs
		} else {
			newAvg = (current*9 + durationNs) / 10
		}
		if atomic.CompareAndSwapInt64(&dm.stats.AvgQueryTimeNs, current, newAvg) {
			break
		}
	}

	// 更新最大查询时间
	for {
		current := atomic.LoadInt64(&dm.stats.MaxQueryTimeNs)
		if current >= durationNs {
			break
		}
		if atomic.CompareAndSwapInt64(&dm.stats.MaxQueryTimeNs, current, durationNs) {
			break
		}
	}

	// 更新最小查询时间
	for {
		current := atomic.LoadInt64(&dm.stats.MinQueryTimeNs)
		if current != 0 && current <= durationNs {
			break
		}
		if atomic.CompareAndSwapInt64(&dm.stats.MinQueryTimeNs, current, durationNs) {
			break
		}
	}

	// 检查慢查询
	if duration > 100*time.Millisecond { // 100ms作为慢查询阈值
		atomic.AddInt64(&dm.stats.SlowQueries, 1)
	}

	// 检查超时
	if err != nil && contains(err.Error(), "timeout") {
		atomic.AddInt64(&dm.stats.QueryTimeouts, 1)
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
			 s[len(s)-len(substr):] == substr ||
			 containsInMiddle(s, substr))))
}

func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// GetCompatibleStats 获取兼容格式的统计信息
func (dm *DBPoolManager) GetCompatibleStats() map[string]interface{} {
	stats := dm.GetStats()

	return map[string]interface{}{
		"max_open_conns":        stats.MaxOpenConns,
		"max_idle_conns":        stats.MaxIdleConns,
		"open_conns":            stats.OpenConns,
		"in_use_conns":          stats.InUseConns,
		"idle_conns":            stats.IdleConns,
		"total_queries":         stats.TotalQueries,
		"success_queries":       stats.SuccessQueries,
		"failed_queries":        stats.FailedQueries,
		"slow_queries":          stats.SlowQueries,
		"avg_query_time":        time.Duration(stats.AvgQueryTimeNs),
		"max_query_time":        time.Duration(stats.MaxQueryTimeNs),
		"min_query_time":        time.Duration(stats.MinQueryTimeNs),
		"health_check_count":    stats.HealthCheckCount,
		"health_check_failures": stats.HealthCheckFailures,
		"last_health_check":     time.Unix(0, stats.LastHealthCheckNs),
		"connections_created":   stats.ConnectionsCreated,
		"connections_closed":    stats.ConnectionsClosed,
		"connection_errors":     stats.ConnectionErrors,
		"query_timeouts":        stats.QueryTimeouts,
		"retry_attempts":        stats.RetryAttempts,
		"start_time":            time.Unix(0, stats.StartTimeNs),
		"success_rate":          dm.calculateSuccessRate(stats),
		"connection_utilization": dm.calculateConnectionUtilization(stats),
		"health_status":         dm.calculateHealthStatus(stats),
	}
}

// calculateSuccessRate 计算成功率
func (dm *DBPoolManager) calculateSuccessRate(stats *DBPoolStats) float64 {
	if stats.TotalQueries == 0 {
		return 0.0
	}
	return float64(stats.SuccessQueries) / float64(stats.TotalQueries)
}

// calculateConnectionUtilization 计算连接利用率
func (dm *DBPoolManager) calculateConnectionUtilization(stats *DBPoolStats) float64 {
	if stats.MaxOpenConns == 0 {
		return 0.0
	}
	return float64(stats.InUseConns) / float64(stats.MaxOpenConns)
}

// calculateHealthStatus 计算健康状态
func (dm *DBPoolManager) calculateHealthStatus(stats *DBPoolStats) string {
	// 检查连接利用率
	utilization := dm.calculateConnectionUtilization(stats)
	if utilization > 0.9 {
		return "危险"
	}
	if utilization > 0.7 {
		return "警告"
	}

	// 检查成功率
	successRate := dm.calculateSuccessRate(stats)
	if successRate < 0.9 && stats.TotalQueries > 10 {
		return "警告"
	}

	// 检查健康检查失败率
	if stats.HealthCheckCount > 0 {
		failureRate := float64(stats.HealthCheckFailures) / float64(stats.HealthCheckCount)
		if failureRate > 0.1 {
			return "警告"
		}
	}

	return "健康"
}
