<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="diskGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- 外圆 -->
  <circle cx="12" cy="12" r="10" fill="url(#diskGradient)" filter="url(#glow)" opacity="0.8"/>
  
  <!-- 内圆 -->
  <circle cx="12" cy="12" r="6" fill="none" stroke="white" stroke-width="1" opacity="0.6"/>
  
  <!-- 中心孔 -->
  <circle cx="12" cy="12" r="2" fill="white" opacity="0.9"/>
  
  <!-- 数据轨道 -->
  <circle cx="12" cy="12" r="8" fill="none" stroke="white" stroke-width="0.5" opacity="0.4" stroke-dasharray="2,2">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 12 12"
      to="360 12 12"
      dur="4s"
      repeatCount="indefinite"/>
  </circle>
  
  <!-- 读写头 -->
  <rect x="6" y="11" width="4" height="2" fill="white" opacity="0.8" rx="1">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 12 12"
      to="360 12 12"
      dur="2s"
      repeatCount="indefinite"/>
  </rect>
</svg>