package basic

// SystemStatus 系统状态信息
type SystemStatus struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU使用率 (0-100)
	MemoryUsage float64 `json:"memory_usage"` // 内存使用率 (0-100)
	DiskUsage   float64 `json:"disk_usage"`   // 磁盘使用率 (0-100)
	Uptime      int64   `json:"uptime"`       // 系统运行时间（秒）
	LoadAvg     float64 `json:"load_avg"`     // 系统负载平均值
}

// NetworkStatus 网络状态信息
type NetworkStatus struct {
	LocalIP        string  `json:"local_ip"`         // 本地IP地址
	PublicIP       string  `json:"public_ip"`        // 公网IP地址
	Latency        int64   `json:"latency"`          // 网络延迟（毫秒）
	PacketLoss     float64 `json:"packet_loss"`      // 丢包率 (0-100)
	Bandwidth      int64   `json:"bandwidth"`        // 可用带宽（字节/秒）
	TotalBytesRecv uint64  `json:"total_bytes_recv"` // 总接收字节数
	TotalBytesSent uint64  `json:"total_bytes_sent"` // 总发送字节数
}

// HeartbeatConfig 心跳配置信息
type HeartbeatConfig struct {
	Interval    int `json:"interval"`     // 心跳间隔（秒）
	Timeout     int `json:"timeout"`      // 心跳超时时间（秒）
	MaxRetries  int `json:"max_retries"`  // 最大重试次数
	JitterRange int `json:"jitter_range"` // 抖动范围（毫秒）
}

// ClientManagement 客户端管理信息
type ClientManagement struct {
	ShouldReconnect bool     `json:"should_reconnect"` // 是否需要重连
	NewServerAddr   string   `json:"new_server_addr"`  // 新的服务器地址（用于迁移）
	ConfigUpdate    bool     `json:"config_update"`    // 是否有配置更新
	Commands        []string `json:"commands"`         // 待执行的命令列表
}

// ServerStatus 服务器状态信息（仅包含客户端需要知道的最小信息）
type ServerStatus struct {
	Status    uint8  `json:"status"`    // 服务器状态：1=正常，2=维护中，3=即将重启
	Timestamp int64  `json:"timestamp"` // 服务器时间戳（用于时间同步）
	Version   string `json:"version"`   // 服务器版本（用于兼容性检查）
}
