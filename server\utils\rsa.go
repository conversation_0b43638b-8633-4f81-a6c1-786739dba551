package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"

	"github.com/minio/sha256-simd"

	"golang.org/x/crypto/pbkdf2"
)

// deterministicReader 实现 io.Reader 接口，用于确定性密钥生成
type deterministicReader struct {
	seed   []byte
	offset int
}

func (r *deterministicReader) Read(p []byte) (n int, err error) {
	if r.offset >= len(r.seed) {
		// 不是循环使用种子，而是用 HMAC 扩展种子
		h := hmac.New(sha256.New, r.seed)
		h.Write([]byte{byte(r.offset >> 24), byte(r.offset >> 16), byte(r.offset >> 8), byte(r.offset)})
		r.seed = h.Sum(nil)
		r.offset = 0
	}
	n = copy(p, r.seed[r.offset:])
	r.offset += n
	return n, nil
}

func GenerateRSAKeyPair(key, salt string, keysize int) (*rsa.PrivateKey, error) {
	seed := pbkdf2.Key([]byte(key), []byte(salt), 4096, keysize, sha256.New)
	reader := &deterministicReader{seed: seed}
	privateKey, err := rsa.GenerateKey(reader, keysize)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %v", err)
	}
	return privateKey, nil
}

func EncodePrivateKeyToPEM(privateKey *rsa.PrivateKey) string {
	privDER := x509.MarshalPKCS1PrivateKey(privateKey)
	privBlock := pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privDER,
	}
	privatePEM := pem.EncodeToMemory(&privBlock)
	return string(privatePEM)
}

// EncodePublicKeyToPEM 将公钥编码为 PEM 格式
func EncodePublicKeyToPEM(publicKey *rsa.PublicKey) (string, error) {
	pubDER, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", err
	}
	pubBlock := pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: pubDER,
	}
	return string(pem.EncodeToMemory(&pubBlock)), nil
}

// DecodePublicKeyFromPEM 从PEM字符串还原RSA公钥
func DecodePublicKeyFromPEM(pemString string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(pemString))
	if block == nil {
		return nil, errors.New("failed to parse PEM block containing public key")
	}
	if block.Type != "RSA PUBLIC KEY" {
		return nil, errors.New("invalid PEM block type, expected PUBLIC KEY")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("not an RSA public key")
	}
	return rsaPub, nil
}

// DecodePrivateKeyFromPEM 从PEM字符串还原RSA私钥
func DecodePrivateKeyFromPEM(pemString string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(pemString))
	if block == nil {
		return nil, errors.New("failed to parse PEM block containing private key")
	}
	if block.Type != "RSA PRIVATE KEY" {
		return nil, errors.New("invalid PEM block type, expected RSA PRIVATE KEY")
	}
	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return priv, nil
}

// EncryptOAEP 使用 RSA-OAEP 加密
func EncryptOAEP(publicKey *rsa.PublicKey, text string) (string, error) {
	ciphertext, err := rsa.EncryptOAEP(
		sha256.New(),
		rand.Reader,
		publicKey,
		[]byte(text),
		nil, // label
	)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptOAEP 使用 RSA-OAEP 解密
func DecryptOAEP(privateKey *rsa.PrivateKey, ciphertext string) (string, error) {
	decoded, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	plaintext, err := rsa.DecryptOAEP(
		sha256.New(),
		rand.Reader,
		privateKey,
		decoded,
		nil, // label (需与加密时一致)
	)
	if err != nil {
		return "", err
	}
	return string(plaintext), nil
}

func TestKeyPair(privateKey *rsa.PrivateKey, publicKey *rsa.PublicKey) bool {
	// 简单的验证方法：检查模数(N)和指数(E)是否相同
	return privateKey.PublicKey.N.Cmp(publicKey.N) == 0 &&
		privateKey.PublicKey.E == publicKey.E
}

// decryptAES 使用AES-GCM解密数据
func DecryptAES(ciphertext, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, errors.New("密文长度不足")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}
