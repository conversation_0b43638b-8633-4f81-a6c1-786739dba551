package main

import (
	"debug/elf"
	"fmt"
	"io"
)

// ELFSegment represents a PT_LOAD segment from ELF file
type ELFSegment struct {
	VAddr    uint64 // Virtual address
	FileSize uint64 // Size in file
	MemSize  uint64 // Size in memory
	Offset   uint64 // Offset in file
	Flags    uint32 // Segment flags (PF_R, PF_W, PF_X)
	Data     []byte // Segment data
}

// ELFInfo contains parsed ELF information needed for shellcode generation
type ELFInfo struct {
	Entry     uint64       // Entry point address
	Segments  []ELFSegment // PT_LOAD segments
	IsPIE     bool         // Position Independent Executable
	Machine   elf.Machine  // Target architecture
	BaseAddr  uint64       // Base address for non-PIE executables
	TotalSize uint64       // Total memory size needed
}

// ParseELF parses an ELF file and extracts information needed for shellcode generation
func ParseELF(reader io.ReaderAt) (*ELFInfo, error) {
	// Open ELF file
	elfFile, err := elf.NewFile(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to parse ELF file: %v", err)
	}
	defer elfFile.Close()

	// Validate ELF file
	if err := validateELF(elfFile); err != nil {
		return nil, err
	}

	info := &ELFInfo{
		Entry:   elfFile.Entry,
		Machine: elfFile.Machine,
	}

	// Parse program headers to find PT_LOAD segments
	var minAddr, maxAddr uint64 = ^uint64(0), 0

	for _, prog := range elfFile.Progs {
		if prog.Type != elf.PT_LOAD {
			continue
		}

		// Read segment data
		data := make([]byte, prog.Filesz)
		if _, err := prog.ReadAt(data, 0); err != nil {
			return nil, fmt.Errorf("failed to read segment data: %v", err)
		}

		segment := ELFSegment{
			VAddr:    prog.Vaddr,
			FileSize: prog.Filesz,
			MemSize:  prog.Memsz,
			Offset:   prog.Off,
			Flags:    uint32(prog.Flags),
			Data:     data,
		}

		info.Segments = append(info.Segments, segment)

		// Track address range
		if prog.Vaddr < minAddr {
			minAddr = prog.Vaddr
		}
		if prog.Vaddr+prog.Memsz > maxAddr {
			maxAddr = prog.Vaddr + prog.Memsz
		}
	}

	if len(info.Segments) == 0 {
		return nil, fmt.Errorf("no PT_LOAD segments found")
	}

	// Determine if this is a PIE executable
	info.IsPIE = (info.Segments[0].VAddr == 0)
	info.BaseAddr = minAddr
	info.TotalSize = maxAddr - minAddr

	return info, nil
}

// validateELF validates that the ELF file is supported
func validateELF(elfFile *elf.File) error {
	// Check file type
	if elfFile.Type != elf.ET_EXEC && elfFile.Type != elf.ET_DYN {
		return fmt.Errorf("unsupported ELF type: %v (only ET_EXEC and ET_DYN are supported)", elfFile.Type)
	}

	// Check architecture - support multiple architectures
	switch elfFile.Machine {
	case elf.EM_X86_64:
		// x86_64 is supported
	case elf.EM_AARCH64:
		// ARM64 is supported
	case elf.EM_386:
		// i386 is supported
	case elf.EM_ARM:
		// ARM is supported
	default:
		return fmt.Errorf("unsupported architecture: %v (supported: x86_64, ARM64, i386, ARM)", elfFile.Machine)
	}

	// Check class
	if elfFile.Class != elf.ELFCLASS64 {
		return fmt.Errorf("unsupported ELF class: %v (only 64-bit is supported)", elfFile.Class)
	}

	return nil
}

// GetMemoryLayout calculates the memory layout for the ELF
func (info *ELFInfo) GetMemoryLayout() (baseAddr uint64, totalSize uint64) {
	if info.IsPIE {
		// For PIE executables, we'll let the system choose the base address
		return 0, info.TotalSize
	}
	// For non-PIE executables, use the original addresses
	return info.BaseAddr, info.TotalSize
}

// IsStaticLinked checks if the ELF is statically linked
func (info *ELFInfo) IsStaticLinked() bool {
	// A simple heuristic: if there's no PT_INTERP segment, it's likely static
	// This is not 100% accurate but good enough for our purposes
	return len(info.Segments) > 0 && info.Entry != 0
}

// GetSegmentFlags converts ELF segment flags to mmap protection flags
func GetSegmentFlags(elfFlags uint32) int {
	var prot int
	if elfFlags&uint32(elf.PF_R) != 0 {
		prot |= 0x1 // PROT_READ
	}
	if elfFlags&uint32(elf.PF_W) != 0 {
		prot |= 0x2 // PROT_WRITE
	}
	if elfFlags&uint32(elf.PF_X) != 0 {
		prot |= 0x4 // PROT_EXEC
	}
	return prot
}
