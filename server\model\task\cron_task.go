package task

import (
	"time"
)

// CronTask 定时任务模型
type CronTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	Name        string     `json:"name" gorm:"size:100;not null"`           // 任务名称
	Description string     `json:"description" gorm:"size:500"`             // 任务描述
	ClientID    uint       `json:"client_id" gorm:"not null;index"`         // 目标客户端ID
	TaskType    string     `json:"task_type" gorm:"size:50;not null"`       // 任务类型: command, script, screenshot, file
	CronExpr    string     `json:"cron_expr" gorm:"size:100;not null"`      // Cron表达式
	Command     string     `json:"command" gorm:"type:text"`                // 执行命令/脚本内容
	Params      string     `json:"params" gorm:"type:text"`                 // 任务参数(JSON格式)
	Status      string     `json:"status" gorm:"size:20;default:'stopped'"` // 状态: active, paused, stopped
	Timeout     int        `json:"timeout" gorm:"default:300"`              // 超时时间(秒)
	RetryCount  int        `json:"retry_count" gorm:"default:0"`            // 重试次数
	CreatedBy   uint       `json:"created_by" gorm:"not null"`              // 创建者ID
	SourceType  string     `json:"source_type" gorm:"size:50"`              // 来源类型: manual, screenshot_timer
	SourceID    *uint64    `json:"source_id" gorm:"index"`                  // 来源ID(关联原始任务)
	CreatedAt   time.Time  `json:"created_at"`                              // 创建时间
	UpdatedAt   time.Time  `json:"updated_at"`                              // 更新时间
	NextRunAt   *time.Time `json:"next_run_at"`                             // 下次执行时间
	LastRunAt   *time.Time `json:"last_run_at"`                             // 上次执行时间
}

// TableName 指定表名
func (CronTask) TableName() string {
	return "cron_tasks"
}

// IsActive 检查任务是否处于活跃状态
func (t *CronTask) IsActive() bool {
	return t.Status == "active"
}

// IsPaused 检查任务是否处于暂停状态
func (t *CronTask) IsPaused() bool {
	return t.Status == "paused"
}

// IsStopped 检查任务是否处于停止状态
func (t *CronTask) IsStopped() bool {
	return t.Status == "stopped"
}

// CronExecution 定时任务执行记录模型
type CronExecution struct {
	ID        uint64    `json:"id" gorm:"primarykey"`
	TaskID    uint64    `json:"task_id" gorm:"not null;index"`   // 任务ID
	ClientID  uint      `json:"client_id" gorm:"not null;index"` // 客户端ID
	Status    string    `json:"status" gorm:"size:20;not null"`  // 执行状态: success, failed, timeout, cancelled
	Output    string    `json:"output" gorm:"type:text"`         // 执行输出
	ErrorMsg  string    `json:"error_msg" gorm:"type:text"`      // 错误信息
	StartTime time.Time `json:"start_time" gorm:"not null"`      // 开始时间
	EndTime   time.Time `json:"end_time"`                        // 结束时间
	Duration  int64     `json:"duration"`                        // 执行时长(毫秒)
	CreatedAt time.Time `json:"created_at"`                      // 创建时间
}

// TableName 指定表名
func (CronExecution) TableName() string {
	return "cron_executions"
}

// IsSuccess 检查执行是否成功
func (e *CronExecution) IsSuccess() bool {
	return e.Status == "success"
}

// IsFailed 检查执行是否失败
func (e *CronExecution) IsFailed() bool {
	return e.Status == "failed"
}

// IsTimeout 检查执行是否超时
func (e *CronExecution) IsTimeout() bool {
	return e.Status == "timeout"
}

// CronTemplate 定时任务模板模型
type CronTemplate struct {
	ID          uint64    `json:"id" gorm:"primarykey"`
	Name        string    `json:"name" gorm:"size:100;not null"`      // 模板名称
	Description string    `json:"description" gorm:"size:500"`        // 模板描述
	Category    string    `json:"category" gorm:"size:50;not null"`   // 模板分类
	TaskType    string    `json:"task_type" gorm:"size:50;not null"`  // 任务类型
	CronExpr    string    `json:"cron_expr" gorm:"size:100;not null"` // 默认Cron表达式
	Command     string    `json:"command" gorm:"type:text"`           // 默认命令/脚本
	Params      string    `json:"params" gorm:"type:text"`            // 默认参数(JSON格式)
	Timeout     int       `json:"timeout" gorm:"default:300"`         // 默认超时时间
	RetryCount  int       `json:"retry_count" gorm:"default:0"`       // 默认重试次数
	IsBuiltIn   bool      `json:"is_built_in" gorm:"default:false"`   // 是否为内置模板
	CreatedBy   uint      `json:"created_by"`                         // 创建者ID
	CreatedAt   time.Time `json:"created_at"`                         // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`                         // 更新时间
}

// TableName 指定表名
func (CronTemplate) TableName() string {
	return "cron_templates"
}

// TaskTypeConstants 任务类型常量
const (
	TaskTypeCommand    = "command"    // 命令执行
	TaskTypeScript     = "script"     // 脚本执行
	TaskTypeScreenshot = "screenshot" // 截图任务
	TaskTypeFile       = "file"       // 文件操作
	TaskTypeNetwork    = "network"    // 网络任务
	TaskTypeMonitor    = "monitor"    // 监控任务
)

// TaskStatusConstants 任务状态常量
const (
	TaskStatusActive  = "active"  // 活跃
	TaskStatusPaused  = "paused"  // 暂停
	TaskStatusStopped = "stopped" // 停止
)

// ExecutionStatusConstants 执行状态常量
const (
	ExecutionStatusSuccess   = "success"   // 成功
	ExecutionStatusFailed    = "failed"    // 失败
	ExecutionStatusTimeout   = "timeout"   // 超时
	ExecutionStatusCancelled = "cancelled" // 取消
)

// TemplateCategoryConstants 模板分类常量
const (
	TemplateCategorySystem   = "system"   // 系统管理
	TemplateCategoryMonitor  = "monitor"  // 监控类
	TemplateCategoryMaintain = "maintain" // 维护类
	TemplateCategoryBackup   = "backup"   // 备份类
	TemplateCategorySecurity = "security" // 安全类
	TemplateCategoryCustom   = "custom"   // 自定义
)

// GetTaskTypeDisplayName 获取任务类型显示名称
func GetTaskTypeDisplayName(taskType string) string {
	switch taskType {
	case TaskTypeCommand:
		return "命令执行"
	case TaskTypeScript:
		return "脚本执行"
	case TaskTypeScreenshot:
		return "截图任务"
	case TaskTypeFile:
		return "文件操作"
	case TaskTypeNetwork:
		return "网络任务"
	case TaskTypeMonitor:
		return "监控任务"
	default:
		return "未知类型"
	}
}

// GetTaskStatusDisplayName 获取任务状态显示名称
func GetTaskStatusDisplayName(status string) string {
	switch status {
	case TaskStatusActive:
		return "运行中"
	case TaskStatusPaused:
		return "已暂停"
	case TaskStatusStopped:
		return "已停止"
	default:
		return "未知状态"
	}
}

// GetExecutionStatusDisplayName 获取执行状态显示名称
func GetExecutionStatusDisplayName(status string) string {
	switch status {
	case ExecutionStatusSuccess:
		return "成功"
	case ExecutionStatusFailed:
		return "失败"
	case ExecutionStatusTimeout:
		return "超时"
	case ExecutionStatusCancelled:
		return "取消"
	default:
		return "未知状态"
	}
}

// GetTemplateCategoryDisplayName 获取模板分类显示名称
func GetTemplateCategoryDisplayName(category string) string {
	switch category {
	case TemplateCategorySystem:
		return "系统管理"
	case TemplateCategoryMonitor:
		return "监控类"
	case TemplateCategoryMaintain:
		return "维护类"
	case TemplateCategoryBackup:
		return "备份类"
	case TemplateCategorySecurity:
		return "安全类"
	case TemplateCategoryCustom:
		return "自定义"
	default:
		return "未知分类"
	}
}
