<template>
  <!-- 全屏预览覆盖层 -->
  <div v-if="visible" class="file-preview-overlay" @click="handleOverlayClick">
    <!-- 加载状态 -->
    <div v-if="loading" class="preview-loading">
      <div class="loading-spinner">
        <a-spin size="large" />
        <p class="loading-text">正在加载文件...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="preview-error">
      <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h3 class="error-title">{{ error }}</h3>
        <p class="error-subtitle">无法预览此文件</p>
        <button class="error-close-btn" @click="handleClose">关闭</button>
      </div>
    </div>

    <!-- 预览内容 -->
    <div v-else class="preview-container" @click.stop>
      <!-- 图片预览 - 全屏纯净模式 -->
      <div v-if="previewType === 'image'" class="image-preview-fullscreen">
        <img
          :src="fileUrl"
          :alt="fileName"
          class="fullscreen-image"
          @error="handleImageError"
          @click.stop
        />
        <!-- 圆形半透明关闭按钮 -->
        <button class="close-btn-circle" @click="handleClose">
          <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
        <!-- 文件名显示 -->
        <div class="image-info">
          <span class="image-filename">{{ fileName }}</span>
        </div>
      </div>

      <!-- PDF预览 - 优雅的全屏模式 -->
      <div v-else-if="previewType === 'pdf'" class="pdf-preview-fullscreen">
        <div class="pdf-header">
          <h3 class="pdf-title">{{ fileName }}</h3>
          <button class="close-btn-modern" @click="handleClose">
            <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
        <div class="pdf-content">
          <iframe
            :src="fileUrl"
            class="pdf-iframe-fullscreen"
            frameborder="0"
          ></iframe>
        </div>
      </div>
      <!-- 视频预览 - 现代化播放器 -->
      <div v-else-if="previewType === 'video'" class="video-preview-fullscreen">
        <div class="media-header">
          <h3 class="media-title">{{ fileName }}</h3>
          <button class="close-btn-modern" @click="handleClose">
            <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
        <div class="video-content">
          <video
            :src="fileUrl"
            controls
            class="fullscreen-video"
            @error="handleVideoError"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>

      <!-- 音频预览 - 优雅的音频播放器 -->
      <div v-else-if="previewType === 'audio'" class="audio-preview-fullscreen">
        <div class="audio-container">
          <button class="close-btn-modern audio-close" @click="handleClose">
            <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
          <div class="audio-visual">
            <div class="audio-icon">🎵</div>
            <h3 class="audio-title">{{ fileName }}</h3>
            <p class="audio-subtitle">音频文件</p>
          </div>
          <audio
            :src="fileUrl"
            controls
            class="modern-audio-player"
            @error="handleAudioError"
          >
            您的浏览器不支持音频播放
          </audio>
        </div>
      </div>

      <!-- Office文档预览 - 使用vue-office组件 -->
      <div v-else-if="previewType === 'office'" class="office-preview-fullscreen">
        <div class="office-header">
          <h3 class="office-title">{{ fileName }}</h3>
          <div class="office-actions">
            <!-- Office文档缩放控件 -->
            <div v-if="officeComponent && officeFileData" class="office-zoom-controls">
              <button class="zoom-btn" @click="zoomOut" :disabled="officeZoom <= 50">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                  <path d="M19 13H5v-2h14v2z"/>
                </svg>
              </button>
              <span class="zoom-display">{{ officeZoom }}%</span>
              <button class="zoom-btn" @click="zoomIn" :disabled="officeZoom >= 200">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
              </button>
              <button class="zoom-btn" @click="resetZoom" title="重置缩放">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </button>
              <button class="zoom-btn" @click="fitToWidth" title="适应宽度">
                <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                  <path d="M9 9h6v6h-6z M3 3v18h18V3H3zm16 16H5V5h14v14z"/>
                </svg>
              </button>
            </div>

            <button class="download-btn" @click="handleDownload">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
              </svg>
              下载
            </button>
            <button class="close-btn-modern" @click="handleClose">
              <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="office-content" :class="{ 'has-zoom': officeComponent && officeFileData }">
          <!-- 动态加载的Office组件 -->
          <div
            v-if="officeComponent && officeFileData"
            class="office-container-wrapper"
            :style="{ transform: `scale(${officeZoom / 100})`, transformOrigin: 'top left' }"
          >
            <component
              :is="officeComponent"
              :src="officeFileData"
              class="vue-office-container"
              @rendered="handleOfficeRendered"
              @error="handleOfficeError"
            />
          </div>

          <!-- 加载中或降级预览 -->
          <div v-else class="office-loading">
            <div v-if="loading" class="loading-content">
              <a-spin size="large" />
              <p>正在加载Office文档...</p>
            </div>
            <div v-else class="fallback-preview">
              <div class="office-icon">📄</div>
              <h3>Office文档预览</h3>
              <p>{{ fileName }}</p>
              <p class="fallback-text">使用在线预览服务...</p>
              <!-- 降级到iframe预览 -->
              <iframe
                v-if="fileUrl"
                :src="fileUrl"
                class="office-iframe-fallback"
                frameborder="0"
                @error="handleOfficeError"
              ></iframe>
            </div>
          </div>
        </div>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview-fullscreen">
        <div class="unsupported-container">
          <button class="close-btn-modern unsupported-close" @click="handleClose">
            <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
          <div class="unsupported-icon">📄</div>
          <h3 class="unsupported-title">不支持预览此文件类型</h3>
          <p class="unsupported-subtitle">{{ fileName }}</p>
          <button class="download-btn-large" @click="handleDownload">
            <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
              <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
            下载文件
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import { getPreviewType } from './fileUtils.js';
import { readFileContent } from '@/api/file.js';
// 导入 vue-office 组件 - 使用动态导入避免构建时错误
// import VueOfficeDocx from '@vue-office/docx';
// import VueOfficeExcel from '@vue-office/excel';
// import VueOfficePdf from '@vue-office/pdf';
// 导入样式
// import '@vue-office/docx/lib/index.css';
// import '@vue-office/excel/lib/index.css';
// import '@vue-office/pdf/lib/index.css';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  },
  clientId: {
    type: String,
    required: true
  },
  currentPath: {
    type: String,
    required: true
  }
});

// Emits
const emit = defineEmits(['update:visible', 'download']);

// 响应式数据
const loading = ref(false);
const error = ref('');
const fileUrl = ref('');
const officeFileData = ref(null); // 用于存储Office文件的二进制数据
const officeFileType = ref(''); // 用于确定使用哪个vue-office组件
const officeComponent = ref(null); // 动态加载的Office组件
const officeZoom = ref(100); // Office文档缩放比例

// 计算属性
const fileName = computed(() => props.file?.name || '');
const previewType = computed(() => props.file ? getPreviewType(props.file.name) : 'unknown');

// 处理覆盖层点击（点击背景关闭）
const handleOverlayClick = (event) => {
  // 只有点击覆盖层背景时才关闭，点击内容区域不关闭
  if (event.target === event.currentTarget) {
    handleClose();
  }
};

// 监听文件变化
watch(() => props.file, (newFile) => {
  if (newFile && props.visible) {
    loadFilePreview();
  }
}, { immediate: true });

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.file) {
    loadFilePreview();
  }
});

// 方法
const loadFilePreview = async () => {
  if (!props.file) return;

  loading.value = true;
  error.value = '';

  try {
    const filePath = `${props.currentPath}/${props.file.name}`.replace(/\/+/g, '/');

    if (previewType.value === 'pdf') {
      // PDF文件：读取文件内容并转换为Blob URL
      await loadPdfPreview(filePath);
    } else if (['image', 'video', 'audio'].includes(previewType.value)) {
      // 媒体文件：读取文件内容并转换为Blob URL
      await loadMediaPreview(filePath);
    } else if (previewType.value === 'office') {
      // Office文档：使用第三方预览服务
      await loadOfficePreview(filePath);
    }

    loading.value = false;
  } catch (err) {
    console.error('加载文件预览失败:', err);
    error.value = '加载文件失败: ' + (err.message || '未知错误');
    loading.value = false;
  }
};

const handleClose = () => {
  emit('update:visible', false);
};

const handleDownload = () => {
  emit('download', props.file);
};

// 通用的数据内容提取函数
const extractFileContent = (result, fileType = 'unknown') => {
  console.log(`${fileType}完整响应数据:`, result);

  let content;
  let fileResponse;

  // 首先提取FileReadResponse对象
  if (result.data && result.data.data) {
    fileResponse = result.data.data;
  } else if (result.data) {
    fileResponse = result.data;
  } else {
    console.error(`无法找到${fileType}响应数据，数据结构:`, result);
    throw new Error(`${fileType}数据格式不正确`);
  }

  console.log(`${fileType}响应对象:`, fileResponse);

  // 检查是否有错误
  if (fileResponse.error) {
    throw new Error(fileResponse.error);
  }

  // 检查操作是否成功
  if (!fileResponse.success) {
    if (fileResponse.not_exist) {
      throw new Error('文件不存在');
    }
    if (fileResponse.not_allow) {
      throw new Error('权限不足');
    }
    if (fileResponse.too_large) {
      throw new Error('文件过大');
    }
    throw new Error('文件读取失败');
  }

  // 提取文件内容
  content = fileResponse.content;

  console.log(`${fileType}内容长度:`, content?.length);
  console.log(`${fileType}是否为二进制文件:`, fileResponse.is_binary);
  console.log(`${fileType}编码方式:`, fileResponse.encoding);
  console.log(`${fileType}内容前100字符:`, content?.substring(0, 100));

  if (!content) {
    throw new Error(`${fileType}内容为空`);
  }

  return { content, isBinary: fileResponse.is_binary, encoding: fileResponse.encoding };
};

// 加载PDF预览
const loadPdfPreview = async (filePath) => {
  try {
    const result = await readFileContent(props.clientId, { path: filePath });

    if (result.code !== 200) {
      throw new Error(result.message || '文件读取失败');
    }

    // 使用通用函数提取PDF内容
    const fileData = extractFileContent(result, 'PDF');
    const { content: binaryContent, isBinary, encoding } = fileData;

    // 根据编码方式处理内容
    let bytes;
    if (encoding === 'base64' || isBinary) {
      // 对于Base64编码的二进制文件
      try {
        const binaryString = atob(binaryContent);
        bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        console.log('使用Base64解码处理PDF数据');
      } catch (base64Error) {
        console.error('Base64解码失败:', base64Error);
        throw new Error('PDF文件数据格式错误');
      }
    } else {
      // 对于直接的二进制字符串（兼容旧版本）
      bytes = new Uint8Array(binaryContent.length);
      for (let i = 0; i < binaryContent.length; i++) {
        bytes[i] = binaryContent.charCodeAt(i) & 0xFF;
      }
    }

    // 创建PDF Blob
    const blob = new Blob([bytes], { type: 'application/pdf' });
    fileUrl.value = URL.createObjectURL(blob);

    console.log('PDF Blob URL创建成功:', fileUrl.value);
    console.log('PDF Blob大小:', blob.size, '字节');
  } catch (err) {
    throw new Error('PDF加载失败: ' + err.message);
  }
};

// 加载媒体文件预览
const loadMediaPreview = async (filePath) => {
  try {
    const result = await readFileContent(props.clientId, { path: filePath });

    if (result.code !== 200) {
      throw new Error(result.message || '文件读取失败');
    }

    // 根据文件类型确定MIME类型
    const extension = props.file.name.split('.').pop().toLowerCase();
    let mimeType = 'application/octet-stream';

    if (['jpg', 'jpeg'].includes(extension)) mimeType = 'image/jpeg';
    else if (extension === 'png') mimeType = 'image/png';
    else if (extension === 'gif') mimeType = 'image/gif';
    else if (extension === 'bmp') mimeType = 'image/bmp';
    else if (extension === 'webp') mimeType = 'image/webp';
    else if (extension === 'svg') mimeType = 'image/svg+xml';
    else if (extension === 'ico') mimeType = 'image/x-icon';
    else if (extension === 'mp4') mimeType = 'video/mp4';
    else if (extension === 'avi') mimeType = 'video/avi';
    else if (extension === 'mov') mimeType = 'video/quicktime';
    else if (extension === 'wmv') mimeType = 'video/x-ms-wmv';
    else if (extension === 'webm') mimeType = 'video/webm';
    else if (extension === 'mkv') mimeType = 'video/x-matroska';
    else if (extension === 'flv') mimeType = 'video/x-flv';
    else if (extension === 'mp3') mimeType = 'audio/mpeg';
    else if (extension === 'wav') mimeType = 'audio/wav';
    else if (extension === 'ogg') mimeType = 'audio/ogg';
    else if (extension === 'aac') mimeType = 'audio/aac';
    else if (extension === 'flac') mimeType = 'audio/flac';
    else if (extension === 'wma') mimeType = 'audio/x-ms-wma';
    else if (extension === 'm4a') mimeType = 'audio/mp4';

    // 使用通用函数提取媒体文件内容
    const fileData = extractFileContent(result, '媒体文件');
    const { content: binaryContent, isBinary, encoding } = fileData;

    // 根据编码方式处理内容
    let bytes;
    if (encoding === 'base64' || isBinary) {
      // 对于Base64编码的二进制文件
      try {
        const binaryString = atob(binaryContent);
        bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        console.log('使用Base64解码处理媒体文件数据');
      } catch (base64Error) {
        console.error('Base64解码失败:', base64Error);
        throw new Error('媒体文件数据格式错误');
      }
    } else {
      // 对于直接的二进制字符串（兼容旧版本）
      bytes = new Uint8Array(binaryContent.length);
      for (let i = 0; i < binaryContent.length; i++) {
        bytes[i] = binaryContent.charCodeAt(i) & 0xFF;
      }
    }

    // 创建媒体文件Blob
    const blob = new Blob([bytes], { type: mimeType });
    fileUrl.value = URL.createObjectURL(blob);

    console.log('媒体文件Blob URL创建成功:', fileUrl.value);
    console.log('Blob大小:', blob.size, '字节');
  } catch (err) {
    throw new Error('媒体文件加载失败: ' + err.message);
  }
};

// 加载Office文档预览
const loadOfficePreview = async (filePath) => {
  try {
    console.log('开始加载Office文档:', filePath);

    // 确定Office文件类型
    const fileExtension = fileName.value.split('.').pop()?.toLowerCase();
    console.log('文件扩展名:', fileExtension);

    // 动态加载对应的vue-office组件（只处理Word和Excel，PDF有专门的预览）
    let componentModule = null;
    try {
      if (['doc', 'docx'].includes(fileExtension)) {
        officeFileType.value = 'docx';
        componentModule = await import('@vue-office/docx');
        // 动态导入CSS时需要处理可能的错误
        try {
          await import('@vue-office/docx/lib/index.css');
        } catch (cssError) {
          console.warn('docx CSS加载失败，但不影响功能:', cssError);
        }
      } else if (['xls', 'xlsx'].includes(fileExtension)) {
        officeFileType.value = 'excel';
        componentModule = await import('@vue-office/excel');
        // 动态导入CSS时需要处理可能的错误
        try {
          await import('@vue-office/excel/lib/index.css');
        } catch (cssError) {
          console.warn('excel CSS加载失败，但不影响功能:', cssError);
        }
      } else {
        // PDF和其他格式不使用vue-office，直接降级
        throw new Error('不支持的Office格式，使用降级预览');
      }

      // 设置动态组件
      officeComponent.value = componentModule.default;
      console.log('vue-office组件加载成功:', officeFileType.value);

    } catch (componentError) {
      console.error('vue-office组件加载失败:', componentError);
      throw componentError;
    }

    // 使用与PDF预览相同的方式读取Office文件
    console.log('开始读取Office文件内容...');
    const result = await readFileContent(props.clientId, { path: filePath });

    if (result.code !== 200) {
      throw new Error(result.message || 'Office文件读取失败');
    }

    // 使用通用函数提取Office文件内容
    const fileData = extractFileContent(result, 'Office文档');
    const { content: binaryContent, isBinary, encoding } = fileData;

    // 根据编码方式处理内容，转换为ArrayBuffer
    let arrayBuffer;
    if (encoding === 'base64' || isBinary) {
      // 对于Base64编码的二进制文件
      try {
        const binaryString = atob(binaryContent);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        arrayBuffer = bytes.buffer;
        console.log('使用Base64解码处理Office数据，大小:', arrayBuffer.byteLength, 'bytes');
      } catch (base64Error) {
        console.error('Base64解码失败:', base64Error);
        throw new Error('Office文件数据格式错误');
      }
    } else {
      // 对于直接的二进制字符串（兼容旧版本）
      const bytes = new Uint8Array(binaryContent.length);
      for (let i = 0; i < binaryContent.length; i++) {
        bytes[i] = binaryContent.charCodeAt(i) & 0xFF;
      }
      arrayBuffer = bytes.buffer;
      console.log('使用二进制字符串处理Office数据，大小:', arrayBuffer.byteLength, 'bytes');
    }

    // 将ArrayBuffer设置为vue-office需要的格式
    officeFileData.value = arrayBuffer;

    console.log('Office文档加载成功，类型:', officeFileType.value);
  } catch (err) {
    console.error('vue-office加载失败，尝试降级预览:', err);
    // 如果vue-office加载失败，降级到在线预览
    officeComponent.value = null;
    officeFileData.value = null;
    await loadOfficePreviewFallback(filePath);
  }
};

// 降级的Office预览方法（使用在线服务）
const loadOfficePreviewFallback = async (filePath) => {
  try {
    // 构建正确的文件下载URL用于第三方预览服务
    const downloadUrl = await readFileContent(props.clientId, { path: filePath });
    console.log('Office文档下载URL:', downloadUrl);

    // 使用Google Docs Viewer进行在线预览
    const encodedUrl = encodeURIComponent(downloadUrl);
    fileUrl.value = `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`;

    // 清空vue-office数据，使用iframe显示
    officeFileData.value = null;
    officeFileType.value = '';

    console.log('Office文档在线预览URL:', fileUrl.value);
  } catch (err) {
    console.error('Office文档预览失败:', err);
    throw new Error('Office文档预览失败: ' + err.message);
  }
};

// 错误处理函数
const handleImageError = (event) => {
  console.error('图片加载失败:', event);
  error.value = '图片加载失败，可能是文件格式不支持或文件已损坏';
};

const handleVideoError = (event) => {
  console.error('视频加载失败:', event);
  error.value = '视频加载失败，可能是文件格式不支持或文件已损坏';
};

const handleAudioError = (event) => {
  console.error('音频加载失败:', event);
  error.value = '音频加载失败，可能是文件格式不支持或文件已损坏';
};

const handleOfficeError = (err) => {
  console.error('Office文档预览失败:', err);
  error.value = 'Office文档预览失败，请尝试下载文件查看';
};

const handleOfficeRendered = () => {
  console.log('Office文档渲染成功');
  loading.value = false;
};

// Office文档缩放控制
const zoomIn = () => {
  if (officeZoom.value < 200) {
    officeZoom.value = Math.min(200, officeZoom.value + 25);
  }
};

const zoomOut = () => {
  if (officeZoom.value > 50) {
    officeZoom.value = Math.max(50, officeZoom.value - 25);
  }
};

const resetZoom = () => {
  officeZoom.value = 100;
};

const fitToWidth = () => {
  // 根据容器宽度计算合适的缩放比例
  // 这里设置一个经验值，可以根据实际情况调整
  officeZoom.value = 120;
};



// 组件卸载时清理Blob URL
onUnmounted(() => {
  if (fileUrl.value && fileUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(fileUrl.value);
  }
});
</script>

<style scoped>
/* 全屏覆盖层 */
.file-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 加载状态 */
.preview-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-text {
  color: white;
  font-size: 16px;
  margin: 0;
}

/* 错误状态 */
.preview-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.error-container {
  background: white;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  color: #ff4d4f;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.error-subtitle {
  color: #666;
  margin: 0 0 24px 0;
  font-size: 14px;
}

.error-close-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.error-close-btn:hover {
  background: #40a9ff;
}

/* 预览容器 */
.preview-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片预览 - 全屏纯净模式 */
.image-preview-fullscreen {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.fullscreen-image {
  max-width: 95vw;
  max-height: 95vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  animation: imageZoomIn 0.3s ease-out;
}

@keyframes imageZoomIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 圆形半透明关闭按钮 */
.close-btn-circle {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.close-btn-circle:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* 图片信息显示 */
.image-info {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  padding: 12px 24px;
  border-radius: 25px;
  z-index: 10;
}

.image-filename {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* PDF预览 - 全屏优雅模式 */
.pdf-preview-fullscreen {
  width: 95vw;
  height: 95vh;
  max-width: 1400px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  margin: auto;
}

.pdf-header {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pdf-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 60px);
}

.pdf-content {
  flex: 1;
  position: relative;
}

.pdf-iframe-fullscreen {
  width: 100%;
  height: 100%;
  border: none;
}

/* 现代化关闭按钮 */
.close-btn-modern {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #f5f5f5;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn-modern:hover {
  background: #e6f7ff;
  color: #1890ff;
}

/* 视频预览 - 现代化播放器 */
.video-preview-fullscreen {
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  margin: auto;
}

.media-header {
  background: rgba(0, 0, 0, 0.8);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.media-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 60px);
}

.video-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.fullscreen-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 音频预览 - 优雅的音频播放器 */
.audio-preview-fullscreen {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.audio-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90vw;
  margin: auto;
}

.audio-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.audio-close:hover {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

.audio-visual {
  margin-bottom: 40px;
}

.audio-icon {
  font-size: 64px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.audio-title {
  color: white;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.audio-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 14px;
}

.modern-audio-player {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Office文档预览 - 全屏优雅模式 */
.office-preview-fullscreen {
  width: 95vw;
  height: 95vh;
  max-width: 1400px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  margin: auto;
}

.office-header {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.office-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 120px);
}

.office-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Office缩放控件样式 */
.office-zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.zoom-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  color: #495057;
}

.zoom-btn:hover:not(:disabled) {
  background: #e9ecef;
  color: #1890ff;
}

.zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-display {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  min-width: 45px;
  text-align: center;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.download-btn:hover {
  background: #40a9ff;
}

.office-content {
  flex: 1;
  position: relative;
  background: #ffffff;
  overflow: auto;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
}

.office-content.has-zoom {
  padding: 40px;
}

.office-container-wrapper {
  transition: transform 0.3s ease;
  transform-origin: top left;
  width: 100%;
  max-width: 1200px;
  min-width: 800px;
}

/* vue-office 组件样式 */
.vue-office-container {
  width: 100%;
  height: auto;
  min-height: 600px;
  border: none;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 确保Office组件内容有足够的最小尺寸 */
.vue-office-container :deep(.vue-office-docx),
.vue-office-container :deep(.vue-office-excel) {
  min-height: 600px;
  min-width: 800px;
}

/* Office加载状态 */
.office-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content p {
  margin-top: 16px;
  font-size: 14px;
}

.unsupported-office {
  text-align: center;
  color: #666;
  max-width: 400px;
}

.office-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.unsupported-office h3 {
  color: #333;
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 500;
}

.unsupported-office p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.fallback-text {
  color: #999 !important;
  font-style: italic;
  margin-bottom: 20px !important;
}

.office-iframe-fallback {
  width: 100%;
  height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.office-iframe-fullscreen {
  width: 100%;
  height: 100%;
  border: none;
}

/* 不支持的文件类型 */
.unsupported-preview-fullscreen {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.unsupported-container {
  background: white;
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90vw;
  margin: auto;
}

.unsupported-close {
  position: absolute;
  top: 20px;
  right: 20px;
}

.unsupported-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
}

.unsupported-title {
  color: #333;
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
}

.unsupported-subtitle {
  color: #666;
  margin: 0 0 32px 0;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.download-btn-large {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0 auto;
}

.download-btn-large:hover {
  background: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .close-btn-circle {
    top: 20px;
    right: 20px;
    width: 44px;
    height: 44px;
  }

  .image-info {
    bottom: 20px;
    padding: 10px 20px;
  }

  .pdf-preview-fullscreen,
  .office-preview-fullscreen {
    width: 98vw;
    height: 90vh;
  }

  .video-preview-fullscreen {
    width: 98vw;
    height: 70vh;
  }

  .audio-container,
  .unsupported-container {
    width: 95vw;
    padding: 40px 30px;
  }
}
</style>
