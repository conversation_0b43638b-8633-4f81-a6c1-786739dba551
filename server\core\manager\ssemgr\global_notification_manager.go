package ssemgr

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"server/global"
	"server/model/response"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// NotificationSSEConnection SSE连接信息
type NotificationSSEConnection struct {
	UserID       uint                              `json:"userId"`
	ConnectionID string                            `json:"connectionId"`
	Writer       gin.ResponseWriter                `json:"-"`
	Request      *http.Request                     `json:"-"`
	Context      context.Context                   `json:"-"`
	Cancel       context.CancelFunc                `json:"-"`
	LastPing     time.Time                         `json:"lastPing"`
	CreatedAt    time.Time                         `json:"createdAt"`
	RemoteAddr   string                            `json:"remoteAddr"`
	UserAgent    string                            `json:"userAgent"`
	Channel      chan response.NotificationSSEData `json:"-"`
}

// NotificationSSEManager SSE连接管理器
type NotificationSSEManager struct {
	connections map[uint]map[string]*NotificationSSEConnection // userID -> connectionID -> connection
	mutex       sync.RWMutex
	pingTicker  *time.Ticker
	stopChan    chan struct{}
}

// NewNotificationSSEManager 创建SSE管理器
func NewNotificationSSEManager() *NotificationSSEManager {
	manager := &NotificationSSEManager{
		connections: make(map[uint]map[string]*NotificationSSEConnection),
		pingTicker:  time.NewTicker(60 * time.Second), // 60秒心跳检测
		stopChan:    make(chan struct{}),
	}

	// 启动心跳检测
	go manager.startHeartbeat()

	return manager
}

// AddConnection 添加SSE连接
func (m *NotificationSSEManager) AddConnection(userID uint, w gin.ResponseWriter, r *http.Request) *NotificationSSEConnection {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 为用户创建连接映射（如果不存在）
	if m.connections[userID] == nil {
		m.connections[userID] = make(map[string]*NotificationSSEConnection)
	}

	// 生成唯一的连接ID
	connectionID := fmt.Sprintf("%s_%d", r.RemoteAddr, time.Now().UnixNano())

	// 记录现有连接数量
	existingCount := len(m.connections[userID])
	if existingCount > 0 {
		global.LOG.Info("用户已有SSE连接，添加新连接",
			zap.Uint("userID", userID),
			zap.Int("existingConnections", existingCount),
			zap.String("newConnectionID", connectionID))
	}

	// 创建新连接
	ctx, cancel := context.WithCancel(r.Context())
	conn := &NotificationSSEConnection{
		UserID:       userID,
		ConnectionID: connectionID,
		Writer:       w,
		Request:      r,
		Context:      ctx,
		Cancel:       cancel,
		LastPing:     time.Now(),
		CreatedAt:    time.Now(),
		RemoteAddr:   r.RemoteAddr,
		UserAgent:    r.UserAgent(),
		Channel:      make(chan response.NotificationSSEData, 100), // 缓冲100条消息
	}

	// 添加连接
	m.connections[userID][connectionID] = conn

	totalConnections := 0
	for _, userConns := range m.connections {
		totalConnections += len(userConns)
	}

	global.LOG.Info("新增SSE连接",
		zap.Uint("userID", userID),
		zap.String("connectionID", connectionID),
		zap.String("remoteAddr", conn.RemoteAddr),
		zap.Int("userConnections", len(m.connections[userID])),
		zap.Int("totalConnections", totalConnections))

	return conn
}

// RemoveConnection 移除用户的所有SSE连接
func (m *NotificationSSEManager) RemoveConnection(userID uint) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if userConns, exists := m.connections[userID]; exists {
		// 关闭用户的所有连接
		for connectionID, conn := range userConns {
			m.closeConnection(conn, userID, connectionID)
		}

		delete(m.connections, userID)

		totalConnections := 0
		for _, userConns := range m.connections {
			totalConnections += len(userConns)
		}

		global.LOG.Info("移除用户所有SSE连接",
			zap.Uint("userID", userID),
			zap.Int("totalConnections", totalConnections))
	}
}

// RemoveSpecificConnection 移除特定的SSE连接
func (m *NotificationSSEManager) RemoveSpecificConnection(userID uint, connectionID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if userConns, exists := m.connections[userID]; exists {
		if conn, connExists := userConns[connectionID]; connExists {
			m.closeConnection(conn, userID, connectionID)
			delete(userConns, connectionID)

			// 如果用户没有其他连接，删除用户条目
			if len(userConns) == 0 {
				delete(m.connections, userID)
			}

			totalConnections := 0
			for _, userConns := range m.connections {
				totalConnections += len(userConns)
			}

			global.LOG.Info("移除特定SSE连接",
				zap.Uint("userID", userID),
				zap.String("connectionID", connectionID),
				zap.Int("userConnections", len(userConns)),
				zap.Int("totalConnections", totalConnections))
		}
	}
}

// closeConnection 关闭单个连接的辅助方法
func (m *NotificationSSEManager) closeConnection(conn *NotificationSSEConnection, userID uint, connectionID string) {
	// 取消上下文
	conn.Cancel()

	// 安全关闭channel
	select {
	case <-conn.Channel:
		// channel已经关闭或为空
	default:
		// 尝试关闭channel
		defer func() {
			if r := recover(); r != nil {
				global.LOG.Warn("关闭SSE channel时发生panic",
					zap.Uint("userID", userID),
					zap.String("connectionID", connectionID),
					zap.Any("panic", r))
			}
		}()
		close(conn.Channel)
	}
}

// GetConnection 获取用户的第一个SSE连接（兼容性方法）
func (m *NotificationSSEManager) GetConnection(userID uint) (*NotificationSSEConnection, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	userConns, exists := m.connections[userID]
	if !exists || len(userConns) == 0 {
		return nil, false
	}

	// 返回第一个连接
	for _, conn := range userConns {
		return conn, true
	}
	return nil, false
}

// GetAllUserConnections 获取用户的所有SSE连接
func (m *NotificationSSEManager) GetAllUserConnections(userID uint) (map[string]*NotificationSSEConnection, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	userConns, exists := m.connections[userID]
	return userConns, exists
}

// SendToUser 发送消息给指定用户
func (m *NotificationSSEManager) SendToUser(userID uint, data response.NotificationSSEData) error {
	conn, exists := m.GetConnection(userID)
	if !exists {
		return fmt.Errorf("用户 %d 的SSE连接不存在", userID)
	}

	select {
	case conn.Channel <- data:
		global.LOG.Debug("SSE消息已发送到用户队列",
			zap.Uint("userID", userID),
			zap.String("type", data.Type),
			zap.String("action", data.Action))
		return nil
	case <-time.After(5 * time.Second):
		global.LOG.Warn("SSE消息发送超时，移除连接",
			zap.Uint("userID", userID))
		m.RemoveConnection(userID)
		return fmt.Errorf("发送消息超时")
	}
}

// SendToMultipleUsers 发送消息给多个用户
func (m *NotificationSSEManager) SendToMultipleUsers(userIDs []uint, data response.NotificationSSEData) {
	for _, userID := range userIDs {
		if err := m.SendToUser(userID, data); err != nil {
			global.LOG.Warn("发送SSE消息失败",
				zap.Uint("userID", userID),
				zap.Error(err))
		}
	}
}

// BroadcastToAll 广播消息给所有连接的用户
func (m *NotificationSSEManager) BroadcastToAll(data response.NotificationSSEData) {
	m.mutex.RLock()
	userIDs := make([]uint, 0, len(m.connections))
	for userID := range m.connections {
		userIDs = append(userIDs, userID)
	}
	m.mutex.RUnlock()

	m.SendToMultipleUsers(userIDs, data)
}

// HandleSSEConnection 处理SSE连接
func (m *NotificationSSEManager) HandleSSEConnection(conn *NotificationSSEConnection) {
	// 确保在函数退出时清理连接
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("SSE连接处理发生panic",
				zap.Uint("userID", conn.UserID),
				zap.Any("panic", r))
		}
		m.RemoveConnection(conn.UserID)
	}()

	// 响应头已在API层设置，这里只需要确保立即发送
	if flusher, ok := conn.Writer.(http.Flusher); ok {
		flusher.Flush()
	}

	// 延迟发送连接成功消息，确保连接已完全建立
	go func() {
		time.Sleep(100 * time.Millisecond) // 等待连接稳定
		if err := m.sendSSEMessage(conn, "connected", map[string]interface{}{
			"message":   "SSE连接已建立",
			"timestamp": time.Now(),
			"userID":    conn.UserID,
		}); err != nil {
			global.LOG.Warn("发送连接成功消息失败，连接可能已断开",
				zap.Uint("userID", conn.UserID),
				zap.Error(err))
		} else {
			global.LOG.Debug("连接成功消息发送完成", zap.Uint("userID", conn.UserID))
		}
	}()

	// 处理消息循环
	for {
		select {
		case <-conn.Context.Done():
			global.LOG.Info("SSE连接上下文已取消", zap.Uint("userID", conn.UserID))
			return

		case data, ok := <-conn.Channel:
			if !ok {
				global.LOG.Info("SSE连接通道已关闭", zap.Uint("userID", conn.UserID))
				return
			}

			// 发送通知数据
			if err := m.sendSSEMessage(conn, "notification", data); err != nil {
				global.LOG.Error("发送SSE消息失败",
					zap.Uint("userID", conn.UserID),
					zap.Error(err))
				return
			}

		case <-time.After(60 * time.Second):
			// 发送心跳
			global.LOG.Debug("发送SSE心跳", zap.Uint("userID", conn.UserID))

			// 检查连接是否仍然有效
			select {
			case <-conn.Context.Done():
				global.LOG.Debug("连接已关闭，停止发送心跳", zap.Uint("userID", conn.UserID))
				return
			default:
			}

			if err := m.sendSSEMessage(conn, "ping", map[string]interface{}{
				"timestamp": time.Now(),
				"message":   "heartbeat",
			}); err != nil {
				global.LOG.Warn("发送SSE心跳失败，连接可能已断开",
					zap.Uint("userID", conn.UserID),
					zap.Error(err))
				// 心跳失败说明连接有问题，应该退出
				return
			}
			conn.LastPing = time.Now()
			global.LOG.Debug("SSE心跳发送成功", zap.Uint("userID", conn.UserID))
		}
	}
}

// sendSSEMessage 发送SSE消息
func (m *NotificationSSEManager) sendSSEMessage(conn *NotificationSSEConnection, event string, data interface{}) error {
	// 检查连接是否仍然有效
	select {
	case <-conn.Context.Done():
		return fmt.Errorf("连接已关闭")
	default:
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 确保消息格式正确，避免chunked编码问题
	// 使用标准的SSE格式，确保每行都以\n结尾
	message := fmt.Sprintf("event: %s\ndata: %s\n\n", event, string(jsonData))

	// 直接写入，不使用goroutine避免竞争条件
	n, err := conn.Writer.Write([]byte(message))
	if err != nil {
		return fmt.Errorf("写入响应失败: %v", err)
	}

	// 确保数据完整写入
	if n != len(message) {
		return fmt.Errorf("数据写入不完整: 期望 %d 字节，实际写入 %d 字节", len(message), n)
	}

	// 立即刷新缓冲区，确保数据发送
	if flusher, ok := conn.Writer.(http.Flusher); ok {
		flusher.Flush()
	} else {
		return fmt.Errorf("Writer不支持Flush操作")
	}

	return nil
}

// startHeartbeat 启动心跳检测
func (m *NotificationSSEManager) startHeartbeat() {
	for {
		select {
		case <-m.pingTicker.C:
			m.checkConnections()
		case <-m.stopChan:
			return
		}
	}
}

// checkConnections 检查连接状态
func (m *NotificationSSEManager) checkConnections() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	timeoutDuration := 3 * time.Minute // 3分钟超时，给心跳足够的缓冲时间

	var usersToRemove []uint
	var connectionsToRemove []struct {
		userID       uint
		connectionID string
	}

	for userID, userConns := range m.connections {
		for connectionID, conn := range userConns {
			if now.Sub(conn.LastPing) > timeoutDuration {
				global.LOG.Info("SSE连接超时，移除连接",
					zap.Uint("userID", userID),
					zap.String("connectionID", connectionID),
					zap.Duration("timeout", now.Sub(conn.LastPing)))

				connectionsToRemove = append(connectionsToRemove, struct {
					userID       uint
					connectionID string
				}{userID, connectionID})
			}
		}
	}

	// 移除超时的连接
	for _, item := range connectionsToRemove {
		if userConns, exists := m.connections[item.userID]; exists {
			if conn, connExists := userConns[item.connectionID]; connExists {
				m.closeConnection(conn, item.userID, item.connectionID)
				delete(userConns, item.connectionID)

				// 如果用户没有其他连接，标记为删除
				if len(userConns) == 0 {
					usersToRemove = append(usersToRemove, item.userID)
				}
			}
		}
	}

	// 删除没有连接的用户
	for _, userID := range usersToRemove {
		delete(m.connections, userID)
	}
}

// GetStats 获取连接统计信息
func (m *NotificationSSEManager) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	totalConnections := 0
	for _, userConns := range m.connections {
		totalConnections += len(userConns)
	}

	return map[string]interface{}{
		"totalUsers":       len(m.connections),
		"totalConnections": totalConnections,
		"connections":      m.getAllConnectionInfo(),
	}
}

// getAllConnectionInfo 获取所有连接信息
func (m *NotificationSSEManager) getAllConnectionInfo() []map[string]interface{} {
	var connections []map[string]interface{}

	for userID, userConns := range m.connections {
		for connectionID, conn := range userConns {
			connections = append(connections, map[string]interface{}{
				"userID":       userID,
				"connectionID": connectionID,
				"remoteAddr":   conn.RemoteAddr,
				"userAgent":    conn.UserAgent,
				"createdAt":    conn.CreatedAt,
				"lastPing":     conn.LastPing,
				"duration":     time.Since(conn.CreatedAt).String(),
			})
		}
	}

	return connections
}

// Stop 停止SSE管理器
func (m *NotificationSSEManager) Stop() {
	close(m.stopChan)
	m.pingTicker.Stop()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭所有连接
	for userID, userConns := range m.connections {
		for connectionID, conn := range userConns {
			m.closeConnection(conn, userID, connectionID)
			global.LOG.Info("关闭SSE连接",
				zap.Uint("userID", userID),
				zap.String("connectionID", connectionID))
		}
	}

	m.connections = make(map[uint]map[string]*NotificationSSEConnection)
}

// 全局SSE管理器实例
var GlobalNotificationSSEManager *NotificationSSEManager

// InitNotificationSSEManager 初始化全局SSE管理器
func InitNotificationSSEManager() {
	GlobalNotificationSSEManager = NewNotificationSSEManager()
	global.LOG.Info("SSE管理器初始化完成")
}
