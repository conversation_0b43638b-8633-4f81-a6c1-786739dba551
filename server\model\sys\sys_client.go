package sys

import (
	"server/global"
	"time"
)

// Client 模型定义
type Client struct {
	global.DBModel
	ListenerID   uint      `json:"listenerId" gorm:"column:listener_id;comment:关联的监听器ID"`
	ListenerType string    `json:"listenerType" gorm:"column:listener_type;comment:监听器类型"`
	OS           string    `json:"os" gorm:"column:os;comment:客户端系统"`
	SessionType  string    `json:"sessionType" gorm:"column:session_type;comment:客户端交互性类型"`
	RemoteAddr   string    `json:"remoteAddr" gorm:"column:remote_addr;comment:客户端远程地址"`
	ConnectedAt  time.Time `json:"connectedAt" gorm:"column:connected_at;comment:连接时间"`
	LastActiveAt time.Time `json:"lastActiveAt" gorm:"column:last_active_at;comment:最后活动时间"`
	Status       int       `json:"status" gorm:"column:status;default:1;comment:状态 1在线 0离线"`
	Remark       string    `json:"remark" gorm:"column:remark;comment:备注"`
	TimeOutCount int       `json:"timeOutCount" gorm:"column:timeOut_count;comment:心跳超时次数"`
	// 新增字段 - 从METADATA中获取的详细信息
	Username     string `json:"username" gorm:"column:username;comment:用户名"`
	Hostname     string `json:"hostname" gorm:"column:hostname;comment:主机名"`
	ProcessName  string `json:"processName" gorm:"column:process_name;comment:进程名"`
	Architecture string `json:"architecture" gorm:"column:architecture;comment:系统架构"`
	ClientID     string `json:"client_id" gorm:"column:client_id;comment:客户端唯一标识"`
	// 网络信息字段
	LocalIP  string `json:"local_ip" gorm:"column:local_ip;comment:内网地址"`
	PublicIP string `json:"public_ip" gorm:"column:public_ip;comment:公网地址"`

	// 心跳数据字段
	CPUUsage    float64 `json:"cpu_usage" gorm:"column:cpu_usage;comment:CPU使用率"`
	MemoryUsage float64 `json:"memory_usage" gorm:"column:memory_usage;comment:内存使用率"`
	DiskUsage   float64 `json:"disk_usage" gorm:"column:disk_usage;comment:磁盘使用率"`
	Uptime      int64   `json:"uptime" gorm:"column:uptime;comment:运行时间(秒)"`
	LoadAvg     float64 `json:"load_avg" gorm:"column:load_avg;comment:系统负载"`
	Latency     int64   `json:"latency" gorm:"column:latency;comment:网络延迟(毫秒)"`
	PacketLoss  float64 `json:"packet_loss" gorm:"column:packet_loss;comment:丢包率"`
	Bandwidth   int64   `json:"bandwidth" gorm:"column:bandwidth;comment:带宽(Mbps)"`

	// 🌐 网络拓扑图相关字段
	OSVersion     string  `json:"os_version" gorm:"column:os_version;comment:操作系统版本"`
	OSBuild       string  `json:"os_build" gorm:"column:os_build;comment:系统构建版本"`
	KernelVersion string  `json:"kernel_version" gorm:"column:kernel_version;comment:内核版本"`
	MachineID     string  `json:"machine_id" gorm:"column:machine_id;comment:机器唯一标识"`
	Domain        string  `json:"domain" gorm:"column:domain;comment:域名"`
	Workgroup     string  `json:"workgroup" gorm:"column:workgroup;comment:工作组"`
	NetworkType   string  `json:"network_type" gorm:"column:network_type;comment:网络类型(LAN/WAN/VPN)"`
	GeoLocation   string  `json:"geo_location" gorm:"column:geo_location;comment:地理位置"`
	ISP           string  `json:"isp" gorm:"column:isp;comment:网络服务提供商"`
	// 详细地理位置信息
	Country       string  `json:"country" gorm:"column:country;comment:国家"`
	Province      string  `json:"province" gorm:"column:province;comment:省份/州"`
	City          string  `json:"city" gorm:"column:city;comment:城市"`
	ASN           string  `json:"asn" gorm:"column:asn;comment:自治系统号"`
	GeoSource     string  `json:"geo_source" gorm:"column:geo_source;comment:地理位置数据来源"`
	// 拓扑图位置信息（用于保存节点在图中的位置）
	TopologyX     float64 `json:"topology_x" gorm:"column:topology_x;comment:拓扑图X坐标"`
	TopologyY     float64 `json:"topology_y" gorm:"column:topology_y;comment:拓扑图Y坐标"`
}

// TableName 设置表名
func (Client) TableName() string {
	return "sys_client"
}
