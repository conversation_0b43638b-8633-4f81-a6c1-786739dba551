package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type NetworkRoute struct{}

func (r *NetworkRoute) InitNetworkRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	networkRouter := Router.Group("/network").Use(middleware.OperationRecord())
	{
		networkRouter.GET("/:clientId/stats", networkApi.GetNetworkStats)                                        // 获取网络统计信息
		networkRouter.GET("/:clientId/interfaces", networkApi.GetNetworkInterfaces)                              // 获取网络接口信息
		networkRouter.GET("/:clientId/interfaces/progress-stream", networkApi.GetNetworkInterfaceProgressStream) // 获取网络接口进度流 (SSE)
		networkRouter.GET("/:clientId/connections", networkApi.GetNetworkConnections)                            // 获取网络连接信息
		networkRouter.POST("/:clientId/close-connection", networkApi.CloseConnection)                            // 关闭网络连接
	}
	return networkRouter
}
