package basic

// DiskInfo 磁盘信息
type DiskInfo struct {
	MountPoint   string  `json:"mount_point"`   // 挂载点/驱动器盘符
	FileSystem   string  `json:"file_system"`   // 文件系统类型
	Device       string  `json:"device"`        // 设备名称
	TotalSize    int64   `json:"total_size"`    // 总大小（字节）
	UsedSize     int64   `json:"used_size"`     // 已使用大小（字节）
	AvailSize    int64   `json:"avail_size"`    // 可用大小（字节）
	UsagePercent float64 `json:"usage_percent"` // 使用百分比
	IsReadOnly   bool    `json:"is_read_only"`  // 是否只读
	Label        string  `json:"label"`         // 磁盘标签
	Icon         string  `json:"icon"`          // 图标类型
}
