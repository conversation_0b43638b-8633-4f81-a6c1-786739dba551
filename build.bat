@echo off
echo 开始构建项目...

:: 设置路径变量
set PROJECT_ROOT=%cd%
set FRONTEND_DIR=%PROJECT_ROOT%\fronted
set SERVER_DIR=%PROJECT_ROOT%\server
set DIST_DIR=%FRONTEND_DIR%\dist
set SERVER_DIST_DIR=%SERVER_DIR%\core\static\dist

:: 清理旧的构建文件
echo 清理旧的构建文件...
if exist "%DIST_DIR%" rd /s /q "%DIST_DIR%"
if exist "%SERVER_DIST_DIR%" rd /s /q "%SERVER_DIST_DIR%"

:: 构建前端
echo 构建前端...
cd "%FRONTEND_DIR%"
call npm install
call npm run build

:: 确保服务器的静态目录存在
echo 准备服务器静态文件目录...
if not exist "%SERVER_DIST_DIR%" mkdir "%SERVER_DIST_DIR%"

:: 复制前端构建到服务器的静态目录
echo 复制前端构建到服务器的静态目录...
xcopy "%DIST_DIR%\*" "%SERVER_DIST_DIR%\" /E /I /Y

:: 构建服务器
echo 构建服务器...
cd "%SERVER_DIR%"
go build -o c2server.exe main.go

:: 完成
echo 构建完成！
echo 可执行文件位于: %SERVER_DIR%\c2server.exe

pause