package fs

// FileInfoRequest 文件信息请求
type FileInfoRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	Path   string `json:"path"`    // 文件路径
	IsFull bool   `json:"is_full"` // 是否获取完整信息
}

// FileCopyRequest 文件复制请求
type FileCopyRequest struct {
	TaskID          uint64 `json:"task_id"`          // 任务ID
	Source          string `json:"source"`           // 源文件路径
	Destination     string `json:"destination"`      // 目标文件路径
	Force           bool   `json:"force"`            // 是否强制覆盖
	PreserveAttrs   bool   `json:"preserve_attrs"`   // 是否保留文件属性
	FollowSymlinks  bool   `json:"follow_symlinks"`  // 是否跟随符号链接
	VerifyIntegrity bool   `json:"verify_integrity"` // 是否验证完整性
}

type FileUploadRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Destination  string `json:"destination"`   // 目标文件路径
	StartChunk   int64  `json:"start_chunk"`   // 起始分块编号（-1表示取消信令）
	CurrentChunk int64  `json:"current_chunk"` // 当前分块编号（从0开始）
	TotalChunk   int64  `json:"total_chunk"`   // 总分块数
	ChunkContent []byte `json:"chunk_content"` // 当前分块内容
	Force        bool   `json:"force"`         // 是否强制覆盖
	FileSize     int64  `json:"file_size"`     // 文件总大小
	FileHash     string `json:"file_hash"`     // 文件SHA256哈希（用于完整性验证）
}

// FileDownloadRequest 文件下载请求
type FileDownloadRequest struct {
	TaskID     uint64 `json:"task_id"`
	Path       string `json:"path"`        // 文件路径
	StartChunk int64  `json:"start_chunk"` // 起始分块编号
	ChunkSize  int64  `json:"chunk_size"`  // 分块大小（字节）
	RangeStart int64  `json:"range_start"` // 范围下载起始位置
	RangeEnd   int64  `json:"range_end"`   // 范围下载结束位置
}

// FileDeleteRequest 文件删除请求
type FileDeleteRequest struct {
	TaskID     uint64 `json:"task_id"`     // 任务ID
	Path       string `json:"path"`        // 文件路径
	Secure     bool   `json:"secure"`      // 是否安全删除（多次覆写）
	Recursive  bool   `json:"recursive"`   // 是否递归删除（如果是目录）
	ForceWrite bool   `json:"force_write"` // 是否强制移除只读属性
}

// FileMoveRequest 文件移动请求
type FileMoveRequest struct {
	TaskID          uint64 `json:"task_id"`          // 任务ID
	Source          string `json:"source"`           // 源文件路径
	Destination     string `json:"destination"`      // 目标文件路径
	Force           bool   `json:"force"`            // 是否强制覆盖
	CreateDirs      bool   `json:"create_dirs"`      // 是否创建目标目录
	PreserveAttrs   bool   `json:"preserve_attrs"`   // 是否保留文件属性
	VerifyIntegrity bool   `json:"verify_integrity"` // 是否验证完整性
}

// FileReadRequest 文件内容读取请求
type FileReadRequest struct {
	TaskID   uint64 `json:"task_id"`  // 任务ID
	Path     string `json:"path"`     // 文件路径
	MaxSize  int64  `json:"max_size"` // 最大读取大小（字节）
	Offset   int64  `json:"offset"`   // 读取偏移量
	Length   int64  `json:"length"`   // 读取长度（0表示读取到文件末尾）
	Encoding string `json:"encoding"` // 文件编码（如utf-8, gbk等）
}

// FileWriteRequest 文件内容写入请求
type FileWriteRequest struct {
	TaskID   uint64 `json:"task_id"`  // 任务ID
	Path     string `json:"path"`     // 文件路径
	Content  string `json:"content"`  // 文件内容
	Encoding string `json:"encoding"` // 文件编码（如utf-8, gbk等）
	Backup   bool   `json:"backup"`   // 是否创建备份
	Force    bool   `json:"force"`    // 是否强制覆盖
}

// FileCreateRequest 文件创建请求
type FileCreateRequest struct {
	TaskID   uint64 `json:"task_id"`  // 任务ID
	Path     string `json:"path"`     // 文件路径
	Content  string `json:"content"`  // 初始文件内容
	Encoding string `json:"encoding"` // 文件编码（如utf-8, gbk等）
	Force    bool   `json:"force"`    // 是否强制覆盖已存在的文件
	Mode     uint32 `json:"mode"`     // 文件权限模式
}

// TransferServerFileToClientRequest 传输服务器文件到客户端请求
type TransferServerFileToClientRequest struct {
	ServerFilePath  string `json:"server_file_path"` // 服务器文件路径（相对于上传目录）
	DestinationPath string `json:"destination_path"` // 客户端目标路径
	Force           bool   `json:"force"`            // 是否强制覆盖
	ChunkSize       int64  `json:"chunk_size"`       // 分块大小（字节）
}
