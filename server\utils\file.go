package utils

import (
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"server/global"
	"strings"

	"go.uber.org/zap"
)

func PathExists(path string) (bool, error) {
	fi, err := os.Stat(path)
	if err == nil {
		if fi.IsDir() {
			return true, nil
		}
		return false, errors.New("存在同名文件")
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func NormalizePath(path string) string {
	return filepath.ToSlash(filepath.Clean(path))
}

func CreateDir(dirs ...string) (err error) {
	for _, v := range dirs {
		exist, err := PathExists(v)
		if err != nil {
			return err
		}
		if !exist {
			global.LOG.Debug("create directory" + v)
			if err := os.MkdirAll(v, os.ModePerm); err != nil {
				global.LOG.Error("create directory"+v, zap.Any(" error:", err))
				return err
			}
		}
	}
	return err
}

func FileMove(src string, dst string) (err error) {
	if dst == "" {
		return nil
	}
	src, err = filepath.Abs(src)
	if err != nil {
		return err
	}
	dst, err = filepath.Abs(dst)
	if err != nil {
		return err
	}
	revoke := false
	dir := filepath.Dir(dst)
Redirect:
	_, err = os.Stat(dir)
	if err != nil {
		err = os.MkdirAll(dir, 0o755)
		if err != nil {
			return err
		}
		if !revoke {
			revoke = true
			goto Redirect
		}
	}
	return os.Rename(src, dst)
}

func DeLFile(filePath string) error {
	return os.RemoveAll(filePath)
}

func TrimSpace(target interface{}) {
	t := reflect.TypeOf(target)
	if t.Kind() != reflect.Ptr {
		return
	}
	t = t.Elem()
	v := reflect.ValueOf(target).Elem()
	for i := 0; i < t.NumField(); i++ {
		switch v.Field(i).Kind() {
		case reflect.String:
			v.Field(i).SetString(strings.TrimSpace(v.Field(i).String()))
		}
	}
}

// FileExist 判断文件是否存在
func FileExist(path string) bool {
	fi, err := os.Lstat(path)
	if err == nil {
		return !fi.IsDir()
	}
	return !os.IsNotExist(err)
}

var illegalFilenameChars = regexp.MustCompile(`[\\/:*?"<>|[:cntrl:]]`)

// 定义一个正则表达式，用于匹配Windows文件名末尾不允许的点和空格。
var trailingWindowsChars = regexp.MustCompile(`[ .]+$`)

const (
	// 定义文件名最大长度，255是一个比较安全的值，大多数文件系统都支持。
	maxFilenameLength = 255
)

func SanitizeFilename(filename string, replacement string) string {
	if filename == "" {
		return "unnamed_file"
	}

	// 1. 替换所有非法字符和控制字符
	sanitized := illegalFilenameChars.ReplaceAllString(filename, replacement)

	// 2. 移除Windows文件名末尾的点和空格
	sanitized = trailingWindowsChars.ReplaceAllString(sanitized, "")

	// 3. 检查文件名是否过长。我们需要考虑文件扩展名。
	ext := filepath.Ext(sanitized)
	base := strings.TrimSuffix(sanitized, ext)

	// 如果基础部分过长，则进行截断
	if len(base) > maxFilenameLength-len(ext) {
		base = base[:maxFilenameLength-len(ext)]
	}

	sanitized = base + ext

	// 4. 如果经过所有清理后，文件名变为空字符串（例如，输入是 "/*?<>\"），
	//    则返回一个默认的文件名。
	if sanitized == "" || sanitized == ext { // 也处理只剩扩展名的情况
		return "sanitized_file" + ext
	}

	return sanitized
}

func WriteFile(filePath string, data []byte) error {
	// 1. 获取文件所在的目录
	dir := filepath.Dir(filePath)

	// 2. 检查目录是否存在，如果不存在则创建它
	// os.MkdirAll 可以安全地处理目录已存在的情况，不会返回错误。
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// 3. 原子写入：创建一个临时文件在同一个目录下
	// 使用 "tempfile-*" 模式，ioutil.TempFile会自动生成一个唯一的文件名。
	// 将临时文件创建在目标目录，可以确保重命名操作是原子性的（在同一文件系统下）。
	tempFile, err := ioutil.TempFile(dir, "tempfile-*.tmp")
	if err != nil {
		return fmt.Errorf("failed to create temporary file: %w", err)
	}

	// 确保临时文件在出错时被关闭和删除
	defer func() {
		tempFile.Close()
		// 如果重命名失败，或者在重命名之前发生错误，则清理临时文件
		if err != nil {
			os.Remove(tempFile.Name())
		}
	}()

	// 4. 将数据写入临时文件
	_, err = tempFile.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write data to temporary file: %w", err)
	}

	// 5. 关闭临时文件句柄，以便后续可以重命名它
	if err = tempFile.Close(); err != nil {
		return fmt.Errorf("failed to close temporary file: %w", err)
	}

	// 6. 将临时文件重命名为最终的目标文件名
	// 这是一个原子操作，能保证最终文件要么是完整的旧文件，要么是完整的新文件。
	err = os.Rename(tempFile.Name(), filePath)
	if err != nil {
		return fmt.Errorf("failed to rename temporary file to %s: %w", filePath, err)
	}

	// 写入成功
	return nil
}
