<template>
  <div class="screenshot-manager">
    <div class="screenshot-header">
      <h3>屏幕截屏</h3>
      <div class="screenshot-actions">
        <a-tooltip 
          :title="!clientInfo ? '客户端信息加载中...' : clientInfo.status !== 1 ? '客户端离线，无法截屏' : ''"
        >
          <a-button 
            type="primary" 
            @click="takeScreenshot"
            :loading="screenshotLoading"
            :disabled="!clientInfo || clientInfo.status !== 1"
          >
            <template #icon>
              <CameraOutlined />
            </template>
            立即截屏
          </a-button>
        </a-tooltip>
        
        <!-- 屏幕流控制 -->
        <a-tooltip title="开始/停止实时屏幕流">
          <a-button 
            :type="isStreaming ? 'danger' : 'default'"
            @click="toggleScreenStream"
            :loading="streamLoading"
            :disabled="!clientInfo || clientInfo.status !== 1"
          >
            <template #icon>
              <PlayCircleOutlined v-if="!isStreaming" />
              <PauseCircleOutlined v-else />
            </template>
            {{ isStreaming ? '停止流' : '开始流' }}
          </a-button>
        </a-tooltip>
        
        <!-- 定时截图控制 -->
        <a-tooltip title="开始/停止定时截图">
          <a-button 
            :type="isTimerRunning ? 'danger' : 'default'"
            @click="toggleTimerScreenshot"
            :disabled="!clientInfo || clientInfo.status !== 1"
          >
            <template #icon>
              <ClockCircleOutlined />
            </template>
            {{ isTimerRunning ? '停止定时' : '定时截图' }}
          </a-button>
        </a-tooltip>
        
        <a-button 
          @click="refreshScreenshots"
          :loading="loading"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新列表
        </a-button>
        <a-button 
          @click="clearScreenshots"
          danger
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          清空截屏
        </a-button>
      </div>
    </div>

    <div class="screenshot-content">
      <!-- 截屏设置 -->
      <div class="screenshot-settings">
        <a-card title="截屏设置" size="small">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="截图类型">
                  <a-select v-model:value="screenshotType" style="width: 100%" @change="onScreenshotTypeChange">
                    <a-select-option :value="0">全屏截图</a-select-option>
                    <a-select-option :value="1">活动窗口</a-select-option>
                    <a-select-option :value="2">区域截图</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="显示器">
                  <a-select 
                    v-model:value="screenIndex" 
                    style="width: 100%"
                    :loading="monitorsLoading"
                    :disabled="screenshotType === 1"
                    @dropdown-visible-change="(open) => open && getMonitorList()"
                  >
                    <a-select-option 
                      v-for="(monitor, index) in monitors" 
                      :key="index" 
                      :value="index"
                    >
                      {{ monitor.name || `显示器 ${index + 1}` }} ({{ monitor.width }}x{{ monitor.height }})
                    </a-select-option>
                    <a-select-option v-if="monitors.length === 0" :value="0">
                      主屏幕
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="质量">
                  <a-tooltip title="质量越高图片越清晰，但文件也越大。建议：截图用高质量，流媒体用中等质量">
                    <a-select v-model:value="quality" style="width: 100%">
                      <a-select-option :value="100">超高质量 (2K)</a-select-option>
                      <a-select-option :value="95">高质量 (2K)</a-select-option>
                      <a-select-option :value="80">中质量 (1080p)</a-select-option>
                      <a-select-option :value="60">低质量 (720p)</a-select-option>
                      <a-select-option :value="30">最低质量 (720p)</a-select-option>
                    </a-select>
                  </a-tooltip>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="格式">
                  <a-select v-model:value="format" style="width: 100%">
                    <a-select-option value="png">PNG (无损)</a-select-option>
                    <a-select-option value="jpg">JPG (压缩)</a-select-option>
                    <a-select-option value="bmp">BMP (原始)</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="自动刷新">
                  <a-checkbox v-model:checked="autoRefresh">
                    30秒刷新
                  </a-checkbox>
                </a-form-item>
              </a-col>
            </a-row>
            
            <!-- 区域截图设置 -->
            <div v-if="screenshotType === 2" class="region-settings">
              <a-divider>区域设置</a-divider>
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-form-item label="X坐标">
                    <a-input-number v-model:value="regionX" :min="0" style="width: 100%" placeholder="起始X坐标" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="Y坐标">
                    <a-input-number v-model:value="regionY" :min="0" style="width: 100%" placeholder="起始Y坐标" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="宽度">
                    <a-input-number v-model:value="regionWidth" :min="1" style="width: 100%" placeholder="区域宽度" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="高度">
                    <a-input-number v-model:value="regionHeight" :min="1" style="width: 100%" placeholder="区域高度" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-alert 
                message="区域截图提示" 
                description="请输入要截取的屏幕区域坐标和尺寸。坐标(0,0)为屏幕左上角。" 
                type="info" 
                show-icon 
                style="margin-top: 8px;"
              />
            </div>
            
            <!-- 高级设置 -->
            <a-divider>
              <span @click="showAdvancedSettings = !showAdvancedSettings" style="cursor: pointer; user-select: none;">
                高级设置 
                <DownOutlined v-if="!showAdvancedSettings" />
                <UpOutlined v-else />
              </span>
            </a-divider>
            <a-row v-show="showAdvancedSettings" :gutter="16">
              <!-- 屏幕流设置 -->
              <a-col :span="8">
                <a-card title="屏幕流设置" size="small">
                  <a-form-item label="帧率 (FPS)">
                    <a-slider v-model:value="streamFps" :min="1" :max="240" :step="1" />
                    <span style="font-size: 12px; color: #666;">{{ streamFps }} FPS</span>
                  </a-form-item>
                  <a-form-item label="差异检测">
                    <a-checkbox v-model:checked="enableDiffDetection">
                      启用差异检测
                    </a-checkbox>
                  </a-form-item>
                  <a-form-item v-if="enableDiffDetection" label="差异阈值">
                    <a-slider v-model:value="diffThreshold" :min="1" :max="50" :step="1" />
                    <span style="font-size: 12px; color: #666;">{{ diffThreshold }}%</span>
                  </a-form-item>
                </a-card>
              </a-col>
              
              <!-- 定时截图设置 -->
              <a-col :span="8">
                <a-card title="定时截图设置" size="small">
                  <a-form-item label="间隔时间">
                    <a-select v-model:value="timerInterval" style="width: 100%">
                      <a-select-option :value="5">5秒</a-select-option>
                      <a-select-option :value="10">10秒</a-select-option>
                      <a-select-option :value="30">30秒</a-select-option>
                      <a-select-option :value="60">1分钟</a-select-option>
                      <a-select-option :value="300">5分钟</a-select-option>
                      <a-select-option :value="600">10分钟</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="最大次数">
                    <a-input-number v-model:value="maxTimerCount" :min="0" :max="1000" style="width: 100%" placeholder="0=无限制" />
                  </a-form-item>
                  <a-form-item label="智能触发">
                    <a-checkbox v-model:checked="enableSmartTrigger">
                      活动检测触发
                    </a-checkbox>
                  </a-form-item>
                </a-card>
              </a-col>
              
              <!-- 性能控制设置 -->
              <a-col :span="8">
                <a-card title="性能控制" size="small">
                  <a-form-item label="CPU限制">
                    <a-slider v-model:value="cpuLimit" :min="10" :max="100" :step="5" />
                    <span style="font-size: 12px; color: #666;">{{ cpuLimit }}%</span>
                  </a-form-item>
                  <a-form-item label="带宽限制">
                    <a-select v-model:value="bandwidthLimit" style="width: 100%">
                      <a-select-option :value="0">无限制</a-select-option>
                      <a-select-option :value="100">100 KB/s</a-select-option>
                      <a-select-option :value="500">500 KB/s</a-select-option>
                      <a-select-option :value="1024">1 MB/s</a-select-option>
                      <a-select-option :value="5120">5 MB/s</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="内存优化">
                    <a-checkbox v-model:checked="enableMemoryOptimization">
                      启用内存优化
                    </a-checkbox>
                  </a-form-item>
                </a-card>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- 屏幕流显示区域 -->
      <div v-if="isStreaming" class="stream-display">
        <a-card size="small">
          <template #title>
            <div class="stream-title">
              <span>实时屏幕流</span>
              <div class="stream-controls">
                <a-button-group size="small">
                  <a-button @click="zoomOut" :disabled="zoomLevel <= 0.2">
                    <template #icon><MinusOutlined /></template>
                  </a-button>
                  <a-button @click="resetZoom">
                    {{ Math.round(zoomLevel * 100) }}%
                  </a-button>
                  <a-button @click="zoomIn" :disabled="zoomLevel >= 3">
                    <template #icon><PlusOutlined /></template>
                  </a-button>
                </a-button-group>
                <a-button size="small" @click="toggleFullscreen" style="margin-left: 8px;">
                  <template #icon><ExpandOutlined /></template>
                  全屏
                </a-button>
              </div>
            </div>
          </template>
          <div class="stream-container" ref="streamContainer">
            <canvas
              ref="streamCanvas"
              class="stream-canvas"
              :style="{
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'top left',
                cursor: isDragging ? 'grabbing' : 'grab'
              }"
              @mousedown="startDrag"
              @mousemove="onDrag"
              @mouseup="endDrag"
              @mouseleave="endDrag"
              @wheel="onWheel"
            ></canvas>
            <div v-if="!streamData" class="stream-loading">
              <a-spin size="large" />
              <p>正在等待屏幕流数据...</p>
            </div>
          </div>
          <div class="stream-info">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="帧率" :value="streamFps" suffix="FPS" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="差异检测" :value="enableDiffDetection ? '启用' : '禁用'" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="CPU限制" :value="cpuLimit" suffix="%" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="带宽限制" :value="bandwidthLimit === 0 ? '无限制' : `${bandwidthLimit} KB/s`" />
              </a-col>
            </a-row>
          </div>
        </a-card>
      </div>

      <!-- 截屏列表 -->
      <div class="screenshot-list">
        <a-spin :spinning="loading">
          <div v-if="screenshots.length === 0" class="empty-state">
            <a-empty description="暂无截屏">
              <template #image>
                <CameraOutlined style="font-size: 48px; color: #ccc;" />
              </template>
            </a-empty>
          </div>
          
          <div v-else class="screenshot-grid">
            <div 
              v-for="screenshot in screenshots" 
              :key="screenshot.id"
              class="screenshot-item"
            >
              <div class="screenshot-image">
                <img 
                  :src="getScreenshotUrl(screenshot)" 
                  :alt="screenshot.filename"
                  @click="previewScreenshot(screenshot)"
                />
                <div class="screenshot-overlay">
                  <a-button-group>
                    <a-button 
                      type="primary" 
                      size="small"
                      @click="previewScreenshot(screenshot)"
                    >
                      <template #icon>
                        <EyeOutlined />
                      </template>
                    </a-button>
                    <a-button 
                      size="small"
                      @click="downloadScreenshot(screenshot)"
                    >
                      <template #icon>
                        <DownloadOutlined />
                      </template>
                    </a-button>
                    <a-button 
                      danger 
                      size="small"
                      @click="deleteScreenshot(screenshot)"
                    >
                      <template #icon>
                        <DeleteOutlined />
                      </template>
                    </a-button>
                  </a-button-group>
                </div>
              </div>
              <div class="screenshot-info">
                <div class="screenshot-name">{{ screenshot.filename }}</div>
                <div class="screenshot-time">{{ formatDate(screenshot.created_at) }}</div>
                <div class="screenshot-size">{{ formatFileSize(screenshot.size) }}</div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 截屏预览弹窗 -->
    <a-modal
      v-model:open="previewModalVisible"
      :title="currentScreenshot?.name"
      width="80%"
      :footer="null"
      centered
    >
      <div class="screenshot-preview">
        <img 
          v-if="currentScreenshot"
          :src="getScreenshotUrl(currentScreenshot)" 
          :alt="currentScreenshot.filename"
          style="width: 100%; height: auto;"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  CameraOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ClockCircleOutlined,
  DownOutlined,
  UpOutlined,
  MinusOutlined,
  PlusOutlined,
  ExpandOutlined
} from '@ant-design/icons-vue';
import { screenshotApi } from '@/api/screenshot';

// 接收属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  clientInfo: {
    type: Object,
    default: () => ({})
  }
});

// 状态变量
const loading = ref(false);
const screenshotLoading = ref(false);
const screenshots = ref([]);
const monitors = ref([]);
const monitorsLoading = ref(false);

// 截屏设置
const screenshotType = ref(0); // 0=全屏, 1=活动窗口, 2=区域截图
const screenIndex = ref(0);
const quality = ref(95); // 提高默认质量到95
const format = ref('png');
const autoRefresh = ref(false);

// 区域截图设置
const regionX = ref(0);
  const regionY = ref(0);
  const regionWidth = ref(800);
  const regionHeight = ref(600);
  
  // 屏幕流设置
  const streamFps = ref(30);
  const enableDiffDetection = ref(true);
  const diffThreshold = ref(10);
  const isStreaming = ref(false);
  const streamLoading = ref(false);
  const streamData = ref(null);
  const streamCanvas = ref(null);
  const streamContext = ref(null);
  const showAdvancedSettings = ref(false);

  // 缩放和拖拽设置
  const zoomLevel = ref(1);
  const isDragging = ref(false);
  const dragStart = ref({ x: 0, y: 0 });
  const streamContainer = ref(null);


  
  // 定时截图设置
  const timerInterval = ref(30);
  const maxTimerCount = ref(0);
  const enableSmartTrigger = ref(false);
  const isTimerRunning = ref(false);
  const timerCount = ref(0);
  const timerId = ref(null);

  // 定时截图状态轮询
  let timerStatusPollingTimer = null;
  
  // 性能控制设置
  const cpuLimit = ref(50);
  const bandwidthLimit = ref(1024);
  const enableMemoryOptimization = ref(true);

// 预览相关
const previewModalVisible = ref(false);
const currentScreenshot = ref(null);

// 自动刷新定时器
let autoRefreshTimer = null;

// 截图类型变化处理
const onScreenshotTypeChange = (type) => {
  // 当切换到活动窗口截图时，重置区域设置
  if (type === 1) {
    // 活动窗口截图不需要显示器选择
  } else if (type === 2) {
    // 区域截图时设置默认值
    if (regionWidth.value === 0) regionWidth.value = 800;
    if (regionHeight.value === 0) regionHeight.value = 600;
  }
};

// 获取显示器列表
const getMonitorList = async () => {
  if (!props.clientInfo || props.clientInfo.status !== 1) {
    return;
  }

  try {
    monitorsLoading.value = true;
    const response = await screenshotApi.getMonitorList(props.clientId);
    if (response.code === 200) {
      monitors.value = response.data?.monitors || [];
      // 如果有显示器列表，默认选择第一个
      if (monitors.value.length > 0 && screenIndex.value >= monitors.value.length) {
        screenIndex.value = 0;
      }
    } else {
      console.error('获取显示器列表失败:', response.msg);
    }
  } catch (error) {
    console.error('获取显示器列表失败:', error);
  } finally {
    monitorsLoading.value = false;
  }
};

// 立即截屏
const takeScreenshot = async () => {
  if (!props.clientInfo || props.clientInfo.status !== 1) {
    message.warning('客户端离线，无法截屏');
    return;
  }

  // 验证区域截图参数
  if (screenshotType.value === 2) {
    if (regionWidth.value <= 0 || regionHeight.value <= 0) {
      message.error('请输入有效的区域尺寸');
      return;
    }
    if (regionX.value < 0 || regionY.value < 0) {
      message.error('区域坐标不能为负数');
      return;
    }
  }

  try {
    screenshotLoading.value = true;
    
    const requestData = {
      type: screenshotType.value,
      format: format.value,
      quality: quality.value,
      monitor_index: screenshotType.value === 1 ? 0 : screenIndex.value // 活动窗口截图不需要指定显示器
    };

    // 如果是区域截图，添加区域参数
    if (screenshotType.value === 2) {
      requestData.x = regionX.value;
      requestData.y = regionY.value;
      requestData.width = regionWidth.value;
      requestData.height = regionHeight.value;
    }
    
    const response = await screenshotApi.takeScreenshot(props.clientId, requestData);
    if (response.code === 200) {
      const typeNames = ['全屏', '活动窗口', '区域'];
      message.success(`${typeNames[screenshotType.value]}截屏请求已发送`);
      // 延迟刷新截图列表
      setTimeout(() => {
        refreshScreenshots();
      }, 2000);
    } else {
      message.error(response.msg || '截屏请求失败');
    }
  } catch (error) {
    console.error('截屏失败:', error);
    message.error('截屏失败');
  } finally {
    screenshotLoading.value = false;
  }
};

// 刷新截屏列表
const refreshScreenshots = async () => {
  if (!props.clientId) {
    return;
  }
  
  try {
    loading.value = true;
    
    const response = await screenshotApi.getScreenshotList(props.clientId);
    if (response.code === 200) {
      // API返回的数据结构是嵌套的，真正的截图数组在response.data.data中
      screenshots.value = response.data?.data || [];
    } else {
      message.error(response.data.msg || '获取截屏列表失败');
    }
  } catch (error) {
    console.error('获取截屏列表失败:', error);
    message.error('获取截屏列表失败');
  } finally {
    loading.value = false;
  }
};

// 清空截屏
const clearScreenshots = async () => {
  try {
    loading.value = true;
    // 删除所有截图
    const deletePromises = screenshots.value.map(screenshot => 
      screenshotApi.deleteScreenshot(props.clientId, screenshot.filename)
    );
    await Promise.all(deletePromises);
    screenshots.value = [];
    message.success('已清空所有截屏');
  } catch (error) {
    console.error('清空截屏失败:', error);
    message.error('清空截屏失败');
  } finally {
    loading.value = false;
  }
};

// 预览截屏
const previewScreenshot = (screenshot) => {
  currentScreenshot.value = screenshot;
  previewModalVisible.value = true;
};

// 下载截屏
const downloadScreenshot = (screenshot) => {
  const link = document.createElement('a');
  link.href = getScreenshotUrl(screenshot);
  link.download = screenshot.filename || screenshot.name;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 删除截屏
const deleteScreenshot = async (screenshot) => {
  try {
    const response = await screenshotApi.deleteScreenshot(props.clientId, screenshot.filename);
    if (response.code === 200) {
      screenshots.value = screenshots.value.filter(s => s.filename !== screenshot.filename);
      message.success('删除截图成功');
    } else {
      message.error(response.msg || '删除截图失败');
    }
  } catch (error) {
    console.error('删除截图失败:', error);
    message.error('删除截图失败');
  }
};

// 切换屏幕流
const toggleScreenStream = async () => {
  if (isStreaming.value) {
    await stopScreenStream();
  } else {
    await startScreenStream();
  }
};

// 开始屏幕流
const startScreenStream = async () => {
  if (!props.clientId) {
    message.error('请先选择客户端');
    return;
  }
  
  try {
    streamLoading.value = true;
    const params = {
      type: screenshotType.value,
      monitor_index: screenshotType.value === 1 ? 0 : screenIndex.value,
      format: format.value,
      quality: quality.value,
      fps: streamFps.value,
      enableDiffDetection: enableDiffDetection.value,
      diffThreshold: diffThreshold.value,
      cpuLimit: cpuLimit.value,
      bandwidthLimit: bandwidthLimit.value,
      enableMemoryOptimization: enableMemoryOptimization.value
    };
    
    // 如果是区域截图，添加区域参数
    if (screenshotType.value === 2) {
      params.x = regionX.value;
      params.y = regionY.value;
      params.width = regionWidth.value;
      params.height = regionHeight.value;
    }
    
    const response = await screenshotApi.startScreenStream(props.clientId, params);
    if (response.code === 200) {
      isStreaming.value = true;
      message.success('屏幕流已启动');
      // 开始获取流数据
      startStreamDataPolling();
    } else {
      message.error(response.msg || '启动屏幕流失败');
    }
    
  } catch (error) {
    console.error('启动屏幕流失败:', error);
    message.error('启动失败: ' + (error.response?.data?.message || error.message));
  } finally {
    streamLoading.value = false;
  }
};

// 停止屏幕流
const stopScreenStream = async () => {
  if (!props.clientId) {
    message.error('请先选择客户端');
    return;
  }
  
  try {
    streamLoading.value = true;
    const response = await screenshotApi.stopScreenStream(props.clientId);
    if (response.code === 200) {
      isStreaming.value = false;
      streamData.value = null;
      stopStreamDataPolling();
      message.success('屏幕流已停止');
    } else {
      message.error(response.msg || '停止屏幕流失败');
    }
    
  } catch (error) {
    console.error('停止屏幕流失败:', error);
    message.error('停止失败: ' + (error.response?.data?.message || error.message));
  } finally {
    streamLoading.value = false;
  }
};

// 流数据轮询相关
let streamPollingTimer = null;

// 开始流数据轮询
const startStreamDataPolling = () => {
  if (streamPollingTimer) {
    clearInterval(streamPollingTimer);
  }
  
  // 立即获取一次数据
  getStreamData();
  
  // 根据帧率设置轮询间隔
  const interval = Math.max(100, 1000 / streamFps.value); // 最小100ms间隔
  streamPollingTimer = setInterval(getStreamData, interval);
};

// 停止流数据轮询
const stopStreamDataPolling = () => {
  if (streamPollingTimer) {
    clearInterval(streamPollingTimer);
    streamPollingTimer = null;
  }
};

// 获取流数据
const getStreamData = async () => {
  if (!isStreaming.value || !props.clientId) {
    return;
  }
  
  try {
    const response = await screenshotApi.getStreamData(props.clientId);
    if (response.code === 200 && response.data) {
      streamData.value = response.data;
      displayStreamFrame(response.data);
    }
  } catch (error) {
    console.error('获取流数据失败:', error);
    // 如果连续失败，可能需要停止流
  }
};

// 显示流帧
const displayStreamFrame = (data) => {
  // 检查数据是否有效
  if (!streamCanvas.value || !data) {
    return;
  }
  
  // 兼容不同的数据格式
  let frameData = data.frame_data || data.image_data;
  let format = data.format || 'png';
  
  // 如果没有帧数据，检查是否有错误信息
  if (!frameData) {
    console.log('流数据状态:', data);
    return;
  }
  
  const canvas = streamCanvas.value;
  const ctx = canvas.getContext('2d');
  
  // 创建图片对象
  const img = new Image();
  img.onload = () => {
    // 设置画布尺寸
    canvas.width = img.width;
    canvas.height = img.height;
    
    // 绘制图片
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(img, 0, 0);
  };
  
  img.onerror = () => {
    console.error('图片加载失败');
  };
  
  // 设置图片数据（base64格式）
  img.src = `data:image/${format};base64,${frameData}`;
};

// 开始定时截图
const startTimerScreenshot = async () => {
  if (!props.clientId) {
    message.error('请先选择客户端');
    return;
  }

  if (isTimerRunning.value) {
    message.warning('定时截图已在运行中');
    return;
  }

  try {
    // 构建定时截图请求参数
    const requestData = {
      interval_seconds: timerInterval.value,
      max_count: maxTimerCount.value,
      enable_smart_trigger: enableSmartTrigger.value,
      type: screenshotType.value,
      format: format.value,
      quality: quality.value,
      monitor_index: screenshotType.value === 1 ? 0 : screenIndex.value
    };

    // 如果是区域截图，添加区域参数
    if (screenshotType.value === 2) {
      requestData.x = regionX.value;
      requestData.y = regionY.value;
      requestData.width = regionWidth.value;
      requestData.height = regionHeight.value;
    }

    // 调用后端API启动定时截图
    const response = await screenshotApi.startTimedScreenshot(props.clientId, requestData);

    if (response.code === 200) {
      isTimerRunning.value = true;
      timerCount.value = 0;
      message.success(`定时截图已启动，间隔${timerInterval.value}秒`);

      // 启动状态轮询，定期检查定时截图状态和更新截图列表
      startTimerStatusPolling();
    } else {
      message.error(response.msg || '启动定时截图失败');
    }

  } catch (error) {
    console.error('启动定时截图失败:', error);
    message.error('启动定时截图失败: ' + (error.response?.data?.msg || error.message));
    isTimerRunning.value = false;
  }
};

// 停止定时截图
const stopTimerScreenshot = async () => {
  try {
    await screenshotApi.stopTimedScreenshot(props.clientId);
    isTimerRunning.value = false;
    message.success('定时截图已停止');

    // 清理前端定时器（如果有的话）
    if (timerId.value) {
      clearTimeout(timerId.value);
      timerId.value = null;
    }
  } catch (error) {
    console.error('停止定时截图失败:', error);
    message.error('停止定时截图失败');
  }
};

// 切换定时截图状态
const toggleTimerScreenshot = async () => {
  if (isTimerRunning.value) {
    await stopTimerScreenshot();
  } else {
    await startTimerScreenshot();
  }
};

// 启动定时截图状态轮询
const startTimerStatusPolling = () => {
  if (timerStatusPollingTimer) {
    clearInterval(timerStatusPollingTimer);
  }

  // 立即检查一次状态
  checkTimerStatus();

  // 每5秒检查一次状态
  timerStatusPollingTimer = setInterval(checkTimerStatus, 5000);
};

// 停止定时截图状态轮询
const stopTimerStatusPolling = () => {
  if (timerStatusPollingTimer) {
    clearInterval(timerStatusPollingTimer);
    timerStatusPollingTimer = null;
  }
};

// 检查定时截图状态
const checkTimerStatus = async () => {
  if (!props.clientId) return;

  try {
    const response = await screenshotApi.getTimedScreenshotStatus(props.clientId);
    if (response.code === 200) {
      const status = response.data;

      // 更新前端状态
      isTimerRunning.value = status.is_active;
      timerCount.value = status.current_count || 0;

      // 如果任务不再活跃，停止轮询
      if (!status.is_active && timerStatusPollingTimer) {
        stopTimerStatusPolling();

        // 如果任务完成，刷新截图列表
        if (status.status === 'completed' || status.status === 'stopped') {
          refreshScreenshots();
        }
      }

      // 定期刷新截图列表（如果定时截图正在运行）
      if (status.is_active) {
        refreshScreenshots();
      }
    }
  } catch (error) {
    console.error('检查定时截图状态失败:', error);
    // 如果检查状态失败，可能是任务已经不存在，停止轮询
    if (error.response?.status === 404) {
      isTimerRunning.value = false;
      stopTimerStatusPolling();
    }
  }
};

// 获取截图URL
const getScreenshotUrl = (screenshot) => {
  // 使用下载API来获取截图文件
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  // file_path格式: 192.168.184.1:62023/filename
  // 直接使用file_path作为相对路径
  let relativePath = screenshot.filename;
  if (screenshot.file_path) {
    relativePath = screenshot.file_path;
  }
  return `/api/download/screenshot?path=${encodeURIComponent(relativePath)}&token=${token}`;
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 设置自动刷新
const setupAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
  }
  
  if (autoRefresh.value) {
    autoRefreshTimer = setInterval(() => {
      refreshScreenshots();
    }, 30000); // 30秒刷新一次
  }
};

// 监听自动刷新设置变化
watch(() => autoRefresh.value, () => {
  setupAutoRefresh();
});

// 监听客户端信息变化
watch(() => props.clientInfo, (newClientInfo) => {
  if (newClientInfo && newClientInfo.status === 1) {
    // 客户端上线时获取显示器列表
    getMonitorList();
    // 检查定时截图状态
    checkTimerStatus();
  } else {
    // 客户端离线时停止状态轮询
    stopTimerStatusPolling();
    isTimerRunning.value = false;
  }
}, { immediate: true });



// 组件挂载时获取截屏列表
onMounted(() => {
  refreshScreenshots();
  setupAutoRefresh();
  // 如果客户端已经在线，立即获取显示器列表
  if (props.clientInfo && props.clientInfo.status === 1) {
    getMonitorList();
    // 检查是否有活跃的定时截图任务
    checkTimerStatus();
  }
});



// 组件卸载时清理定时器
onUnmounted(() => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer);
  }
  // 清理定时截图
  if (timerId.value) {
    clearTimeout(timerId.value);
  }
  // 停止定时截图状态轮询
  stopTimerStatusPolling();
  // 停止屏幕流
  if (isStreaming.value) {
    stopScreenStream();
  }
  // 停止流数据轮询
  stopStreamDataPolling();
});

// 缩放控制方法
const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(3, zoomLevel.value * 1.2);
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 0.2) {
    zoomLevel.value = Math.max(0.2, zoomLevel.value / 1.2);
  }
};

const resetZoom = () => {
  zoomLevel.value = 1;
  // 重置容器滚动位置
  if (streamContainer.value) {
    streamContainer.value.scrollLeft = 0;
    streamContainer.value.scrollTop = 0;
  }
};

// 鼠标滚轮缩放
const onWheel = (event) => {
  event.preventDefault();
  if (event.deltaY < 0) {
    zoomIn();
  } else {
    zoomOut();
  }
};

// 拖拽控制方法
const startDrag = (event) => {
  isDragging.value = true;
  dragStart.value = {
    x: event.clientX - (streamContainer.value?.scrollLeft || 0),
    y: event.clientY - (streamContainer.value?.scrollTop || 0)
  };
  event.preventDefault();
};

const onDrag = (event) => {
  if (!isDragging.value || !streamContainer.value) return;

  event.preventDefault();
  const x = event.clientX - dragStart.value.x;
  const y = event.clientY - dragStart.value.y;

  streamContainer.value.scrollLeft = -x;
  streamContainer.value.scrollTop = -y;
};

const endDrag = () => {
  isDragging.value = false;
};

// 全屏切换
const toggleFullscreen = () => {
  if (!streamContainer.value) return;

  if (!document.fullscreenElement) {
    streamContainer.value.requestFullscreen().catch(err => {
      message.error('无法进入全屏模式');
    });
  } else {
    document.exitFullscreen();
  }
};
</script>

<style scoped>
.screenshot-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.screenshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.screenshot-header h3 {
  margin: 0;
}

.screenshot-actions {
  display: flex;
  gap: 8px;
}

.screenshot-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.screenshot-settings {
  margin: 16px 0;
}

.screenshot-list {
  flex: 1;
  overflow: auto;
}

.empty-state {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.screenshot-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s;
}

.screenshot-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.screenshot-image {
  position: relative;
  height: 150px;
  overflow: hidden;
  background: #f5f5f5;
}

.screenshot-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.screenshot-item:hover .screenshot-overlay {
  opacity: 1;
}

.screenshot-info {
  padding: 12px;
}

.screenshot-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.screenshot-time,
.screenshot-size {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.screenshot-preview {
  text-align: center;
}

.stream-display {
  margin: 16px 0;
}

.stream-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.stream-controls {
  display: flex;
  align-items: center;
}

.stream-container {
  position: relative;
  background: #f5f5f5;
  border-radius: 6px;
  overflow: auto;
  min-height: 400px;
  max-height: 80vh;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 10px;
}

.stream-canvas {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease;
  user-select: none;
}

.stream-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #666;
}

.stream-info {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}


</style>