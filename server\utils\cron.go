package utils

import (
	"fmt"
	"time"

	"github.com/robfig/cron/v3"
)

// ValidateCronExpression 验证Cron表达式
func ValidateCronExpression(cronExpr string) error {
	// 首先尝试6字段格式（支持秒级精度）
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	_, err := parser.Parse(cronExpr)
	if err == nil {
		return nil
	}

	// 如果6字段失败，尝试5字段格式（标准格式）
	_, err = cron.ParseStandard(cronExpr)
	if err != nil {
		return fmt.Errorf("无效的Cron表达式: %w", err)
	}
	return nil
}

// GetNextRunTimes 获取Cron表达式的下几次执行时间
func GetNextRunTimes(cronExpr string, count int) ([]time.Time, error) {
	// 首先尝试6字段格式（支持秒级精度）
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		// 如果6字段失败，尝试5字段格式（标准格式）
		schedule, err = cron.ParseStandard(cronExpr)
		if err != nil {
			return nil, fmt.Errorf("解析Cron表达式失败: %w", err)
		}
	}

	var times []time.Time
	now := time.Now()
	for i := 0; i < count; i++ {
		now = schedule.Next(now)
		times = append(times, now)
	}

	return times, nil
}

// DescribeCronExpression 描述Cron表达式
func DescribeCronExpression(cronExpr string) string {
	// 这里可以实现更复杂的Cron表达式描述逻辑
	// 暂时返回简单描述
	return fmt.Sprintf("Cron表达式: %s", cronExpr)
}
