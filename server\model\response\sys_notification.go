package response

import (
	"server/model/sys"
	"time"
)

// NotificationResponse 通知响应
type NotificationResponse struct {
	ID        uint                  `json:"id"`
	UserID    uint                  `json:"userId"`
	Type      sys.NotificationType  `json:"type"`
	Level     sys.NotificationLevel `json:"level"`
	Title     string                `json:"title"`
	Content   string                `json:"content"`
	Data      sys.NotificationData  `json:"data"`
	ReadAt    *time.Time            `json:"readAt"`
	CreatedAt time.Time             `json:"createdAt"`
	UpdatedAt time.Time             `json:"updatedAt"`
	IsRead    bool                  `json:"isRead"`
	Icon      string                `json:"icon"`
	Color     string                `json:"color"`
}

// NotificationListResponse 通知列表响应
type NotificationListResponse struct {
	List     []NotificationResponse `json:"list"`
	Total    int64                  `json:"total"`
	Page     int                    `json:"page"`
	PageSize int                    `json:"pageSize"`
	Stats    sys.NotificationStats  `json:"stats"`
}

// NotificationStatsResponse 通知统计响应
type NotificationStatsResponse struct {
	Total  int64 `json:"total"`
	Unread int64 `json:"unread"`
	Read   int64 `json:"read"`
}

// NotificationSettingsResponse 通知设置响应
type NotificationSettingsResponse struct {
	ClientOnline     bool `json:"clientOnline"`
	ClientOffline    bool `json:"clientOffline"`
	ClientDeleted    bool `json:"clientDeleted"`
	ListenerCreated  bool `json:"listenerCreated"`
	ListenerClosed   bool `json:"listenerClosed"`
	ListenerDeleted  bool `json:"listenerDeleted"`
	ServerAlert      bool `json:"serverAlert"`
	SystemWarning    bool `json:"systemWarning"`
	SoundEnabled     bool `json:"soundEnabled"`
	DesktopEnabled   bool `json:"desktopEnabled"`
	EmailEnabled     bool `json:"emailEnabled"`
	DisplayDuration  int  `json:"displayDuration"`
	MaxNotifications int  `json:"maxNotifications"`
}

// NotificationSSEData SSE推送的通知数据
type NotificationSSEData struct {
	Type         string                `json:"type"`         // 事件类型：notification
	Action       string                `json:"action"`       // 动作：create, update, delete
	Notification NotificationResponse  `json:"notification"` // 通知数据
	Stats        sys.NotificationStats `json:"stats"`        // 统计信息
	Timestamp    time.Time             `json:"timestamp"`    // 时间戳
}

// ConvertToResponse 将模型转换为响应格式
func ConvertNotificationToResponse(notification *sys.SysNotification) NotificationResponse {
	data, _ := notification.GetData()

	return NotificationResponse{
		ID:        notification.ID,
		UserID:    notification.UserID,
		Type:      notification.Type,
		Level:     notification.Level,
		Title:     notification.Title,
		Content:   notification.Content,
		Data:      data,
		ReadAt:    notification.ReadAt,
		CreatedAt: notification.CreatedAt,
		UpdatedAt: notification.UpdatedAt,
		IsRead:    notification.IsRead(),
		Icon:      notification.GetLevelIcon(),
		Color:     notification.GetLevelColor(),
	}
}

// ConvertToResponseList 批量转换为响应格式
func ConvertNotificationListToResponse(notifications []sys.SysNotification) []NotificationResponse {
	responses := make([]NotificationResponse, len(notifications))
	for i, notification := range notifications {
		responses[i] = ConvertNotificationToResponse(&notification)
	}
	return responses
}
