<template>
  <div class="file-list">
    <a-table
      :columns="columns"
      :data-source="displayDataSource"
      :loading="loading"
      :pagination="false"
      size="small"
      :rowKey="(record, index) => record.path || record.name || record.id || index"
      :row-selection="{
        selectedRowKeys: selectedFiles,
        onChange: handleSelectionChange,
        getCheckboxProps: (record) => ({
          disabled: record.isBackButton
        })
      }"
      :customRow="(record) => ({ 
        onDblclick: () => {
          // 如果是返回上级行，执行返回操作
          if (record.isBackButton) {
            handleGoBack();
          } else {
            handleEnterDirectory(record);
          }
        },
        onContextmenu: (event) => {
          event.preventDefault();
          if (!record.isBackButton) {
            handleContextMenu(event, record);
          }
        }
      })"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <!-- 如果是返回按钮，显示返回上级按钮 -->
          <div v-if="record.isBackButton" class="file-item back-item">
            <a-button 
              type="link" 
              size="small" 
              @click="handleGoBack"
              class="back-button"
            >
              <template #icon>
                <ArrowLeftOutlined />
              </template>
              返回上级目录
            </a-button>
          </div>
          <!-- 正常文件项 -->
          <div v-else class="file-item">
            <component 
              :is="getFileIcon(record.type)"
              :style="{ color: getFileIconColor(record.type), marginRight: '8px' }"
            />
            <span>{{ record.name }}</span>
          </div>
        </template>
        
        <template v-if="column.dataIndex === 'size'">
          <span v-if="record.isBackButton">-</span>
          <span v-else>{{ formatFileSize(record.size) }}</span>
        </template>
        
        <template v-if="column.dataIndex === 'permissions'">
          <span v-if="record.isBackButton">-</span>
          <span v-else>{{ record.permissions }}</span>
        </template>
        
        <template v-if="column.dataIndex === 'owner'">
          <span v-if="record.isBackButton">-</span>
          <span v-else>{{ record.owner }}</span>
        </template>
        
        <template v-if="column.dataIndex === 'group'">
          <span v-if="record.isBackButton">-</span>
          <span v-else>{{ record.group }}</span>
        </template>
        
        <template v-if="column.dataIndex === 'modifiedTime'">
          <span v-if="record.isBackButton">-</span>
          <span v-else>{{ formatDate(record.modifiedTime) }}</span>
        </template>
        
        <template v-if="column.dataIndex === 'action'">
          <FileTableActions
            :record="record"
            :is-back-button="record.isBackButton"
            @download-file="handleDownloadFile"
            @edit-file="handleEditFile"
            @preview-file="handlePreviewFile"
            @start-rename="handleStartRename"
            @menu-click="handleMenuClick"
          />
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  ArrowLeftOutlined
} from '@ant-design/icons-vue';
import { formatFileSize, formatDate } from '@/utils/format';
import {
  getFileIcon,
  getFileIconColor
} from './constants.js';
import FileTableActions from './FileTableActions.vue';

// 定义 props
interface Props {
  columns: any[];
  dataSource: any[];
  loading: boolean;
  selectedFiles: string[];
  showBackButton: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  dataSource: () => [],
  loading: false,
  selectedFiles: () => [],
  showBackButton: false
});

// 定义 emits
interface Emits {
  selectionChange: [selectedRowKeys: string[]];
  goBack: [];
  enterDirectory: [record: any];
  contextMenu: [event: MouseEvent, record: any];
  downloadFile: [record: any];
  editFile: [record: any];
  previewFile: [record: any];
  startRename: [record: any];
  menuClick: [key: string, record: any];
}

const emit = defineEmits<Emits>();

// 计算属性：处理显示数据源
const displayDataSource = computed(() => {
  if (props.showBackButton) {
    // 在文件列表前面插入返回按钮项
    const backButtonItem = {
      id: '__back_button__',
      name: '返回上级目录',
      isBackButton: true,
      type: 'directory',
      size: 0,
      permissions: '-',
      owner: '-',
      group: '-',
      modifiedTime: null
    };
    return [backButtonItem, ...props.dataSource];
  }
  return props.dataSource;
});

// 事件处理函数
const handleSelectionChange = (selectedRowKeys: string[]) => {
  emit('selectionChange', selectedRowKeys);
};

const handleGoBack = () => {
  emit('goBack');
};

const handleEnterDirectory = (record: any) => {
  emit('enterDirectory', record);
};

const handleContextMenu = (event: MouseEvent, record: any) => {
  emit('contextMenu', event, record);
};

const handleDownloadFile = (record: any) => {
  emit('downloadFile', record);
};

const handleEditFile = (record: any) => {
  emit('editFile', record);
};

const handlePreviewFile = (record: any) => {
  emit('previewFile', record);
};

const handleStartRename = (record: any) => {
  emit('startRename', record);
};

const handleMenuClick = (key: string, record: any) => {
  emit('menuClick', key, record);
};
</script>

<style scoped>
.file-list {
  flex: 1;
  overflow: auto;
  padding: 0 20px 20px 20px;
}

.file-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-item {
  padding: 4px 0;
}

.back-button {
  color: #1890ff !important;
  font-weight: 500;
  padding: 0;
}

.back-button:hover {
  color: #40a9ff !important;
}

.file-item:hover {
  background-color: #f5f5f5;
}

.file-item span {
  cursor: pointer;
  user-select: none;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  color: #262626;
}

:deep(.ant-table-tbody > tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.ant-table-tbody > tr:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-table-tbody > tr > td) {
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #f0f0f0;
}

/* 危险菜单项样式已移动到 FileTableActions 组件中 */
</style>