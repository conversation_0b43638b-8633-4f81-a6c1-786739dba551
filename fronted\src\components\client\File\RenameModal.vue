<template>
  <a-modal
    v-model:open="visible"
    title="重命名"
    @ok="handleRename"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item
        label="新名称"
        name="fileName"
      >
        <a-input
          ref="fileNameInput"
          v-model:value="formData.fileName"
          placeholder="请输入新名称"
          @press-enter="handleRename"
          :maxlength="255"
          show-count
        />
      </a-form-item>
    </a-form>
    
    <div v-if="renameFile" class="file-info">
      <div class="info-item">
        <span class="label">原名称：</span>
        <span class="value">{{ renameFile.name }}</span>
      </div>
      <div class="info-item">
        <span class="label">类型：</span>
        <span class="value">{{ renameFile.type === 'directory' ? '目录' : '文件' }}</span>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { fileApi, dirApi } from '@/api';
import { buildPath } from './fileUtils.js';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  clientId: {
    type: [String, Number],
    required: true
  },
  currentPath: {
    type: String,
    default: '/'
  },
  renameFile: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits([
  'update:modelValue',
  'rename-complete'
]);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref(null);
const fileNameInput = ref(null);

// 表单数据
const formData = reactive({
  fileName: ''
});



// 表单验证规则
const rules = {
  fileName: [
    {
      required: true,
      message: '请输入新名称',
      trigger: 'blur'
    },
    {
      min: 1,
      max: 255,
      message: '文件名长度应在1-255个字符之间',
      trigger: 'blur'
    },
    {
      pattern: /^[^\\/:*?"<>|]+$/,
      message: '文件名不能包含以下字符：\\ / : * ? " < > |',
      trigger: 'blur'
    },
    {
      validator: (rule, value) => {
        if (value && props.renameFile && value === props.renameFile.name) {
          return Promise.reject('新名称与原名称相同');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

// 重命名处理
const handleRename = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    if (!props.renameFile) {
      message.error('没有选择要重命名的文件');
      return;
    }
    
    loading.value = true;
    
    const sourcePath = buildPath(props.currentPath, props.renameFile.name);
    const destinationPath = buildPath(props.currentPath, formData.fileName.trim());
    
    if (props.renameFile.type === 'directory') {
      await dirApi.moveDir(props.clientId, {
        source: sourcePath,
        destination: destinationPath,
        force: false
      });
    } else {
      await fileApi.moveFile(props.clientId, {
        source: sourcePath,
        destination: destinationPath,
        force: false,
        create_dirs: false,
        preserve_attrs: true,
        verify_integrity: false
      });
    }
    
    message.success('重命名成功');
    handleCancel();
    emit('rename-complete');
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误，不显示错误消息
      return;
    }
    
    console.error('重命名失败:', error);
    message.error('重命名失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  formData.fileName = '';
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    visible.value = newValue;
  },
  { immediate: true }
);

// 监听 visible 变化
watch(
  () => visible.value,
  (newValue) => {
    emit('update:modelValue', newValue);
    if (!newValue) {
      // 弹窗关闭时重置表单
      formData.fileName = '';
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    }
  }
);

// 监听 renameFile 变化，自动填充文件名
watch(
  () => props.renameFile,
  (newFile) => {
    if (newFile && newFile.name) {
      formData.fileName = newFile.name;
      // 弹窗打开时自动聚焦并选中文件名（不包括扩展名）
      if (visible.value) {
        nextTick(() => {
          if (fileNameInput.value) {
            fileNameInput.value.focus();
            // 如果是文件，选中文件名部分（不包括扩展名）
            if (newFile.type === 'file') {
              const lastDotIndex = newFile.name.lastIndexOf('.');
              if (lastDotIndex > 0) {
                fileNameInput.value.setSelectionRange(0, lastDotIndex);
              } else {
                fileNameInput.value.select();
              }
            } else {
              // 目录则选中全部
              fileNameInput.value.select();
            }
          }
        });
      }
    }
  },
  { immediate: true }
);

// 监听弹窗打开状态，自动聚焦输入框
watch(
  () => visible.value,
  (newValue) => {
    if (newValue && props.renameFile) {
      nextTick(() => {
        if (fileNameInput.value) {
          fileNameInput.value.focus();
          // 如果是文件，选中文件名部分（不包括扩展名）
          if (props.renameFile.type === 'file') {
            const lastDotIndex = props.renameFile.name.lastIndexOf('.');
            if (lastDotIndex > 0) {
              fileNameInput.value.setSelectionRange(0, lastDotIndex);
            } else {
              fileNameInput.value.select();
            }
          } else {
            // 目录则选中全部
            fileNameInput.value.select();
          }
        }
      });
    }
  }
);
</script>

<style scoped>
/* 表单样式优化 */
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input) {
  border-radius: 6px;
}

:deep(.ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 字符计数样式 */
:deep(.ant-input-show-count-suffix) {
  color: #8c8c8c;
}

/* 文件信息样式 */
.file-info {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 60px;
}

.value {
  color: #333;
  word-break: break-all;
}
</style>