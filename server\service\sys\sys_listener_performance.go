package sys

import (
	"server/core/listener/stats"
	"server/factory"
	"server/global"
	"time"

	"go.uber.org/zap"
)

type ListenerPerformanceService struct{}

// GetListenerPerformanceStats 获取监听器性能统计
func (s *ListenerPerformanceService) GetListenerPerformanceStats() map[string]interface{} {
	// 获取所有类型监听器的统计信息
	allStats := factory.GetAllListenerStats()
	
	// 按类型分组统计
	statsByType := make(map[string][]stats.ListenerStats)
	totalConnections := 0
	totalDataTransferred := int64(0)
	activeListeners := 0

	for _, stat := range allStats {
		listenerType := stat.GetListenerType()
		if statsByType[listenerType] == nil {
			statsByType[listenerType] = []stats.ListenerStats{}
		}
		statsByType[listenerType] = append(statsByType[listenerType], stat)
		
		totalConnections += int(stat.GetTotalConnections())
		totalDataTransferred += stat.GetDataTransferred()
		if stat.IsActive() {
			activeListeners++
		}
	}
	
	global.LOG.Info("获取监听器性能统计",
		zap.Int("total_listeners", len(allStats)),
		zap.Int("active_listeners", activeListeners),
		zap.Int("total_connections", totalConnections))
	
	return map[string]interface{}{
		"listener_details":      allStats,
		"listeners_by_type":     statsByType,
		"summary": map[string]interface{}{
			"total_listeners":       len(allStats),
			"active_listeners":      activeListeners,
			"total_connections":     totalConnections,
			"total_data_transferred": totalDataTransferred,
			"data_transferred_mb":   float64(totalDataTransferred) / (1024 * 1024),
		},
		"timestamp": time.Now(),
	}
}

// GetListenerConnectionStats 获取监听器连接统计
func (s *ListenerPerformanceService) GetListenerConnectionStats() map[string]interface{} {
	connectionStatsByType := factory.GetConnectionStatsByType()
	
	// 计算总体统计
	var totalConnections, totalActiveConnections int64
	var avgConnectionsPerHour, avgConnTime float64
	var lastConnectionTime time.Time
	
	typeCount := 0
	for listenerType, stats := range connectionStatsByType {
		totalConnections += stats.TotalConnections
		totalActiveConnections += stats.ActiveConnections
		avgConnectionsPerHour += stats.ConnectionsPerHour
		avgConnTime += stats.AverageConnTime
		
		if stats.LastConnectionTime.After(lastConnectionTime) {
			lastConnectionTime = stats.LastConnectionTime
		}
		
		typeCount++
		
		global.LOG.Debug("监听器连接统计",
			zap.String("type", listenerType),
			zap.Int64("total_connections", stats.TotalConnections),
			zap.Int64("active_connections", stats.ActiveConnections))
	}
	
	if typeCount > 0 {
		avgConnectionsPerHour /= float64(typeCount)
		avgConnTime /= float64(typeCount)
	}
	
	return map[string]interface{}{
		"connection_stats_by_type": connectionStatsByType,
		"summary": map[string]interface{}{
			"total_connections":        totalConnections,
			"total_active_connections": totalActiveConnections,
			"avg_connections_per_hour": avgConnectionsPerHour,
			"avg_connection_time":      avgConnTime,
			"last_connection_time":     lastConnectionTime,
		},
		"timestamp": time.Now(),
	}
}

// GetListenerTrafficStats 获取监听器流量统计
func (s *ListenerPerformanceService) GetListenerTrafficStats() map[string]interface{} {
	trafficStatsByType := factory.GetTrafficStatsByType()
	
	// 计算总体统计
	var totalBytesReceived, totalBytesSent int64
	var totalPacketsReceived, totalPacketsSent int64
	var avgSpeed, peakSpeed float64
	var earliestResetTime time.Time
	
	typeCount := 0
	for listenerType, stats := range trafficStatsByType {
		totalBytesReceived += stats.BytesReceived
		totalBytesSent += stats.BytesSent
		totalPacketsReceived += stats.PacketsReceived
		totalPacketsSent += stats.PacketsSent
		avgSpeed += stats.AverageSpeed
		
		if stats.PeakSpeed > peakSpeed {
			peakSpeed = stats.PeakSpeed
		}
		
		if earliestResetTime.IsZero() || stats.LastResetTime.Before(earliestResetTime) {
			earliestResetTime = stats.LastResetTime
		}
		
		typeCount++
		
		global.LOG.Debug("监听器流量统计",
			zap.String("type", listenerType),
			zap.Int64("bytes_received", stats.BytesReceived),
			zap.Int64("bytes_sent", stats.BytesSent))
	}
	
	if typeCount > 0 {
		avgSpeed /= float64(typeCount)
	}
	
	return map[string]interface{}{
		"traffic_stats_by_type": trafficStatsByType,
		"summary": map[string]interface{}{
			"total_bytes_received":    totalBytesReceived,
			"total_bytes_sent":        totalBytesSent,
			"total_packets_received":  totalPacketsReceived,
			"total_packets_sent":      totalPacketsSent,
			"total_bytes_received_mb": float64(totalBytesReceived) / (1024 * 1024),
			"total_bytes_sent_mb":     float64(totalBytesSent) / (1024 * 1024),
			"average_speed_mbps":      avgSpeed,
			"peak_speed_mbps":         peakSpeed,
			"last_reset_time":         earliestResetTime,
		},
		"timestamp": time.Now(),
	}
}

// GetListenerStatsByType 按类型获取监听器统计
func (s *ListenerPerformanceService) GetListenerStatsByType(listenerType string) (map[string]interface{}, error) {
	provider, err := factory.GetListenerStatsProvider(listenerType)
	if err != nil {
		return nil, err
	}
	
	stats := provider.GetAllListenerStats()
	connectionStats := provider.GetConnectionStats()
	trafficStats := provider.GetTrafficStats()
	activeCount := provider.GetActiveListenerCount()
	
	return map[string]interface{}{
		"listener_type":     listenerType,
		"listener_stats":    stats,
		"connection_stats":  connectionStats,
		"traffic_stats":     trafficStats,
		"active_count":      activeCount,
		"total_count":       len(stats),
		"timestamp":         time.Now(),
	}, nil
}

// GetListenerStatsByID 根据ID和类型获取特定监听器统计
func (s *ListenerPerformanceService) GetListenerStatsByID(listenerType string, listenerID uint) (map[string]interface{}, error) {
	stats, err := factory.GetListenerStatsByID(listenerType, listenerID)
	if err != nil {
		return nil, err
	}
	
	return map[string]interface{}{
		"listener_id":   listenerID,
		"listener_type": listenerType,
		"stats":         stats,
		"timestamp":     time.Now(),
	}, nil
}

// ResetAllListenerStats 重置所有监听器统计
func (s *ListenerPerformanceService) ResetAllListenerStats() {
	factory.ResetAllListenerStats()
	
	global.LOG.Info("所有监听器统计信息已重置")
}

// GetActiveListenerCountByType 按类型获取活跃监听器数量
func (s *ListenerPerformanceService) GetActiveListenerCountByType() map[string]interface{} {
	countsByType := factory.GetActiveListenerCountByType()
	
	totalActive := 0
	for _, count := range countsByType {
		totalActive += count
	}
	
	return map[string]interface{}{
		"counts_by_type": countsByType,
		"total_active":   totalActive,
		"timestamp":      time.Now(),
	}
}
