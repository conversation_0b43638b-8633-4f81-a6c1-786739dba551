package dbpool

import (
	"time"

	"gorm.io/gorm"
)

// 便捷函数
// ExecuteDBOperation 执行数据库操作（带监控）
func ExecuteDBOperation(operation string, fn func(*gorm.DB) error) error {
	// 🚀 记录连接池操作
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithMonitoring(operation, fn)
}

// ExecuteDBOperationWithRetry 执行数据库操作（带重试）
func ExecuteDBOperationWithRetry(operation string, fn func(*gorm.DB) error, maxRetries int) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithRetry(operation, fn, maxRetries)
}

// ExecuteDBOperationAsync 异步执行数据库操作
// ⚠️  警告：此函数是真正的异步操作，不会等待数据库操作完成就返回
// ⚠️  不适用于需要立即获取自动生成ID的场景（如 task.ID）
// ⚠️  如需等待操作完成，请使用 ExecuteDBOperationAsyncAndWait
// 适用场景：不需要立即结果的后台任务、日志记录、统计更新等
func ExecuteDBOperationAsync(operation string, fn func(*gorm.DB) error) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithWorkerpool(operation, fn)
}

// ExecuteDBOperationAsyncWithCallback 异步执行数据库操作（带回调）
// ⚠️  警告：此函数是真正的异步操作，不会等待数据库操作完成就返回
// ⚠️  不适用于需要立即获取自动生成ID的场景（如 task.ID）
// ⚠️  如需等待操作完成，请使用 ExecuteDBOperationAsyncWithCallbackAndWait
// 适用场景：不需要立即结果但需要回调通知的后台任务
func ExecuteDBOperationAsyncWithCallback(operation string, fn func(*gorm.DB) error, callback func(error)) {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	wrapper.WithWorkerPoolAndCallback(operation, fn, callback)
}

// 🚀 新增：同步等待版本的异步函数

// ExecuteDBOperationAsyncAndWait 异步执行数据库操作并等待完成
// ✅ 适用于需要立即获取自动生成ID的场景（如 task.ID）
// ✅ 提供超时控制，默认30秒
// ✅ 完整的错误处理和panic恢复
func ExecuteDBOperationAsyncAndWait(operation string, fn func(*gorm.DB) error) error {
	return ExecuteDBOperationAsyncAndWaitWithTimeout(operation, fn, 30*time.Second)
}

// ExecuteDBOperationAsyncAndWaitWithTimeout 异步执行数据库操作并等待完成（自定义超时）
func ExecuteDBOperationAsyncAndWaitWithTimeout(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithWorkerpoolAndWait(operation, fn, timeout)
}

// ExecuteDBOperationAsyncWithCallbackAndWait 异步执行数据库操作（带回调）并等待完成
// ✅ 适用于需要立即获取自动生成ID且需要回调通知的场景
// ✅ 提供超时控制，默认30秒
func ExecuteDBOperationAsyncWithCallbackAndWait(operation string, fn func(*gorm.DB) error, callback func(error)) error {
	return ExecuteDBOperationAsyncWithCallbackAndWaitWithTimeout(operation, fn, callback, 30*time.Second)
}

// ExecuteDBOperationAsyncWithCallbackAndWaitWithTimeout 异步执行数据库操作（带回调）并等待完成（自定义超时）
func ExecuteDBOperationAsyncWithCallbackAndWaitWithTimeout(operation string, fn func(*gorm.DB) error, callback func(error), timeout time.Duration) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.WithWorkerPoolCallbackAndWait(operation, fn, callback, timeout)
}

// ExecuteDBTransaction 执行数据库事务
func ExecuteDBTransaction(operation string, fn func(*gorm.DB) error) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.Transaction(operation, fn)
}

// ExecuteDBTransactionAsync 异步执行数据库事务
// ⚠️  警告：此函数是真正的异步操作，不会等待事务完成就返回
// ⚠️  不适用于需要立即获取自动生成ID的场景（如 task.ID）
// ⚠️  如需等待事务完成，请使用 ExecuteDBTransactionAsyncAndWait
func ExecuteDBTransactionAsync(operation string, fn func(*gorm.DB) error) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.TransactionWithWorkerpool(operation, fn)
}

// ExecuteDBTransactionAsyncAndWait 异步执行数据库事务并等待完成
// ✅ 适用于需要立即获取自动生成ID的事务场景
// ✅ 提供超时控制，默认30秒
func ExecuteDBTransactionAsyncAndWait(operation string, fn func(*gorm.DB) error) error {
	return ExecuteDBTransactionAsyncAndWaitWithTimeout(operation, fn, 30*time.Second)
}

// ExecuteDBTransactionAsyncAndWaitWithTimeout 异步执行数据库事务并等待完成（自定义超时）
func ExecuteDBTransactionAsyncAndWaitWithTimeout(operation string, fn func(*gorm.DB) error, timeout time.Duration) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.TransactionWithWorkerpoolAndWait(operation, fn, timeout)
}

// ExecuteDBBatch 执行批量数据库操作
func ExecuteDBBatch(operation string, batchSize int, items interface{}) error {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.BatchOperation(operation, batchSize, items, nil)
}

// QueryDBAsync 异步查询数据库
// ⚠️  警告：此函数是真正的异步操作，不会等待查询完成就返回
// ⚠️  结果只能通过回调函数获取
// ⚠️  如需等待查询完成并获取结果，请使用 QueryDBAsyncAndWait
func QueryDBAsync(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error)) {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	wrapper.AsyncQuery(operation, fn, callback)
}

// QueryDBAsyncAndWait 异步查询数据库并等待完成
// ✅ 适用于需要立即获取查询结果的场景
// ✅ 提供超时控制，默认30秒
// ✅ 支持可选的回调函数
func QueryDBAsyncAndWait(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error)) (interface{}, error) {
	return QueryDBAsyncAndWaitWithTimeout(operation, fn, callback, 30*time.Second)
}

// QueryDBAsyncAndWaitWithTimeout 异步查询数据库并等待完成（自定义超时）
func QueryDBAsyncAndWaitWithTimeout(operation string, fn func(*gorm.DB) (interface{}, error), callback func(interface{}, error), timeout time.Duration) (interface{}, error) {
	RecordPoolOp(operation)
	wrapper := GetDBWrapper()
	return wrapper.AsyncQueryAndWait(operation, fn, callback, timeout)
}

/*
🚀 数据库操作函数使用指南

📋 函数选择指南：

1. 需要立即获取自动生成ID（如 task.ID）：
   ✅ ExecuteDBOperation (同步)
   ✅ ExecuteDBOperationAsyncAndWait (异步+等待)
   ❌ ExecuteDBOperationAsync (纯异步，不等待)

2. 不需要立即结果的后台任务：
   ✅ ExecuteDBOperationAsync (纯异步)
   ✅ ExecuteDBOperationAsyncWithCallback (异步+回调)

3. 事务操作：
   - 需要立即结果：ExecuteDBTransaction 或 ExecuteDBTransactionAsyncAndWait
   - 后台事务：ExecuteDBTransactionAsync

4. 查询操作：
   - 需要立即结果：QueryDBAsyncAndWait
   - 后台查询：QueryDBAsync

⚠️  常见错误场景：
- 使用 ExecuteDBOperationAsync 创建任务后立即返回 task.ID
- 在需要事务一致性的场景使用纯异步函数
- 在高并发场景不设置合适的超时时间

✅ 最佳实践：
1. 创建任务记录时使用同步或异步+等待版本
2. 日志记录、统计更新使用纯异步版本
3. 设置合适的超时时间（默认30秒）
4. 在关键路径上使用同步版本确保数据一致性
5. 使用回调版本处理异步结果通知

🔧 性能考虑：
- 同步版本：阻塞当前goroutine，但确保操作完成
- 异步+等待版本：使用工作池但等待完成，平衡性能和一致性
- 纯异步版本：最高性能，但无法保证立即一致性

📊 超时设置建议：
- 简单CRUD操作：5-10秒
- 复杂查询：15-30秒
- 批量操作：30-60秒
- 事务操作：根据业务复杂度调整
*/
