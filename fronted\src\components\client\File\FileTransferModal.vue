<template>
  <!-- 传输任务管理弹窗 -->
  <a-modal
    v-model:open="visible"
    title="文件传输任务"
    width="80%"
    :footer="null"
    @cancel="handleCancel"
  >
    <a-table
      :columns="taskColumns"
      :data-source="transferTasks"
      :pagination="false"
      size="small"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'progress_bar'">
          <a-progress 
            :percent="record.progress || 0" 
            size="small" 
            :status="record.status === 'failed' ? 'exception' : (record.status === 'completed' ? 'success' : 'active')"
          />
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-button
            type="link"
            danger
            size="small"
            @click="cancelTransferTask(record.id)"
            :disabled="record.status === 'completed' || record.status === 'failed'"
          >
            取消
          </a-button>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { ref, watch, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { fileApi } from '@/api';
import { formatFileSize, formatDate } from '@/utils/format';

// 接收属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue']);

// 内部可见性状态
const visible = ref(props.modelValue);

// 文件传输任务
const transferTasks = ref([]);

// 监听外部modelValue变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue;
});

// 监听内部visible变化，同步到外部
watch(visible, (newValue) => {
  emit('update:modelValue', newValue);
});

// 任务表格列定义
const taskColumns = [
  {
    title: '文件名',
    dataIndex: 'source_path',
    key: 'source_path',
    width: '20%',
    customRender: ({ text }) => {
      return text ? text.split('/').pop() : '-';
    }
  },
  {
    title: '传输方向',
    dataIndex: 'task_type',
    key: 'task_type',
    width: '12%',
    customRender: ({ text }) => {
      const typeMap = {
        'upload_file': '上传到客户端',
        'download_file': '下载到服务器',
        'move_file': '移动文件',
        'delete_file': '删除文件'
      };
      return typeMap[text] || text;
    }
  },
  {
    title: '文件大小',
    dataIndex: 'file_size',
    key: 'file_size',
    width: '10%',
    customRender: ({ text }) => {
      return text > 0 ? formatFileSize(text) : '-';
    }
  },
  {
    title: '传输进度',
    dataIndex: 'progress',
    key: 'progress',
    width: '18%',
    customRender: ({ text, record }) => {
      const transferred = record.transferred_size || 0;
      const total = record.file_size || 0;
      const progressText = total > 0 ? `${formatFileSize(transferred)} / ${formatFileSize(total)}` : '-';
      return progressText;
    }
  },
  {
    title: '进度条',
    dataIndex: 'progress_bar',
    key: 'progress_bar',
    width: '15%'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '10%'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: '15%',
    customRender: ({ text }) => formatDate(text)
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: '8%'
  }
];

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'pending': 'orange',
    'running': 'blue',
    'completed': 'green',
    'failed': 'red',
    'cancelled': 'gray'
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'pending': '等待中',
    'running': '传输中',
    'downloading': '下载中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  };
  return textMap[status] || status;
};

// 获取传输任务
const getTransferTasks = async () => {
  try {
    const res = await fileApi.getFileTransferTasks();
    if (res.code === 200) {
      // 过滤掉list_dir、get_drives和list_disk类型的任务
      const filteredTasks = (res.data || []).filter(task => 
        task.task_type !== 'list_dir' && 
        task.task_type !== 'get_drives' && 
        task.task_type !== 'list_disk'
      );
      transferTasks.value = filteredTasks;
      
      // 处理任务数据，确保文件名和类型正确显示
      transferTasks.value.forEach(task => {
        // 确保source_path有值
        if (!task.source_path && task.destination_path) {
          task.source_path = task.destination_path;
        }
        
        // 计算进度
        if (task.file_size > 0 && task.transferred_size >= 0) {
          task.progress = Math.round((task.transferred_size / task.file_size) * 100);
        } else if (task.status === 'completed') {
          task.progress = 100;
        } else if (task.status === 'failed' || task.status === 'cancelled') {
          // 保持当前进度不变
        } else {
          task.progress = 0;
        }
        
        // 确保进度在0-100范围内
        if (task.progress > 100) task.progress = 100;
        if (task.progress < 0) task.progress = 0;
      });
    }
  } catch (error) {
    console.error('获取传输任务失败:', error);
  }
};

// 取消传输任务
const cancelTransferTask = async (taskId) => {
  try {
    const res = await fileApi.cancelFileTransferTask({ taskId });
    if (res.code === 200) {
      message.success('任务已取消');
      getTransferTasks();
    } else {
      message.error(res.message || '取消任务失败');
    }
  } catch (error) {
    console.error('取消任务失败:', error);
    message.error('取消任务失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  }
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 定时器
let timer = null;

// 监听弹窗显示状态，控制定时器
watch(visible, (newValue) => {
  if (newValue) {
    // 弹窗打开时，立即获取任务并启动定时器
    getTransferTasks();
    timer = setInterval(() => {
      getTransferTasks();
    }, 1000);
  } else {
    // 弹窗关闭时，清除定时器
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  }
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});

// 暴露方法给父组件
defineExpose({
  getTransferTasks,
  show: () => { visible.value = true; }
});

</script>

<style scoped>
/* 文件传输任务弹窗相关样式 */
</style>