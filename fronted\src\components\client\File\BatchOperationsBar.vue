<template>
  <div class="batch-operations-bar" v-if="selectedFiles.length > 0">
    <div class="selected-count">
      已选择 {{ selectedFiles.length }} 个项目
    </div>
    <div class="batch-actions">
      <a-button 
        type="primary" 
        size="small" 
        @click="handleCopy"
        :icon="h(CopyOutlined)"
      >
        复制
      </a-button>
      <a-button 
        size="small" 
        @click="handleCut"
        :icon="h(ScissorOutlined)"
      >
        剪切
      </a-button>
      <a-button 
        size="small" 
        @click="handlePaste"
        :disabled="!canPaste"
        :icon="h(FileAddOutlined)"
      >
        粘贴
      </a-button>
      <a-button 
        danger 
        size="small" 
        @click="handleDelete"
        :icon="h(DeleteOutlined)"
      >
        删除
      </a-button>
      <a-button 
        size="small" 
        @click="handleClearSelection"
        :icon="h(CloseOutlined)"
      >
        取消选择
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue';
import { 
  CopyOutlined, 
  ScissorOutlined, 
  FileAddOutlined, 
  DeleteOutlined, 
  CloseOutlined 
} from '@ant-design/icons-vue';

// 定义 props
interface Props {
  selectedFiles: string[];
  canPaste: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selectedFiles: () => [],
  canPaste: false
});

// 定义 emits
interface Emits {
  copy: [];
  cut: [];
  paste: [];
  delete: [];
  clearSelection: [];
}

const emit = defineEmits<Emits>();

// 事件处理函数
const handleCopy = () => {
  emit('copy');
};

const handleCut = () => {
  emit('cut');
};

const handlePaste = () => {
  emit('paste');
};

const handleDelete = () => {
  emit('delete');
};

const handleClearSelection = () => {
  emit('clearSelection');
};
</script>

<style scoped>
.batch-operations-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
  background: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 16px;
}

.selected-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.batch-actions .ant-btn {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
}

@media (max-width: 768px) {
  .batch-operations-bar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .batch-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .selected-count {
    text-align: center;
  }
}
</style>