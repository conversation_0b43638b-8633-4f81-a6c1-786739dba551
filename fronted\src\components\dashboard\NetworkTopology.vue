<template>
  <div class="network-topology">
    <div class="topology-header">
      <div class="header-left">
        <h3 class="topology-title">网络拓扑</h3>
        <div class="topology-stats">
          <span class="stat-item">
            <span class="stat-value">{{ listeners.length }}</span>
            <span class="stat-label">监听器</span>
          </span>
          <span class="stat-divider">|</span>
          <span class="stat-item">
            <span class="stat-value">{{ clients.length }}</span>
            <span class="stat-label">客户端</span>
          </span>
        </div>
      </div>
      <div class="header-right">
        <div class="topology-controls">
          <!-- 缩放控制 -->
          <div class="zoom-controls">
            <button class="control-btn" @click="zoomIn" title="放大">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </button>
            <span class="zoom-level">{{ Math.round(zoomState.scale * 100) }}%</span>
            <button class="control-btn" @click="zoomOut" title="缩小">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 13H5v-2h14v2z"/>
              </svg>
            </button>
            <button class="control-btn" @click="resetZoom" title="重置缩放">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </button>
          </div>

          <!-- 布局控制 -->
          <div class="layout-controls">
            <button class="control-btn" @click="resetLayout" title="重置布局">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
              </svg>
            </button>
            <button class="control-btn" @click="toggleAutoLayout" :class="{ active: autoLayout }" title="自动布局">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="topology-canvas" ref="canvasContainer">
      <svg
        ref="svgCanvas"
        class="topology-svg"
        :width="canvasWidth"
        :height="canvasHeight"
        @mousedown="onCanvasMouseDown"
        @mousemove="onCanvasMouseMove"
        @mouseup="onCanvasMouseUp"
        @wheel="onCanvasWheel"
      >
        <!-- 定义渐变、滤镜和图案 -->
        <defs>
          <!-- 小网格图案 - 完美循环，适应缩放 -->
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <!-- 完整的网格单元 -->
            <rect width="20" height="20" fill="none"/>
            <!-- 右边线 -->
            <line x1="20" y1="0" x2="20" y2="20" stroke="rgba(100, 116, 139, 0.4)" stroke-width="0.8"/>
            <!-- 底边线 -->
            <line x1="0" y1="20" x2="20" y2="20" stroke="rgba(100, 116, 139, 0.4)" stroke-width="0.8"/>
          </pattern>

          <!-- 大网格图案 - 完美循环，适应缩放 -->
          <pattern id="gridLarge" width="100" height="100" patternUnits="userSpaceOnUse">
            <!-- 完整的网格单元 -->
            <rect width="100" height="100" fill="none"/>
            <!-- 右边线 -->
            <line x1="100" y1="0" x2="100" y2="100" stroke="rgba(100, 116, 139, 0.6)" stroke-width="1.5"/>
            <!-- 底边线 -->
            <line x1="0" y1="100" x2="100" y2="100" stroke="rgba(100, 116, 139, 0.6)" stroke-width="1.5"/>
          </pattern>

          <!-- 连接线渐变 -->
          <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.8" />
          </linearGradient>

          <!-- 节点阴影 -->
          <filter id="nodeShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0,0,0,0.1)"/>
          </filter>

          <!-- 网格发光效果 -->
          <filter id="gridGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <!-- 缩放和平移变换组 - 包含网格和所有可交互元素 -->
        <g :transform="`translate(${zoomState.translateX},${zoomState.translateY}) scale(${zoomState.scale})`">

          <!-- 网格背景层 - 跟随缩放变换 -->
          <g class="grid-layer">
            <!-- 小网格 - 使用超大矩形确保无边界 -->
            <rect
              :x="-canvasWidth * 3"
              :y="-canvasHeight * 3"
              :width="canvasWidth * 7"
              :height="canvasHeight * 7"
              fill="url(#grid)"
              opacity="1.0"
            />
            <!-- 大网格 - 使用超大矩形确保无边界 -->
            <rect
              :x="-canvasWidth * 3"
              :y="-canvasHeight * 3"
              :width="canvasWidth * 7"
              :height="canvasHeight * 7"
              fill="url(#gridLarge)"
              opacity="1.0"
            />
          </g>

        <!-- 网络拓扑连接线 -->
        <g class="topology-connections">
          <line
            v-for="connection in dynamicConnections"
            :key="`connection-${connection.id}`"
            :x1="connection.x1"
            :y1="connection.y1"
            :x2="connection.x2"
            :y2="connection.y2"
            class="topology-line"
            :class="connection.type"
            stroke="#10b981"
            stroke-width="2"
            opacity="0.8"
          />
        </g>

        <!-- Server节点 -->
        <g class="server-layer">
          <g
            :transform="`translate(${serverNode.x}, ${serverNode.y})`"
            class="server-node"
          >
            <!-- 拖拽区域 -->
            <circle
              cx="0"
              cy="0"
              :r="nodeRadius.server"
              fill="transparent"
              stroke="transparent"
              class="drag-area"
              @mousedown="onNodeMouseDown($event, serverNode, 'server')"
            />

            <!-- Server图标 -->
            <foreignObject
              :x="-nodeRadius.server"
              :y="-nodeRadius.server"
              :width="nodeRadius.server * 2"
              :height="nodeRadius.server * 2"
              class="server-icon-container"
              pointer-events="none"
            >
              <SystemIcons
                type="c2server"
                size="large"
                status="online"
              />
            </foreignObject>

            <!-- Server标签 -->
            <text
              x="0"
              :y="nodeRadius.server + 24"
              text-anchor="middle"
              class="node-label server-label"
              pointer-events="none"
            >
              C2 Server
            </text>
          </g>
        </g>
        
        <!-- 监听器节点已移除，信息显示在客户端节点上 -->
        
        <!-- 客户端节点 -->
        <g class="clients-layer">
          <g
            v-for="client in clients"
            :key="`client-${client.id}`"
            :transform="`translate(${client.x}, ${client.y})`"
            class="client-node"
          >
            <!-- 拖拽区域 (透明圆形) -->
            <circle
              cx="0"
              cy="0"
              :r="nodeRadius.client"
              fill="transparent"
              stroke="transparent"
              class="drag-area"
              @mousedown="onNodeMouseDown($event, client, 'client')"
            />

            <!-- 客户端系统图标 -->
            <foreignObject
              :x="-nodeRadius.client"
              :y="-nodeRadius.client"
              :width="nodeRadius.client * 2"
              :height="nodeRadius.client * 2"
              class="client-icon-container"
              pointer-events="none"
            >
              <SystemIcons
                :type="getClientIconType(client)"
                size="large"
                :status="client.status === 1 ? 'online' : 'offline'"
              />
            </foreignObject>

            <!-- 客户端标签 -->
            <text
              x="0"
              :y="nodeRadius.client + 24"
              text-anchor="middle"
              class="node-label"
              pointer-events="none"
            >
              {{ client.hostname || client.remoteAddr }}
            </text>

            <!-- 客户端详细信息 -->
            <text
              x="0"
              :y="nodeRadius.client + 38"
              text-anchor="middle"
              class="node-detail"
              pointer-events="none"
            >
              {{ client.username }}@{{ client.architecture }}
            </text>
          </g>
        </g>

        </g> <!-- 关闭变换组 -->
      </svg>

      <!-- 🔍 操作提示面板 -->
      <div class="operation-tips" :class="{ 'show': showTips }">
        <div class="tips-header">
          <h4>🖱️ 操作指南</h4>
          <button class="close-btn" @click="showTips = false">×</button>
        </div>
        <div class="tips-content">
          <div class="tip-section">
            <h5>🎯 画布操作</h5>
            <ul>
              <li><strong>缩放：</strong>鼠标滚轮 或 使用缩放按钮</li>
              <li><strong>平移：</strong>中键拖拽 或 Ctrl+左键拖拽</li>
              <li><strong>长按拖拽：</strong>左键长按500ms后拖拽（主要垂直移动）</li>
            </ul>
          </div>
          <div class="tip-section">
            <h5>🎮 节点操作</h5>
            <ul>
              <li><strong>拖拽节点：</strong>直接拖拽任意节点（包括Server）</li>
              <li><strong>查看信息：</strong>鼠标悬停查看节点详情</li>
            </ul>
          </div>
          <div class="tip-section">
            <h5>⚙️ 布局控制</h5>
            <ul>
              <li><strong>自动布局：</strong>开启后每5秒自动重新排列</li>
              <li><strong>重置布局：</strong>恢复到默认算法位置</li>
              <li><strong>重置缩放：</strong>恢复到100%缩放比例</li>
            </ul>
          </div>
          <div class="tip-section">
            <h5>⌨️ 快捷键</h5>
            <ul>
              <li><strong>H键：</strong>显示/隐藏操作提示</li>
              <li><strong>R键：</strong>重置布局</li>
              <li><strong>空格键：</strong>重置缩放和位置</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 🔍 操作提示按钮 -->
      <button class="tips-toggle-btn" @click="showTips = !showTips" title="操作提示 (按H键)">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
        </svg>
      </button>

      <!-- 🎯 状态指示器 -->
      <div class="status-indicator" v-if="panState.isPanning || dragState.isDragging">
        <div class="status-content">
          <span v-if="panState.isLongPressing">🖱️ 长按拖拽模式</span>
          <span v-else-if="panState.isPanning">🖱️ 画布拖拽模式</span>
          <span v-else-if="dragState.isDragging">🎯 节点拖拽模式</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, computed, nextTick } from 'vue'
import { dashboardApi } from '@/api'
import SystemIcons from '../icons/SystemIcons.vue'

// Props
const props = defineProps({
  dashboardStats: {
    type: Object,
    default: () => ({})
  },
  topologyData: {
    type: Object,
    default: null
  },
  sseConnected: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const canvasContainer = ref(null)
const svgCanvas = ref(null)
const canvasWidth = ref(800)
const canvasHeight = ref(400)

// 节点半径配置
const nodeRadius = {
  listener: 25,
  client: 32,  // 增大以适应新的图标
  server: 40   // Server节点更大
}

// 拖拽状态
const dragState = reactive({
  isDragging: false,
  dragNode: null,
  dragType: null,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0,
  lastX: 0,
  lastY: 0
})

// 控制状态
const autoLayout = ref(true)
const loading = ref(true)
const autoLayoutTimer = ref(null)
const showTips = ref(false)

// 🔍 缩放和平移状态
const zoomState = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0,
  minScale: 0.1,
  maxScale: 3
})

// 平移状态
const panState = reactive({
  isPanning: false,
  startX: 0,
  startY: 0,
  lastTranslateX: 0,
  lastTranslateY: 0,
  longPressTimer: null,
  isLongPressing: false,
  longPressThreshold: 500 // 长按阈值500ms
})

// 🌐 真实数据
const listeners = ref([])
const clients = ref([])
const topologyData = ref(null)
const topologyConnections = ref([])

// 🌐 动态连接线计算（实现真实的网络层次结构）
const dynamicConnections = computed(() => {
  const connections = []

  // 构建父子关系映射
  const parentChildMap = buildNetworkHierarchy()

  clients.value.forEach(client => {
    if (client.status === 1) { // 只处理在线的客户端
      const parentId = parentChildMap.get(client.id)

      if (parentId) {
        // 有父节点，连接到父节点
        const parentClient = clients.value.find(c => c.id === parentId)
        if (parentClient) {
          connections.push({
            id: `client-${parentId}-to-client-${client.id}`,
            x1: parentClient.x,
            y1: parentClient.y,
            x2: client.x,
            y2: client.y,
            type: 'child-connection'
          })
        }
      } else {
        // 没有父节点，直接连接到服务器
        connections.push({
          id: `server-to-client-${client.id}`,
          x1: serverNode.value.x,
          y1: serverNode.value.y,
          x2: client.x,
          y2: client.y,
          type: 'root-connection'
        })
      }
    }
  })

  return connections
})

// 🌐 构建网络层次结构
const buildNetworkHierarchy = () => {
  const parentChildMap = new Map() // 子节点ID -> 父节点ID

  clients.value.forEach(client => {
    // 提取客户端的远程地址IP（去掉端口号）
    const remoteIP = extractIPFromAddress(client.remoteAddr)

    // 查找是否有其他客户端的内网地址与此远程IP匹配
    const potentialParent = clients.value.find(otherClient =>
      otherClient.id !== client.id &&
      otherClient.localIP === remoteIP
    )

    if (potentialParent) {
      parentChildMap.set(client.id, potentialParent.id)
      console.log(`🔗 发现层次关系: ${client.hostname}(${client.id}) -> ${potentialParent.hostname}(${potentialParent.id})`)
    }
  })

  return parentChildMap
}

// 🔧 从地址中提取IP（去掉端口号）
const extractIPFromAddress = (address) => {
  if (!address) return ''

  // 处理IPv4地址，格式如 "***********:8080"
  const match = address.match(/^([^:]+):/)
  return match ? match[1] : address
}

// Server节点（固定位置）
const serverNode = ref({
  x: 0,
  y: 0,
  id: 'server',
  type: 'server',
  status: 'online'
})

// 获取操作系统类型（支持更多发行版）
const getOSType = (os) => {
  const osLower = os.toLowerCase()

  // Windows系列
  if (osLower.includes('windows')) return 'windows'

  // Linux发行版
  if (osLower.includes('ubuntu')) return 'ubuntu'
  if (osLower.includes('debian')) return 'debian'
  if (osLower.includes('centos')) return 'centos'
  if (osLower.includes('redhat') || osLower.includes('rhel')) return 'redhat'
  if (osLower.includes('fedora')) return 'fedora'
  if (osLower.includes('suse') || osLower.includes('opensuse')) return 'suse'
  if (osLower.includes('arch')) return 'arch'
  if (osLower.includes('linux')) return 'linux' // 通用Linux
  if (osLower.includes('kali')) return 'kali'
  // macOS系列
  if (osLower.includes('darwin') || osLower.includes('macos') || osLower.includes('mac')) return 'macos'

  // Unix系统（pipe监听器检测到的）
  if (osLower === 'unix') return 'linux' // Unix系统默认显示为Linux图标

  // 移动端
  if (osLower.includes('android')) return 'android'
  if (osLower.includes('ios')) return 'ios'

  return 'default'
}



// 检查客户端是否通过pipe监听器连接
const isClientFromPipeListener = (client) => {
  const listener = listeners.value.find(l => l.id === client.listenerId)
  return listener && listener.type === 'pipe'
}

// 获取客户端的图标类型
const getClientIconType = (client) => {
  // pipe监听器的客户端也根据实际操作系统显示图标
  // 因为pipe监听器会检测并更新client.os字段
  let osType = getOSType(client.os)

  // 如果检测到的是unix，但通过pipe监听器连接，可能是未知的Unix系统
  if (osType === 'default' && client.os === 'unix' && isClientFromPipeListener(client)) {
    return 'linux' // 默认显示为Linux图标，因为大多数Unix系统都是Linux-like
  }

  return osType
}

// 🌐 获取网络拓扑数据
const fetchTopologyData = async () => {
  try {
    loading.value = true
    console.log('🔄 正在获取网络拓扑数据...')

    const response = await dashboardApi.getNetworkTopology()
    console.log('📊 拓扑数据响应:', response)

    if (response.code === 200 && response.data && response.data.topology) {
      topologyData.value = response.data.topology

      // 更新监听器数据
      listeners.value = response.data.topology.listeners.map(listener => ({
        id: listener.id,
        type: listener.type,
        remark: listener.remark || `${listener.type.toUpperCase()}-${listener.id}`,
        status: listener.status,
        clientCount: listener.clientCount,
        localListenAddr: listener.localListenAddr,
        remoteConnectAddr: listener.remoteConnectAddr,
        x: listener.x || 0,
        y: listener.y || 0
      }))

      // 更新客户端数据
      clients.value = response.data.topology.clients.map(client => ({
        id: client.id,
        listenerId: client.listenerId,
        os: client.os,
        osVersion: client.osVersion,
        hostname: client.hostname,
        username: client.username,
        remoteAddr: client.remoteAddr,
        localIP: client.localIP,
        publicIP: client.publicIP,
        status: client.status,
        architecture: client.architecture,
        domain: client.domain,
        workgroup: client.workgroup,
        networkType: client.networkType,
        geoLocation: client.geoLocation,
        isp: client.isp,
        cpuUsage: client.cpuUsage,
        memoryUsage: client.memoryUsage,
        latency: client.latency,
        x: client.x || 0,
        y: client.y || 0
      }))

      console.log(`✅ 拓扑数据加载完成: ${listeners.value.length} 个监听器, ${clients.value.length} 个客户端`)

      // 如果节点没有位置信息，执行自动布局
      const needsLayout = listeners.value.some(l => l.x === 0 && l.y === 0) ||
                         clients.value.some(c => c.x === 0 && c.y === 0)
      if (needsLayout) {
        console.log('🎯 执行自动布局...')
        autoLayoutNodes()
      }
    } else {
      console.error('❌ 拓扑数据格式错误:', response)
    }
  } catch (error) {
    console.error('❌ 获取拓扑数据失败:', error)
  } finally {
    loading.value = false
  }
}



// 🔍 缩放和平移事件处理
const onCanvasMouseDown = (event) => {
  // 检查是否点击在节点上，如果是则不处理画布拖拽
  const target = event.target
  if (target.closest('.client-node') || target.closest('.server-node')) {
    return // 让节点拖拽处理
  }

  if (event.button === 1 || (event.button === 0 && event.ctrlKey)) {
    // 中键或Ctrl+左键立即开始平移
    event.preventDefault()
    panState.isPanning = true
    panState.isLongPressing = false
    panState.startX = event.clientX
    panState.startY = event.clientY
    panState.lastTranslateX = zoomState.translateX
    panState.lastTranslateY = zoomState.translateY
    document.body.style.cursor = 'grabbing'
    console.log('🖱️ 中键/Ctrl+左键拖拽开始')
  } else if (event.button === 0) {
    // 左键长按检测
    panState.startX = event.clientX
    panState.startY = event.clientY
    panState.lastTranslateX = zoomState.translateX
    panState.lastTranslateY = zoomState.translateY

    // 设置长按定时器
    panState.longPressTimer = setTimeout(() => {
      if (!panState.isPanning) { // 确保没有其他拖拽在进行
        panState.isLongPressing = true
        panState.isPanning = true
        document.body.style.cursor = 'move'
        console.log('🖱️ 长按检测成功，开始画布移动模式')
      }
    }, panState.longPressThreshold)
  }
}

const onCanvasMouseMove = (event) => {
  // 如果鼠标移动距离超过阈值，取消长按检测
  if (panState.longPressTimer && !panState.isLongPressing && !panState.isPanning) {
    const deltaX = Math.abs(event.clientX - panState.startX)
    const deltaY = Math.abs(event.clientY - panState.startY)
    if (deltaX > 5 || deltaY > 5) {
      clearTimeout(panState.longPressTimer)
      panState.longPressTimer = null
    }
  }

  if (panState.isPanning) {
    // 平移画布
    event.preventDefault()
    const deltaX = event.clientX - panState.startX
    const deltaY = event.clientY - panState.startY

    if (panState.isLongPressing) {
      // 长按模式：主要响应垂直移动，水平移动权重较小
      zoomState.translateX = panState.lastTranslateX + deltaX * 0.3
      zoomState.translateY = panState.lastTranslateY + deltaY
      console.log(`🖱️ 长按拖拽: deltaX=${deltaX}, deltaY=${deltaY}`)
    } else {
      // 普通平移模式（中键或Ctrl+左键）
      zoomState.translateX = panState.lastTranslateX + deltaX
      zoomState.translateY = panState.lastTranslateY + deltaY
      console.log(`🖱️ 普通拖拽: deltaX=${deltaX}, deltaY=${deltaY}`)
    }
  } else if (dragState.isDragging && dragState.dragNode) {
    // 拖拽节点
    event.preventDefault()
    event.stopPropagation()

    const rect = svgCanvas.value.getBoundingClientRect()
    const currentX = (event.clientX - rect.left - zoomState.translateX) / zoomState.scale
    const currentY = (event.clientY - rect.top - zoomState.translateY) / zoomState.scale

    // 计算移动距离，避免微小抖动
    const deltaX = currentX - dragState.lastX
    const deltaY = currentY - dragState.lastY

    // 只有移动距离大于阈值才更新位置
    if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
      const newX = currentX - dragState.offsetX
      const newY = currentY - dragState.offsetY

      // 更新节点位置
      if (dragState.dragType === 'client') {
        const client = clients.value.find(c => c.id === dragState.dragNode.id)
        if (client) {
          client.x = newX
          client.y = newY
        }
      } else if (dragState.dragType === 'server') {
        // Server节点也可以拖拽
        serverNode.value.x = newX
        serverNode.value.y = newY
      }

      // 更新最后位置
      dragState.lastX = currentX
      dragState.lastY = currentY
    }
  }
}

const onCanvasMouseUp = () => {
  // 清除长按定时器
  if (panState.longPressTimer) {
    clearTimeout(panState.longPressTimer)
    panState.longPressTimer = null
  }

  if (panState.isPanning) {
    console.log('🖱️ 画布拖拽结束')
    panState.isPanning = false
    panState.isLongPressing = false
    document.body.style.cursor = ''
  }

  if (dragState.isDragging) {
    console.log('🖱️ 节点拖拽结束')
    dragState.isDragging = false
    dragState.dragNode = null
    dragState.dragType = null

    // 恢复样式
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
}

const onCanvasWheel = (event) => {
  event.preventDefault()

  const rect = svgCanvas.value.getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  // 计算缩放前的世界坐标
  const worldX = (mouseX - zoomState.translateX) / zoomState.scale
  const worldY = (mouseY - zoomState.translateY) / zoomState.scale

  // 计算缩放因子
  const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(zoomState.minScale, Math.min(zoomState.maxScale, zoomState.scale * scaleFactor))

  // 计算新的平移量，保持鼠标位置不变
  const newTranslateX = mouseX - worldX * newScale
  const newTranslateY = mouseY - worldY * newScale

  zoomState.scale = newScale
  zoomState.translateX = newTranslateX
  zoomState.translateY = newTranslateY
}

// ⌨️ 键盘快捷键处理
const onKeyDown = (event) => {
  // 避免在输入框中触发快捷键
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return
  }

  switch (event.key.toLowerCase()) {
    case 'h':
      event.preventDefault()
      showTips.value = !showTips.value
      break
    case 'r':
      event.preventDefault()
      resetLayout()
      break
    case ' ':
      event.preventDefault()
      resetZoom()
      break
  }
}

// 🔍 缩放控制函数
const zoomIn = () => {
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2

  const worldX = (centerX - zoomState.translateX) / zoomState.scale
  const worldY = (centerY - zoomState.translateY) / zoomState.scale

  const newScale = Math.min(zoomState.maxScale, zoomState.scale * 1.2)

  zoomState.scale = newScale
  zoomState.translateX = centerX - worldX * newScale
  zoomState.translateY = centerY - worldY * newScale
}

const zoomOut = () => {
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2

  const worldX = (centerX - zoomState.translateX) / zoomState.scale
  const worldY = (centerY - zoomState.translateY) / zoomState.scale

  const newScale = Math.max(zoomState.minScale, zoomState.scale / 1.2)

  zoomState.scale = newScale
  zoomState.translateX = centerX - worldX * newScale
  zoomState.translateY = centerY - worldY * newScale
}

const resetZoom = () => {
  zoomState.scale = 1
  zoomState.translateX = 0
  zoomState.translateY = 0
}

// 节点拖拽
const onNodeMouseDown = (event, node, type) => {
  event.stopPropagation()
  event.preventDefault()

  const rect = svgCanvas.value.getBoundingClientRect()
  const screenX = event.clientX - rect.left
  const screenY = event.clientY - rect.top

  // 转换为世界坐标
  const worldX = (screenX - zoomState.translateX) / zoomState.scale
  const worldY = (screenY - zoomState.translateY) / zoomState.scale

  dragState.isDragging = true
  dragState.dragNode = node
  dragState.dragType = type
  dragState.startX = screenX
  dragState.startY = screenY
  dragState.lastX = worldX
  dragState.lastY = worldY
  dragState.offsetX = worldX - node.x
  dragState.offsetY = worldY - node.y

  // 添加临时样式
  document.body.style.cursor = 'grabbing'
  document.body.style.userSelect = 'none'
}

// 控制功能
const resetLayout = () => {
  // 重置为默认布局
  autoLayoutNodes()
}

const toggleAutoLayout = () => {
  autoLayout.value = !autoLayout.value
  if (autoLayout.value) {
    // 启用自动布局时立即执行一次
    autoLayoutNodes()

    // 启动定时器，每5秒自动布局一次
    if (autoLayoutTimer.value) {
      clearInterval(autoLayoutTimer.value)
    }
    autoLayoutTimer.value = setInterval(() => {
      if (autoLayout.value) {
        autoLayoutNodes()
      }
    }, 5000)
  } else {
    // 关闭自动布局时清除定时器
    if (autoLayoutTimer.value) {
      clearInterval(autoLayoutTimer.value)
      autoLayoutTimer.value = null
    }
  }
}



// 🌐 横向布局算法
const autoLayoutNodes = () => {
  if (clients.value.length === 0) {
    // 如果没有客户端，只显示Server在中央
    serverNode.value.x = canvasWidth.value / 2
    serverNode.value.y = canvasHeight.value / 2
    return
  }

  // 动态计算布局参数，基于画布尺寸
  const padding = Math.max(80, Math.min(120, canvasWidth.value * 0.1))
  const availableWidth = canvasWidth.value - 2 * padding
  const availableHeight = canvasHeight.value - 2 * padding

  // 根据画布大小和节点数量调整间距
  const levelSpacingX = Math.max(180, Math.min(250, availableWidth * 0.25))
  const nodeSpacingY = Math.max(100, Math.min(150, availableHeight / Math.max(clients.value.length, 4)))
  const childOffsetX = Math.max(120, Math.min(180, levelSpacingX * 0.7))
  const childOffsetY = Math.max(60, Math.min(100, nodeSpacingY * 0.8))

  console.log(`🎯 布局参数: 画布${canvasWidth.value}x${canvasHeight.value}, 间距X:${levelSpacingX}, 间距Y:${nodeSpacingY}`)

  // Server节点位置（左侧，垂直居中）
  serverNode.value.x = padding + nodeRadius.server
  serverNode.value.y = canvasHeight.value / 2

  // 构建层次结构
  const parentChildMap = buildNetworkHierarchy()

  // 找出根节点（直接连接到服务器的客户端）
  const rootClients = clients.value.filter(client => !parentChildMap.has(client.id))

  // 找出子节点
  const childClients = clients.value.filter(client => parentChildMap.has(client.id))

  console.log(`🌐 布局信息: ${rootClients.length} 个根节点, ${childClients.length} 个子节点`)

  // 布局根节点（第一层）- 智能分布
  const rootX = serverNode.value.x + levelSpacingX

  if (rootClients.length === 1) {
    // 单个根节点，居中对齐服务器
    rootClients[0].x = rootX
    rootClients[0].y = serverNode.value.y
  } else {
    // 多个根节点，垂直分布
    const totalRootHeight = (rootClients.length - 1) * nodeSpacingY
    const rootStartY = Math.max(padding + nodeRadius.client,
                               (canvasHeight.value - totalRootHeight) / 2)

    rootClients.forEach((client, index) => {
      client.x = rootX
      client.y = rootStartY + index * nodeSpacingY

      // 确保在画布范围内
      client.x = Math.max(padding + nodeRadius.client,
                         Math.min(canvasWidth.value - padding - nodeRadius.client, client.x))
      client.y = Math.max(padding + nodeRadius.client,
                         Math.min(canvasHeight.value - padding - nodeRadius.client, client.y))

      console.log(`📍 根节点 ${client.hostname}: (${client.x}, ${client.y})`)
    })
  }

  // 布局子节点（第二层）- 智能避免重叠
  const childPositions = new Map() // 记录每个父节点的子节点数量

  // 统计每个父节点的子节点数量
  childClients.forEach(child => {
    const parentId = parentChildMap.get(child.id)
    if (!childPositions.has(parentId)) {
      childPositions.set(parentId, [])
    }
    childPositions.get(parentId).push(child)
  })

  // 为每个父节点的子节点分配位置
  childPositions.forEach((children, parentId) => {
    const parent = clients.value.find(c => c.id === parentId)
    if (!parent) return

    if (children.length === 1) {
      // 单个子节点，放在父节点右下方
      const child = children[0]
      child.x = parent.x + childOffsetX
      child.y = parent.y + childOffsetY
    } else {
      // 多个子节点，垂直分布在父节点右侧
      const childSpacing = Math.min(nodeSpacingY * 0.8, availableHeight / (children.length + 1))
      const startY = parent.y - ((children.length - 1) * childSpacing) / 2

      children.forEach((child, index) => {
        child.x = parent.x + childOffsetX
        child.y = startY + index * childSpacing
      })
    }

    // 确保所有子节点在画布范围内
    children.forEach(child => {
      child.x = Math.max(padding + nodeRadius.client,
                        Math.min(canvasWidth.value - padding - nodeRadius.client, child.x))
      child.y = Math.max(padding + nodeRadius.client,
                        Math.min(canvasHeight.value - padding - nodeRadius.client, child.y))

      console.log(`📍 子节点 ${child.hostname} -> 父节点 ${parent.hostname}: (${child.x}, ${child.y})`)
    })
  })

  // 如果画布太小，自动扩展
  const maxX = Math.max(
    serverNode.value.x,
    ...rootClients.map(c => c.x),
    ...childClients.map(c => c.x)
  )
  const minRequiredWidth = maxX + padding + 100
  if (minRequiredWidth > canvasWidth.value) {
    canvasWidth.value = minRequiredWidth
  }
}

// 响应式调整画布大小
const resizeCanvas = () => {
  if (canvasContainer.value) {
    const rect = canvasContainer.value.getBoundingClientRect()
    const newWidth = Math.max(rect.width, 800) // 最小宽度800px
    const newHeight = Math.max(rect.height, 400) // 最小高度400px

    // 只有尺寸变化较大时才更新，避免频繁重新布局
    if (Math.abs(canvasWidth.value - newWidth) > 50 ||
        Math.abs(canvasHeight.value - newHeight) > 50) {
      const oldWidth = canvasWidth.value
      const oldHeight = canvasHeight.value

      canvasWidth.value = newWidth
      canvasHeight.value = newHeight

      console.log(`🖼️ 画布尺寸更新: ${oldWidth}x${oldHeight} -> ${newWidth}x${newHeight}`)

      // 如果画布尺寸变化较大且有客户端，重新布局节点
      if (clients.value.length > 0 && (oldWidth > 0 || oldHeight > 0)) {
        setTimeout(() => {
          console.log('🎯 画布尺寸变化，重新布局节点')
          autoLayoutNodes()
        }, 100)
      }
    }
  }
}

// 生命周期
onMounted(async () => {
  // 初始化画布尺寸
  resizeCanvas()

  // 等待DOM完全渲染后再次检查尺寸
  await nextTick()
  setTimeout(() => {
    resizeCanvas()
    console.log(`🖼️ 初始画布尺寸: ${canvasWidth.value}x${canvasHeight.value}`)
  }, 100)

  // 🌐 获取真实拓扑数据
  await fetchTopologyData()

  window.addEventListener('resize', resizeCanvas)
  window.addEventListener('mousemove', onCanvasMouseMove)
  window.addEventListener('mouseup', onCanvasMouseUp)
  window.addEventListener('keydown', onKeyDown)

  // 🔍 首次访问显示操作提示
  const hasSeenTips = localStorage.getItem('topology-tips-seen')
  if (!hasSeenTips) {
    setTimeout(() => {
      showTips.value = true
      // 8秒后自动隐藏
      setTimeout(() => {
        showTips.value = false
        localStorage.setItem('topology-tips-seen', 'true')
      }, 8000)
    }, 1500)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCanvas)
  window.removeEventListener('mousemove', onCanvasMouseMove)
  window.removeEventListener('mouseup', onCanvasMouseUp)
  window.removeEventListener('keydown', onKeyDown)

  // 清除自动布局定时器
  if (autoLayoutTimer.value) {
    clearInterval(autoLayoutTimer.value)
    autoLayoutTimer.value = null
  }

  // 清除长按定时器
  if (panState.longPressTimer) {
    clearTimeout(panState.longPressTimer)
    panState.longPressTimer = null
  }
})

// 监听拓扑数据变化
watch(() => props.topologyData, (newTopologyData) => {
  if (newTopologyData) {
    console.log('🔄 拓扑数据更新:', newTopologyData)
    updateTopologyFromSSE(newTopologyData)
  }
}, { deep: true })

// 🌐 从SSE数据更新拓扑
const updateTopologyFromSSE = (topologyData) => {
  if (!topologyData) return

  // 更新监听器数据
  if (topologyData.listeners) {
    listeners.value = topologyData.listeners.map(listener => ({
      id: listener.id,
      type: listener.type,
      remark: listener.remark || `${listener.type.toUpperCase()}-${listener.id}`,
      status: listener.status,
      clientCount: listener.clientCount,
      localListenAddr: listener.localListenAddr,
      remoteConnectAddr: listener.remoteConnectAddr,
      x: listener.x || listeners.value.find(l => l.id === listener.id)?.x || 0,
      y: listener.y || listeners.value.find(l => l.id === listener.id)?.y || 0
    }))
  }

  // 更新客户端数据
  if (topologyData.clients) {
    clients.value = topologyData.clients.map(client => ({
      id: client.id,
      listenerId: client.listenerId,
      os: client.os,
      osVersion: client.osVersion,
      hostname: client.hostname,
      username: client.username,
      remoteAddr: client.remoteAddr,
      localIP: client.localIP,
      publicIP: client.publicIP,
      status: client.status,
      architecture: client.architecture,
      domain: client.domain,
      workgroup: client.workgroup,
      networkType: client.networkType,
      geoLocation: client.geoLocation,
      isp: client.isp,
      cpuUsage: client.cpuUsage,
      memoryUsage: client.memoryUsage,
      latency: client.latency,
      x: client.x || clients.value.find(c => c.id === client.id)?.x || 0,
      y: client.y || clients.value.find(c => c.id === client.id)?.y || 0
    }))
  }
  console.log(clients.value)
  console.log(`✅ SSE拓扑数据更新完成: ${listeners.value.length} 个监听器, ${clients.value.length} 个客户端`)
}
</script>

<style scoped lang="scss">
.network-topology {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .topology-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-left {
      .topology-title {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 8px 0;
      }

      .topology-stats {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;

          .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #3b82f6;
          }

          .stat-label {
            font-size: 12px;
            color: #6b7280;
          }
        }

        .stat-divider {
          color: #d1d5db;
          font-size: 12px;
        }
      }
    }

    .header-right {
      .topology-controls {
        display: flex;
        gap: 16px;

        .zoom-controls, .layout-controls {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .zoom-level {
          font-size: 12px;
          color: #6b7280;
          font-weight: 500;
          min-width: 40px;
          text-align: center;
        }

        .control-btn {
          width: 32px;
          height: 32px;
          border: 1px solid rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.8);
          color: #6b7280;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(255, 255, 255, 1);
            color: #3b82f6;
            border-color: rgba(59, 130, 246, 0.2);
          }

          &.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .rotating {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }

  .topology-canvas {
    flex: 1;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;

    // 网格层样式
    .grid-layer {
      pointer-events: none;
      opacity: 1.0;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 1.0;
      }
    }
    position: relative;

    .topology-svg {
      width: 100%;
      height: 100%;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }

      // 拓扑连接线样式
      .topology-connections {
        .topology-line {
          transition: all 0.3s ease;

          &.root-connection {
            stroke: #10b981;
            stroke-width: 3;
            opacity: 0.9;
          }

          &.child-connection {
            stroke: #f59e0b;
            stroke-width: 2;
            opacity: 0.8;
            stroke-dasharray: 5,5;
          }

          &:hover {
            opacity: 1;
            stroke-width: 4;
            filter: drop-shadow(0 0 6px currentColor);
          }
        }
      }

      // 监听器节点样式
      .listeners-layer {
        .listener-node {
          cursor: move;
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.05);
          }

          .listener-bg {
            fill: #e5e7eb;
            stroke: #d1d5db;
            stroke-width: 2;
            transition: all 0.3s ease;

            &.running {
              fill: #10b981;
              stroke: #059669;
              filter: drop-shadow(0 0 8px rgba(16, 185, 129, 0.4));
            }
          }

          .listener-icon {
            font-size: 16px;
            fill: #6b7280;
            transition: all 0.3s ease;
            user-select: none;

            &.running {
              fill: white;
            }
          }
        }
      }

      // Server节点样式
      .server-layer {
        .server-node {
          .drag-area {
            cursor: grab; // Server节点可拖拽

            &:hover {
              stroke: rgba(16, 185, 129, 0.3);
              stroke-width: 3;
            }

            &:active {
              cursor: grabbing;
            }
          }

          &:hover {
            .server-icon-container {
              transform: scale(1.05);
            }
          }

          .server-icon-container {
            transition: transform 0.2s ease;
            pointer-events: none;
          }

          .server-label {
            font-weight: 700;
            fill: #1f2937;
          }
        }
      }

      // 客户端节点样式
      .clients-layer {
        .client-node {
          .drag-area {
            cursor: grab;

            &:hover {
              stroke: rgba(59, 130, 246, 0.3);
              stroke-width: 2;
            }

            &:active {
              cursor: grabbing;
            }
          }

          &:hover {
            .client-icon-container {
              transform: scale(1.05);
            }
          }

          .client-icon-container {
            transition: transform 0.2s ease;
            pointer-events: none;
          }
        }
      }

      // 节点标签样式
      .node-label {
        font-size: 12px;
        fill: #374151;
        font-weight: 600;
        user-select: none;
        text-anchor: middle;
      }

      // 节点详细信息样式
      .node-detail {
        font-size: 10px;
        fill: #6b7280;
        font-weight: 400;
        user-select: none;
        text-anchor: middle;
      }
    }
  }

  // 🔍 操作提示面板样式
  .operation-tips {
    position: absolute;
    top: 80px;
    right: 20px;
    width: 320px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;

    &.show {
      transform: translateX(0);
      opacity: 1;
    }

    .tips-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }

      .close-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: none;
        font-size: 18px;
        color: #6b7280;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
          color: #374151;
        }
      }
    }

    .tips-content {
      padding: 16px 20px;
      max-height: 400px;
      overflow-y: auto;

      .tip-section {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        h5 {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
        }

        ul {
          margin: 0;
          padding-left: 16px;

          li {
            margin-bottom: 6px;
            font-size: 13px;
            line-height: 1.4;
            color: #6b7280;

            &:last-child {
              margin-bottom: 0;
            }

            strong {
              color: #374151;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  // 🔍 操作提示按钮样式
  .tips-toggle-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #6b7280;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 999;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 1);
      color: #3b82f6;
      border-color: rgba(59, 130, 246, 0.2);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // 🎯 状态指示器样式
  .status-indicator {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;

    .status-content {
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      animation: statusFadeIn 0.3s ease;
    }
  }

  @keyframes statusFadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .network-topology {
    padding: 16px;

    .topology-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .header-right {
        align-self: flex-end;
      }
    }

    .topology-canvas {
      min-height: 300px;
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
