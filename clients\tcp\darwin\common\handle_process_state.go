//go:build darwin
// +build darwin

package common

/*
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/sysctl.h>
#include <sys/proc.h>
#include <libproc.h>
#include <errno.h>

// 快速获取macOS进程状态 - C语言实现，使用系统调用替代ps命令
// 返回值需要在Go中使用C.free()释放
char* get_macos_process_status_fast(int pid) {
    struct proc_taskinfo pti;
    char *result = NULL;
    int ret;

    // 使用proc_pidinfo获取进程信息
    ret = proc_pidinfo(pid, PROC_PIDTASKINFO, 0, &pti, sizeof(pti));
    if (ret <= 0) {
        return NULL; // 进程不存在或无权限访问
    }

    // 分配结果字符串内存
    result = (char*)malloc(128);
    if (result == NULL) {
        return NULL;
    }

    // 根据进程状态生成描述
    char base_status[64] = {0};
    char modifiers[64] = {0};
    int has_modifiers = 0;

    // 解析基本状态
    // 注意：macOS的proc_taskinfo结构体中没有直接的状态字段
    // 我们需要通过其他信息推断状态

    // 检查进程是否在运行
    if (pti.pti_virtual_size > 0) {
        // 进程有虚拟内存，可能在运行或睡眠
        if (pti.pti_resident_size > 0) {
            // 有物理内存，可能在运行
            strcpy(base_status, "Running");
        } else {
            // 没有物理内存，可能被换出
            strcpy(base_status, "Sleeping");
            if (!has_modifiers) {
                strcpy(modifiers, "swapped out");
                has_modifiers = 1;
            } else {
                strcat(modifiers, ", swapped out");
            }
        }
    } else {
        // 没有虚拟内存，可能是僵尸进程
        strcpy(base_status, "Zombie");
    }

    // 检查优先级
    // 注意：这里需要额外的系统调用来获取优先级信息
    // 为了简化，我们暂时跳过优先级检查

    // 组合最终结果
    if (has_modifiers) {
        snprintf(result, 128, "%s (%s)", base_status, modifiers);
    } else {
        strcpy(result, base_status);
    }

    return result;
}

// 使用sysctl获取更详细的进程信息
char* get_macos_process_status_detailed(int pid) {
    int mib[4];
    size_t size;
    struct kinfo_proc kp;
    char *result = NULL;

    // 设置sysctl参数
    mib[0] = CTL_KERN;
    mib[1] = KERN_PROC;
    mib[2] = KERN_PROC_PID;
    mib[3] = pid;

    size = sizeof(kp);
    if (sysctl(mib, 4, &kp, &size, NULL, 0) == -1) {
        return NULL; // 获取失败
    }

    // 分配结果字符串内存
    result = (char*)malloc(128);
    if (result == NULL) {
        return NULL;
    }

    // 解析kinfo_proc结构体中的状态信息
    char base_status[64] = {0};
    char modifiers[64] = {0};
    int has_modifiers = 0;

    // 解析进程状态
    switch (kp.kp_proc.p_stat) {
        case SIDL:
            strcpy(base_status, "Idle");
            break;
        case SRUN:
            strcpy(base_status, "Running");
            break;
        case SSLEEP:
            strcpy(base_status, "Sleeping");
            break;
        case SSTOP:
            strcpy(base_status, "Suspended");
            break;
        case SZOMB:
            strcpy(base_status, "Zombie");
            break;
        default:
            snprintf(base_status, sizeof(base_status), "Unknown(%d)", kp.kp_proc.p_stat);
            break;
    }

    // 检查进程标志
    if (kp.kp_proc.p_flag & P_INMEM) {
        // 进程在内存中
    } else {
        // 进程被换出
        if (!has_modifiers) {
            strcpy(modifiers, "swapped out");
            has_modifiers = 1;
        } else {
            strcat(modifiers, ", swapped out");
        }
    }

    if (kp.kp_proc.p_flag & P_TRACED) {
        // 进程被跟踪
        if (!has_modifiers) {
            strcpy(modifiers, "traced or debugged");
            has_modifiers = 1;
        } else {
            strcat(modifiers, ", traced or debugged");
        }
    }

    // 🔧 优化：会话领导者检测
    // 在macOS中，检查进程是否为会话领导者
    if (kp.kp_eproc.e_pgid == kp.kp_proc.p_pid && kp.kp_proc.p_pid > 2) {
        // 当进程组ID等于进程ID时，通常表示这是一个会话领导者
        if (!has_modifiers) {
            strcpy(modifiers, "session leader");
            has_modifiers = 1;
        } else {
            strcat(modifiers, ", session leader");
        }
    }

    // 🔧 优化：更精确的优先级检测，与Linux版本保持一致
    // 只有显著偏离默认值的优先级才显示修饰符
    if (kp.kp_proc.p_nice < -10) {  // 只有显著高优先级才显示
        if (!has_modifiers) {
            strcpy(modifiers, "high priority");
            has_modifiers = 1;
        } else {
            strcat(modifiers, ", high priority");
        }
    } else if (kp.kp_proc.p_nice > 10) {  // 只有显著低优先级才显示
        if (!has_modifiers) {
            strcpy(modifiers, "low priority");
            has_modifiers = 1;
        } else {
            strcat(modifiers, ", low priority");
        }
    }

    // 组合最终结果
    if (has_modifiers) {
        snprintf(result, 128, "%s (%s)", base_status, modifiers);
    } else {
        strcpy(result, base_status);
    }

    return result;
}

// 批量获取多个进程状态 - macOS优化版本
// pids: 进程ID数组
// count: 进程数量
// results: 结果数组，调用者需要为每个结果调用free()
int get_multiple_macos_process_status_fast(int *pids, int count, char **results) {
    int success_count = 0;

    // 批量处理，减少系统调用开销
    for (int i = 0; i < count; i++) {
        // 优先使用详细方法
        results[i] = get_macos_process_status_detailed(pids[i]);
        if (results[i] == NULL) {
            // 如果详细方法失败，尝试快速方法
            results[i] = get_macos_process_status_fast(pids[i]);
        }

        if (results[i] != NULL) {
            success_count++;
        }
    }

    return success_count;
}
*/
import "C"

import (
	"fmt"
	"os/exec"
	"strings"
	"unsafe"
)

// getProcessStatus 获取 macOS 进程状态，优先使用高性能C语言实现。
// 如果C语言实现失败，则回退到ps命令。
func getProcessStatus(pid int32) string {
	// 🚀 性能优化：方案一 - 使用C语言系统调用直接获取进程状态
	// 这比调用ps命令快10-100倍，避免了进程创建和上下文切换开销

	// 首先尝试使用sysctl获取详细信息
	cResult := C.get_macos_process_status_detailed(C.int(pid))
	if cResult != nil {
		// 将C字符串转换为Go字符串
		goResult := C.GoString(cResult)
		// 释放C分配的内存
		C.free(unsafe.Pointer(cResult))
		if goResult != "" {
			return goResult
		}
	}

	// 如果详细方法失败，尝试使用proc_pidinfo
	cResult = C.get_macos_process_status_fast(C.int(pid))
	if cResult != nil {
		// 将C字符串转换为Go字符串
		goResult := C.GoString(cResult)
		// 释放C分配的内存
		C.free(unsafe.Pointer(cResult))
		if goResult != "" {
			return goResult
		}
	}

	// 方案二：如果C语言实现失败，回退到ps命令（保持兼容性）
	cmd := exec.Command("ps", "-p", fmt.Sprint(pid), "-o", "state=")
	output, err := cmd.Output()
	if err != nil {
		// 如果命令执行失败，很可能是进程不存在
		return "" // 返回空字符串表示检测失败
	}

	// ps 的输出通常会带一个换行符，需要去除
	stateStr := strings.TrimSpace(string(output))

	if stateStr == "" {
		// 进程可能在命令执行期间退出了
		return ""
	}

	return parseMacosPsState(stateStr)
}

// parseMacosPsState 根据 `man ps` 文档，全面解析 macOS `ps` 命令输出的状态字符串。
func parseMacosPsState(stateStr string) string {
	if len(stateStr) == 0 {
		return ""
	}

	// 1. 解析主状态 (第一个字符)
	mainStateChar := stateStr[0]
	var baseStatus string
	switch mainStateChar {
	case 'I':
		baseStatus = "Idle" // (sleeping > 20s)
	case 'R':
		baseStatus = "Running"
	case 'S':
		baseStatus = "Sleeping" // (< 20s)
	case 'T':
		baseStatus = "Suspended" // or "Stopped"
	case 'U':
		baseStatus = "Disk Sleep" // (Uninterruptible wait)
	case 'Z':
		baseStatus = "Zombie"
	default:
		// 如果主状态未知，直接返回原始字符串
		return fmt.Sprintf("Unknown(%s)", stateStr)
	}

	// 2. 解析修饰符 (主状态后的所有字符)
	var modifiers []string
	if len(stateStr) > 1 {
		for _, modChar := range stateStr[1:] {
			switch modChar {
			case '+':
				modifiers = append(modifiers, "foreground")
			case '<':
				modifiers = append(modifiers, "high priority")
			case '>':
				modifiers = append(modifiers, "soft limit on memory")
			case 'A':
				modifiers = append(modifiers, "random page replacement")
			case 'E':
				modifiers = append(modifiers, "exiting")
			case 'L':
				modifiers = append(modifiers, "pages locked")
			case 'N':
				modifiers = append(modifiers, "low priority")
			case 'S': // 注意：这里是修饰符'S'，不是主状态'S'
				modifiers = append(modifiers, "FIFO page replacement")
			case 's':
				modifiers = append(modifiers, "session leader")
			case 'V':
				modifiers = append(modifiers, "suspended during vfork")
			case 'W':
				modifiers = append(modifiers, "swapped out")
			case 'X':
				modifiers = append(modifiers, "traced or debugged")
			}
		}
	}

	// 3. 组合最终的状态字符串
	if len(modifiers) > 0 {
		return fmt.Sprintf("%s (%s)", baseStatus, strings.Join(modifiers, ", "))
	}

	return baseStatus
}

// getMultipleProcessStatusFast 批量获取多个进程状态 - macOS高性能C语言实现
// 🚀 性能优化：批量处理减少函数调用开销，提高整体性能
func getMultipleProcessStatusFast(pids []int32) map[int32]string {
	if len(pids) == 0 {
		return make(map[int32]string)
	}

	// 转换Go切片为C数组
	cPids := make([]C.int, len(pids))
	for i, pid := range pids {
		cPids[i] = C.int(pid)
	}

	// 分配结果数组
	cResults := make([]*C.char, len(pids))

	// 调用C函数批量获取状态
	successCount := C.get_multiple_macos_process_status_fast(
		(*C.int)(unsafe.Pointer(&cPids[0])),
		C.int(len(pids)),
		(**C.char)(unsafe.Pointer(&cResults[0])),
	)

	// 转换结果并释放内存
	results := make(map[int32]string)
	for i, pid := range pids {
		if cResults[i] != nil {
			// 转换C字符串为Go字符串
			goResult := C.GoString(cResults[i])
			results[pid] = goResult
			// 释放C分配的内存
			C.free(unsafe.Pointer(cResults[i]))
		}
	}

	// 记录批量处理统计
	if successCount > 0 {
		// 可以在这里添加性能统计日志
	}

	return results
}

// // RunMacOSPerformanceTest 运行macOS性能测试，比较优化前后的性能差异
// func RunMacOSPerformanceTest() {
// 	// 获取所有进程ID
// 	pids, err := process.Pids()
// 	if err != nil {
// 		fmt.Println("获取进程ID失败:", err)
// 		return
// 	}

// 	// 限制测试进程数量
// 	maxTestPids := 100
// 	if len(pids) > maxTestPids {
// 		pids = pids[:maxTestPids]
// 	}

// 	fmt.Printf("开始macOS性能测试，测试 %d 个进程...\n", len(pids))

// 	// 测试原始ps命令方法
// 	startTime := time.Now()
// 	for _, pid := range pids {
// 		cmd := exec.Command("ps", "-p", fmt.Sprint(pid), "-o", "state=")
// 		output, _ := cmd.Output()
// 		if len(output) > 0 {
// 			stateStr := strings.TrimSpace(string(output))
// 			parseMacosPsState(stateStr)
// 		}
// 	}
// 	psTime := time.Since(startTime)
// 	fmt.Printf("ps命令方法耗时: %v, 平均每个进程: %v\n", psTime, psTime/time.Duration(len(pids)))

// 	// 测试C语言优化方法
// 	startTime = time.Now()
// 	for _, pid := range pids {
// 		cResult := C.get_macos_process_status_detailed(C.int(pid))
// 		if cResult != nil {
// 			C.GoString(cResult)
// 			C.free(unsafe.Pointer(cResult))
// 		}
// 	}
// 	cTime := time.Since(startTime)
// 	fmt.Printf("C语言优化方法耗时: %v, 平均每个进程: %v\n", cTime, cTime/time.Duration(len(pids)))

// 	// 计算性能提升
// 	if psTime > 0 {
// 		speedup := float64(psTime) / float64(cTime)
// 		fmt.Printf("macOS性能提升: %.2f倍\n", speedup)
// 	}

// 	// 测试批量处理方法
// 	startTime = time.Now()
// 	getMultipleProcessStatusFast(pids)
// 	batchTime := time.Since(startTime)
// 	fmt.Printf("批量处理方法耗时: %v, 平均每个进程: %v\n", batchTime, batchTime/time.Duration(len(pids)))

// 	// 计算批量处理相对于单个C调用的性能提升
// 	if cTime > 0 {
// 		batchSpeedup := float64(cTime) / float64(batchTime)
// 		fmt.Printf("批量处理相对于单个C调用的性能提升: %.2f倍\n", batchSpeedup)
// 	}

// 	// 验证结果一致性
// 	fmt.Println("\nmacOS验证结果一致性:")
// 	for i := 0; i < 5 && i < len(pids); i++ {
// 		pid := pids[i]

// 		// 获取ps命令结果
// 		cmd := exec.Command("ps", "-p", fmt.Sprint(pid), "-o", "state=")
// 		output, err := cmd.Output()
// 		var psResult string
// 		if err == nil && len(output) > 0 {
// 			stateStr := strings.TrimSpace(string(output))
// 			psResult = parseMacosPsState(stateStr)
// 		}

// 		// 获取C语言方法结果
// 		var cResult string
// 		cPtr := C.get_macos_process_status_detailed(C.int(pid))
// 		if cPtr != nil {
// 			cResult = C.GoString(cPtr)
// 			C.free(unsafe.Pointer(cPtr))
// 		}

// 		fmt.Printf("PID %d: PS结果=%q, C语言结果=%q, 一致=%v\n",
// 			pid, psResult, cResult, psResult == cResult)
// 	}
// }
