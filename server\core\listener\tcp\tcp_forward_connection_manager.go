package tcp

import (
	"context"
	"fmt"
	"net"
	"server/core/manager/dbpool"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/sys"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ForwardConnectionInfo 正向连接信息
type ForwardConnectionInfo struct {
	Config        *sys.ForwardConnection
	IsConnecting  bool
	CurrentRetry  int
	LastAttempt   time.Time
	StopChan      chan struct{}
	Context       context.Context
	Cancel        context.CancelFunc
}

// ForwardConnectionManager 正向连接管理器
type ForwardConnectionManager struct {
	listener    *TCPListener
	connections map[string]*ForwardConnectionInfo // clientAddr -> info
	mutex       sync.RWMutex
	stopChan    chan struct{}
	isRunning   bool
}

// NewForwardConnectionManager 创建正向连接管理器
func NewForwardConnectionManager(listener *TCPListener) *ForwardConnectionManager {
	return &ForwardConnectionManager{
		listener:    listener,
		connections: make(map[string]*ForwardConnectionInfo),
		stopChan:    make(chan struct{}),
	}
}

// Start 启动正向连接管理器
func (fcm *ForwardConnectionManager) Start() {
	fcm.mutex.Lock()
	if fcm.isRunning {
		fcm.mutex.Unlock()
		return
	}
	fcm.isRunning = true
	fcm.mutex.Unlock()

	global.LOG.Info("启动正向连接管理器", zap.Uint("listenerID", fcm.listener.ID))

	// 从数据库加载正向连接配置
	fcm.loadForwardConnections()

	// 启动连接监控goroutine
	go fcm.monitorConnections()
}

// Stop 停止正向连接管理器
func (fcm *ForwardConnectionManager) Stop() {
	fcm.mutex.Lock()
	defer fcm.mutex.Unlock()

	if !fcm.isRunning {
		return
	}

	fcm.isRunning = false
	close(fcm.stopChan)

	// 停止所有连接尝试
	for _, info := range fcm.connections {
		if info.Cancel != nil {
			info.Cancel()
		}
	}

	global.LOG.Info("正向连接管理器已停止", zap.Uint("listenerID", fcm.listener.ID))
}

// loadForwardConnections 从数据库加载正向连接配置
func (fcm *ForwardConnectionManager) loadForwardConnections() {
	var configs []sys.ForwardConnection
	if err := dbpool.ExecuteDBOperationAsyncAndWait("load_forward_connections", func(db *gorm.DB) error {
		return db.Where("listener_id = ? AND status = ?", fcm.listener.ID, 1).Find(&configs).Error
	}); err != nil {
		global.LOG.Error("加载正向连接配置失败", 
			zap.Uint("listenerID", fcm.listener.ID), 
			zap.Error(err))
		return
	}

	fcm.mutex.Lock()
	defer fcm.mutex.Unlock()

	for _, config := range configs {
		ctx, cancel := context.WithCancel(context.Background())
		info := &ForwardConnectionInfo{
			Config:      &config,
			Context:     ctx,
			Cancel:      cancel,
			StopChan:    make(chan struct{}),
		}
		fcm.connections[config.ClientAddr] = info
		
		global.LOG.Info("加载正向连接配置", 
			zap.Uint("listenerID", fcm.listener.ID),
			zap.String("clientAddr", config.ClientAddr),
			zap.Int("maxRetries", config.MaxRetries))
	}

	global.LOG.Info("正向连接配置加载完成", 
		zap.Uint("listenerID", fcm.listener.ID),
		zap.Int("count", len(configs)))
}

// monitorConnections 监控连接状态
func (fcm *ForwardConnectionManager) monitorConnections() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-fcm.stopChan:
			return
		case <-ticker.C:
			fcm.checkAndReconnect()
		}
	}
}

// checkAndReconnect 检查并重连断开的连接
func (fcm *ForwardConnectionManager) checkAndReconnect() {
	fcm.mutex.RLock()
	var needReconnect []*ForwardConnectionInfo
	
	for clientAddr, info := range fcm.connections {
		// 检查连接是否存在
		if _, exists := fcm.listener.connections.Load(clientAddr); !exists {
			// 连接不存在，需要重连
			if !info.IsConnecting && info.CurrentRetry < info.Config.MaxRetries {
				needReconnect = append(needReconnect, info)
			}
		}
	}
	fcm.mutex.RUnlock()

	// 启动重连
	for _, info := range needReconnect {
		go fcm.attemptReconnect(info)
	}
}

// attemptReconnect 尝试重连
func (fcm *ForwardConnectionManager) attemptReconnect(info *ForwardConnectionInfo) {
	fcm.mutex.Lock()
	if info.IsConnecting {
		fcm.mutex.Unlock()
		return
	}
	info.IsConnecting = true
	info.CurrentRetry++
	info.LastAttempt = time.Now()
	fcm.mutex.Unlock()

	defer func() {
		fcm.mutex.Lock()
		info.IsConnecting = false
		fcm.mutex.Unlock()
	}()

	clientAddr := info.Config.ClientAddr
	retryDelay := time.Duration(info.Config.RetryInterval) * time.Second
	
	// 实现指数退避
	if info.CurrentRetry > 1 {
		retryDelay = retryDelay * time.Duration(info.CurrentRetry)
		if retryDelay > 60*time.Second {
			retryDelay = 60 * time.Second
		}
	}

	global.LOG.Info("开始重连正向客户端",
		zap.Uint("listenerID", fcm.listener.ID),
		zap.String("clientAddr", clientAddr),
		zap.Int("retry", info.CurrentRetry),
		zap.Int("maxRetries", info.Config.MaxRetries),
		zap.Duration("delay", retryDelay))

	// 等待重连间隔
	select {
	case <-time.After(retryDelay):
	case <-info.Context.Done():
		return
	case <-fcm.stopChan:
		return
	}

	// 尝试连接
	if err := fcm.connectToClient(clientAddr); err != nil {
		global.LOG.Warn("正向客户端重连失败",
			zap.Uint("listenerID", fcm.listener.ID),
			zap.String("clientAddr", clientAddr),
			zap.Int("retry", info.CurrentRetry),
			zap.Int("maxRetries", info.Config.MaxRetries),
			zap.Error(err))

		// 更新数据库中的重连次数
		fcm.updateRetryCount(info.Config.ID, info.CurrentRetry)

		if info.CurrentRetry >= info.Config.MaxRetries {
			global.LOG.Error("正向客户端重连次数已达上限，停止重连",
				zap.Uint("listenerID", fcm.listener.ID),
				zap.String("clientAddr", clientAddr),
				zap.Int("maxRetries", info.Config.MaxRetries))
		}
	} else {
		global.LOG.Info("正向客户端重连成功",
			zap.Uint("listenerID", fcm.listener.ID),
			zap.String("clientAddr", clientAddr),
			zap.Int("retry", info.CurrentRetry))

		// 重连成功，重置重连次数
		fcm.mutex.Lock()
		info.CurrentRetry = 0
		fcm.mutex.Unlock()

		// 更新数据库
		fcm.updateRetryCount(info.Config.ID, 0)
		fcm.updateLastConnectTime(info.Config.ID)
	}
}

// connectToClient 连接到客户端
func (fcm *ForwardConnectionManager) connectToClient(clientAddr string) error {
	// 检查是否已经存在连接
	if _, loaded := fcm.listener.connections.Load(clientAddr); loaded {
		return fmt.Errorf("已存在到 %s 的连接", clientAddr)
	}

	// 主动连接到客户端
	conn, err := net.DialTimeout("tcp", clientAddr, 10*time.Second)
	if err != nil {
		return fmt.Errorf("连接到 %s 失败: %s", clientAddr, err.Error())
	}

	global.LOG.Info("成功连接到正向客户端",
		zap.Uint("listenerID", fcm.listener.ID),
		zap.String("clientAddr", clientAddr))

	// 使用网络工作池处理新连接
	connCopy := conn
	task := workerpool.NewNetworkTask("tcp_handle_forward_reconnection", func() error {
		fcm.listener.handleConnection(connCopy)
		return nil
	})

	if err := workerpool.SubmitNetworkTask(task); err != nil {
		global.LOG.Error("提交TCP正向重连处理任务失败",
			zap.Uint("listenerID", fcm.listener.ID),
			zap.String("clientAddr", clientAddr),
			zap.Error(err))
		conn.Close()
		return fmt.Errorf("提交连接处理任务失败: %s", err.Error())
	}

	return nil
}

// updateRetryCount 更新重连次数
func (fcm *ForwardConnectionManager) updateRetryCount(configID uint, retryCount int) {
	dbpool.ExecuteDBOperationAsyncAndWait("update_forward_retry_count", func(db *gorm.DB) error {
		return db.Model(&sys.ForwardConnection{}).
			Where("id = ?", configID).
			Update("retry_count", retryCount).Error
	})
}

// updateLastConnectTime 更新最后连接时间
func (fcm *ForwardConnectionManager) updateLastConnectTime(configID uint) {
	dbpool.ExecuteDBOperationAsyncAndWait("update_forward_last_connect", func(db *gorm.DB) error {
		return db.Model(&sys.ForwardConnection{}).
			Where("id = ?", configID).
			Update("last_connect_at", time.Now()).Error
	})
}

// AddForwardConnection 添加正向连接配置
func (fcm *ForwardConnectionManager) AddForwardConnection(clientAddr string, maxRetries, retryInterval int, remark string) error {
	// 检查是否已存在
	fcm.mutex.RLock()
	if _, exists := fcm.connections[clientAddr]; exists {
		fcm.mutex.RUnlock()
		return fmt.Errorf("正向连接配置已存在: %s", clientAddr)
	}
	fcm.mutex.RUnlock()

	// 创建配置
	config := &sys.ForwardConnection{
		ListenerID:    fcm.listener.ID,
		ClientAddr:    clientAddr,
		Status:        1,
		MaxRetries:    maxRetries,
		RetryInterval: retryInterval,
		RetryCount:    0,
		Remark:        remark,
	}

	// 保存到数据库
	if err := dbpool.ExecuteDBOperationAsyncAndWait("add_forward_connection", func(db *gorm.DB) error {
		return db.Create(config).Error
	}); err != nil {
		return fmt.Errorf("保存正向连接配置失败: %v", err)
	}

	// 添加到内存
	fcm.mutex.Lock()
	ctx, cancel := context.WithCancel(context.Background())
	info := &ForwardConnectionInfo{
		Config:   config,
		Context:  ctx,
		Cancel:   cancel,
		StopChan: make(chan struct{}),
	}
	fcm.connections[clientAddr] = info
	fcm.mutex.Unlock()

	global.LOG.Info("添加正向连接配置",
		zap.Uint("listenerID", fcm.listener.ID),
		zap.String("clientAddr", clientAddr),
		zap.Int("maxRetries", maxRetries))

	return nil
}

// RemoveForwardConnection 移除正向连接配置
func (fcm *ForwardConnectionManager) RemoveForwardConnection(clientAddr string) error {
	fcm.mutex.Lock()
	defer fcm.mutex.Unlock()

	info, exists := fcm.connections[clientAddr]
	if !exists {
		return fmt.Errorf("正向连接配置不存在: %s", clientAddr)
	}

	// 停止重连
	if info.Cancel != nil {
		info.Cancel()
	}

	// 从数据库删除
	if err := dbpool.ExecuteDBOperationAsyncAndWait("remove_forward_connection", func(db *gorm.DB) error {
		return db.Where("listener_id = ? AND client_addr = ?", fcm.listener.ID, clientAddr).
			Delete(&sys.ForwardConnection{}).Error
	}); err != nil {
		return fmt.Errorf("删除正向连接配置失败: %v", err)
	}

	// 从内存删除
	delete(fcm.connections, clientAddr)

	global.LOG.Info("移除正向连接配置",
		zap.Uint("listenerID", fcm.listener.ID),
		zap.String("clientAddr", clientAddr))

	return nil
}
